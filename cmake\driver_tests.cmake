# KMDF Driver Testing Framework
# u8fd9u4e2au6a21u5757u63d0u4f9bu4e86u7edfu4e00u7684u9a71u52a8u6d4bu8bd5u6846u67b6u914du7f6e

# u6dfbu52a0u9a71u52a8u6d4bu8bd5u5355u5143
#
# u53c2u6570:
#   NAME - u6d4bu8bd5u540du79f0
#   SOURCES - u6d4bu8bd5u6e90u6587u4ef6
#   LIBRARIES - u4f9du8d56u5e93
#   INCLUDE_DIRS - u5305u542bu76eeu5f55
#
function(add_driver_test)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs NAME)
    set(multiValueArgs SOURCES LIBRARIES INCLUDE_DIRS)
    cmake_parse_arguments(TEST "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT TEST_NAME)
        message(FATAL_ERROR "Test NAME not specified")
    endif()
    
    if(NOT TEST_SOURCES)
        message(FATAL_ERROR "Test SOURCES not specified")
    endif()
    
    # u6dfbu52a0u6d4bu8bd5u53efu6267u884cu7a0bu5e8f
    add_executable(${TEST_NAME} ${TEST_SOURCES})
    
    # u6dfbu52a0u5305u542bu76eeu5f55
    target_include_directories(${TEST_NAME} PRIVATE
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/tests/include
        ${TEST_INCLUDE_DIRS}
    )
    
    # 添加依赖库 - 只链接非可执行文件或带ENABLE_EXPORTS属性的目标
    if(TEST_LIBRARIES)
        foreach(lib ${TEST_LIBRARIES})
            if(TARGET ${lib})
                get_target_property(lib_type ${lib} TYPE)
                get_target_property(lib_exported ${lib} ENABLE_EXPORTS)
                
                if(NOT lib_type STREQUAL "EXECUTABLE" OR lib_exported)
                    target_link_libraries(${TEST_NAME} PRIVATE ${lib})
                else()
                    message(STATUS "Skipping linking of executable target ${lib} to ${TEST_NAME}")
                    # 直接引用目标的头文件路径
                    get_target_property(lib_include_dirs ${lib} INCLUDE_DIRECTORIES)
                    if(lib_include_dirs)
                        target_include_directories(${TEST_NAME} PRIVATE ${lib_include_dirs})
                    endif()
                endif()
            else()
                # 如果不是目标而是库文件，直接链接
                target_link_libraries(${TEST_NAME} PRIVATE ${lib})
            endif()
        endforeach()
    endif()
    
    # u8bbeu7f6eu6d4bu8bd5u8f93u51fau76eeu5f55
    set_target_properties(${TEST_NAME} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/bin
    )
    
    # u5c06u6d4bu8bd5u6dfbu52a0u5230CTest
    add_test(NAME ${TEST_NAME} COMMAND ${TEST_NAME})
    
    # u6253u5370u6d88u606f
    message(STATUS "Added driver test: ${TEST_NAME}")
endfunction()

# u6dfbu52a0u9a71u52a8u6a21u5757u6d4bu8bd5u5957u4ef6
#
# u53c2u6570:
#   MODULE - u6a21u5757u540du79f0
#   SOURCES - u6d4bu8bd5u6e90u6587u4ef6
#   LIBRARIES - u4f9du8d56u5e93 (u53efu9009)
#
function(add_module_test_suite)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs MODULE)
    set(multiValueArgs SOURCES LIBRARIES)
    cmake_parse_arguments(SUITE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT SUITE_MODULE)
        message(FATAL_ERROR "Test SUITE MODULE not specified")
    endif()
    
    if(NOT SUITE_SOURCES)
        message(FATAL_ERROR "Test SUITE SOURCES not specified")
    endif()
    
    # u521bu5efau6d4bu8bd5u53efu6267u884cu6587u4ef6
    add_driver_test(
        NAME test_${SUITE_MODULE}
        SOURCES ${SUITE_SOURCES}
        LIBRARIES ${SUITE_LIBRARIES}
        INCLUDE_DIRS ""
    )
    
    # u6dfbu52a0u6a21u5757u5b8fu5b9au4e49
    target_compile_definitions(test_${SUITE_MODULE} PRIVATE
        -DMODULE_NAME="${SUITE_MODULE}"
    )
endfunction()

# u542fu7528u6d4bu8bd5u8986u76d6u7387u5206u6790
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#
function(enable_test_coverage)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs "")
    cmake_parse_arguments(COVERAGE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT COVERAGE_TARGET)
        message(FATAL_ERROR "Coverage TARGET not specified")
    endif()
    
    # u6dfbu52a0u8986u76d6u7387u6807u5fd7
    target_compile_options(${COVERAGE_TARGET} PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/PROFILE>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-fprofile-arcs -ftest-coverage>
    )
    
    # u94feu63a5u8986u76d6u7387u5e93
    if(NOT MSVC)
        target_link_libraries(${COVERAGE_TARGET} PRIVATE gcov)
    endif()
    
    # u6dfbu52a0u6d4bu8bd5u8986u76d6u7387u76eeu6807
    add_custom_target(${COVERAGE_TARGET}_coverage
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_BINARY_DIR}/coverage
        COMMAND $<$<CXX_COMPILER_ID:MSVC>:vsinstr /coverage $<TARGET_FILE:${COVERAGE_TARGET}>>
        COMMAND $<$<CXX_COMPILER_ID:MSVC>:${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/coverage/${COVERAGE_TARGET}.coverage ${CMAKE_BINARY_DIR}/coverage/${COVERAGE_TARGET}.coverage.old>
        COMMAND $<$<CXX_COMPILER_ID:MSVC>:${COVERAGE_TARGET}>
        COMMAND $<$<CXX_COMPILER_ID:MSVC>:vsperfcmd /shutdown>
        COMMAND $<$<CXX_COMPILER_ID:MSVC>:vsperf /summary ${CMAKE_BINARY_DIR}/coverage/${COVERAGE_TARGET}.coverage /output:${CMAKE_BINARY_DIR}/coverage/${COVERAGE_TARGET}_coverage.xml>
        COMMAND $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:gcov -p -b $<TARGET_FILE:${COVERAGE_TARGET}>>
        DEPENDS ${COVERAGE_TARGET}
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/coverage
        COMMENT "Generating coverage report for ${COVERAGE_TARGET}"
    )
endfunction()
