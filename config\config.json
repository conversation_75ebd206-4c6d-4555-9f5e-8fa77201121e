{"version": "1.1.0", "environment": "development", "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/app.log"}, "paths": {"templates_dir": "templates", "output_dir": "output"}, "api": {"default_provider": "openai", "default_model": "gpt-3.5-turbo", "models_directory": "models", "available_models": ["openai", "openrouter", "gemini", "mistral", "e2b", "cloudflare", "glhf", "xun<PERSON>i", "jina", "huo<PERSON>", "deepseek"], "task_assignments": {"code_analysis": ["e2b", "deepseek", "openrouter"], "documentation": ["openai", "xun<PERSON>i", "gemini"], "security_audit": ["mistral", "e2b"], "performance_optimization": ["deepseek", "mistral"], "driver_development": ["e2b", "deepseek", "xun<PERSON>i"]}, "workflow": {"parallel_processing": true, "consensus_threshold": 0.7, "fallback_strategy": "use_master_model", "task_timeout": 300, "enable_model_voting": true}, "load_balancing": {"enabled": true, "strategy": "round_robin", "health_check_interval": 60}}, "quality": {"threshold": 0.7, "max_retries": 3, "metrics": {"completeness": 0.8, "accuracy": 0.9, "clarity": 0.8}, "rules": {"min_length": 50, "max_length": 500, "min_section_length": 20, "require_parameters": true, "require_returns": true, "require_examples": true}}, "format": {"style": "markdown", "include_metadata": true, "output_dir": "output", "formats": {"python": {"docstring_style": "google", "include_type_hints": true, "include_examples": true}, "javascript": {"docstring_style": "jsdoc", "include_type_hints": true, "include_examples": true}, "generic": {"docstring_style": "basic", "include_type_hints": false, "include_examples": false}}, "report": {"format": "markdown", "include_statistics": true, "include_file_list": true, "include_errors": true}, "template": {"header": "# {filename}\n\n", "function": "## {name}\n\n{description}\n\n### 参数\n{parameters}\n\n### 返回值\n{returns}\n\n### 示例\n{examples}\n\n", "class": "## {name}\n\n{description}\n\n### 方法\n{methods}\n\n### 属性\n{properties}\n\n"}, "metadata": {"include_timestamp": true, "include_version": true, "include_author": true}}, "cache": {"enabled": true, "ttl": 3600}, "security": {"api_key_required": true, "rate_limit": 60}, "analysis": {"scope": {"include_patterns": ["*.c", "*.h", "*.cpp"], "exclude_patterns": ["*test*", "*mock*", "*backup*"]}, "metrics": {"complexity": {"max_complexity": 15, "max_function_length": 100}, "naming": {"function_prefix": [""], "type_prefix": [""]}}, "rules": {"security_patterns": {"buffer_overflow": {"pattern": "strcpy|strcat|sprintf|vsprintf", "importance": "critical"}, "null_pointer": {"pattern": "\\s*if\\s*\\(\\s*[a-zA-Z_][a-zA-Z0-9_]*\\s*==\\s*NULL\\s*\\)", "importance": "high"}}, "code_quality_patterns": {"error_handling": {"pattern": "NT_SUCCESS|status|return.*error", "description": "proper error handling"}, "logging": {"pattern": "KdPrint|DbgPrint|WPP_TRACE", "description": "logging statements"}}, "function_patterns": {"driver_entry": {"pattern": "NTSTATUS\\s+DriverEntry\\s*\\([^)]*\\)"}, "device_io": {"pattern": "EVT_WDF_IO_QUEUE_IO_[A-Z_]+"}, "pnp": {"pattern": "EVT_WDF_DEVICE_[A-Z_]+"}}}}}