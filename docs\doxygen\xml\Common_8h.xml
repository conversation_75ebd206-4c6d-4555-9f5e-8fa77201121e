<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="Common_8h" kind="file" language="C++">
    <compoundname>Common.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <incdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/common/Common.h</label>
        <link refid="Common_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="Common_8h_1ab47772eb758c3b2a1a990360eaf8ad5c" prot="public" static="no">
        <name>HANDLE_ERROR</name>
        <param><defname>status</defname></param>
        <param><defname>message</defname></param>
        <initializer>    <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)) { \
        KmdfHandleError(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>, message, __LINE__, __FILE__); \
        return <ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>; \
    }</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/common/Common.h" line="35" column="9" bodyfile="C:/KMDF Driver1/include/core/common/Common.h" bodystart="35" bodyend="39"/>
      </memberdef>
      <memberdef kind="define" id="Common_8h_1a9ee9b45a31a1e12103d9ea23cc983d0c" prot="public" static="no">
        <name>KMDF_POOL_TAG</name>
        <initializer>&apos;FMDK&apos;</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/common/Common.h" line="18" column="9" bodyfile="C:/KMDF Driver1/include/core/common/Common.h" bodystart="18" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="Common_8h_1acfe39a25e08737b535dc881071ebf149" prot="public" static="no">
        <name>LOG_DEBUG</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, &quot;[KMDF] &quot; Format &quot;\n&quot;, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/common/Common.h" line="42" column="9" bodyfile="C:/KMDF Driver1/include/core/common/Common.h" bodystart="42" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="Common_8h_1ab90a3a515b5c728d627bf24ac17c3702" prot="public" static="no">
        <name>SAFE_FREE</name>
        <param><defname>Ptr</defname></param>
        <initializer>    <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Ptr) { \
        <ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>((Ptr), <ref refid="Common_8h_1a9ee9b45a31a1e12103d9ea23cc983d0c" kindref="member">KMDF_POOL_TAG</ref>); \
        (Ptr) = NULL; \
    }</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/common/Common.h" line="21" column="9" bodyfile="C:/KMDF Driver1/include/core/common/Common.h" bodystart="21" bodyend="25"/>
      </memberdef>
      <memberdef kind="define" id="Common_8h_1a6038f7bdb274c2e159988a57dedbf93d" prot="public" static="no">
        <name>SAFE_RELEASE</name>
        <param><defname>p</defname></param>
        <initializer>    <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (p) { \
        (p)-&gt;Release(); \
        (p) = NULL; \
    }</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/common/Common.h" line="28" column="9" bodyfile="C:/KMDF Driver1/include/core/common/Common.h" bodystart="28" bodyend="32"/>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>该文件定义了...</para>
<para><simplesect kind="author"><para>KMDF团队 </para>
</simplesect>
<simplesect kind="date"><para>2025-05-03 </para>
</simplesect>
</para>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_DRIVER1_INCLUDE_CORE_COMMON_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_DRIVER1_INCLUDE_CORE_COMMON_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Include<sp/>necessary<sp/>headers</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Memory<sp/>pool<sp/>tag</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="Common_8h_1a9ee9b45a31a1e12103d9ea23cc983d0c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_POOL_TAG<sp/>&apos;FMDK&apos;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Memory<sp/>allocation<sp/>and<sp/>free<sp/>macros</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="Common_8h_1ab90a3a515b5c728d627bf24ac17c3702" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SAFE_FREE(Ptr)<sp/>\</highlight></codeline>
<codeline lineno="22"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>if<sp/>(Ptr)<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="23"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>ExFreePoolWithTag((Ptr),<sp/>KMDF_POOL_TAG);<sp/>\</highlight></codeline>
<codeline lineno="24"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(Ptr)<sp/>=<sp/>NULL;<sp/>\</highlight></codeline>
<codeline lineno="25"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>安全释放资源的宏</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="Common_8h_1a6038f7bdb274c2e159988a57dedbf93d" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SAFE_RELEASE(p)<sp/>\</highlight></codeline>
<codeline lineno="29"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>if<sp/>(p)<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="30"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(p)-&gt;Release();<sp/>\</highlight></codeline>
<codeline lineno="31"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(p)<sp/>=<sp/>NULL;<sp/>\</highlight></codeline>
<codeline lineno="32"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="comment">//<sp/>错误处理宏</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="Common_8h_1ab47772eb758c3b2a1a990360eaf8ad5c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>HANDLE_ERROR(status,<sp/>message)<sp/>\</highlight></codeline>
<codeline lineno="36"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>if<sp/>(!NT_SUCCESS(status))<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="37"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KmdfHandleError(status,<sp/>message,<sp/>__LINE__,<sp/>__FILE__);<sp/>\</highlight></codeline>
<codeline lineno="38"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>return<sp/>status;<sp/>\</highlight></codeline>
<codeline lineno="39"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Debug<sp/>print<sp/>macros</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="Common_8h_1acfe39a25e08737b535dc881071ebf149" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_DEBUG(Format,<sp/>...)<sp/>DbgPrintEx(DPFLTR_IHVDRIVER_ID,<sp/>DPFLTR_INFO_LEVEL,<sp/>&quot;[KMDF]<sp/>&quot;<sp/>Format<sp/>&quot;\n&quot;,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43"><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_DRIVER1_INCLUDE_CORE_COMMON_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/core/common/Common.h"/>
  </compounddef>
</doxygen>
