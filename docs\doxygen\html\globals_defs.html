<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li class="current"><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index__5F"><span>_</span></a></li>
      <li><a href="#index_a"><span>a</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_f"><span>f</span></a></li>
      <li><a href="#index_g"><span>g</span></a></li>
      <li><a href="#index_h"><span>h</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_k"><span>k</span></a></li>
      <li><a href="#index_l"><span>l</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_n"><span>n</span></a></li>
      <li><a href="#index_o"><span>o</span></a></li>
      <li><a href="#index_r"><span>r</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li class="current"><a href="#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_defs.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all macros with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index__5F" name="index__5F"></a>- _ -</h3><ul>
<li>_KSPIN_LOCK_&#160;:&#160;<a class="el" href="core__types_8h.html#adf3938c38fa151a4752926adc328705f">core_types.h</a></li>
<li>_LIST_ENTRY_DEFINED&#160;:&#160;<a class="el" href="core__types_8h.html#af62a7f2c165fbe4d707330731083ebf9">core_types.h</a></li>
<li>_WDF_PNP_DEVICE_STATE_TYPE&#160;:&#160;<a class="el" href="core__types_8h.html#a977bf1d43521f47048b6d555ef981398">core_types.h</a></li>
<li>_WIN32_WINNT&#160;:&#160;<a class="el" href="precomp_8h.html#ac50762666aa00bd3a4308158510f1748">precomp.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>ARRAY_SIZE&#160;:&#160;<a class="el" href="precomp_8h.html#abf0924766241e8f46e68e2dcbca9ac5b">precomp.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>DEBUG_PRINT&#160;:&#160;<a class="el" href="precomp_8h.html#a7ba5cb5a943f312e7b50de9cd8ffaf37">precomp.h</a></li>
<li>DRIVER_ASSERT&#160;:&#160;<a class="el" href="error__handling_8h.html#a3a8ba39810c16fad65af2271c2dd6a6c">error_handling.h</a></li>
<li>DRIVER_NAME&#160;:&#160;<a class="el" href="driver__main_8c.html#a25634d21648ca7fb7a2aca614bafaaeb">driver_main.c</a></li>
<li>DRIVER_VERSION_BUILD&#160;:&#160;<a class="el" href="driver__main_8c.html#afe74bc852cf46e301606e5ac20720e34">driver_main.c</a></li>
<li>DRIVER_VERSION_MAJOR&#160;:&#160;<a class="el" href="driver__main_8c.html#a90d323e79537750a33d74eeab4a66837">driver_main.c</a></li>
<li>DRIVER_VERSION_MINOR&#160;:&#160;<a class="el" href="driver__main_8c.html#a31ba5103952e666f059389c86d76b640">driver_main.c</a></li>
<li>DRIVER_VERSION_REV&#160;:&#160;<a class="el" href="driver__main_8c.html#a3b369f69dba713375e6704ac172ecceb">driver_main.c</a></li>
</ul>


<h3 class="doxsection"><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>ERROR_BUS_PROTOCOL_ERROR&#160;:&#160;<a class="el" href="error__codes_8h.html#affd17c80938eed361b46758b3ab696cd">error_codes.h</a></li>
<li>ERROR_DEVICE_HARDWARE_ERROR&#160;:&#160;<a class="el" href="error__codes_8h.html#a254cfc13872e4cf4fe0f89067d00f86a">error_codes.h</a></li>
<li>ERROR_DEVICE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#adbe74e534b99eb53edd59a05ee8e426f">error_codes.h</a></li>
<li>ERROR_DEVICE_NOT_AVAILABLE&#160;:&#160;<a class="el" href="error__codes_8h.html#a869e1fe828b9b51d971bcfe6595101bd">error_codes.h</a></li>
<li>ERROR_DEVICE_NOT_READY&#160;:&#160;<a class="el" href="error__codes_8h.html#ac80b2a8fac0e8f846c5f16200c7bb19a">error_codes.h</a></li>
<li>ERROR_DRIVER_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a2db6445dc95ce4a34ce59d355566545c">error_codes.h</a></li>
<li>ERROR_DRIVER_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#af5020ced0fe4cabcbd18b06de7b4fd7f">error_codes.h</a></li>
<li>ERROR_HARDWARE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a1ee37513091f1fa4fd23ad7a31815169">error_codes.h</a></li>
<li>ERROR_I2C_ARBITRATION_LOST&#160;:&#160;<a class="el" href="error__codes_8h.html#abf05011deed9a7688487446dcedb7734">error_codes.h</a></li>
<li>ERROR_I2C_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a37b24e0918ff0d7e358234acfd7514bf">error_codes.h</a></li>
<li>ERROR_I2C_BUS_BUSY&#160;:&#160;<a class="el" href="error__codes_8h.html#a361a624ddf35d38e3673f8fd648bc533">error_codes.h</a></li>
<li>ERROR_I2C_DEVICE_NOT_RESPONDING&#160;:&#160;<a class="el" href="error__codes_8h.html#a8977c5e15064527b2c7e501feb3915de">error_codes.h</a></li>
<li>ERROR_INSTALL_FAILURE&#160;:&#160;<a class="el" href="error__codes_8h.html#a117a0e9b2ae10c0775f947eb6aaf2e43">error_codes.h</a></li>
<li>ERROR_INVALID_PARAMETER&#160;:&#160;<a class="el" href="error__codes_8h.html#a176960f93d11db6f58d10a6957a80f38">error_codes.h</a></li>
<li>ERROR_IO_DEVICE&#160;:&#160;<a class="el" href="error__codes_8h.html#a1d5275aca16a6e82736bccadf6d54f51">error_codes.h</a></li>
<li>ERROR_IO_OPERATION_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#ab62592f2d0fe2a25aceff8a2aea7120d">error_codes.h</a></li>
<li>ERROR_MEMORY_ALLOCATION_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a8c7e35c51169754be6ad9a33b4d0ede3">error_codes.h</a></li>
<li>ERROR_NOT_ENOUGH_MEMORY&#160;:&#160;<a class="el" href="error__codes_8h.html#a0e8ed8133680d2d6b8909e71b8048307">error_codes.h</a></li>
<li>ERROR_NOT_READY&#160;:&#160;<a class="el" href="error__codes_8h.html#a61a95049738405a02053f09a5707e1c9">error_codes.h</a></li>
<li>ERROR_QUEUE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a1dc03de0e9017ea32c0bab1658627cab">error_codes.h</a></li>
<li>ERROR_RESOURCE_NOT_FOUND&#160;:&#160;<a class="el" href="error__codes_8h.html#add601dcfffde2047b7051c4990d109e6">error_codes.h</a></li>
<li>ERROR_SPI_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a224c1c26e72afd5d9b1eb990a3808ba0">error_codes.h</a></li>
<li>ERROR_SPI_TRANSFER_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a8a14c6072318a642cb973e5e1b3749cf">error_codes.h</a></li>
<li>ERROR_SUCCESS&#160;:&#160;<a class="el" href="error__codes_8h.html#aea0ae801b7d25c979655a7eb20d034af">error_codes.h</a></li>
<li>ERROR_TIMEOUT&#160;:&#160;<a class="el" href="error__codes_8h.html#ad58fd51c3e56b5d9555ce48be5cc53bd">error_codes.h</a></li>
<li>ERROR_USB_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a3b6ff704bf43afbc0cd69efc9d429f64">error_codes.h</a></li>
<li>ERROR_USB_ENDPOINT_NOT_FOUND&#160;:&#160;<a class="el" href="error__codes_8h.html#a23a63dc8ca4262eacbd1b165c5c9de52">error_codes.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_f" name="index_f"></a>- f -</h3><ul>
<li>FUNCTION_ENTRY&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#ac56df030fb93601c871fd894e289601a">driver_log.h</a></li>
<li>FUNCTION_EXIT&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a347d8e7da8a8e1d1cebfd45f4055a4a8">driver_log.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_g" name="index_g"></a>- g -</h3><ul>
<li>GPIO_DEVICE_POOL_TAG&#160;:&#160;<a class="el" href="gpio__device_8c.html#a6acc285bc10925e95287d1821e20f2f7">gpio_device.c</a></li>
<li>GPIO_POOL_TAG&#160;:&#160;<a class="el" href="gpio__core_8c.html#aaccace669b39ad606306ac907224ae82">gpio_core.c</a></li>
</ul>


<h3 class="doxsection"><a id="index_h" name="index_h"></a>- h -</h3><ul>
<li>HANDLE_ERROR&#160;:&#160;<a class="el" href="Common_8h.html#ab47772eb758c3b2a1a990360eaf8ad5c">Common.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>I2C_ADDRESS_10BIT&#160;:&#160;<a class="el" href="i2c__device_8h.html#a64738b9f6c1ba2f1f919ef7a61ad1e35">i2c_device.h</a></li>
<li>I2C_ADDRESS_7BIT&#160;:&#160;<a class="el" href="i2c__device_8h.html#ab65b9830c26b77346e0f420c643d63b9">i2c_device.h</a></li>
<li>I2C_DEFAULT_TIMEOUT&#160;:&#160;<a class="el" href="i2c__device_8c.html#a4bbdac9bba21cb3959a7355001ea590f">i2c_device.c</a></li>
<li>I2C_TRANSFER_READ&#160;:&#160;<a class="el" href="i2c__device_8h.html#ad6922f686b3fc13f8365467975aba1d7">i2c_device.h</a></li>
<li>IN&#160;:&#160;<a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">precomp.h</a></li>
<li>INITGUID&#160;:&#160;<a class="el" href="device__manager_8c.html#af11aade3f3741fb554915d10d3f514eb">device_manager.c</a>, <a class="el" href="driver__core_8c.html#af11aade3f3741fb554915d10d3f514eb">driver_core.c</a></li>
<li>InitializeListHead&#160;:&#160;<a class="el" href="core__types_8h.html#acec93a755836f578590339c921773e21">core_types.h</a></li>
<li>InsertHeadList&#160;:&#160;<a class="el" href="core__types_8h.html#a14f3c1fe642e4927959b4beed2852e2a">core_types.h</a></li>
<li>InsertTailList&#160;:&#160;<a class="el" href="core__types_8h.html#a0bf4f8c1a40d587fc04e48b16db5a8c3">core_types.h</a></li>
<li>IOCTL_DEVICE_SPECIFIC_COMMAND&#160;:&#160;<a class="el" href="device__manager_8h.html#a06bac98d6f690509acee10555e212f41">device_manager.h</a></li>
<li>IOCTL_DRIVER_BASE&#160;:&#160;<a class="el" href="driver__core_8h.html#a0785b75b63fb30d328b99d809b6ef8d9">driver_core.h</a></li>
<li>IOCTL_DRIVER_GET_STATISTICS&#160;:&#160;<a class="el" href="driver__core_8h.html#a6d4bdae2f23ab96032a14098fb28b1f2">driver_core.h</a></li>
<li>IOCTL_DRIVER_GET_VERSION&#160;:&#160;<a class="el" href="driver__core_8h.html#a07c561db6f9baf7d9f5452605546aed9">driver_core.h</a></li>
<li>IOCTL_DRIVER_RESET&#160;:&#160;<a class="el" href="driver__core_8h.html#a9cc7d1b6ef52a7692138d85aa247f989">driver_core.h</a></li>
<li>IOCTL_DRIVER_SET_LOGGING&#160;:&#160;<a class="el" href="driver__core_8h.html#a3cfaff7b63b2bbdd9583818a4f090d3b">driver_core.h</a></li>
<li>IOCTL_GET_DEVICE_INFO&#160;:&#160;<a class="el" href="device__manager_8h.html#a5d922ac9f0d09258cd06cb7d8d7160af">device_manager.h</a></li>
<li>IOCTL_GPIO_DEVICE_BASE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a0ab51b588e38783851968f26ed81d41e">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_GET_STATE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a531a7dc21007b6e72d524a9fa29de43d">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_REGISTER_CALLBACK&#160;:&#160;<a class="el" href="gpio__device_8h.html#ada8c56039bf34983b27bd0338982f351">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_SET_STATE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a15338fabc6809209e45575e75f4867e4">gpio_device.h</a></li>
<li>IOCTL_GPIO_GET_VALUE&#160;:&#160;<a class="el" href="gpio__core_8c.html#a09573d341b2d8f94a213241de2444b0b">gpio_core.c</a></li>
<li>IOCTL_GPIO_SET_DIRECTION&#160;:&#160;<a class="el" href="gpio__core_8c.html#aa5235f4dd44bf922bf5befb2ef0b3b4b">gpio_core.c</a></li>
<li>IOCTL_GPIO_SET_VALUE&#160;:&#160;<a class="el" href="gpio__core_8c.html#a921358974fe0b0cbe1288fd8bdc34196">gpio_core.c</a></li>
<li>IOCTL_I2C_GET_STATISTICS&#160;:&#160;<a class="el" href="i2c__device_8h.html#a23164ebc8fc22800438176c588caa941">i2c_device.h</a></li>
<li>IOCTL_I2C_RESET&#160;:&#160;<a class="el" href="i2c__device_8h.html#a69ebe19cf050058019d84f005052cc00">i2c_device.h</a></li>
<li>IOCTL_I2C_SET_BUS_SPEED&#160;:&#160;<a class="el" href="i2c__device_8h.html#a9c3881592ae1c10fbd5ba1b9ae7e85ec">i2c_device.h</a></li>
<li>IOCTL_I2C_TRANSFER&#160;:&#160;<a class="el" href="i2c__device_8h.html#a15204e5c2582622fc3ef40f01fe93322">i2c_device.h</a></li>
<li>IOCTL_I2C_TRANSFER_SEQUENCE&#160;:&#160;<a class="el" href="i2c__device_8h.html#a478905e3b3f7f700dd9e0975f42fe1a3">i2c_device.h</a></li>
<li>IOCTL_RESET_DEVICE&#160;:&#160;<a class="el" href="device__manager_8h.html#a2f0ab8f1ea9c5b1dbd09bf73f736a7b6">device_manager.h</a></li>
<li>IOCTL_SPI_BASE&#160;:&#160;<a class="el" href="spi__device_8h.html#aad9cdee9a56a867985b5110add53ed94">spi_device.h</a></li>
<li>IOCTL_SPI_GET_STATISTICS&#160;:&#160;<a class="el" href="spi__device_8h.html#a97fe5a41276df38e46922527c4b9baf9">spi_device.h</a></li>
<li>IOCTL_SPI_RESET&#160;:&#160;<a class="el" href="spi__device_8h.html#a8442695a715b85f6516ff535bc5d1409">spi_device.h</a></li>
<li>IOCTL_SPI_SET_BUS_SPEED&#160;:&#160;<a class="el" href="spi__device_8h.html#aaba9d20f35713a3c0dd088bfdb433a0b">spi_device.h</a></li>
<li>IOCTL_SPI_SET_MODE&#160;:&#160;<a class="el" href="spi__device_8h.html#a471b24a3583fd2212e30e4619ae701be">spi_device.h</a></li>
<li>IOCTL_SPI_TRANSFER&#160;:&#160;<a class="el" href="spi__device_8h.html#a72feec97101aca3be161a59ffe40cc2c">spi_device.h</a></li>
<li>IOCTL_SPI_TRANSFER_FULL_DUPLEX&#160;:&#160;<a class="el" href="spi__device_8h.html#a21382b2df65b9cb8a11da17114ab9491">spi_device.h</a></li>
<li>IOCTL_TOUCH_GET_DATA&#160;:&#160;<a class="el" href="ioctl_8h.html#ad40a8f5f93a2d0fdabdc3b13510850b6">ioctl.h</a></li>
<li>IsListEmpty&#160;:&#160;<a class="el" href="core__types_8h.html#a1f65a0c67211ca77b491405e7c0d539e">core_types.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_k" name="index_k"></a>- k -</h3><ul>
<li>KeAcquireSpinLock&#160;:&#160;<a class="el" href="core__types_8h.html#a653f024ec628312a6a0d7943ddfc0b04">core_types.h</a></li>
<li>KeInitializeSpinLock&#160;:&#160;<a class="el" href="core__types_8h.html#a280f0a35b2869a5fb76a31d3d430834f">core_types.h</a></li>
<li>KeReleaseSpinLock&#160;:&#160;<a class="el" href="core__types_8h.html#aebf33c329588245abd814d788d1b23a3">core_types.h</a></li>
<li>KMDF_POOL_TAG&#160;:&#160;<a class="el" href="Common_8h.html#a9ee9b45a31a1e12103d9ea23cc983d0c">Common.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>LOG_ALERT&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#aa1911455782e83f3b06fab600be0e43e">driver_log.h</a></li>
<li>LOG_ALERT_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a4c42b3fa94110619ab8458eb672d189d">driver_log.h</a></li>
<li>LOG_CRITICAL&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#abc03884460a6987df33fea0d5cae8302">driver_log.h</a></li>
<li>LOG_CRITICAL_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ab48ce4a2ee7f0b5f74153fedf6ad7c25">driver_log.h</a></li>
<li>LOG_DEBUG&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#acfe39a25e08737b535dc881071ebf149">driver_log.h</a>, <a class="el" href="Common_8h.html#acfe39a25e08737b535dc881071ebf149">Common.h</a>, <a class="el" href="include_2core_2log_2driver__log_8h.html#abd0b0523397fb05f0ed46fc217fb630f">driver_log.h</a></li>
<li>LOG_DEBUG_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ae930e4b3ae4e59dc6a7b6a4feefb116f">driver_log.h</a></li>
<li>LOG_EMERGENCY&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ad706db1253940848e01bbc71ede868ef">driver_log.h</a></li>
<li>LOG_EMERGENCY_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a570937723f42dd301b24b631ec455b58">driver_log.h</a></li>
<li>LOG_ERROR&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a29e75b488d8e8ef5641c5bd16709faec">driver_log.h</a></li>
<li>LOG_ERROR_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#aff0a0cc082f6b2fad9ed0979da6e8a9b">driver_log.h</a></li>
<li>LOG_INFO&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7748b322eafa9e058c518fef49b110cb">driver_log.h</a></li>
<li>LOG_INFO_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a94d7f96857344352ffbc6ee65e9f2390">driver_log.h</a></li>
<li>LOG_NOTICE&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a05bf2404451e701f51d18409e72321fd">driver_log.h</a></li>
<li>LOG_NOTICE_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a6cf4240fc51cea71e901acf9df797b98">driver_log.h</a></li>
<li>LOG_TRACE&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a0972af62c9ad7b688924604669d7d762">driver_log.h</a></li>
<li>LOG_TRACE_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a8793c218a97ef927e72271b80a872495">driver_log.h</a></li>
<li>LOG_VERBOSE&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a6594ece0df59e19da1473edfc079fd45">driver_log.h</a></li>
<li>LOG_WARNING&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a1dd05e1ef2b66fc68251edacaa75e9f7">driver_log.h</a></li>
<li>LOG_WARNING_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a238f142a1b0fcbd8378c38d99b233baa">driver_log.h</a></li>
<li>LogInfo&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a1f2322291188e5d028f82ff2f340e5e3">driver_log.h</a></li>
<li>LogWarning&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#aa47a100aaaa86f29c113feda40125d64">driver_log.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>MAX&#160;:&#160;<a class="el" href="precomp_8h.html#afa99ec4acc4ecb2dc3c2d05da15d0e3f">precomp.h</a></li>
<li>MAX_DRIVER_DEVICES&#160;:&#160;<a class="el" href="driver__core_8h.html#adb9000833a6dada8f3fd1e267c320b2b">driver_core.h</a></li>
<li>MAX_LOG_MESSAGE_LENGTH&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a3adf7dc8e9dcfe1da6e33aa8043a80c3">driver_log.h</a></li>
<li>MIN&#160;:&#160;<a class="el" href="precomp_8h.html#a3acffbd305ee72dcd4593c0d8af64a4f">precomp.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_n" name="index_n"></a>- n -</h3><ul>
<li>NT_ERROR&#160;:&#160;<a class="el" href="precomp_8h.html#abc91c0b65b29dc8fb94e770e4066e07c">precomp.h</a></li>
<li>NT_SUCCESS&#160;:&#160;<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">precomp.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_o" name="index_o"></a>- o -</h3><ul>
<li>OPTIONAL&#160;:&#160;<a class="el" href="precomp_8h.html#afdff552467cc2d2d5815831e9656cffc">precomp.h</a></li>
<li>OUT&#160;:&#160;<a class="el" href="precomp_8h.html#aec78e7a9e90a406a56f859ee456e8eae">precomp.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>RemoveEntryList&#160;:&#160;<a class="el" href="core__types_8h.html#a0780a52679f3fd4a75aec1f901b881ff">core_types.h</a></li>
<li>RETURN_ON_FAILURE&#160;:&#160;<a class="el" href="error__handling_8h.html#a98899d26e6d8086d4ae7268a1a953c01">error_handling.h</a></li>
<li>RETURN_ON_FALSE&#160;:&#160;<a class="el" href="error__handling_8h.html#a2929f142620a357d4bb33852c676e822">error_handling.h</a></li>
<li>RETURN_ON_NULL&#160;:&#160;<a class="el" href="error__handling_8h.html#a726809026748fc6eba00fc55e5a26ff0">error_handling.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>SAFE_FREE&#160;:&#160;<a class="el" href="Common_8h.html#ab90a3a515b5c728d627bf24ac17c3702">Common.h</a></li>
<li>SAFE_RELEASE&#160;:&#160;<a class="el" href="Common_8h.html#a6038f7bdb274c2e159988a57dedbf93d">Common.h</a></li>
<li>SPI_TRANSFER_FULL_DUPLEX&#160;:&#160;<a class="el" href="spi__device_8h.html#aa7a1a825b415e6aa12c47463eefc0bb7">spi_device.h</a></li>
<li>SPI_TRANSFER_NO_CHIPSEL&#160;:&#160;<a class="el" href="spi__device_8h.html#a8595e49b5f8ddb021462587455bd2ff5">spi_device.h</a></li>
<li>SPI_TRANSFER_READ&#160;:&#160;<a class="el" href="spi__device_8h.html#a935c3756801c209968fb16a7be795396">spi_device.h</a></li>
<li>SPI_TRANSFER_WRITE&#160;:&#160;<a class="el" href="spi__device_8h.html#af27a6537c3222c6796876ff953298b42">spi_device.h</a></li>
<li>STATUS_ALREADY_INITIALIZED&#160;:&#160;<a class="el" href="core__types_8h.html#aa4e1fcf1f71277fe7bbb792e4bc4f1f8">core_types.h</a></li>
<li>STATUS_NOT_INITIALIZED&#160;:&#160;<a class="el" href="core__types_8h.html#ac336d6e1c8601404a514a7a1630aa804">core_types.h</a></li>
</ul>


<h3 class="doxsection"><a id="index_w" name="index_w"></a>- w -</h3><ul>
<li>WDF_NO_HANDLE&#160;:&#160;<a class="el" href="core__types_8h.html#aa5171e0d8c548a75e9b42333beb6390e">core_types.h</a></li>
<li>WDFAPI&#160;:&#160;<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">core_types.h</a></li>
<li>WINVER&#160;:&#160;<a class="el" href="precomp_8h.html#a966cd377b9f3fdeb1432460c33352af1">precomp.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
