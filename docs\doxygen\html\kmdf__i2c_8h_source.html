<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__i2c_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_i2c.h</div></div>
</div><!--header-->
<div class="contents">
<a href="kmdf__i2c_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * kmdf_i2c.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * I2C总线接口头文件 * 提供I2C特定的接口和数据结构</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> */</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span> </div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="preprocessor">#ifndef KMDF_I2C_H</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#define KMDF_I2C_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span> </div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="preprocessor">#include &quot;<a class="code" href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a>&quot;</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span> </div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment">// I2C地址类型</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">   13</a></span><span class="keyword">typedef</span> USHORT <a class="code hl_typedef" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a>, *<a class="code hl_typedef" href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a>;</div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span> </div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">// I2C配置结构体</span></div>
<div class="foldopen" id="foldopen00016" data-start="{" data-end="};">
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html">   16</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__i2c_8h.html#struct__I2C__CONFIG">_I2C_CONFIG</a> {</div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#ae6dda89972493740920ba937c8d27897">   17</a></span>    ULONG <a class="code hl_variable" href="kmdf__i2c_8h.html#ae6dda89972493740920ba937c8d27897">ClockFrequency</a>;    <span class="comment">// I2C时钟频率 (Hz)</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a4f627b1a2607231e3dcb321f5d728c22">   18</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__i2c_8h.html#a4f627b1a2607231e3dcb321f5d728c22">Is10BitAddress</a>;  <span class="comment">// 使用10位地址</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a9942dbdaebaf13e2e51cef985bcc4d0d">   19</a></span>    ULONG <a class="code hl_variable" href="kmdf__i2c_8h.html#a9942dbdaebaf13e2e51cef985bcc4d0d">TimeoutMs</a>;         <span class="comment">// 默认超时时间 (ms)</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a2b8ec7e6589589f70cb5d4bbb55db565">   20</a></span>    ULONG <a class="code hl_variable" href="kmdf__i2c_8h.html#a2b8ec7e6589589f70cb5d4bbb55db565">RetryCount</a>;        <span class="comment">// 失败重试次数</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">   21</a></span>} <a class="code hl_typedef" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>, *<a class="code hl_typedef" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a>;</div>
</div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment">// I2C传输类型枚举</span></div>
<div class="foldopen" id="foldopen00024" data-start="{" data-end="};">
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3">   24</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a> {</div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630">   25</a></span>    <a class="code hl_enumvalue" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630">I2CWrite</a>,              <span class="comment">// 写操作</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e">   26</a></span>    <a class="code hl_enumvalue" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e">I2CRead</a>,               <span class="comment">// 读操作</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7">   27</a></span>    <a class="code hl_enumvalue" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7">I2CWriteRead</a>           <span class="comment">// 先写后读操作</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b">   28</a></span>} <a class="code hl_typedef" href="kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a>;</div>
</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span> </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="comment">// I2C传输包结构体</span></div>
<div class="foldopen" id="foldopen00031" data-start="{" data-end="};">
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html">   31</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__i2c_8h.html#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a> {</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a20dd97830e1adfd27b9d7e46204415db">   32</a></span>    <a class="code hl_typedef" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a> <a class="code hl_variable" href="kmdf__i2c_8h.html#a20dd97830e1adfd27b9d7e46204415db">Common</a>;  <span class="comment">// 通用总线传输包</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a75e9952dba35a8ba1937901272c7f340">   33</a></span>    <a class="code hl_typedef" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> <a class="code hl_variable" href="kmdf__i2c_8h.html#a75e9952dba35a8ba1937901272c7f340">SlaveAddress</a>;   <span class="comment">// 从设备地址</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a6239118143a5327bfd92d6086107e101">   34</a></span>    <a class="code hl_typedef" href="kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a> <a class="code hl_variable" href="kmdf__i2c_8h.html#a6239118143a5327bfd92d6086107e101">Type</a>;     <span class="comment">// 传输类型</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#ab68810a9e3adeed2d53d5858fdd9cb3e">   35</a></span>    PVOID <a class="code hl_variable" href="kmdf__i2c_8h.html#ab68810a9e3adeed2d53d5858fdd9cb3e">WriteBuffer</a>;          <span class="comment">// 写缓冲区</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#afaaced0212ee18a776559ff7045b7aa4">   36</a></span>    SIZE_T <a class="code hl_variable" href="kmdf__i2c_8h.html#afaaced0212ee18a776559ff7045b7aa4">WriteBufferLength</a>;   <span class="comment">// 写缓冲区长度</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a1e07ec690f00c7a71deca7d96b3a97ad">   37</a></span>    PVOID <a class="code hl_variable" href="kmdf__i2c_8h.html#a1e07ec690f00c7a71deca7d96b3a97ad">ReadBuffer</a>;           <span class="comment">// 读缓冲区</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#aa2edc457f7179d999a40b1fe065c3532">   38</a></span>    SIZE_T <a class="code hl_variable" href="kmdf__i2c_8h.html#aa2edc457f7179d999a40b1fe065c3532">ReadBufferLength</a>;    <span class="comment">// 读缓冲区长度</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">   39</a></span>} <a class="code hl_typedef" href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a>, *<a class="code hl_typedef" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a>;</div>
</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="comment">// I2C总线接口函数声明</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><a class="code hl_function" href="kmdf__i2c_8h.html#a5467da0184a8f514f9ff43ab28f7d2d0">I2CInitialize</a>(</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span>    _In_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="code hl_variable" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span>);</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span> </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><a class="code hl_function" href="kmdf__i2c_8h.html#aa8f6531c5b52bc6d04ca38fbaab3c223">I2CUninitialize</a>(</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>);</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span> </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><a class="code hl_function" href="kmdf__i2c_8h.html#ae74bb3af98d6a79ba5774f9c3a480ca7">I2CTransferSynchronous</a>(</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>    _Inout_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>);</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span> </div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><a class="code hl_function" href="kmdf__i2c_8h.html#a363c4a8b2ee1e16d8a6aaf35b0e67722">I2CTransferAsynchronous</a>(</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>    _Inout_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>    _In_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback,</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span>    _In_opt_ PVOID Context</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span>);</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span> </div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span><a class="code hl_function" href="kmdf__i2c_8h.html#aa4838a1894b94b950fc4a7e73624d7ed">I2CWriteRegister</a>(</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>    _In_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress,</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>    _In_ UCHAR Value,</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>);</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span> </div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><a class="code hl_function" href="kmdf__i2c_8h.html#aad5c9145daea9c25554b814bfed47756">I2CReadRegister</a>(</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span>    _In_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress,</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    _Out_ PUCHAR Value,</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>);</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span> </div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span><a class="code hl_function" href="kmdf__i2c_8h.html#a1b937c9865418ca9d50b16766c8ceb66">I2CScanBus</a>(</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <a class="code hl_typedef" href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a> DeviceAddresses,</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    _In_ ULONG MaxDeviceAddresses,</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>    _Out_ PULONG DeviceCount</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>);</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span> </div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="preprocessor">#endif </span><span class="comment">// KMDF_I2C_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="acore__types_8h_html_a1cb14808a3eba8cd3fcc47bd1207a805"><div class="ttname"><a href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a></div><div class="ttdeci">#define WDFAPI</div><div class="ttdef"><b>Definition</b> core_types.h:21</div></div>
<div class="ttc" id="ai2c__device_8c_html_a3e1e82f2b44144b87469685950b3b501"><div class="ttname"><a href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></div><div class="ttdeci">PI2C_CONFIG I2cConfig</div><div class="ttdef"><b>Definition</b> i2c_device.c:19</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html"><div class="ttname"><a href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a></div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a3709500586d6c79d8df0693c133a3f2d"><div class="ttname"><a href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></div><div class="ttdeci">VOID(* BUS_OPERATION_CALLBACK)(PBUS_TRANSFER_PACKET TransferPacket)</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:50</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_aac06c68a58c9667998bbe0975aa78c51"><div class="ttname"><a href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></div><div class="ttdeci">struct _BUS_TRANSFER_PACKET BUS_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a1b937c9865418ca9d50b16766c8ceb66"><div class="ttname"><a href="kmdf__i2c_8h.html#a1b937c9865418ca9d50b16766c8ceb66">I2CScanBus</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CScanBus(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount)</div><div class="ttdef"><b>Definition</b> i2c_core.c:361</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a1e07ec690f00c7a71deca7d96b3a97ad"><div class="ttname"><a href="kmdf__i2c_8h.html#a1e07ec690f00c7a71deca7d96b3a97ad">_I2C_TRANSFER_PACKET::ReadBuffer</a></div><div class="ttdeci">PVOID ReadBuffer</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:37</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a20dd97830e1adfd27b9d7e46204415db"><div class="ttname"><a href="kmdf__i2c_8h.html#a20dd97830e1adfd27b9d7e46204415db">_I2C_TRANSFER_PACKET::Common</a></div><div class="ttdeci">BUS_TRANSFER_PACKET Common</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:32</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a26d8a1f8a56e4808ad0856f1dc02461c"><div class="ttname"><a href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></div><div class="ttdeci">struct _I2C_TRANSFER_PACKET * PI2C_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a2b8ec7e6589589f70cb5d4bbb55db565"><div class="ttname"><a href="kmdf__i2c_8h.html#a2b8ec7e6589589f70cb5d4bbb55db565">_I2C_CONFIG::RetryCount</a></div><div class="ttdeci">ULONG RetryCount</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:20</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a363c4a8b2ee1e16d8a6aaf35b0e67722"><div class="ttname"><a href="kmdf__i2c_8h.html#a363c4a8b2ee1e16d8a6aaf35b0e67722">I2CTransferAsynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</div><div class="ttdef"><b>Definition</b> i2c_core.c:250</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a4f627b1a2607231e3dcb321f5d728c22"><div class="ttname"><a href="kmdf__i2c_8h.html#a4f627b1a2607231e3dcb321f5d728c22">_I2C_CONFIG::Is10BitAddress</a></div><div class="ttdeci">BOOLEAN Is10BitAddress</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:18</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a519fae2d9daaac809af65134907b2fb0"><div class="ttname"><a href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></div><div class="ttdeci">USHORT I2C_ADDRESS</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:13</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a5467da0184a8f514f9ff43ab28f7d2d0"><div class="ttname"><a href="kmdf__i2c_8h.html#a5467da0184a8f514f9ff43ab28f7d2d0">I2CInitialize</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</div><div class="ttdef"><b>Definition</b> i2c_core.c:44</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a6239118143a5327bfd92d6086107e101"><div class="ttname"><a href="kmdf__i2c_8h.html#a6239118143a5327bfd92d6086107e101">_I2C_TRANSFER_PACKET::Type</a></div><div class="ttdeci">I2C_TRANSFER_TYPE Type</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:34</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a70b5b0e5b59f4301d02402a14c4ecb0b"><div class="ttname"><a href="kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a></div><div class="ttdeci">enum _I2C_TRANSFER_TYPE I2C_TRANSFER_TYPE</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a75e9952dba35a8ba1937901272c7f340"><div class="ttname"><a href="kmdf__i2c_8h.html#a75e9952dba35a8ba1937901272c7f340">_I2C_TRANSFER_PACKET::SlaveAddress</a></div><div class="ttdeci">I2C_ADDRESS SlaveAddress</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:33</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a77f19b8dc0e1c39c18d00d90e211afb3"><div class="ttname"><a href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a></div><div class="ttdeci">_I2C_TRANSFER_TYPE</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:24</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7"><div class="ttname"><a href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7">I2CWriteRead</a></div><div class="ttdeci">@ I2CWriteRead</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:27</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630"><div class="ttname"><a href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630">I2CWrite</a></div><div class="ttdeci">@ I2CWrite</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:25</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e"><div class="ttname"><a href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e">I2CRead</a></div><div class="ttdeci">@ I2CRead</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:26</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a8275fd1e76bc02628ddb4cf647c947c4"><div class="ttname"><a href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a></div><div class="ttdeci">struct _I2C_CONFIG I2C_CONFIG</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a941c9f88004c4f54719bc4a3b7083fff"><div class="ttname"><a href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a></div><div class="ttdeci">struct _I2C_TRANSFER_PACKET I2C_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a9942dbdaebaf13e2e51cef985bcc4d0d"><div class="ttname"><a href="kmdf__i2c_8h.html#a9942dbdaebaf13e2e51cef985bcc4d0d">_I2C_CONFIG::TimeoutMs</a></div><div class="ttdeci">ULONG TimeoutMs</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:19</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a9d4df46fafece7b304c57d2e0e1bfd51"><div class="ttname"><a href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></div><div class="ttdeci">struct _I2C_CONFIG * PI2C_CONFIG</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_aa2edc457f7179d999a40b1fe065c3532"><div class="ttname"><a href="kmdf__i2c_8h.html#aa2edc457f7179d999a40b1fe065c3532">_I2C_TRANSFER_PACKET::ReadBufferLength</a></div><div class="ttdeci">SIZE_T ReadBufferLength</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:38</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_aa4838a1894b94b950fc4a7e73624d7ed"><div class="ttname"><a href="kmdf__i2c_8h.html#aa4838a1894b94b950fc4a7e73624d7ed">I2CWriteRegister</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CWriteRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</div><div class="ttdef"><b>Definition</b> i2c_core.c:283</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_aa8f6531c5b52bc6d04ca38fbaab3c223"><div class="ttname"><a href="kmdf__i2c_8h.html#aa8f6531c5b52bc6d04ca38fbaab3c223">I2CUninitialize</a></div><div class="ttdeci">WDFAPI VOID I2CUninitialize(_In_ WDFDEVICE Device)</div><div class="ttdef"><b>Definition</b> i2c_core.c:111</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_aad5c9145daea9c25554b814bfed47756"><div class="ttname"><a href="kmdf__i2c_8h.html#aad5c9145daea9c25554b814bfed47756">I2CReadRegister</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CReadRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</div><div class="ttdef"><b>Definition</b> i2c_core.c:321</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_ab68810a9e3adeed2d53d5858fdd9cb3e"><div class="ttname"><a href="kmdf__i2c_8h.html#ab68810a9e3adeed2d53d5858fdd9cb3e">_I2C_TRANSFER_PACKET::WriteBuffer</a></div><div class="ttdeci">PVOID WriteBuffer</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:35</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_ae6dda89972493740920ba937c8d27897"><div class="ttname"><a href="kmdf__i2c_8h.html#ae6dda89972493740920ba937c8d27897">_I2C_CONFIG::ClockFrequency</a></div><div class="ttdeci">ULONG ClockFrequency</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:17</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_ae74bb3af98d6a79ba5774f9c3a480ca7"><div class="ttname"><a href="kmdf__i2c_8h.html#ae74bb3af98d6a79ba5774f9c3a480ca7">I2CTransferSynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS I2CTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</div><div class="ttdef"><b>Definition</b> i2c_core.c:142</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_af11040ef31cae611dac879352c4fab17"><div class="ttname"><a href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a></div><div class="ttdeci">USHORT * PI2C_ADDRESS</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:13</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_afaaced0212ee18a776559ff7045b7aa4"><div class="ttname"><a href="kmdf__i2c_8h.html#afaaced0212ee18a776559ff7045b7aa4">_I2C_TRANSFER_PACKET::WriteBufferLength</a></div><div class="ttdeci">SIZE_T WriteBufferLength</div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:36</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_struct__I2C__CONFIG"><div class="ttname"><a href="kmdf__i2c_8h.html#struct__I2C__CONFIG">_I2C_CONFIG</a></div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:16</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_struct__I2C__TRANSFER__PACKET"><div class="ttname"><a href="kmdf__i2c_8h.html#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a></div><div class="ttdef"><b>Definition</b> kmdf_i2c.h:31</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__i2c_8h.html">kmdf_i2c.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
