<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li class="current"><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_s.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>SAFE_FREE&#160;:&#160;<a class="el" href="Common_8h.html#ab90a3a515b5c728d627bf24ac17c3702">Common.h</a></li>
<li>SAFE_RELEASE&#160;:&#160;<a class="el" href="Common_8h.html#a6038f7bdb274c2e159988a57dedbf93d">Common.h</a></li>
<li>SampleDriverInterfaceGuid&#160;:&#160;<a class="el" href="driver__entry_8c.html#a0cbf21a68b80f2cd62f3a028c051dd53">driver_entry.c</a>, <a class="el" href="driver__main_8c.html#a0cbf21a68b80f2cd62f3a028c051dd53">driver_main.c</a></li>
<li>spbDevicePath&#160;:&#160;<a class="el" href="gpio__core_8c.html#af7d60c8c4b9613f737c7d254aced2bde">gpio_core.c</a></li>
<li>SpbIoTarget&#160;:&#160;<a class="el" href="gpio__core_8c.html#af781006198c718a3a6e46d55cdb1e74c">gpio_core.c</a></li>
<li>SPI_BUS_SPEED&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57">kmdf_spi.h</a></li>
<li>SPI_CONFIG&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">kmdf_spi.h</a></li>
<li>SPI_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="spi__core_8c.html#a2a4a689dbe0ef33045635ddfa5db3194">spi_core.c</a>, <a class="el" href="spi__device_8c.html#aa226b0d93154d552caefa2ca1550155c">spi_device.c</a></li>
<li>SPI_DEVICE_TRANSFER_PACKET&#160;:&#160;<a class="el" href="spi__device_8h.html#a22213828588d3110e34d053c7f191fc0">spi_device.h</a></li>
<li>SPI_MODE&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">kmdf_spi.h</a></li>
<li>SPI_STATISTICS&#160;:&#160;<a class="el" href="spi__device_8h.html#aa83effe237db8be37288dceaeeab8d57">spi_device.h</a></li>
<li>SPI_TRANSFER_FULL_DUPLEX&#160;:&#160;<a class="el" href="spi__device_8h.html#aa7a1a825b415e6aa12c47463eefc0bb7">spi_device.h</a></li>
<li>SPI_TRANSFER_NO_CHIPSEL&#160;:&#160;<a class="el" href="spi__device_8h.html#a8595e49b5f8ddb021462587455bd2ff5">spi_device.h</a></li>
<li>SPI_TRANSFER_PACKET&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#ae53b781a84db92ccef86085a53448289">kmdf_spi.h</a></li>
<li>SPI_TRANSFER_READ&#160;:&#160;<a class="el" href="spi__device_8h.html#a935c3756801c209968fb16a7be795396">spi_device.h</a></li>
<li>SPI_TRANSFER_TYPE&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca">kmdf_spi.h</a></li>
<li>SPI_TRANSFER_WRITE&#160;:&#160;<a class="el" href="spi__device_8h.html#af27a6537c3222c6796876ff953298b42">spi_device.h</a></li>
<li>SpiBusSpeed10MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997">kmdf_spi.h</a></li>
<li>SpiBusSpeed1MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2">kmdf_spi.h</a></li>
<li>SpiBusSpeed20MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90">kmdf_spi.h</a></li>
<li>SpiBusSpeed25MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef">kmdf_spi.h</a></li>
<li>SpiBusSpeed2MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab">kmdf_spi.h</a></li>
<li>SpiBusSpeed4MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94">kmdf_spi.h</a></li>
<li>SpiBusSpeed50MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc">kmdf_spi.h</a></li>
<li>SpiBusSpeed8MHz&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928">kmdf_spi.h</a></li>
<li>SpiConfig&#160;:&#160;<a class="el" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">spi_device.c</a></li>
<li>SpiDeviceCleanup()&#160;:&#160;<a class="el" href="spi__device_8c.html#a052b57a96b994325a574bcb9f3db837a">spi_device.c</a>, <a class="el" href="spi__device_8h.html#a052b57a96b994325a574bcb9f3db837a">spi_device.h</a></li>
<li>SpiDeviceGetStatistics()&#160;:&#160;<a class="el" href="spi__device_8c.html#ae2be7c6b48ddf5b08876e1115879469d">spi_device.c</a>, <a class="el" href="spi__device_8h.html#ae2be7c6b48ddf5b08876e1115879469d">spi_device.h</a></li>
<li>SpiDeviceInitialize()&#160;:&#160;<a class="el" href="spi__device_8c.html#a6939e12311ec72f975bcd03a4250a3e2">spi_device.c</a>, <a class="el" href="spi__device_8h.html#a6939e12311ec72f975bcd03a4250a3e2">spi_device.h</a></li>
<li>SpiDeviceRead()&#160;:&#160;<a class="el" href="spi__device_8c.html#a3bc98267d67ee8988179bde952efaa87">spi_device.c</a>, <a class="el" href="spi__device_8h.html#a3bc98267d67ee8988179bde952efaa87">spi_device.h</a></li>
<li>SpiDeviceSetClockFrequency()&#160;:&#160;<a class="el" href="spi__device_8h.html#a30b9d7f482d2a1343e50a60ea8d4135a">spi_device.h</a></li>
<li>SpiDeviceSetMode()&#160;:&#160;<a class="el" href="spi__device_8h.html#a3c91b33450d309fa46affa22959f8607">spi_device.h</a></li>
<li>SpiDeviceTransfer()&#160;:&#160;<a class="el" href="spi__device_8c.html#a2428921b9d71ab9d24f34e0a7b23487c">spi_device.c</a>, <a class="el" href="spi__device_8h.html#a2428921b9d71ab9d24f34e0a7b23487c">spi_device.h</a></li>
<li>SpiDeviceWrite()&#160;:&#160;<a class="el" href="spi__device_8c.html#ae90ccf3d865bebb54c2c76e10fcbcaa8">spi_device.c</a>, <a class="el" href="spi__device_8h.html#ae90ccf3d865bebb54c2c76e10fcbcaa8">spi_device.h</a></li>
<li>SPIInitialize()&#160;:&#160;<a class="el" href="spi__core_8c.html#a685d8d7731e750c1512b975df16cc030">spi_core.c</a>, <a class="el" href="kmdf__spi_8h.html#a685d8d7731e750c1512b975df16cc030">kmdf_spi.h</a></li>
<li>SpiMode0&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249">kmdf_spi.h</a></li>
<li>SpiMode1&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972">kmdf_spi.h</a></li>
<li>SpiMode2&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e">kmdf_spi.h</a></li>
<li>SpiMode3&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470">kmdf_spi.h</a></li>
<li>SpiRead&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d">kmdf_spi.h</a></li>
<li>SPIReadRegister()&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885">kmdf_spi.h</a></li>
<li>SPITransferAsynchronous()&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033">kmdf_spi.h</a></li>
<li>SPITransferSynchronous()&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c">kmdf_spi.h</a></li>
<li>SPITransferTimerExpired&#160;:&#160;<a class="el" href="spi__core_8c.html#a85e21cb755e2b8afb53a12e0413ddfb1">spi_core.c</a></li>
<li>SPIUninitialize()&#160;:&#160;<a class="el" href="spi__core_8c.html#ad756f8e3b06fdfa545a7048661038513">spi_core.c</a>, <a class="el" href="kmdf__spi_8h.html#ad756f8e3b06fdfa545a7048661038513">kmdf_spi.h</a></li>
<li>SpiWrite&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a">kmdf_spi.h</a></li>
<li>SPIWriteRead()&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f">kmdf_spi.h</a></li>
<li>SpiWriteRead&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36">kmdf_spi.h</a></li>
<li>SPIWriteRegister()&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8">kmdf_spi.h</a></li>
<li>status&#160;:&#160;<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#a9611b3a00430a86619b5923de30f9fdb">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a9611b3a00430a86619b5923de30f9fdb">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a9611b3a00430a86619b5923de30f9fdb">spi_device.c</a></li>
<li>STATUS_ALREADY_INITIALIZED&#160;:&#160;<a class="el" href="core__types_8h.html#aa4e1fcf1f71277fe7bbb792e4bc4f1f8">core_types.h</a></li>
<li>STATUS_INVALID_PARAMETER&#160;:&#160;<a class="el" href="gpio__core_8c.html#a29feb6a8256030d356f8bfd96118d1cf">gpio_core.c</a></li>
<li>STATUS_NOT_INITIALIZED&#160;:&#160;<a class="el" href="core__types_8h.html#ac336d6e1c8601404a514a7a1630aa804">core_types.h</a></li>
<li>STATUS_SUCCESS&#160;:&#160;<a class="el" href="gpio__core_8c.html#a77b4762318f24dff847f94f382cfeea6">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#a77b4762318f24dff847f94f382cfeea6">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a77b4762318f24dff847f94f382cfeea6">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a77b4762318f24dff847f94f382cfeea6">spi_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
