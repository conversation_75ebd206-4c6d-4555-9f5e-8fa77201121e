<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/devices/i2c_device.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('i2c__device_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">i2c_device.h</div></div>
</div><!--header-->
<div class="contents">
<a href="i2c__device_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * i2c_device.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * I2C璁惧椹卞姩鎺ュ彛澶存枃浠? * 鎻愪緵I2C璁惧鎿嶄綔鐨勭粺涓€鎺ュ彛</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> */</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span> </div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="preprocessor">#ifndef I2C_DEVICE_H</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#define I2C_DEVICE_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span> </div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="preprocessor">#include &quot;<a class="code" href="kmdf__i2c_8h.html">../bus/kmdf_i2c.h</a>&quot;</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span> </div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">// 甯搁噺鍜岀粨鏋勫畾涔?//===============================================================================</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment">// I2C浼犺緭鏍囧織</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#ad6922f686b3fc13f8365467975aba1d7">   18</a></span><span class="preprocessor">#define I2C_TRANSFER_READ      0x00000001  </span><span class="comment">// 璇讳紶杈?#define I2C_TRANSFER_WRITE     0x00000002  // 鍐欎紶杈?#define I2C_TRANSFER_FAST_MODE 0x00000004  // 蹇€熸ā寮?#define I2C_TRANSFER_NO_DELAY  0x00000008  // 鏃犲欢杩?#define I2C_TRANSFER_NO_STOP   0x00000010  // 鏃犲仠姝㈡潯浠?</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment">// I2C鍦板潃闀垮害</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#ab65b9830c26b77346e0f420c643d63b9">   20</a></span><span class="preprocessor">#define I2C_ADDRESS_7BIT       0           </span><span class="comment">// 7浣嶅湴鍧€</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a64738b9f6c1ba2f1f919ef7a61ad1e35">   21</a></span><span class="preprocessor">#define I2C_ADDRESS_10BIT      1           </span><span class="comment">// 10浣嶅湴鍧€</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment">// I2C浼犺緭鏁版嵁鍖呯粨鏋?typedef struct _I2C_TRANSFER_PACKET {</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">   24</a></span>    UCHAR <a class="code hl_variable" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>;           <span class="comment">// 璁惧鍦板潃</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a2c5c92bbcc636db6bd909df12a1802da">   25</a></span>    UCHAR <a class="code hl_variable" href="i2c__device_8c.html#a63268b1e9e5ee12309a44d8d6c9fc652">DataAddress</a>;             <span class="comment">// 瀵勫瓨鍣?鏁版嵁鍦板潃</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a40f62c35b73ac8d898f982eba3295ed5">   26</a></span>    PVOID <a class="code hl_variable" href="i2c__device_8c.html#a7cd334185ddc76afeacf2cd57615dc81">Data</a>;                    <span class="comment">// 鏁版嵁缂撳啿鍖?    ULONG DataLength;              // 鏁版嵁闀垮害</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a385668d045c2844f0f13d8613b3e0459">   27</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8c.html#aaea9f9b32650901ecb0d31cb5066cd7f">Flags</a>;                   <span class="comment">// 浼犺緭鏍囧織</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a306a7e4c1d020919c3002e3b8bd37c27">   28</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8c.html#af9a881dabb7ea1e15ee2808cca09fd6a">DelayInMicroseconds</a>;     <span class="comment">// 浼犺緭鍚庣殑寤惰繜锛堝井绉掞級</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#af8486d1fd9d3904ca8eb4408ec81d9c4">   29</a></span>} <a class="code hl_typedef" href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a>, *<a class="code hl_typedef" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a>;</div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span> </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="comment">// I2C缁熻淇℃伅缁撴瀯</span></div>
<div class="foldopen" id="foldopen00032" data-start="{" data-end="};">
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="i2c__device_8h.html">   32</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="i2c__device_8h.html#struct__I2C__STATISTICS">_I2C_STATISTICS</a> {</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#add2f1e1c89d79eee2641ef44c12f29d3">   33</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8h.html#add2f1e1c89d79eee2641ef44c12f29d3">TransactionCount</a>;        <span class="comment">// 浼犺緭鎬绘暟</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a717ee36112176580f0ec5152cd12e0a2">   34</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8h.html#a717ee36112176580f0ec5152cd12e0a2">ErrorCount</a>;              <span class="comment">// 閿欒鎬绘暟</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a1ac16411f8ad21fe7d60868251438978">   35</a></span>    BOOLEAN <a class="code hl_variable" href="i2c__device_8h.html#a1ac16411f8ad21fe7d60868251438978">DeviceInitialized</a>;     <span class="comment">// 璁惧鏄惁宸插垵濮嬪寲</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a0d581d279416ac434f26896560418188">   36</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8h.html#a0d581d279416ac434f26896560418188">BusNumber</a>;               <span class="comment">// 鎬荤嚎缂栧彿</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#ac6637ce7aa131fb27cf2b60ce31f35db">   37</a></span>    ULONG <a class="code hl_variable" href="i2c__device_8h.html#ac6637ce7aa131fb27cf2b60ce31f35db">BusClockFrequency</a>;       <span class="comment">// 鎬荤嚎鏃堕挓棰戠巼</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a7d8f35e89fbf4832545b3dfab3bd1ae1">   38</a></span>} <a class="code hl_typedef" href="i2c__device_8h.html#a7d8f35e89fbf4832545b3dfab3bd1ae1">I2C_STATISTICS</a>, *<a class="code hl_typedef" href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a>;</div>
</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span> </div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="comment">// I2C IOCTL 鎺у埗鐮?#define IOCTL_I2C_BASE                      0x8100</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a15204e5c2582622fc3ef40f01fe93322">   41</a></span><span class="preprocessor">#define IOCTL_I2C_TRANSFER                  CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a478905e3b3f7f700dd9e0975f42fe1a3">   42</a></span><span class="preprocessor">#define IOCTL_I2C_TRANSFER_SEQUENCE         CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a23164ebc8fc22800438176c588caa941">   43</a></span><span class="preprocessor">#define IOCTL_I2C_GET_STATISTICS            CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a69ebe19cf050058019d84f005052cc00">   44</a></span><span class="preprocessor">#define IOCTL_I2C_RESET                     CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="i2c__device_8h.html#a9c3881592ae1c10fbd5ba1b9ae7e85ec">   45</a></span><span class="preprocessor">#define IOCTL_I2C_SET_BUS_SPEED             CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span> </div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="comment">// I2C璁惧鎺ュ彛鍑芥暟澹版槑</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>NTSTATUS</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><a class="code hl_function" href="i2c__device_8h.html#ab0c3b778b5a363d418c3d768cdb1e2d4">I2cDeviceInitialize</a>(</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    _In_ <a class="code hl_typedef" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="code hl_variable" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>);</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span></div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>VOID</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span><a class="code hl_function" href="i2c__device_8h.html#a5da67a960d3cf99caa6874438a84629b">I2cDeviceCleanup</a>(</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>);</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span></div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>NTSTATUS</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span><a class="code hl_function" href="i2c__device_8h.html#a6576f1e3485d12c22c444244044c1d30">I2cDeviceRead</a>(</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    _In_ UCHAR <a class="code hl_variable" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>,</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    _Out_writes_bytes_(Length) PVOID Buffer,</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>    _In_ ULONG Length,</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    _Out_opt_ PULONG BytesRead</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>);</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span></div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>NTSTATUS</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><a class="code hl_function" href="i2c__device_8h.html#a580f2434082501937a3d8bc4d5591866">I2cDeviceWrite</a>(</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    _In_ UCHAR <a class="code hl_variable" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>,</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    _In_reads_bytes_(Length) PVOID Buffer,</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    _In_ ULONG Length,</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    _Out_opt_ PULONG BytesWritten</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>);</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span></div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span>NTSTATUS</div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span><a class="code hl_function" href="i2c__device_8h.html#ad84f26684684313ff193803d1d9c7c32">I2cDeviceTransfer</a>(</div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span>    _In_reads_(TransferCount) <a class="code hl_typedef" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> Transfers,</div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span>    _In_ ULONG TransferCount</div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>);</div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span></div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span>NTSTATUS</div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span><a class="code hl_function" href="i2c__device_8h.html#a709aca0009ccfb39adebbdd9ce97e252">I2cDeviceGetStatistics</a>(</div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span>    _Out_ <a class="code hl_typedef" href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a> Statistics</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span>);</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span> </div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span><span class="preprocessor">#endif </span><span class="comment">// I2C_DEVICE_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="ai2c__device_8c_html_a3e1e82f2b44144b87469685950b3b501"><div class="ttname"><a href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></div><div class="ttdeci">PI2C_CONFIG I2cConfig</div><div class="ttdef"><b>Definition</b> i2c_device.c:19</div></div>
<div class="ttc" id="ai2c__device_8c_html_a63268b1e9e5ee12309a44d8d6c9fc652"><div class="ttname"><a href="i2c__device_8c.html#a63268b1e9e5ee12309a44d8d6c9fc652">DataAddress</a></div><div class="ttdeci">packet DataAddress</div><div class="ttdef"><b>Definition</b> i2c_device.c:217</div></div>
<div class="ttc" id="ai2c__device_8c_html_a7cd334185ddc76afeacf2cd57615dc81"><div class="ttname"><a href="i2c__device_8c.html#a7cd334185ddc76afeacf2cd57615dc81">Data</a></div><div class="ttdeci">packet Data</div><div class="ttdef"><b>Definition</b> i2c_device.c:218</div></div>
<div class="ttc" id="ai2c__device_8c_html_aaea9f9b32650901ecb0d31cb5066cd7f"><div class="ttname"><a href="i2c__device_8c.html#aaea9f9b32650901ecb0d31cb5066cd7f">Flags</a></div><div class="ttdeci">halConfig Flags</div><div class="ttdef"><b>Definition</b> i2c_device.c:117</div></div>
<div class="ttc" id="ai2c__device_8c_html_af9a881dabb7ea1e15ee2808cca09fd6a"><div class="ttname"><a href="i2c__device_8c.html#af9a881dabb7ea1e15ee2808cca09fd6a">DelayInMicroseconds</a></div><div class="ttdeci">packet DelayInMicroseconds</div><div class="ttdef"><b>Definition</b> i2c_device.c:221</div></div>
<div class="ttc" id="ai2c__device_8h_html_a0d581d279416ac434f26896560418188"><div class="ttname"><a href="i2c__device_8h.html#a0d581d279416ac434f26896560418188">_I2C_STATISTICS::BusNumber</a></div><div class="ttdeci">ULONG BusNumber</div><div class="ttdef"><b>Definition</b> i2c_device.h:36</div></div>
<div class="ttc" id="ai2c__device_8h_html_a1ac16411f8ad21fe7d60868251438978"><div class="ttname"><a href="i2c__device_8h.html#a1ac16411f8ad21fe7d60868251438978">_I2C_STATISTICS::DeviceInitialized</a></div><div class="ttdeci">BOOLEAN DeviceInitialized</div><div class="ttdef"><b>Definition</b> i2c_device.h:35</div></div>
<div class="ttc" id="ai2c__device_8h_html_a580f2434082501937a3d8bc4d5591866"><div class="ttname"><a href="i2c__device_8h.html#a580f2434082501937a3d8bc4d5591866">I2cDeviceWrite</a></div><div class="ttdeci">NTSTATUS I2cDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</div><div class="ttdef"><b>Definition</b> i2c_device.c:253</div></div>
<div class="ttc" id="ai2c__device_8h_html_a5da67a960d3cf99caa6874438a84629b"><div class="ttname"><a href="i2c__device_8h.html#a5da67a960d3cf99caa6874438a84629b">I2cDeviceCleanup</a></div><div class="ttdeci">VOID I2cDeviceCleanup(_In_ WDFDEVICE Device)</div><div class="ttdef"><b>Definition</b> i2c_device.c:157</div></div>
<div class="ttc" id="ai2c__device_8h_html_a6576f1e3485d12c22c444244044c1d30"><div class="ttname"><a href="i2c__device_8h.html#a6576f1e3485d12c22c444244044c1d30">I2cDeviceRead</a></div><div class="ttdeci">NTSTATUS I2cDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</div><div class="ttdef"><b>Definition</b> i2c_device.c:192</div></div>
<div class="ttc" id="ai2c__device_8h_html_a709aca0009ccfb39adebbdd9ce97e252"><div class="ttname"><a href="i2c__device_8h.html#a709aca0009ccfb39adebbdd9ce97e252">I2cDeviceGetStatistics</a></div><div class="ttdeci">NTSTATUS I2cDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics)</div><div class="ttdef"><b>Definition</b> i2c_device.c:362</div></div>
<div class="ttc" id="ai2c__device_8h_html_a717ee36112176580f0ec5152cd12e0a2"><div class="ttname"><a href="i2c__device_8h.html#a717ee36112176580f0ec5152cd12e0a2">_I2C_STATISTICS::ErrorCount</a></div><div class="ttdeci">ULONG ErrorCount</div><div class="ttdef"><b>Definition</b> i2c_device.h:34</div></div>
<div class="ttc" id="ai2c__device_8h_html_a7d8f35e89fbf4832545b3dfab3bd1ae1"><div class="ttname"><a href="i2c__device_8h.html#a7d8f35e89fbf4832545b3dfab3bd1ae1">I2C_STATISTICS</a></div><div class="ttdeci">struct _I2C_STATISTICS I2C_STATISTICS</div></div>
<div class="ttc" id="ai2c__device_8h_html_a862821561008426245a34e458d02a093"><div class="ttname"><a href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a></div><div class="ttdeci">UCHAR DeviceAddress</div><div class="ttdef"><b>Definition</b> i2c_device.h:24</div></div>
<div class="ttc" id="ai2c__device_8h_html_ab0c3b778b5a363d418c3d768cdb1e2d4"><div class="ttname"><a href="i2c__device_8h.html#ab0c3b778b5a363d418c3d768cdb1e2d4">I2cDeviceInitialize</a></div><div class="ttdeci">NTSTATUS I2cDeviceInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</div><div class="ttdef"><b>Definition</b> i2c_device.c:39</div></div>
<div class="ttc" id="ai2c__device_8h_html_ac6637ce7aa131fb27cf2b60ce31f35db"><div class="ttname"><a href="i2c__device_8h.html#ac6637ce7aa131fb27cf2b60ce31f35db">_I2C_STATISTICS::BusClockFrequency</a></div><div class="ttdeci">ULONG BusClockFrequency</div><div class="ttdef"><b>Definition</b> i2c_device.h:37</div></div>
<div class="ttc" id="ai2c__device_8h_html_ad84f26684684313ff193803d1d9c7c32"><div class="ttname"><a href="i2c__device_8h.html#ad84f26684684313ff193803d1d9c7c32">I2cDeviceTransfer</a></div><div class="ttdeci">NTSTATUS I2cDeviceTransfer(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount)</div><div class="ttdef"><b>Definition</b> i2c_device.c:314</div></div>
<div class="ttc" id="ai2c__device_8h_html_add2f1e1c89d79eee2641ef44c12f29d3"><div class="ttname"><a href="i2c__device_8h.html#add2f1e1c89d79eee2641ef44c12f29d3">_I2C_STATISTICS::TransactionCount</a></div><div class="ttdeci">ULONG TransactionCount</div><div class="ttdef"><b>Definition</b> i2c_device.h:33</div></div>
<div class="ttc" id="ai2c__device_8h_html_aeb652cfe1149dff5ee42abab74d96813"><div class="ttname"><a href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a></div><div class="ttdeci">struct _I2C_STATISTICS * PI2C_STATISTICS</div></div>
<div class="ttc" id="ai2c__device_8h_html_struct__I2C__STATISTICS"><div class="ttname"><a href="i2c__device_8h.html#struct__I2C__STATISTICS">_I2C_STATISTICS</a></div><div class="ttdef"><b>Definition</b> i2c_device.h:32</div></div>
<div class="ttc" id="akmdf__i2c_8h_html"><div class="ttname"><a href="kmdf__i2c_8h.html">kmdf_i2c.h</a></div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a26d8a1f8a56e4808ad0856f1dc02461c"><div class="ttname"><a href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></div><div class="ttdeci">struct _I2C_TRANSFER_PACKET * PI2C_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a941c9f88004c4f54719bc4a3b7083fff"><div class="ttname"><a href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a></div><div class="ttdeci">struct _I2C_TRANSFER_PACKET I2C_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__i2c_8h_html_a9d4df46fafece7b304c57d2e0e1bfd51"><div class="ttname"><a href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></div><div class="ttdeci">struct _I2C_CONFIG * PI2C_CONFIG</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_f51f2e86ea53a1a257ee2ea690474c95.html">devices</a></li><li class="navelem"><a href="i2c__device_8h.html">i2c_device.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
