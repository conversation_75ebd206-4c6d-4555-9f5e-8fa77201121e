<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/core/log/driver_log.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('include_2core_2log_2driver__log_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">driver_log.h</div></div>
</div><!--header-->
<div class="contents">
<a href="include_2core_2log_2driver__log_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="preprocessor">#ifndef DRIVER_LOG_H</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="preprocessor">#define DRIVER_LOG_H</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span> </div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span> </div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a3adf7dc8e9dcfe1da6e33aa8043a80c3">    7</a></span><span class="preprocessor">#define MAX_LOG_MESSAGE_LENGTH 512</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span> </div>
<div class="foldopen" id="foldopen00009" data-start="{" data-end="};">
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">    9</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> {</div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16">   10</a></span>    <a class="code hl_enumvalue" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16">LogLevelDisabled</a> = 0,</div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">   11</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a> = 1,</div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">   12</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a> = 2,</div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">   13</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a> = 3,</div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">   14</a></span>    <a class="code hl_enumvalue" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">LogLevelVerbose</a> = 4,</div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">   15</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a> = 5</div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">   16</a></span>} <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a>;</div>
</div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span> </div>
<div class="foldopen" id="foldopen00018" data-start="{" data-end="};">
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867">   18</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867">_LOG_TYPE</a> {</div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543">   19</a></span>    <a class="code hl_enumvalue" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543">LogTypeKdPrint</a> = 0,</div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7">   20</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a> = 1,</div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3">   21</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a> = 2,</div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d">   22</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a> = 3,</div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee">   23</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a> = 0xFF</div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">   24</a></span>} <a class="code hl_typedef" href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a>;</div>
</div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span> </div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a> {</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ab67683f62a123026ec78fe405cdbb177">   27</a></span>    <a class="code hl_typedef" href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#ab67683f62a123026ec78fe405cdbb177">LogLevel</a>;</div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a4b99237eff4b17c1d4fbfa9697528f70">   28</a></span>    <a class="code hl_typedef" href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a> <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#a4b99237eff4b17c1d4fbfa9697528f70">LogTypes</a>;</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a860b2d89bf1aff272da84d5029dcb0b1">   29</a></span>    BOOLEAN <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#a860b2d89bf1aff272da84d5029dcb0b1">IncludeTimestamp</a>;</div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ac82d96af8adf8a1862d02ac0be15939d">   30</a></span>    BOOLEAN <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#ac82d96af8adf8a1862d02ac0be15939d">IncludeComponentName</a>;</div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a21ca685eeee730a0d8a179892b840d5f">   31</a></span>    PWSTR <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#a21ca685eeee730a0d8a179892b840d5f">LogFilePath</a>;</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a7aa67c4af6a6176801a2b2af65ac3cbb">   32</a></span>    ULONG <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#a7aa67c4af6a6176801a2b2af65ac3cbb">MaxLogFileSize</a>;</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">   33</a></span>} <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>, *<a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a>;</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span> </div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span>VOID</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a">   36</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a">LogConfigInit</a>(</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span>    <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span>);</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span> </div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span>VOID</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9">   41</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9">LogInitialize</a>(</div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span>    PDRIVER_OBJECT DriverObject,</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span>    <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>);</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span> </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span>VOID</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7">   47</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7">LogCleanup</a>(</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>    VOID</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span>);</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span> </div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>VOID</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185">   52</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185">LogSetLevel</a>(</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>    <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>);</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span> </div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>VOID</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1">   57</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>    <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level,</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>    PCSTR Function,</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span>    ULONG Line,</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>    PCSTR Format,</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    ...</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>);</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span> </div>
<div class="foldopen" id="foldopen00065" data-start="" data-end="">
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#abffaf9cecb61026cac6db71a16ace9c5">   65</a></span><span class="preprocessor">#define LOG_ERROR(format, ...) \</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><span class="preprocessor">    LogMessage(LogLevelError, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span> </div>
<div class="foldopen" id="foldopen00068" data-start="" data-end="">
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a1c60134b1702d179d9b86bc618f416fe">   68</a></span><span class="preprocessor">#define LOG_WARNING(format, ...) \</span></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span><span class="preprocessor">    LogMessage(LogLevelWarning, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span> </div>
<div class="foldopen" id="foldopen00071" data-start="" data-end="">
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a89681da4efde0b54dc7f2839665082c8">   71</a></span><span class="preprocessor">#define LOG_INFO(format, ...) \</span></div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span><span class="preprocessor">    LogMessage(LogLevelInfo, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span> </div>
<div class="foldopen" id="foldopen00074" data-start="" data-end="">
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a6594ece0df59e19da1473edfc079fd45">   74</a></span><span class="preprocessor">#define LOG_VERBOSE(format, ...) \</span></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span><span class="preprocessor">    LogMessage(LogLevelVerbose, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span> </div>
<div class="foldopen" id="foldopen00077" data-start="" data-end="">
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#abd0b0523397fb05f0ed46fc217fb630f">   77</a></span><span class="preprocessor">#define LOG_DEBUG(format, ...) \</span></div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><span class="preprocessor">    LogMessage(LogLevelDebug, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span> </div>
<div class="foldopen" id="foldopen00080" data-start="" data-end="">
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a1f2322291188e5d028f82ff2f340e5e3">   80</a></span><span class="preprocessor">#define LogInfo(function, line, format, ...) \</span></div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span><span class="preprocessor">    LogMessage(LogLevelInfo, function, line, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span> </div>
<div class="foldopen" id="foldopen00083" data-start="" data-end="">
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#aa47a100aaaa86f29c113feda40125d64">   83</a></span><span class="preprocessor">#define LogWarning(function, line, format, ...) \</span></div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span><span class="preprocessor">    LogMessage(LogLevelWarning, function, line, format, ##__VA_ARGS__)</span></div>
</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span> </div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span>VOID</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81">   87</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81">LogFunctionEntry</a>(</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    PCSTR FunctionName</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>);</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span> </div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>VOID</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740">   92</a></span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740">LogFunctionExit</a>(</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>    PCSTR FunctionName,</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    NTSTATUS Status</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>);</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span> </div>
<div class="foldopen" id="foldopen00097" data-start="" data-end="">
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ac56df030fb93601c871fd894e289601a">   97</a></span><span class="preprocessor">#define FUNCTION_ENTRY() \</span></div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span><span class="preprocessor">    LogFunctionEntry(__FUNCTION__)</span></div>
</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span> </div>
<div class="foldopen" id="foldopen00100" data-start="" data-end="">
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#a347d8e7da8a8e1d1cebfd45f4055a4a8">  100</a></span><span class="preprocessor">#define FUNCTION_EXIT(status) \</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><span class="preprocessor">    LogFunctionExit(__FUNCTION__, status)</span></div>
</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span> </div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><span class="preprocessor">#endif </span><span class="comment">// DRIVER_LOG_H</span></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a00e4548dd1db35b54cbeb1ee0fe45f66"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a></div><div class="ttdeci">enum _LOG_TYPE LOG_TYPE</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a050bae65361e276b294f785581894867"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867">_LOG_TYPE</a></div><div class="ttdeci">_LOG_TYPE</div><div class="ttdef"><b>Definition</b> driver_log.h:18</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543">LogTypeKdPrint</a></div><div class="ttdeci">@ LogTypeKdPrint</div><div class="ttdef"><b>Definition</b> driver_log.h:19</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a21ca685eeee730a0d8a179892b840d5f"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a21ca685eeee730a0d8a179892b840d5f">_LOG_CONFIG::LogFilePath</a></div><div class="ttdeci">PWSTR LogFilePath</div><div class="ttdef"><b>Definition</b> driver_log.h:31</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a2ebea8a6c7cbdde9ba74e856c73a2740"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740">LogFunctionExit</a></div><div class="ttdeci">VOID LogFunctionExit(PCSTR FunctionName, NTSTATUS Status)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a4b99237eff4b17c1d4fbfa9697528f70"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a4b99237eff4b17c1d4fbfa9697528f70">_LOG_CONFIG::LogTypes</a></div><div class="ttdeci">LOG_TYPE LogTypes</div><div class="ttdef"><b>Definition</b> driver_log.h:28</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16">LogLevelDisabled</a></div><div class="ttdeci">@ LogLevelDisabled</div><div class="ttdef"><b>Definition</b> driver_log.h:10</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">LogLevelVerbose</a></div><div class="ttdeci">@ LogLevelVerbose</div><div class="ttdef"><b>Definition</b> driver_log.h:14</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a7aa67c4af6a6176801a2b2af65ac3cbb"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a7aa67c4af6a6176801a2b2af65ac3cbb">_LOG_CONFIG::MaxLogFileSize</a></div><div class="ttdeci">ULONG MaxLogFileSize</div><div class="ttdef"><b>Definition</b> driver_log.h:32</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a860b2d89bf1aff272da84d5029dcb0b1"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a860b2d89bf1aff272da84d5029dcb0b1">_LOG_CONFIG::IncludeTimestamp</a></div><div class="ttdeci">BOOLEAN IncludeTimestamp</div><div class="ttdef"><b>Definition</b> driver_log.h:29</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a8b05bbaba9e1fe6f53057c15e4a53a81"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81">LogFunctionEntry</a></div><div class="ttdeci">VOID LogFunctionEntry(PCSTR FunctionName)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a93b035f39214ba5782080d504ae3ebc7"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7">LogCleanup</a></div><div class="ttdeci">VOID LogCleanup(VOID)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_aa90925833aff044f4ba03f43f8084bf7"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></div><div class="ttdeci">enum _LOG_LEVEL LOG_LEVEL</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ab4caba1729c833f0c7cce2e72c20e30a"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a">LogConfigInit</a></div><div class="ttdeci">VOID LogConfigInit(PLOG_CONFIG LogConfig)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ab67683f62a123026ec78fe405cdbb177"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ab67683f62a123026ec78fe405cdbb177">_LOG_CONFIG::LogLevel</a></div><div class="ttdeci">LOG_LEVEL LogLevel</div><div class="ttdef"><b>Definition</b> driver_log.h:27</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_abac3042c22899daa9d6987d7f15e0185"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185">LogSetLevel</a></div><div class="ttdeci">VOID LogSetLevel(LOG_LEVEL Level)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ac82d96af8adf8a1862d02ac0be15939d"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ac82d96af8adf8a1862d02ac0be15939d">_LOG_CONFIG::IncludeComponentName</a></div><div class="ttdeci">BOOLEAN IncludeComponentName</div><div class="ttdef"><b>Definition</b> driver_log.h:30</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ae2910293c9c672800cca68427812b7c9"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9">LogInitialize</a></div><div class="ttdeci">VOID LogInitialize(PDRIVER_OBJECT DriverObject, PLOG_CONFIG LogConfig)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ae483585a71d174709d7049cc4b4758e1"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1">LogMessage</a></div><div class="ttdeci">VOID LogMessage(LOG_LEVEL Level, PCSTR Function, ULONG Line, PCSTR Format,...)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_struct__LOG__CONFIG"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a></div><div class="ttdef"><b>Definition</b> driver_log.h:48</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a565ca8d986ea85865e5e0e69c0fccc9d"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></div><div class="ttdeci">struct _LOG_CONFIG LOG_CONFIG</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843f"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a></div><div class="ttdeci">_LOG_LEVEL</div><div class="ttdef"><b>Definition</b> driver_log.h:17</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a></div><div class="ttdeci">@ LogLevelWarning</div><div class="ttdef"><b>Definition</b> driver_log.h:22</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a></div><div class="ttdeci">@ LogLevelError</div><div class="ttdef"><b>Definition</b> driver_log.h:21</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a></div><div class="ttdeci">@ LogLevelDebug</div><div class="ttdef"><b>Definition</b> driver_log.h:25</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a></div><div class="ttdeci">@ LogLevelInfo</div><div class="ttdef"><b>Definition</b> driver_log.h:24</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a></div><div class="ttdeci">@ LogTypeFile</div><div class="ttdef"><b>Definition</b> driver_log.h:37</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a></div><div class="ttdeci">@ LogTypeWPP</div><div class="ttdef"><b>Definition</b> driver_log.h:39</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a></div><div class="ttdeci">@ LogTypeETW</div><div class="ttdef"><b>Definition</b> driver_log.h:38</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a></div><div class="ttdeci">@ LogTypeAll</div><div class="ttdef"><b>Definition</b> driver_log.h:41</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_aa90925833aff044f4ba03f43f8084bf7"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></div><div class="ttdeci">enum _LOG_LEVEL LOG_LEVEL</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_ab99d8d17b06b190b7fecbbadd3d6b7df"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></div><div class="ttdeci">LOG_CONFIG * PLOG_CONFIG</div><div class="ttdef"><b>Definition</b> driver_log.h:57</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_3d69f64eaf81436fe2b22361382717e5.html">core</a></li><li class="navelem"><a href="dir_ebc4183974da10596d83fbd9697e69e1.html">log</a></li><li class="navelem"><a href="include_2core_2log_2driver__log_8h.html">driver_log.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
