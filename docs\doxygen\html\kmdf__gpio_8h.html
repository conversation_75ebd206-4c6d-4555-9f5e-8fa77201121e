<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__gpio_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_gpio.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="kmdf__bus__common_8h_source.html">kmdf_bus_common.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for kmdf_gpio.h:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__gpio_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="16,96,163,123"/>
<area shape="poly" title=" " alt="" coords="92,49,92,80,87,80,87,49"/>
<area shape="rect" title=" " alt="" coords="6,179,59,205"/>
<area shape="poly" title=" " alt="" coords="83,125,52,168,48,165,78,122"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="83,171,213,213"/>
<area shape="poly" title=" " alt="" coords="101,122,127,157,122,160,97,125"/>
<area shape="rect" title=" " alt="" coords="116,261,180,288"/>
<area shape="poly" title=" " alt="" coords="151,214,151,245,146,245,146,214"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__gpio_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8hdep" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__gpio_8hdep">
<area shape="rect" title=" " alt="" coords="734,5,902,48"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="668,96,843,139"/>
<area shape="poly" title=" " alt="" coords="797,62,772,97,768,94,793,59"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="1191,285,1336,328"/>
<area shape="poly" title=" " alt="" coords="866,54,1232,283,1229,287,863,59"/>
<area shape="rect" href="gpio__device_8c.html" title=" " alt="" coords="484,187,659,229"/>
<area shape="poly" title=" " alt="" coords="700,148,615,189,613,184,697,143"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="683,187,828,229"/>
<area shape="poly" title=" " alt="" coords="758,154,758,186,753,186,753,154"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="5,277,151,336"/>
<area shape="poly" title=" " alt="" coords="668,232,525,248,413,252,302,258,237,266,163,280,152,282,150,277,161,275,237,261,301,253,413,247,525,243,667,227"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,277,320,336"/>
<area shape="poly" title=" " alt="" coords="668,233,573,247,498,254,424,262,332,280,321,283,320,278,331,275,423,257,497,248,572,241,667,227"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,285,489,328"/>
<area shape="poly" title=" " alt="" coords="668,234,501,280,478,287,476,282,500,275,666,229"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="513,285,659,328"/>
<area shape="poly" title=" " alt="" coords="707,240,624,287,621,282,705,235"/>
<area shape="poly" title=" " alt="" coords="844,217,1001,239,1180,275,1207,282,1205,287,1179,280,1000,244,843,222"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="683,285,828,328"/>
<area shape="poly" title=" " alt="" coords="758,245,758,285,753,285,753,245"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="852,285,997,328"/>
<area shape="poly" title=" " alt="" coords="806,235,890,282,887,287,803,240"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="1021,285,1167,328"/>
<area shape="poly" title=" " alt="" coords="844,229,1011,275,1034,282,1033,287,1009,280,843,234"/>
</map>
</div>
</div>
<p><a href="kmdf__gpio_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FGPIO_5FPIN_5FCONFIG_5Fstruct_5F_5FGPIO_5F_5FPIN_5F_5FCONFIG" id="r__5FGPIO_5FPIN_5FCONFIG_5Fstruct_5F_5FGPIO_5F_5FPIN_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a9643691e5b435f14c69e6016c2fae45a" id="r_a9643691e5b435f14c69e6016c2fae45a"><td class="memItemLeft" align="right" valign="top">typedef VOID(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a>) (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a941806f1dcee3e7fc53c46d5b60b362b" id="r_a941806f1dcee3e7fc53c46d5b60b362b"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a></td></tr>
<tr class="memitem:ab852084eda7787e469301a172c7498a5" id="r_ab852084eda7787e469301a172c7498a5"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a></td></tr>
<tr class="memitem:a1ca9004cc1371bfb961fb7b894e2cd0a" id="r_a1ca9004cc1371bfb961fb7b894e2cd0a"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a></td></tr>
<tr class="memitem:a96127f2b5d39ef12184a23f9ad42999b" id="r_a96127f2b5d39ef12184a23f9ad42999b"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a></td></tr>
<tr class="memitem:aa14439653449111e1deb51cf90c5b7a0" id="r_aa14439653449111e1deb51cf90c5b7a0"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a7a6307ef3793d4b6fcafc75f54030d33" id="r_a7a6307ef3793d4b6fcafc75f54030d33"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a> { <br />
&#160;&#160;<a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76">GpioInterruptNone</a> = 0
, <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560">GpioInterruptRising</a> = 1
, <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84">GpioInterruptFalling</a> = 2
, <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64">GpioInterruptBoth</a> = 3
, <br />
&#160;&#160;<a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42">GpioInterruptLevel</a> = 4
<br />
 }</td></tr>
<tr class="memitem:a33cdce91cf0e8b3834911035d71d7c4b" id="r_a33cdce91cf0e8b3834911035d71d7c4b"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a> { <a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2">GpioDirectionIn</a> = 0
, <a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c">GpioDirectionOut</a> = 1
 }</td></tr>
<tr class="memitem:a90e508a8bbe068b0558a8fbabf471070" id="r_a90e508a8bbe068b0558a8fbabf471070"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a> { <a class="el" href="#a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78">GpioPolarityActiveLow</a> = 0
, <a class="el" href="#a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d">GpioPolarityActiveHigh</a> = 1
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a32c9f2fdbf98b7e59c2b494d61f465a4" id="r_a32c9f2fdbf98b7e59c2b494d61f465a4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a32c9f2fdbf98b7e59c2b494d61f465a4">GPIODisableInterrupt</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a818408e823499cbc8bf3a09f74062a48" id="r_a818408e823499cbc8bf3a09f74062a48"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a818408e823499cbc8bf3a09f74062a48">GPIOEnableInterrupt</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ <a class="el" href="#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a> InterruptType, _In_ <a class="el" href="#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a> Callback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:adcddf2e62a93fe5cc3fa7f46b67845bb" id="r_adcddf2e62a93fe5cc3fa7f46b67845bb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adcddf2e62a93fe5cc3fa7f46b67845bb">GPIOGetValue</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _Out_ PBOOLEAN Value)</td></tr>
<tr class="memitem:a0a73c23c89291af0e81cef7098d10e29" id="r_a0a73c23c89291af0e81cef7098d10e29"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0a73c23c89291af0e81cef7098d10e29">GPIOInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a> GpioConfig)</td></tr>
<tr class="memitem:ade1cb652014fc4a3984567ff49900d81" id="r_ade1cb652014fc4a3984567ff49900d81"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ade1cb652014fc4a3984567ff49900d81">GPIOPulse</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ ULONG PulseDurationMs)</td></tr>
<tr class="memitem:aab159dfcef06f528d7ebdb9fa3ce8be4" id="r_aab159dfcef06f528d7ebdb9fa3ce8be4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab159dfcef06f528d7ebdb9fa3ce8be4">GPIOSetDirection</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ <a class="el" href="#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a> <a class="el" href="gpio__device_8c.html#a130124259198fee8d71747e31a529e96">Direction</a>)</td></tr>
<tr class="memitem:a81265230a3fa84b9f3a7851d8c9ebe3d" id="r_a81265230a3fa84b9f3a7851d8c9ebe3d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a81265230a3fa84b9f3a7851d8c9ebe3d">GPIOSetValue</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ BOOLEAN Value)</td></tr>
<tr class="memitem:a785f00e9c0879fb478077d2cdce99906" id="r_a785f00e9c0879fb478077d2cdce99906"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a785f00e9c0879fb478077d2cdce99906">GPIOUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__GPIO__PIN__CONFIG" id="struct__GPIO__PIN__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__GPIO__PIN__CONFIG">&#9670;&#160;</a></span>_GPIO_PIN_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _GPIO_PIN_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a8d011afe85a8ab79a1f0ab8a0aeeee9f" name="a8d011afe85a8ab79a1f0ab8a0aeeee9f"></a>ULONG</td>
<td class="fieldname">
DebounceTime</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aa3d8c873130efce02f77b07f742a05ff" name="aa3d8c873130efce02f77b07f742a05ff"></a><a class="el" href="#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a></td>
<td class="fieldname">
Direction</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a16e25974fff79d31225fbb4402059d42" name="a16e25974fff79d31225fbb4402059d42"></a><a class="el" href="#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a></td>
<td class="fieldname">
InterruptType</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="abd8ea283a387003ee58595a12fb2b5ec" name="abd8ea283a387003ee58595a12fb2b5ec"></a>BOOLEAN</td>
<td class="fieldname">
OpenDrain</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="acc85fb800144aa8336d910544113014c" name="acc85fb800144aa8336d910544113014c"></a>ULONG</td>
<td class="fieldname">
PinNumber</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a3298588b14792c3fdd790313352c673e" name="a3298588b14792c3fdd790313352c673e"></a><a class="el" href="#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a></td>
<td class="fieldname">
Polarity</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="afd66b730c3f6bec3b15b28ccd0832f85" name="afd66b730c3f6bec3b15b28ccd0832f85"></a>BOOLEAN</td>
<td class="fieldname">
PullDown</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a0de26b1a7ce28b0b9aa018fe961fdf60" name="a0de26b1a7ce28b0b9aa018fe961fdf60"></a>BOOLEAN</td>
<td class="fieldname">
PullUp</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a6065b7e884557238880cacf2002110d3" name="a6065b7e884557238880cacf2002110d3"></a>LARGE_INTEGER</td>
<td class="fieldname">
SpbConnectionId</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aec4597ae991a215e755336600b15e60a" name="aec4597ae991a215e755336600b15e60a"></a>PVOID</td>
<td class="fieldname">
SpbDeviceObject</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a9643691e5b435f14c69e6016c2fae45a" name="a9643691e5b435f14c69e6016c2fae45a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9643691e5b435f14c69e6016c2fae45a">&#9670;&#160;</a></span>GPIO_INTERRUPT_CALLBACK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef VOID(* GPIO_INTERRUPT_CALLBACK) (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a941806f1dcee3e7fc53c46d5b60b362b" name="a941806f1dcee3e7fc53c46d5b60b362b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a941806f1dcee3e7fc53c46d5b60b362b">&#9670;&#160;</a></span>GPIO_INTERRUPT_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a> <a class="el" href="#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab852084eda7787e469301a172c7498a5" name="ab852084eda7787e469301a172c7498a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab852084eda7787e469301a172c7498a5">&#9670;&#160;</a></span>GPIO_PIN_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a> <a class="el" href="#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1ca9004cc1371bfb961fb7b894e2cd0a" name="a1ca9004cc1371bfb961fb7b894e2cd0a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ca9004cc1371bfb961fb7b894e2cd0a">&#9670;&#160;</a></span>GPIO_PIN_DIRECTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a> <a class="el" href="#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a96127f2b5d39ef12184a23f9ad42999b" name="a96127f2b5d39ef12184a23f9ad42999b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96127f2b5d39ef12184a23f9ad42999b">&#9670;&#160;</a></span>GPIO_PIN_POLARITY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a> <a class="el" href="#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa14439653449111e1deb51cf90c5b7a0" name="aa14439653449111e1deb51cf90c5b7a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa14439653449111e1deb51cf90c5b7a0">&#9670;&#160;</a></span>PGPIO_PIN_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a> * <a class="el" href="#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="a7a6307ef3793d4b6fcafc75f54030d33" name="a7a6307ef3793d4b6fcafc75f54030d33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a6307ef3793d4b6fcafc75f54030d33">&#9670;&#160;</a></span>_GPIO_INTERRUPT_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76" name="a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76"></a>GpioInterruptNone&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560" name="a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560"></a>GpioInterruptRising&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84" name="a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84"></a>GpioInterruptFalling&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64" name="a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64"></a>GpioInterruptBoth&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42" name="a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42"></a>GpioInterruptLevel&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a33cdce91cf0e8b3834911035d71d7c4b" name="a33cdce91cf0e8b3834911035d71d7c4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33cdce91cf0e8b3834911035d71d7c4b">&#9670;&#160;</a></span>_GPIO_PIN_DIRECTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2" name="a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2"></a>GpioDirectionIn&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" name="a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c"></a>GpioDirectionOut&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a90e508a8bbe068b0558a8fbabf471070" name="a90e508a8bbe068b0558a8fbabf471070"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a90e508a8bbe068b0558a8fbabf471070">&#9670;&#160;</a></span>_GPIO_PIN_POLARITY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" name="a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78"></a>GpioPolarityActiveLow&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" name="a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d"></a>GpioPolarityActiveHigh&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a32c9f2fdbf98b7e59c2b494d61f465a4" name="a32c9f2fdbf98b7e59c2b494d61f465a4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32c9f2fdbf98b7e59c2b494d61f465a4">&#9670;&#160;</a></span>GPIODisableInterrupt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID GPIODisableInterrupt </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__gpio_8h_a32c9f2fdbf98b7e59c2b494d61f465a4_icgraph.png" border="0" usemap="#akmdf__gpio_8h_a32c9f2fdbf98b7e59c2b494d61f465a4_icgraph" loading="lazy" alt=""/></div>
<map name="akmdf__gpio_8h_a32c9f2fdbf98b7e59c2b494d61f465a4_icgraph" id="akmdf__gpio_8h_a32c9f2fdbf98b7e59c2b494d61f465a4_icgraph">
<area shape="rect" title=" " alt="" coords="92,5,238,32"/>
<area shape="rect" href="gpio__device_8c.html#aac20ced732c198d7484287c9eb39e413" title=" " alt="" coords="5,5,44,32"/>
<area shape="poly" title=" " alt="" coords="76,21,44,21,44,16,76,16"/>
</map>
</div>

</div>
</div>
<a id="a818408e823499cbc8bf3a09f74062a48" name="a818408e823499cbc8bf3a09f74062a48"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a818408e823499cbc8bf3a09f74062a48">&#9670;&#160;</a></span>GPIOEnableInterrupt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOEnableInterrupt </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a></td>          <td class="paramname"><span class="paramname"><em>InterruptType</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>Callback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="adcddf2e62a93fe5cc3fa7f46b67845bb" name="adcddf2e62a93fe5cc3fa7f46b67845bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcddf2e62a93fe5cc3fa7f46b67845bb">&#9670;&#160;</a></span>GPIOGetValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOGetValue </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PBOOLEAN</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0a73c23c89291af0e81cef7098d10e29" name="a0a73c23c89291af0e81cef7098d10e29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a73c23c89291af0e81cef7098d10e29">&#9670;&#160;</a></span>GPIOInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>GpioConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__gpio_8h_a0a73c23c89291af0e81cef7098d10e29_cgraph.png" border="0" usemap="#akmdf__gpio_8h_a0a73c23c89291af0e81cef7098d10e29_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__gpio_8h_a0a73c23c89291af0e81cef7098d10e29_cgraph" id="akmdf__gpio_8h_a0a73c23c89291af0e81cef7098d10e29_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,108,56"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="156,29,240,56"/>
<area shape="poly" title=" " alt="" coords="108,40,140,40,140,45,108,45"/>
<area shape="poly" title=" " alt="" coords="173,30,169,21,172,11,182,5,198,3,215,5,224,12,221,16,213,10,198,8,184,10,176,14,174,20,178,28"/>
</map>
</div>

</div>
</div>
<a id="ade1cb652014fc4a3984567ff49900d81" name="ade1cb652014fc4a3984567ff49900d81"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade1cb652014fc4a3984567ff49900d81">&#9670;&#160;</a></span>GPIOPulse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOPulse </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PulseDurationMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab159dfcef06f528d7ebdb9fa3ce8be4" name="aab159dfcef06f528d7ebdb9fa3ce8be4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab159dfcef06f528d7ebdb9fa3ce8be4">&#9670;&#160;</a></span>GPIOSetDirection()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOSetDirection </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a></td>          <td class="paramname"><span class="paramname"><em>Direction</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a81265230a3fa84b9f3a7851d8c9ebe3d" name="a81265230a3fa84b9f3a7851d8c9ebe3d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81265230a3fa84b9f3a7851d8c9ebe3d">&#9670;&#160;</a></span>GPIOSetValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS GPIOSetValue </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ BOOLEAN</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a785f00e9c0879fb478077d2cdce99906" name="a785f00e9c0879fb478077d2cdce99906"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a785f00e9c0879fb478077d2cdce99906">&#9670;&#160;</a></span>GPIOUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID GPIOUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__gpio_8h_a785f00e9c0879fb478077d2cdce99906_cgraph.png" border="0" usemap="#akmdf__gpio_8h_a785f00e9c0879fb478077d2cdce99906_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__gpio_8h_a785f00e9c0879fb478077d2cdce99906_cgraph" id="akmdf__gpio_8h_a785f00e9c0879fb478077d2cdce99906_cgraph">
<area shape="rect" title=" " alt="" coords="5,56,124,83"/>
<area shape="rect" href="spi__device_8c.html#ae42ccb14fff6c8b1c06d1ff178b6c146" title=" " alt="" coords="177,5,314,32"/>
<area shape="poly" title=" " alt="" coords="114,53,180,34,181,39,116,58"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="367,81,451,108"/>
<area shape="poly" title=" " alt="" coords="124,71,351,88,351,93,124,76"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="172,107,319,133"/>
<area shape="poly" title=" " alt="" coords="116,81,181,99,180,105,114,86"/>
<area shape="poly" title=" " alt="" coords="381,82,376,73,379,63,391,57,409,55,429,57,440,64,437,68,427,62,409,60,392,62,383,67,381,72,385,80"/>
<area shape="poly" title=" " alt="" coords="319,106,351,101,352,106,320,111"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__gpio_8h.html">kmdf_gpio.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
