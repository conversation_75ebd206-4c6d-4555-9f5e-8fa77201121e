<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="driver__main_8c" kind="file" language="C++">
    <compoundname>driver_main.c</compoundname>
    <includes refid="precomp_8h" local="yes">precomp.h</includes>
    <includes refid="driver__entry_8h" local="yes">../include/core/driver/driver_entry.h</includes>
    <includes refid="driver__core_8h" local="yes">../include/core/driver/driver_core.h</includes>
    <includes refid="device__manager_8h" local="yes">../include/core/device/device_manager.h</includes>
    <includes refid="error__codes_8h" local="yes">../include/core/error/error_codes.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../include/core/log/driver_log.h</includes>
    <includes refid="kmdf__bus__common_8h" local="yes">../include/hal/bus/kmdf_bus_common.h</includes>
    <includes refid="kmdf__i2c_8h" local="yes">../include/hal/bus/kmdf_i2c.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="19">
        <label>../include/core/driver/driver_entry.h</label>
        <link refid="driver__entry_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="20">
        <label>../include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="20" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" prot="public" static="no">
        <name>DRIVER_NAME</name>
        <initializer>&quot;KMDF Sample Driver&quot;</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="20" column="9" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="20" bodyend="-1"/>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
        <referencedby refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__main_8c" startline="146" endline="158">EvtDriverUnload</referencedby>
      </memberdef>
      <memberdef kind="define" id="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" prot="public" static="no">
        <name>DRIVER_VERSION_BUILD</name>
        <initializer>0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="23" column="9" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="23" bodyend="-1"/>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="define" id="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" prot="public" static="no">
        <name>DRIVER_VERSION_MAJOR</name>
        <initializer>1</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="21" column="9" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="21" bodyend="-1"/>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="define" id="driver__main_8c_1a31ba5103952e666f059389c86d76b640" prot="public" static="no">
        <name>DRIVER_VERSION_MINOR</name>
        <initializer>0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="22" column="9" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="22" bodyend="-1"/>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="define" id="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" prot="public" static="no">
        <name>DRIVER_VERSION_REV</name>
        <initializer>0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="24" column="9" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="24" bodyend="-1"/>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="driver__main_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" prot="public" static="no" mutable="no">
        <type>const GUID</type>
        <definition>const GUID SampleDriverInterfaceGuid</definition>
        <argsstring></argsstring>
        <name>SampleDriverInterfaceGuid</name>
        <initializer>= { 0x5d624f94, 0x8850, 0x40c3, { 0xa3, 0xfa, 0xa4, 0xfd, 0x20, 0x80, 0xba, 0xf3 } }</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="31" column="12" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="31" bodyend="31"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>EXTERN_C NTSTATUS</type>
        <definition>EXTERN_C NTSTATUS DriverEntry</definition>
        <argsstring>(_In_ PDRIVER_OBJECT DriverObject, _In_ PUNICODE_STRING RegistryPath)</argsstring>
        <name>DriverEntry</name>
        <param>
          <type>_In_ PDRIVER_OBJECT</type>
          <declname>DriverObject</declname>
        </param>
        <param>
          <type>_In_ PUNICODE_STRING</type>
          <declname>RegistryPath</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="38" column="1" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="38" bodyend="101"/>
        <references refid="struct__DRIVER__VERSION_1a0f5edd5b90ea7d0767d56293b15a36f5" compoundref="driver__core_8h" startline="31">_DRIVER_VERSION::Build</references>
        <references refid="struct__DRIVER__CONFIG_1a000ada599f1b619fbded0ff23963c1f4" compoundref="driver__core_8h" startline="62">_DRIVER_CONFIG::DeviceSymLinkName</references>
        <references refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" compoundref="driver__main_8c" startline="20">DRIVER_NAME</references>
        <references refid="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" compoundref="driver__main_8c" startline="23">DRIVER_VERSION_BUILD</references>
        <references refid="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" compoundref="driver__main_8c" startline="21">DRIVER_VERSION_MAJOR</references>
        <references refid="driver__main_8c_1a31ba5103952e666f059389c86d76b640" compoundref="driver__main_8c" startline="22">DRIVER_VERSION_MINOR</references>
        <references refid="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" compoundref="driver__main_8c" startline="24">DRIVER_VERSION_REV</references>
        <references refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</references>
        <references refid="struct__DRIVER__CONFIG_1ad02e8885eeb5542df2e816c56a3c89c5" compoundref="driver__core_8h" startline="60">_DRIVER_CONFIG::DriverFlags</references>
        <references refid="struct__DRIVER__CONFIG_1a0ad249f48409a76f0a5818a71cb02f97" compoundref="driver__core_8h" startline="59">_DRIVER_CONFIG::DriverType</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b" compoundref="driver__core_8h" startline="17">DriverTypeGeneric</references>
        <references refid="struct__DRIVER__CONFIG_1a46d28956be671bbbda04a5e43175b210" compoundref="driver__core_8h" startline="64">_DRIVER_CONFIG::EnableEventLogging</references>
        <references refid="struct__DRIVER__CONFIG_1aee9c4a267f9198351079c9287c248ff5" compoundref="driver__core_8h" startline="63">_DRIVER_CONFIG::EnableWPP</references>
        <references refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</references>
        <references refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__entry_8c" startline="148" endline="157">EvtDriverUnload</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7">LogCleanup</references>
        <references refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</references>
        <references refid="struct__DRIVER__CONFIG_1a462a9835cac3e90e89501fd667ee0dff" compoundref="driver__core_8h" startline="65">_DRIVER_CONFIG::LogLevel</references>
        <references refid="struct__LOG__CONFIG_1ab67683f62a123026ec78fe405cdbb177" compoundref="include_2core_2log_2driver__log_8h" startline="27">_LOG_CONFIG::LogLevel</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" compoundref="src_2core_2log_2driver__log_8h" startline="24">LogLevelInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" compoundref="include_2core_2log_2driver__log_8h" startline="19">LogTypeKdPrint</references>
        <references refid="struct__LOG__CONFIG_1a4b99237eff4b17c1d4fbfa9697528f70" compoundref="include_2core_2log_2driver__log_8h" startline="28">_LOG_CONFIG::LogTypes</references>
        <references refid="struct__DRIVER__VERSION_1a94c8bf9509d348f5cd15c358a92eb0ce" compoundref="driver__core_8h" startline="29">_DRIVER_VERSION::Major</references>
        <references refid="struct__DRIVER__VERSION_1aaa3047faacbaba04237817cbf6c9f0e9" compoundref="driver__core_8h" startline="30">_DRIVER_VERSION::Minor</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__DRIVER__CONFIG_1a390b2848a9d77722f7dd36b28d87b712" compoundref="driver__core_8h" startline="61">_DRIVER_CONFIG::PowerFlags</references>
        <references refid="struct__DRIVER__VERSION_1a7869e35e89c7ca48a53d1e555a4123cc" compoundref="driver__core_8h" startline="32">_DRIVER_VERSION::Revision</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" compoundref="driver__core_8h" startline="66">_DRIVER_CONFIG::Version</references>
      </memberdef>
      <memberdef kind="function" id="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS EvtDriverDeviceAdd</definition>
        <argsstring>(_In_ WDFDRIVER Driver, _Inout_ PWDFDEVICE_INIT DeviceInit)</argsstring>
        <name>EvtDriverDeviceAdd</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>Driver</declname>
        </param>
        <param>
          <type>_Inout_ PWDFDEVICE_INIT</type>
          <declname>DeviceInit</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="106" column="10" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="106" bodyend="141" declfile="C:/KMDF Driver1/src/driver_main.c" declline="28" declcolumn="10"/>
        <references refid="struct__DEVICE__INIT__CONFIG_1a55239223743c68f1900157add76e756b" compoundref="device__manager_8h" startline="91">_DEVICE_INIT_CONFIG::BusType</references>
        <references refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" compoundref="kmdf__bus__common_8h" startline="17">BusTypeI2C</references>
        <references refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a7ebe4b67b3f9303e10ddace98e7f69ff" compoundref="device__manager_8h" startline="89">_DEVICE_INIT_CONFIG::DeviceId</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" compoundref="device__manager_8h" startline="92">_DEVICE_INIT_CONFIG::DeviceInterfaceGuid</references>
        <references refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a354a892269e5bc5d7859d3c99785d087" compoundref="device__manager_8h" startline="90">_DEVICE_INIT_CONFIG::EnableIdlePowerManagement</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
        <references refid="driver__entry_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" compoundref="driver__entry_8c" startline="13" endline="13">SampleDriverInterfaceGuid</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1accbb4ad53561dc015a600f3684e552e6" compoundref="device__manager_8h" startline="88">_DEVICE_INIT_CONFIG::VendorId</references>
      </memberdef>
      <memberdef kind="function" id="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID EvtDriverUnload</definition>
        <argsstring>(_In_ WDFDRIVER Driver)</argsstring>
        <name>EvtDriverUnload</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>Driver</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/driver_main.c" line="146" column="6" bodyfile="C:/KMDF Driver1/src/driver_main.c" bodystart="146" bodyend="158" declfile="C:/KMDF Driver1/src/driver_main.c" declline="29" declcolumn="6"/>
        <references refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" compoundref="driver__main_8c" startline="20">DRIVER_NAME</references>
        <references refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" compoundref="driver__core_8c" startline="221" endline="249">DriverCoreCleanup</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7">LogCleanup</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>driver_main.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>主驱动程序入口文件</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>提供驱动程序入口点和主要回调函数的实现</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">precomp.h</ref>&quot;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>Precompiled<sp/>header</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="comment">//<sp/>包含模块化架构的头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="driver__entry_8h" kindref="compound">../include/core/driver/driver_entry.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="driver__core_8h" kindref="compound">../include/core/driver/driver_core.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="device__manager_8h" kindref="compound">../include/core/device/device_manager.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__bus__common_8h" kindref="compound">../include/hal/bus/kmdf_bus_common.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__i2c_8h" kindref="compound">../include/hal/bus/kmdf_i2c.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//<sp/>驱动程序定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_NAME<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&quot;KMDF<sp/>Sample<sp/>Driver&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_VERSION_MAJOR<sp/><sp/>1</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="driver__main_8c_1a31ba5103952e666f059389c86d76b640" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_VERSION_MINOR<sp/><sp/>0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_VERSION_BUILD<sp/><sp/>0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_VERSION_REV<sp/><sp/><sp/><sp/>0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight><highlight class="comment">//<sp/>驱动程序<sp/>GUID</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>{5D624F94-8850-40C3-A3FA-A4FD2080BAF3}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal">NTSTATUS<sp/><ref refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" kindref="member">EvtDriverDeviceAdd</ref>(_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver,<sp/>_Inout_<sp/>PWDFDEVICE_INIT<sp/>DeviceInit);</highlight></codeline>
<codeline lineno="29"><highlight class="normal">VOID<sp/><ref refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" kindref="member">EvtDriverUnload</ref>(_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver);</highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="driver__main_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">const</highlight><highlight class="normal"><sp/>GUID<sp/><ref refid="driver__entry_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" kindref="member">SampleDriverInterfaceGuid</ref><sp/>=<sp/>{<sp/>0x5d624f94,<sp/>0x8850,<sp/>0x40c3,<sp/>{<sp/>0xa3,<sp/>0xfa,<sp/>0xa4,<sp/>0xfd,<sp/>0x20,<sp/>0x80,<sp/>0xba,<sp/>0xf3<sp/>}<sp/>};</highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="34"><highlight class="comment"><sp/>*<sp/>随着驱动程序的加载，该函数将被调用</highlight></codeline>
<codeline lineno="35"><highlight class="comment"><sp/>*<sp/>这是驱动程序的主要入口点</highlight></codeline>
<codeline lineno="36"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal">EXTERN_C<sp/>NTSTATUS</highlight></codeline>
<codeline lineno="38" refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" refkind="member"><highlight class="normal"><ref refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" kindref="member">DriverEntry</ref>(</highlight></codeline>
<codeline lineno="39"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PDRIVER_OBJECT<sp/><sp/>DriverObject,</highlight></codeline>
<codeline lineno="40"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PUNICODE_STRING<sp/>RegistryPath</highlight></codeline>
<codeline lineno="41"><highlight class="normal">)</highlight></codeline>
<codeline lineno="42"><highlight class="normal">{</highlight></codeline>
<codeline lineno="43"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_DRIVER_CONFIG<sp/>config;</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>driver;</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref><sp/>logConfig;</highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8h_1ab3f3478bc4dcb804303c86d537979d13" kindref="member">DRIVER_CONFIG</ref><sp/>driverCoreConfig;</highlight></codeline>
<codeline lineno="48"><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[%s]<sp/>Driver<sp/>Entry<sp/>-<sp/>Version<sp/>%d.%d.%d.%d\n&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" kindref="member">DRIVER_NAME</ref>,</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" kindref="member">DRIVER_VERSION_MAJOR</ref>,</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__main_8c_1a31ba5103952e666f059389c86d76b640" kindref="member">DRIVER_VERSION_MINOR</ref>,</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" kindref="member">DRIVER_VERSION_BUILD</ref>,</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" kindref="member">DRIVER_VERSION_REV</ref>));</highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化驱动程序配置</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_DRIVER_CONFIG_INIT(&amp;config,<sp/><ref refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" kindref="member">EvtDriverDeviceAdd</ref>);</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/>config.EvtDriverUnload<sp/>=<sp/><ref refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" kindref="member">EvtDriverUnload</ref>;</highlight></codeline>
<codeline lineno="59"><highlight class="normal"></highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>创建<sp/>WDF<sp/>驱动对象</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfDriverCreate(</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DriverObject,</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>RegistryPath,</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_NO_OBJECT_ATTRIBUTES,</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;config,</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;driver</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="68"><highlight class="normal"></highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[%s]<sp/>WdfDriverCreate<sp/>failed,<sp/>status=0x%08X\n&quot;</highlight><highlight class="normal">,<sp/><ref refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" kindref="member">DRIVER_NAME</ref>,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>));</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化日志系统</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>logConfig.<ref refid="struct__LOG__CONFIG_1ab67683f62a123026ec78fe405cdbb177" kindref="member">LogLevel</ref><sp/>=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>;</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/>logConfig.<ref refid="struct__LOG__CONFIG_1a4b99237eff4b17c1d4fbfa9697528f70" kindref="member">LogTypes</ref><sp/>=<sp/><ref refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" kindref="member">LogTypeKdPrint</ref>;</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" kindref="member">LogInitialize</ref>(DriverObject,<sp/>&amp;logConfig);</highlight></codeline>
<codeline lineno="78"><highlight class="normal"></highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化驱动核心</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(&amp;driverCoreConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1ab3f3478bc4dcb804303c86d537979d13" kindref="member">DRIVER_CONFIG</ref>));</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a0ad249f48409a76f0a5818a71cb02f97" kindref="member">DriverType</ref><sp/>=<sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b" kindref="member">DriverTypeGeneric</ref>;</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1ad02e8885eeb5542df2e816c56a3c89c5" kindref="member">DriverFlags</ref><sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a390b2848a9d77722f7dd36b28d87b712" kindref="member">PowerFlags</ref><sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a000ada599f1b619fbded0ff23963c1f4" kindref="member">DeviceSymLinkName</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1aee9c4a267f9198351079c9287c248ff5" kindref="member">EnableWPP</ref><sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a46d28956be671bbbda04a5e43175b210" kindref="member">EnableEventLogging</ref><sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a462a9835cac3e90e89501fd667ee0dff" kindref="member">LogLevel</ref><sp/>=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>;</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" kindref="member">Version</ref>.<ref refid="struct__DRIVER__VERSION_1a94c8bf9509d348f5cd15c358a92eb0ce" kindref="member">Major</ref><sp/>=<sp/><ref refid="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" kindref="member">DRIVER_VERSION_MAJOR</ref>;</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" kindref="member">Version</ref>.<ref refid="struct__DRIVER__VERSION_1aaa3047faacbaba04237817cbf6c9f0e9" kindref="member">Minor</ref><sp/>=<sp/><ref refid="driver__main_8c_1a31ba5103952e666f059389c86d76b640" kindref="member">DRIVER_VERSION_MINOR</ref>;</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" kindref="member">Version</ref>.<ref refid="struct__DRIVER__VERSION_1a0f5edd5b90ea7d0767d56293b15a36f5" kindref="member">Build</ref><sp/>=<sp/><ref refid="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" kindref="member">DRIVER_VERSION_BUILD</ref>;</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/>driverCoreConfig.<ref refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" kindref="member">Version</ref>.<ref refid="struct__DRIVER__VERSION_1a7869e35e89c7ca48a53d1e555a4123cc" kindref="member">Revision</ref><sp/>=<sp/><ref refid="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" kindref="member">DRIVER_VERSION_REV</ref>;</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" kindref="member">DriverCoreInitialize</ref>(driver,<sp/>&amp;driverCoreConfig);</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>initialize<sp/>driver<sp/>core&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" kindref="member">LogCleanup</ref>();</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="98"><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;%s<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">,<sp/><ref refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" kindref="member">DRIVER_NAME</ref>);</highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="101"><highlight class="normal">}</highlight></codeline>
<codeline lineno="102"><highlight class="normal"></highlight></codeline>
<codeline lineno="103"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="104"><highlight class="comment"><sp/>*<sp/>EvtDriverDeviceAdd<sp/>-<sp/>设备添加回调</highlight></codeline>
<codeline lineno="105"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="106" refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" refkind="member"><highlight class="normal">NTSTATUS<sp/><ref refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" kindref="member">EvtDriverDeviceAdd</ref>(_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver,<sp/>_Inout_<sp/>PWDFDEVICE_INIT<sp/>DeviceInit)</highlight></codeline>
<codeline lineno="107"><highlight class="normal">{</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device;</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a2e1a690ef27ca2498b8893d6bf8ab597" kindref="member">DEVICE_INIT_CONFIG</ref><sp/>deviceConfig;</highlight></codeline>
<codeline lineno="111"><highlight class="normal"></highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Driver);</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(DeviceInit);</highlight></codeline>
<codeline lineno="114"><highlight class="normal"></highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Processing<sp/>device<sp/>add&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"></highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化设备配置</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(&amp;deviceConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a2e1a690ef27ca2498b8893d6bf8ab597" kindref="member">DEVICE_INIT_CONFIG</ref>));</highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceConfig.<ref refid="struct__DEVICE__INIT__CONFIG_1a7ebe4b67b3f9303e10ddace98e7f69ff" kindref="member">DeviceId</ref><sp/>=<sp/>0x1234;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>示例值</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceConfig.<ref refid="struct__DEVICE__INIT__CONFIG_1accbb4ad53561dc015a600f3684e552e6" kindref="member">VendorId</ref><sp/>=<sp/>0x5678;<sp/><sp/></highlight><highlight class="comment">//<sp/>示例值</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceConfig.<ref refid="struct__DEVICE__INIT__CONFIG_1a55239223743c68f1900157add76e756b" kindref="member">BusType</ref><sp/>=<sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" kindref="member">BusTypeI2C</ref>;</highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceConfig.<ref refid="struct__DEVICE__INIT__CONFIG_1a354a892269e5bc5d7859d3c99785d087" kindref="member">EnableIdlePowerManagement</ref><sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceConfig.<ref refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kindref="member">DeviceInterfaceGuid</ref><sp/>=<sp/>&amp;<ref refid="driver__entry_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" kindref="member">SampleDriverInterfaceGuid</ref>;</highlight></codeline>
<codeline lineno="124"><highlight class="normal"></highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>创建设备</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" kindref="member">DeviceCreate</ref>(Driver,<sp/>&amp;deviceConfig,<sp/>&amp;device);</highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;DeviceCreate<sp/>failed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="131"><highlight class="normal"></highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>将设备添加到驱动核心</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" kindref="member">DriverCoreAddDevice</ref>(device);</highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="135"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>add<sp/>device<sp/>to<sp/>driver<sp/>core&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="138"><highlight class="normal"></highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>added<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="141"><highlight class="normal">}</highlight></codeline>
<codeline lineno="142"><highlight class="normal"></highlight></codeline>
<codeline lineno="143"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="144"><highlight class="comment"><sp/>*<sp/>EvtDriverUnload<sp/>-<sp/>驱动卸载回调</highlight></codeline>
<codeline lineno="145"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="146" refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" refkind="member"><highlight class="normal">VOID<sp/><ref refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" kindref="member">EvtDriverUnload</ref>(_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver)</highlight></codeline>
<codeline lineno="147"><highlight class="normal">{</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Driver);</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Driver<sp/>unloading&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="150"><highlight class="normal"></highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清理驱动核心资源</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="152"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" kindref="member">DriverCoreCleanup</ref>();</highlight></codeline>
<codeline lineno="153"><highlight class="normal"></highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清理日志系统</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" kindref="member">LogCleanup</ref>();</highlight></codeline>
<codeline lineno="156"><highlight class="normal"></highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;%s<sp/>unloaded<sp/>successfully&quot;</highlight><highlight class="normal">,<sp/><ref refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" kindref="member">DRIVER_NAME</ref>);</highlight></codeline>
<codeline lineno="158"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/driver_main.c"/>
  </compounddef>
</doxygen>
