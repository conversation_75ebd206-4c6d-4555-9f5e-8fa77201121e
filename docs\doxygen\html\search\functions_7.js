var searchData=
[
  ['i2cdevicecleanup_0',['I2cDeviceCleanup',['../i2c__device_8c.html#a5da67a960d3cf99caa6874438a84629b',1,'I2cDeviceCleanup(_In_ WDFDEVICE Device):&#160;i2c_device.c'],['../i2c__device_8h.html#a5da67a960d3cf99caa6874438a84629b',1,'I2cDeviceCleanup(_In_ WDFDEVICE Device):&#160;i2c_device.c']]],
  ['i2cdevicegetstatistics_1',['I2cDeviceGetStatistics',['../i2c__device_8c.html#a709aca0009ccfb39adebbdd9ce97e252',1,'I2cDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics):&#160;i2c_device.c'],['../i2c__device_8h.html#a709aca0009ccfb39adebbdd9ce97e252',1,'I2cDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics):&#160;i2c_device.c']]],
  ['i2cdeviceinitialize_2',['I2cDeviceInitialize',['../i2c__device_8c.html#ab0c3b778b5a363d418c3d768cdb1e2d4',1,'I2cDeviceInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_device.c'],['../i2c__device_8h.html#ab0c3b778b5a363d418c3d768cdb1e2d4',1,'I2cDeviceInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_device.c']]],
  ['i2cdeviceread_3',['I2cDeviceRead',['../i2c__device_8c.html#a6576f1e3485d12c22c444244044c1d30',1,'I2cDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;i2c_device.c'],['../i2c__device_8h.html#a6576f1e3485d12c22c444244044c1d30',1,'I2cDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;i2c_device.c']]],
  ['i2cdevicetransfer_4',['I2cDeviceTransfer',['../i2c__device_8c.html#ad84f26684684313ff193803d1d9c7c32',1,'I2cDeviceTransfer(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount):&#160;i2c_device.c'],['../i2c__device_8h.html#ad84f26684684313ff193803d1d9c7c32',1,'I2cDeviceTransfer(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount):&#160;i2c_device.c']]],
  ['i2cdevicewrite_5',['I2cDeviceWrite',['../i2c__device_8c.html#a580f2434082501937a3d8bc4d5591866',1,'I2cDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;i2c_device.c'],['../i2c__device_8h.html#a580f2434082501937a3d8bc4d5591866',1,'I2cDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;i2c_device.c']]],
  ['i2cinitialize_6',['I2CInitialize',['../i2c__core_8c.html#a3730a6f611cf9feba7ba954330f41a6c',1,'I2CInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a5467da0184a8f514f9ff43ab28f7d2d0',1,'I2CInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_core.c']]],
  ['i2creadregister_7',['I2CReadRegister',['../i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe',1,'I2CReadRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aad5c9145daea9c25554b814bfed47756',1,'I2CReadRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['i2cscanbus_8',['I2CScanBus',['../i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b',1,'I2CScanBus(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a1b937c9865418ca9d50b16766c8ceb66',1,'I2CScanBus(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount):&#160;i2c_core.c']]],
  ['i2ctransferasynchronous_9',['I2CTransferAsynchronous',['../i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4',1,'I2CTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a363c4a8b2ee1e16d8a6aaf35b0e67722',1,'I2CTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context):&#160;i2c_core.c']]],
  ['i2ctransfersynchronous_10',['I2CTransferSynchronous',['../i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06',1,'I2CTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#ae74bb3af98d6a79ba5774f9c3a480ca7',1,'I2CTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['i2ctransfertimerexpired_11',['I2CTransferTimerExpired',['../i2c__core_8c.html#aefdc06b9d942e6b102424a8a81c0be8a',1,'i2c_core.c']]],
  ['i2cuninitialize_12',['I2CUninitialize',['../i2c__core_8c.html#ae1622080dc9f8424bde67b829ee735c7',1,'I2CUninitialize(_In_ WDFDEVICE Device):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aa8f6531c5b52bc6d04ca38fbaab3c223',1,'I2CUninitialize(_In_ WDFDEVICE Device):&#160;i2c_core.c']]],
  ['i2cwriteregister_13',['I2CWriteRegister',['../i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb',1,'I2CWriteRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aa4838a1894b94b950fc4a7e73624d7ed',1,'I2CWriteRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['if_14',['if',['../struct__GPIO__PIN__CONTEXT.html#acd3cbb2291f76aabd30090072d539050',1,'_GPIO_PIN_CONTEXT::if()'],['../gpio__core_8c.html#a977bbe3e09136dd34381e7f1b889a570',1,'if(pinContext==NULL):&#160;gpio_core.c'],['../gpio__core_8c.html#a495a75edfacf86c27c05ce15ada3509e',1,'if(gpioManager==NULL):&#160;gpio_core.c'],['../gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;gpio_core.c'],['../gpio__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;gpio_device.c'],['../gpio__device_8c.html#ad94ec7eba667568bcd9afe3483282304',1,'if(GpioConfig-&gt;DebounceTime &gt; 0):&#160;gpio_device.c'],['../gpio__device_8c.html#a9eef4336d2e5308042aaa49f8966c7fa',1,'if(GpioConfig-&gt;EnableInterrupt):&#160;gpio_device.c'],['../gpio__device_8c.html#a14190d6765d5c660291c1d6839cc5428',1,'if(NT_SUCCESS(status)):&#160;gpio_device.c'],['../gpio__device_8c.html#aac20ced732c198d7484287c9eb39e413',1,'if(deviceContext-&gt;Config.EnableInterrupt):&#160;gpio_device.c'],['../i2c__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b',1,'if(deviceContext==NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;i2c_device.c'],['../i2c__device_8c.html#adfac0a96ec8249c69bd820670db7f2cd',1,'if(I2cConfig-&gt;InterruptEnabled):&#160;i2c_device.c'],['../i2c__device_8c.html#a161904443c5f73d8654306b3fa8d29bb',1,'if(deviceContext-&gt;Interrupt !=NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#ab29d05a3528131be0d35fe785e85590f',1,'if(deviceContext-&gt;HalHandle !=NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#a6957e0e0f326c7986a222f431530dc94',1,'if(deviceContext-&gt;I2cConfig !=NULL):&#160;i2c_device.c'],['../spi__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b',1,'if(deviceContext==NULL):&#160;spi_device.c'],['../spi__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;spi_device.c'],['../spi__device_8c.html#ab29d05a3528131be0d35fe785e85590f',1,'if(deviceContext-&gt;HalHandle !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#a4b5c92f0859e4be1ead5d71edc903427',1,'if(NT_SUCCESS(status) &amp;&amp;BytesRead !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#afff80b1a0000ef578da0277667a994ff',1,'if(writeBuffer==NULL):&#160;spi_device.c'],['../spi__device_8c.html#a164e77dd43f69d29ea926ae0ec42969b',1,'if(NT_SUCCESS(status) &amp;&amp;BytesWritten !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#ac40f83943701ccbf4235e0c238583dfb',1,'if(deviceContext-&gt;SpiConfig !=NULL):&#160;spi_device.c']]],
  ['initializedevice_15',['InitializeDevice',['../driver__core_8h.html#afc516515541c17e0ba39b5ac97a01636',1,'driver_core.h']]]
];
