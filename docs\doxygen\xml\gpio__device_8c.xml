<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="gpio__device_8c" kind="file" language="C++">
    <compoundname>gpio_device.c</compoundname>
    <includes refid="gpio__device_8h" local="yes">../../../include/hal/devices/gpio_device.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="7">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="8">
        <label>../../../include/core/log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>../bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../../include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="gpio__device_8c_1a6acc285bc10925e95287d1821e20f2f7" prot="public" static="no">
        <name>GPIO_DEVICE_POOL_TAG</name>
        <initializer>&apos;DOPG&apos;</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="16" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="16" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="gpio__device_8c_1af02ced4aae3ad21bbe67ffd9742cda5b" prot="public" static="no" mutable="no">
        <type>PVOID</type>
        <definition>PVOID CallbackContext</definition>
        <argsstring></argsstring>
        <name>CallbackContext</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="25" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="25" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kindref="member">GPIO_DEVICE_CONFIG</ref></type>
        <definition>GPIO_DEVICE_CONFIG Config</definition>
        <argsstring></argsstring>
        <name>Config</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="23" column="24" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="23" bodyend="-1"/>
        <referencedby refid="CoreManager_8h_1ab30fe932990f8d1244364662da1011d4">CoreManagerInitialize</referencedby>
        <referencedby refid="hal__interface_8h_1a77d5908ab2a098166969c80028859e28">HalDeviceOpen</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a351f54e1c120ac29191cd55267d05e00" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref></type>
        <definition>deviceContext CurrentState</definition>
        <argsstring></argsstring>
        <name>CurrentState</name>
        <initializer>= <ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kindref="member">GpioStateUnknown</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="24" column="23" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="24" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a130124259198fee8d71747e31a529e96" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref></type>
        <definition>pinConfig Direction</definition>
        <argsstring></argsstring>
        <name>Direction</name>
        <initializer>= GpioConfig-&gt;Direction</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="213" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="213" bodyend="-1"/>
        <referencedby refid="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4">GPIOSetDirection</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a0544c3fe466e421738dae463968b70ba" prot="public" static="no" mutable="no">
        <type></type>
        <definition>else</definition>
        <argsstring></argsstring>
        <name>else</name>
        <initializer>{
        
            <ref refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kindref="member">HandleStateChange</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>, PinValue)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="144" column="6" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="144" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1addddad69ceb0e8964c3018cd853b60d6" prot="public" static="no" mutable="no">
        <type>EVT_WDF_TIMER</type>
        <definition>EVT_WDF_TIMER EvtDebounceTimerFunc</definition>
        <argsstring></argsstring>
        <name>EvtDebounceTimerFunc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="34" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="34" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1af4f2a770562d41e27d370140ab383393" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
        <definition>deviceContext LastPinValue</definition>
        <argsstring></argsstring>
        <name>LastPinValue</name>
        <initializer>= PinValue</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="147" column="26" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="147" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref></type>
        <definition>pinConfig PinNumber</definition>
        <argsstring></argsstring>
        <name>PinNumber</name>
        <initializer>= GpioConfig-&gt;PinNumber</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="212" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="212" bodyend="-1"/>
        <referencedby refid="gpio__device_8c_1a3d13bafdbad06cbdfb7f43c4c1b098d4" compoundref="gpio__device_8c" startline="130" endline="144">GpioDeviceInterruptCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752">GpioDeviceInterruptCallback</referencedby>
        <referencedby refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4">GPIODisableInterrupt</referencedby>
        <referencedby refid="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48">GPIOEnableInterrupt</referencedby>
        <referencedby refid="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb">GPIOGetValue</referencedby>
        <referencedby refid="kmdf__gpio_8h_1ade1cb652014fc4a3984567ff49900d81">GPIOPulse</referencedby>
        <referencedby refid="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4">GPIOSetDirection</referencedby>
        <referencedby refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d">GPIOSetValue</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" prot="public" static="no" mutable="no">
        <type>BOOLEAN</type>
        <definition>BOOLEAN pinValue</definition>
        <argsstring></argsstring>
        <name>pinValue</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="245" column="17" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="245" bodyend="-1"/>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" compoundref="gpio__device_8c" startline="248" endline="251">if</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1ad6e10a2dc0aebdabca7a9c76612727a3" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref></type>
        <definition>pinConfig Polarity</definition>
        <argsstring></argsstring>
        <name>Polarity</name>
        <initializer>= GpioConfig-&gt;Polarity</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="214" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="214" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a9611b3a00430a86619b5923de30f9fdb" prot="public" static="no" mutable="no">
        <type></type>
        <definition>return status</definition>
        <argsstring></argsstring>
        <name>status</name>
        <initializer>= WdfObjectAllocateContext(Device, &amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>, (PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="180" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="180" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a77b4762318f24dff847f94f382cfeea6" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return STATUS_SUCCESS</definition>
        <argsstring></argsstring>
        <name>STATUS_SUCCESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="264" column="12" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="264" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>deviceContext WdfDevice</definition>
        <argsstring></argsstring>
        <name>WdfDevice</name>
        <initializer>= Device</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="22" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="22" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref> &amp;</type>
        <definition>RtlCopyMemory &amp; deviceContext</definition>
        <argsstring>(GPIO_DEVICE_CONFIG)</argsstring>
        <name>deviceContext</name>
        <param>
          <type><ref refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kindref="member">GPIO_DEVICE_CONFIG</ref></type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="192" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="192" declcolumn="5"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <referencedby refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</referencedby>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" compoundref="device__manager_8c" startline="957" endline="976">DeviceD0Entry</referencedby>
        <referencedby refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" compoundref="device__manager_8c" startline="982" endline="1001">DeviceD0Exit</referencedby>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" compoundref="device__manager_8c" startline="258" endline="272">DeviceInterruptDisable</referencedby>
        <referencedby refid="device__manager_8c_1a7a7512003b2efe8ec1d3412af1b7c0b3" compoundref="device__manager_8c" startline="215" endline="231">DeviceInterruptDpc</referencedby>
        <referencedby refid="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" compoundref="device__manager_8c" startline="237" endline="252">DeviceInterruptEnable</referencedby>
        <referencedby refid="device__manager_8c_1a27d4fa34ed290d1ce4e0cba9b619de2b" compoundref="device__manager_8c" startline="186" endline="209">DeviceInterruptIsr</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" compoundref="device__manager_8c" startline="1113" endline="1145">DeviceManager_EvtDeviceAdd</referencedby>
        <referencedby refid="device__manager_8c_1ae47038053b18948b8fc9579f5a785cf4" compoundref="device__manager_8c" startline="1102" endline="1110">DeviceManager_EvtDeviceContextCleanup</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" compoundref="device__manager_8c" startline="876" endline="951">DeviceReleaseHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" compoundref="gpio__device_8c" startline="102" endline="123">EvtDebounceTimerFunc</referencedby>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" compoundref="gpio__device_8c" startline="271" endline="286">GpioDeviceCleanup</referencedby>
        <referencedby refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" compoundref="gpio__device_8c" startline="367" endline="385">GpioDeviceGetState</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a3d13bafdbad06cbdfb7f43c4c1b098d4" compoundref="gpio__device_8c" startline="130" endline="144">GpioDeviceInterruptCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" compoundref="gpio__device_8c" startline="391" endline="413">GpioDeviceRegisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" compoundref="gpio__device_8c" startline="419" endline="439">GpioDeviceUnregisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4">GPIOUninitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" compoundref="i2c__device_8c" startline="157" endline="186">I2cDeviceCleanup</referencedby>
        <referencedby refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" compoundref="i2c__device_8c" startline="362" endline="372">I2cDeviceGetStatistics</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" compoundref="i2c__device_8c" startline="314" endline="326">I2cDeviceTransfer</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1aefdc06b9d942e6b102424a8a81c0be8a" compoundref="i2c__core_8c" startline="437" endline="447">I2CTransferTimerExpired</referencedby>
        <referencedby refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" compoundref="i2c__core_8c" startline="111" endline="132">I2CUninitialize</referencedby>
        <referencedby refid="gpio__device_8c_1aac20ced732c198d7484287c9eb39e413" compoundref="gpio__device_8c" startline="289" endline="291">if</referencedby>
        <referencedby refid="i2c__device_8c_1ab29d05a3528131be0d35fe785e85590f" compoundref="i2c__device_8c" startline="144" endline="147">if</referencedby>
        <referencedby refid="i2c__device_8c_1a6957e0e0f326c7986a222f431530dc94" compoundref="i2c__device_8c" startline="385" endline="388">if</referencedby>
        <referencedby refid="i2c__device_8c_1a161904443c5f73d8654306b3fa8d29bb" compoundref="i2c__device_8c" startline="139" endline="142">if</referencedby>
        <referencedby refid="spi__device_8c_1ac40f83943701ccbf4235e0c238583dfb" compoundref="spi__device_8c" startline="321" endline="324">if</referencedby>
        <referencedby refid="i2c__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" compoundref="i2c__device_8c" startline="58" endline="61">if</referencedby>
        <referencedby refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" compoundref="gpio__device_8c" startline="195" endline="208">if</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
        <referencedby refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" compoundref="gpio__device_8c" startline="248" endline="251">if</referencedby>
        <referencedby refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</referencedby>
        <referencedby refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5">RtlCopyMemory</referencedby>
        <referencedby refid="gpio__device_8c_1a714d770abd4d38214bae1604c752369a">RtlZeroMemory</referencedby>
        <referencedby refid="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" compoundref="spi__device_8c" startline="113" endline="142">SpiDeviceCleanup</referencedby>
        <referencedby refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" compoundref="spi__device_8c" startline="298" endline="308">SpiDeviceGetStatistics</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" compoundref="spi__device_8c" startline="148" endline="160">SpiDeviceTransfer</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
        <referencedby refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" compoundref="spi__core_8c" startline="138" endline="167">SPIUninitialize</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID EvtDebounceTimerFunc</definition>
        <argsstring>(_In_ WDFTIMER Timer)</argsstring>
        <name>EvtDebounceTimerFunc</name>
        <param>
          <type>_In_ WDFTIMER</type>
          <declname>Timer</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="102" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="102" bodyend="123"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" compoundref="gpio__device_8c" startline="46" endline="97">HandleStateChange</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID GpioDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>GpioDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceCleanup - 濞撳懐鎮奊PIO鐠佹儳顦挧鍕爱 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="271" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="271" bodyend="286"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceGetState</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PGPIO_DEVICE_STATE State)</argsstring>
        <name>GpioDeviceGetState</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ PGPIO_DEVICE_STATE</type>
          <declname>State</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceGetState - 閼惧嘲褰嘒PIO鐠佹儳顦悩鑸碘偓? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="367" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="367" bodyend="385"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PGPIO_DEVICE_CONFIG GpioConfig)</argsstring>
        <name>GpioDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kindref="member">PGPIO_DEVICE_CONFIG</ref></type>
          <declname>GpioConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceInitialize - 閸掓繂顫愰崠鏈慞IO鐠佹儳顦? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="160" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="160" bodyend="177"/>
        <references refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" compoundref="gpio__core_8c" startline="176">attributes</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54">pinConfig</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a3d13bafdbad06cbdfb7f43c4c1b098d4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID GpioDeviceInterruptCallback</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</argsstring>
        <name>GpioDeviceInterruptCallback</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_In_ BOOLEAN</type>
          <declname>PinValue</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="130" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="130" bodyend="144"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID GpioDeviceInterruptCallback</definition>
        <argsstring>(WDFDEVICE Device, ULONG PinNumber, BOOLEAN PinValue, PVOID Context)</argsstring>
        <name>GpioDeviceInterruptCallback</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>BOOLEAN</type>
          <declname>PinValue</declname>
        </param>
        <param>
          <type>PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="35" column="6" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="35" declcolumn="6"/>
        <references refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752">GpioDeviceInterruptCallback</references>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <referencedby refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752">GpioDeviceInterruptCallback</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDevicePulse</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PulseDurationMs)</argsstring>
        <name>GpioDevicePulse</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PulseDurationMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDevicePulse - 鐠佹儳顦崣鎴濆毉閼村鍟块懘澶婂暱 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="445" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="445" bodyend="491"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" compoundref="kmdf__gpio_8h" startline="16">GpioDirectionOut</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" compoundref="gpio__device_8h" startline="31">GpioStateOff</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" compoundref="gpio__device_8h" startline="30">GpioStateOn</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceRegisterCallback</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT_CALLBACK Callback, _In_opt_ PVOID Context)</argsstring>
        <name>GpioDeviceRegisterCallback</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kindref="member">GPIO_DEVICE_EVENT_CALLBACK</ref></type>
          <declname>Callback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceRegisterCallback - 濞夈劌鍞紾PIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="391" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="391" bodyend="413"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceSetState</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_STATE State)</argsstring>
        <name>GpioDeviceSetState</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref></type>
          <declname>State</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceSetState - 鐠佸墽鐤咷PIO鐠佹儳顦悩鑸碘偓? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="302" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="302" bodyend="340"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" compoundref="kmdf__gpio_8h" startline="16">GpioDirectionOut</references>
        <references refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" compoundref="kmdf__gpio_8h" startline="22">GpioPolarityActiveHigh</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" compoundref="gpio__device_8h" startline="32">GpioStateError</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" compoundref="gpio__device_8h" startline="31">GpioStateOff</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" compoundref="gpio__device_8h" startline="30">GpioStateOn</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" compoundref="gpio__device_8h" startline="29">GpioStateUnknown</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" compoundref="gpio__device_8c" startline="245">pinValue</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceUnregisterCallback</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>GpioDeviceUnregisterCallback</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceUnregisterCallback - 閸欐牗绉峰▔銊ュ斀GPIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="419" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="419" bodyend="439"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>GPIOUninitialize</definition>
        <argsstring>(Device, deviceContext-&gt;Config.PinNumber)</argsstring>
        <name>GPIOUninitialize</name>
        <param>
          <type>Device</type>
        </param>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="294" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="294" declcolumn="5"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>STATIC VOID</type>
        <definition>STATIC VOID HandleStateChange</definition>
        <argsstring>(_In_ PGPIO_DEVICE_CONTEXT DeviceContext, _In_ BOOLEAN PinValue)</argsstring>
        <name>HandleStateChange</name>
        <param>
          <type>_In_ PGPIO_DEVICE_CONTEXT</type>
          <declname>DeviceContext</declname>
        </param>
        <param>
          <type>_In_ BOOLEAN</type>
          <declname>PinValue</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="46" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="46" bodyend="97"/>
        <references refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" compoundref="kmdf__gpio_8h" startline="21">GpioPolarityActiveLow</references>
        <references refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" compoundref="gpio__device_8h" startline="21">GpioTypeGeneric</references>
        <references refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" compoundref="gpio__device_8h" startline="23">GpioTypeLed</references>
        <references refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" compoundref="gpio__device_8c" startline="46" endline="97">HandleStateChange</references>
        <referencedby refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" compoundref="gpio__device_8c" startline="102" endline="123">EvtDebounceTimerFunc</referencedby>
        <referencedby refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" compoundref="gpio__device_8c" startline="46" endline="97">HandleStateChange</referencedby>
        <referencedby refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" compoundref="gpio__device_8c" startline="248" endline="251">if</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(!NT_SUCCESS(status))</argsstring>
        <name>if</name>
        <param>
          <type>!</type>
          <declname>NT_SUCCESS</declname>
          <array>status</array>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="181" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="181" bodyend="184"/>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1aac20ced732c198d7484287c9eb39e413" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;Config.EnableInterrupt)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.</type>
          <declname>EnableInterrupt</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="289" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="289" bodyend="291"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4">GPIODisableInterrupt</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(GpioConfig-&gt;DebounceTime &gt; 0)</argsstring>
        <name>if</name>
        <param>
          <type>GpioConfig-&gt;</type>
          <declname>DebounceTime</declname>
        </param>
        <param>
          <type>0</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="195" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="195" bodyend="208"/>
        <references refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" compoundref="gpio__core_8c" startline="176">attributes</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a9eef4336d2e5308042aaa49f8966c7fa" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(GpioConfig-&gt;EnableInterrupt)</argsstring>
        <name>if</name>
        <param>
          <type>GpioConfig-&gt;</type>
          <declname>EnableInterrupt</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="217" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="217" bodyend="219"/>
        <references refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54">pinConfig</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(NT_SUCCESS(status))</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="248" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="248" bodyend="251"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" compoundref="gpio__device_8c" startline="46" endline="97">HandleStateChange</references>
        <references refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" compoundref="gpio__device_8c" startline="245">pinValue</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1ac410021632e38928f5eb4b3bb6939ab9" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;GPIO device initialized successfully, type=%d, pin=%d&quot;, GpioConfig-&gt;DeviceType, GpioConfig-&gt;PinNumber)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;GPIO device initialized</type>
          <declname>successfully</declname>
        </param>
        <param>
          <type>type</type>
          <defval>%d</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="261" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="261" declcolumn="5"/>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a3156531cfce23bfe597c3aaad2f23aad" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;GPIO device resources cleaned up&quot;)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;GPIO device resources cleaned up&quot;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="296" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="296" declcolumn="5"/>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a1fbae9652e19914386dbb4fc4848b63d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogWarning</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;Failed to get initial GPIO pin value&quot;)</argsstring>
        <name>LogWarning</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;Failed to get initial GPIO pin value&quot;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="253" column="13" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="253" declcolumn="13"/>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref> &amp;</type>
        <definition>RtlZeroMemory &amp; pinConfig</definition>
        <argsstring>(GPIO_PIN_CONFIG)</argsstring>
        <name>pinConfig</name>
        <param>
          <type><ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref></type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="211" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="211" declcolumn="5"/>
        <references refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54">pinConfig</references>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a9eef4336d2e5308042aaa49f8966c7fa" compoundref="gpio__device_8c" startline="217" endline="219">if</referencedby>
        <referencedby refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54">pinConfig</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8c_1a714d770abd4d38214bae1604c752369a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>RtlZeroMemory</definition>
        <argsstring>(deviceContext, sizeof(GPIO_DEVICE_CONTEXT))</argsstring>
        <name>RtlZeroMemory</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
        </param>
        <param>
          <type>sizeof(GPIO_DEVICE_CONTEXT)</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c" line="187" column="5" declfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" declline="187" declcolumn="5"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" compoundref="gpio__device_8h" startline="29">GpioStateUnknown</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>gpio_device.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>GPIO鐠佹儳顦す鍗炲З閹恒儱褰涚€圭偟骞?</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>閹绘劒绶礕PIO鐠佹儳顦敍鍫熷瘻闁筋喓鈧俯ED缁涘绱氶惃鍕埠娑撯偓閹垮秳缍旈幒銉ュ經</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="gpio__device_8h" kindref="compound">../../../include/hal/devices/gpio_device.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鐢悂鍣虹€规矮绠?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="gpio__device_8c_1a6acc285bc10925e95287d1821e20f2f7" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>GPIO_DEVICE_POOL_TAG<sp/>&apos;DOPG&apos;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鐠佹儳顦稉濠佺瑓閺傚洨绮ㄩ弸?//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO鐠佹儳顦す鍗炲З娑撳﹣绗呴弬鍥╃波閺?typedef<sp/>struct<sp/>_GPIO_DEVICE_CONTEXT<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="gpio__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF鐠佹儳顦€电钖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kindref="member">GPIO_DEVICE_CONFIG</ref><sp/><ref refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" kindref="member">Config</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹儳顦柊宥囩枂娣団剝浼?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="gpio__device_8c_1a351f54e1c120ac29191cd55267d05e00" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/><ref refid="gpio__device_8c_1a351f54e1c120ac29191cd55267d05e00" kindref="member">CurrentState</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>瑜版挸澧犵拋鎯ь槵閻樿埖鈧?<sp/><sp/><sp/><sp/>GPIO_DEVICE_EVENT_CALLBACK<sp/>EventCallback;<sp/>//<sp/>娴滃娆㈤崶鐐剁殶閸戣姤鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="gpio__device_8c_1af02ced4aae3ad21bbe67ffd9742cda5b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="gpio__device_8c_1af02ced4aae3ad21bbe67ffd9742cda5b" kindref="member">CallbackContext</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸ョ偠鐨熸稉濠佺瑓閺?<sp/><sp/><sp/><sp/>WDFTIMER<sp/>DebounceTimer;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>閸樼粯濮堢€规碍妞傞崳?<sp/><sp/><sp/><sp/>BOOLEAN<sp/>LastPinValue;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>娑撳﹣绔村▎陇顕伴崣鏍畱瀵洝鍓奸崐?<sp/><sp/><sp/><sp/>BOOLEAN<sp/>PendingPinValue;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>缁涘绶熺涵顔款吇閻ㄥ嫬绱╅懘姘偓?}<sp/>GPIO_DEVICE_CONTEXT,<sp/>*PGPIO_DEVICE_CONTEXT;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鐎规矮绠熸稉濠佺瑓閺傚洩顔栭梻顔兼珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal">WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(GPIO_DEVICE_CONTEXT,<sp/>GetGpioDeviceContext)</highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="comment">//<sp/>閸撳秴鎮滄竟鐗堟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="gpio__device_8c_1addddad69ceb0e8964c3018cd853b60d6" refkind="member"><highlight class="normal">EVT_WDF_TIMER<sp/><ref refid="gpio__device_8c_1addddad69ceb0e8964c3018cd853b60d6" kindref="member">EvtDebounceTimerFunc</ref>;</highlight></codeline>
<codeline lineno="35" refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" refkind="member"><highlight class="normal">VOID<sp/><ref refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" kindref="member">GpioDeviceInterruptCallback</ref>(<ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,<sp/>BOOLEAN<sp/>PinValue,<sp/>PVOID<sp/>Context);</highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鏉堝懎濮崙鑺ユ殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="42"><highlight class="comment"><sp/>*<sp/>HandleStateChange<sp/>-<sp/>婢跺嫮鎮婇悩鑸碘偓浣稿綁閸栨牭绱濈憴锕€褰傞崶鐐剁殶</highlight></codeline>
<codeline lineno="43"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal">STATIC</highlight></codeline>
<codeline lineno="45"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="46" refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kindref="member">HandleStateChange</ref>(</highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PGPIO_DEVICE_CONTEXT<sp/>DeviceContext,</highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>BOOLEAN<sp/>PinValue</highlight></codeline>
<codeline lineno="49"><highlight class="normal">)</highlight></codeline>
<codeline lineno="50"><highlight class="normal">{</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/>newState;</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" kindref="member">GPIO_DEVICE_EVENT</ref><sp/>event;</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嶈宓佺拋鎯ь槵缁鐎烽崪灞界穿閼存艾鈧偐鈥樼€规碍鏌婇悩鑸碘偓浣告嫲娴滃娆㈢猾璇茬€?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(DeviceContext-&gt;Config.DeviceType)<sp/>{</highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/>case<sp/>GpioTypeButton:</highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹稿鎸抽敍姘壌閹诡喗鐎幀褏鈥樼€规碍妲搁崥锔藉瘻娑?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>((DeviceContext-&gt;Config.Polarity<sp/>==<sp/>GpioPolarityActiveHigh<sp/>&amp;&amp;<sp/>PinValue)<sp/>||</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(DeviceContext-&gt;Config.Polarity<sp/>==<sp/>GpioPolarityActiveLow<sp/>&amp;&amp;<sp/>!PinValue))<sp/>{</highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>newState<sp/>=<sp/>GpioStateOn;</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event<sp/>=<sp/>GpioEventPress;</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>newState<sp/>=<sp/>GpioStateOff;</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event<sp/>=<sp/>GpioEventRelease;</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" kindref="member">GpioTypeLed</ref>:</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" kindref="member">GpioTypeGeneric</ref>:</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>LED閸滃苯鍙炬禒鏍啎婢跺浄绱伴弽瑙勫祦閺嬩焦鈧呪€樼€规艾绱戦崗宕囧Ц閹?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>((DeviceContext-&gt;Config.Polarity<sp/>==<sp/>GpioPolarityActiveHigh<sp/>&amp;&amp;<sp/>PinValue)<sp/>||</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(DeviceContext-&gt;Config.Polarity<sp/>==<sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" kindref="member">GpioPolarityActiveLow</ref><sp/>&amp;&amp;<sp/>!PinValue))<sp/>{</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>newState<sp/>=<sp/>GpioStateOn;</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event<sp/>=<sp/>GpioEventOn;</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>newState<sp/>=<sp/>GpioStateOff;</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event<sp/>=<sp/>GpioEventOff;</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐悩鑸碘偓浣稿絺閻㈢喎褰夐崠鏍电礉閺囧瓨鏌婇悩鑸碘偓浣歌嫙閸ョ偠鐨?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(DeviceContext-&gt;CurrentState<sp/>!=<sp/>newState)<sp/>{</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LogInfo(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>state<sp/>changed<sp/>from<sp/>%d<sp/>to<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceContext-&gt;CurrentState,<sp/>newState);</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceContext-&gt;CurrentState<sp/>=<sp/>newState;</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夊▔銊ュ斀娴滃棗娲栫拫鍐跨礉鐠嬪啰鏁ら崶鐐剁殶閸戣姤鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(DeviceContext-&gt;EventCallback<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceContext-&gt;EventCallback(</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceContext-&gt;WdfDevice,</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event,</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>PinValue,</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceContext-&gt;CallbackContext);</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="97"><highlight class="normal">}</highlight></codeline>
<codeline lineno="98"><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="100"><highlight class="comment"><sp/>*<sp/>EvtDebounceTimerFunc<sp/>-<sp/>閸樼粯濮堢€规碍妞傞崳銊ユ礀鐠嬪啫鍤遍弫?<sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="101"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="102" refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1addddad69ceb0e8964c3018cd853b60d6" kindref="member">EvtDebounceTimerFunc</ref>(</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFTIMER<sp/>Timer</highlight></codeline>
<codeline lineno="104"><highlight class="normal">)</highlight></codeline>
<codeline lineno="105"><highlight class="normal">{</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device;</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>currentValue;</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>device<sp/>=<sp/>WdfTimerGetParentObject(Timer);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(device);</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸愬秵顐肩拠璇插絿瑜版挸澧犻崐纭风礉绾喛顓荤粙鍐茬暰閹?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>GPIOGetValue(device,<sp/>deviceContext-&gt;Config.PinNumber,<sp/>&amp;currentValue);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>read<sp/>GPIO<sp/>value<sp/>in<sp/>debounce<sp/>timer&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夎ぐ鎾冲閸婇棿绗岀粵澶婄窡绾喛顓婚惃鍕偓鑲╂祲閸氬矉绱濋崚娆捫曢崣鎴犲Ц閹焦鏁奸崣?<sp/><sp/><sp/><sp/>if<sp/>(currentValue<sp/>==<sp/>deviceContext-&gt;PendingPinValue)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kindref="member">HandleStateChange</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/>currentValue);</highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;LastPinValue<sp/>=<sp/>currentValue;</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="124"><highlight class="normal">}</highlight></codeline>
<codeline lineno="125"><highlight class="normal"></highlight></codeline>
<codeline lineno="126"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="127"><highlight class="comment"><sp/>*<sp/>GpioDeviceInterruptCallback<sp/>-<sp/>GPIO娑擃厽鏌囬崶鐐剁殶閸戣姤鏆?</highlight></codeline>
<codeline lineno="128"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="129"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="130" refid="gpio__device_8c_1a3d13bafdbad06cbdfb7f43c4c1b098d4" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" kindref="member">GpioDeviceInterruptCallback</ref>(</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>BOOLEAN<sp/>PinValue,</highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="135"><highlight class="normal">)</highlight></codeline>
<codeline lineno="136"><highlight class="normal">{</highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐崥顖滄暏娴滃棗骞撻幎?<sp/><sp/><sp/><sp/>if<sp/>(deviceContext-&gt;Config.DebounceTime<sp/>&gt;<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娣囨繂鐡ㄨぐ鎾冲閸婅偐鏁ゆ禍搴″箵閹舵牗鐦潏?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceContext-&gt;PendingPinValue<sp/>=<sp/>PinValue;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="142"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸氼垰濮╅崢缁樺鐎规碍妞傞崳?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStart(deviceContext-&gt;DebounceTimer,<sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_REL_TIMEOUT_IN_MS(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.DebounceTime));</highlight></codeline>
<codeline lineno="144" refid="gpio__device_8c_1a0544c3fe466e421738dae463968b70ba" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞屸剝婀侀崥顖滄暏閸樼粯濮堥敍宀€娲块幒銉ヮ槱閻?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(deviceContext-&gt;LastPinValue<sp/>!=<sp/>PinValue)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="146"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kindref="member">HandleStateChange</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/>PinValue);</highlight></codeline>
<codeline lineno="147" refid="gpio__device_8c_1af4f2a770562d41e27d370140ab383393" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;LastPinValue<sp/>=<sp/>PinValue;</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="150"><highlight class="normal">}</highlight></codeline>
<codeline lineno="151"><highlight class="normal"></highlight></codeline>
<codeline lineno="152"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="153"><highlight class="normal"></highlight><highlight class="comment">//<sp/>閹恒儱褰涢崙鑺ユ殶鐎圭偟骞?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="154"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="155"><highlight class="normal"></highlight></codeline>
<codeline lineno="159"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="160" refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" kindref="member">GpioDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kindref="member">PGPIO_DEVICE_CONFIG</ref><sp/>GpioConfig</highlight></codeline>
<codeline lineno="163"><highlight class="normal">)</highlight></codeline>
<codeline lineno="164"><highlight class="normal">{</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>;</highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG<sp/>timerConfig;</highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref><sp/><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>;</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>GPIO<sp/>device<sp/>type<sp/>%d,<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;DeviceType,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="174"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>GpioConfig<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掑棝鍘ら崪灞藉灥婵瀵茬拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&amp;attributes,<sp/>GPIO_DEVICE_CONTEXT);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="180" refid="gpio__device_8c_1a9611b3a00430a86619b5923de30f9fdb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfObjectAllocateContext(Device,<sp/>&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,<sp/>(PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>);</highlight></codeline>
<codeline lineno="181" refid="gpio__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="182"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>allocate<sp/>GPIO<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="183"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="185"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="186"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍︾瑐娑撳鏋?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="187" refid="gpio__device_8c_1a714d770abd4d38214bae1604c752369a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a714d770abd4d38214bae1604c752369a" kindref="member">RtlZeroMemory</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(GPIO_DEVICE_CONTEXT));</highlight></codeline>
<codeline lineno="188"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="189"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentState<sp/>=<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kindref="member">GpioStateUnknown</ref>;</highlight></codeline>
<codeline lineno="190"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="191"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢跺秴鍩楅柊宥囩枂娣団剝浼?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="192" refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config,<sp/>GpioConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kindref="member">GPIO_DEVICE_CONFIG</ref>));</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐崥顖滄暏娴滃棗骞撻幎鏍电礉閸掓稑缂撴稉鈧稉顏勫箵閹舵牕鐣鹃弮璺烘珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="195" refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(GpioConfig-&gt;DebounceTime<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂撶€规碍妞傞崳銊ヮ嚠鐠?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG_INIT(&amp;timerConfig,<sp/>EvtDebounceTimerFunc);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>timerConfig.Period<sp/>=<sp/>0;<sp/></highlight><highlight class="comment">//<sp/>娑撯偓濞嗏剝鈧冪暰閺冭泛娅?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>timerConfig.AutomaticSerialization<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="200"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>);</highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfTimerCreate(&amp;timerConfig,<sp/>&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DebounceTimer);</highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>debounce<sp/>timer&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="210"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸戝棗顦珿PIO瀵洝鍓奸柊宥囩枂</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="211" refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a714d770abd4d38214bae1604c752369a" kindref="member">RtlZeroMemory</ref>(&amp;<ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref>));</highlight></codeline>
<codeline lineno="212" refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>.PinNumber<sp/>=<sp/>GpioConfig-&gt;PinNumber;</highlight></codeline>
<codeline lineno="213" refid="gpio__device_8c_1a130124259198fee8d71747e31a529e96" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>.Direction<sp/>=<sp/>GpioConfig-&gt;Direction;</highlight></codeline>
<codeline lineno="214" refid="gpio__device_8c_1ad6e10a2dc0aebdabca7a9c76612727a3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>.Polarity<sp/>=<sp/>GpioConfig-&gt;Polarity;</highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐崥顖滄暏娴滃棔鑵戦弬顓ㄧ礉闁板秶鐤嗘稉顓熸焽鐠佸墽鐤?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="217" refid="gpio__device_8c_1a9eef4336d2e5308042aaa49f8966c7fa" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(GpioConfig-&gt;EnableInterrupt)<sp/>{</highlight></codeline>
<codeline lineno="218"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>.InterruptType<sp/>=<sp/>GpioConfig-&gt;InterruptType;</highlight></codeline>
<codeline lineno="219"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="221"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏈慞IO瀵洝鍓?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="222"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" kindref="member">GPIOInitialize</ref>(Device,<sp/>&amp;<ref refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kindref="member">pinConfig</ref>);</highlight></codeline>
<codeline lineno="223"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="224"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>initialize<sp/>GPIO<sp/>pin&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐崥顖滄暏娴滃棔鑵戦弬顓ㄧ礉濞夈劌鍞芥稉顓熸焽閸ョ偠鐨?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(GpioConfig-&gt;EnableInterrupt)<sp/>{</highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48" kindref="member">GPIOEnableInterrupt</ref>(</highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Device,</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber,</highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;InterruptType,</highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" kindref="member">GpioDeviceInterruptCallback</ref>,</highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>);</highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="237"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>enable<sp/>GPIO<sp/>interrupt&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4" kindref="member">GPIOUninitialize</ref>(Device,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="240"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="241"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="244"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐弰顖濈翻閸忋儱绱╅懘姘剧礉閼惧嘲褰囬崚婵嗩潗閻樿埖鈧?<sp/><sp/><sp/><sp/>if<sp/>(GpioConfig-&gt;Direction<sp/>==<sp/>GpioDirectionIn)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="245" refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>;</highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="247"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb" kindref="member">GPIOGetValue</ref>(Device,<sp/>GpioConfig-&gt;PinNumber,<sp/>&amp;<ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>);</highlight></codeline>
<codeline lineno="248" refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="249"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;LastPinValue<sp/>=<sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>;</highlight></codeline>
<codeline lineno="250"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kindref="member">HandleStateChange</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>);</highlight></codeline>
<codeline lineno="251"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="252"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentState<sp/>=<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kindref="member">GpioStateUnknown</ref>;</highlight></codeline>
<codeline lineno="253" refid="gpio__device_8c_1a1fbae9652e19914386dbb4fc4848b63d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>initial<sp/>GPIO<sp/>pin<sp/>value&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="254"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="255"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="256"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐎甸€涚艾鏉堟挸鍤鏇″壖閿涘本鐗撮幑顔界€幀褍鍨垫慨瀣娑撶瘺FF閻樿埖鈧?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>BOOLEAN<sp/>initialValue<sp/>=<sp/>(GpioConfig-&gt;Polarity<sp/>==<sp/>GpioPolarityActiveLow)<sp/>?<sp/>TRUE<sp/>:<sp/>FALSE;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" kindref="member">GPIOSetValue</ref>(Device,<sp/>GpioConfig-&gt;PinNumber,<sp/>initialValue);</highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentState<sp/>=<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" kindref="member">GpioStateOff</ref>;</highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="261" refid="gpio__device_8c_1ac410021632e38928f5eb4b3bb6939ab9" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>initialized<sp/>successfully,<sp/>type=%d,<sp/>pin=%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;DeviceType,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="264" refid="gpio__device_8c_1a77b4762318f24dff847f94f382cfeea6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="265"><highlight class="normal">}</highlight></codeline>
<codeline lineno="266"><highlight class="normal"></highlight></codeline>
<codeline lineno="270"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="271" refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" kindref="member">GpioDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="272"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="273"><highlight class="normal">)</highlight></codeline>
<codeline lineno="274"><highlight class="normal">{</highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>GPIO<sp/>device<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="278"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="279"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="280"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>context<sp/>is<sp/>NULL,<sp/>nothing<sp/>to<sp/>cleanup&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="281"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="282"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="283"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="284"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸嬫粍顒涚€规碍妞傞崳顭掔礄婵″倹鐏夌€涙ê婀敍?<sp/><sp/><sp/><sp/>if<sp/>(deviceContext-&gt;DebounceTimer<sp/>!=<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="285"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStop(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DebounceTimer,<sp/>TRUE);</highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="288"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐崥顖滄暏娴滃棔鑵戦弬顓ㄧ礉缁備胶鏁ゆ稉顓熸焽</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="289" refid="gpio__device_8c_1aac20ced732c198d7484287c9eb39e413" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.EnableInterrupt)<sp/>{</highlight></codeline>
<codeline lineno="290"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4" kindref="member">GPIODisableInterrupt</ref>(Device,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.PinNumber);</highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="292"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="293"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>闁插﹥鏂丟PIO瀵洝鍓肩挧鍕爱</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="294" refid="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4" kindref="member">GPIOUninitialize</ref>(Device,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.PinNumber);</highlight></codeline>
<codeline lineno="295"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="296" refid="gpio__device_8c_1a3156531cfce23bfe597c3aaad2f23aad" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>resources<sp/>cleaned<sp/>up&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="297"><highlight class="normal">}</highlight></codeline>
<codeline lineno="298"><highlight class="normal"></highlight></codeline>
<codeline lineno="301"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="302" refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" kindref="member">GpioDeviceSetState</ref>(</highlight></codeline>
<codeline lineno="303"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/>State</highlight></codeline>
<codeline lineno="305"><highlight class="normal">)</highlight></codeline>
<codeline lineno="306"><highlight class="normal">{</highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="308"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="309"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>;</highlight></codeline>
<codeline lineno="310"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="311"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Setting<sp/>GPIO<sp/>device<sp/>to<sp/>state<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>State);</highlight></codeline>
<codeline lineno="312"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="313"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="314"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="315"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="316"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="317"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="318"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濡偓閺屻儱绱╅懘姘Ц閸氾缚璐熸潏鎾冲毉濡€崇础</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="319"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.Direction<sp/>!=<sp/><ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" kindref="member">GpioDirectionOut</ref>)<sp/>{</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_OPERATION,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight></codeline>
<codeline lineno="321"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="stringliteral">&quot;Cannot<sp/>set<sp/>state<sp/>on<sp/>input<sp/>GPIO<sp/>pin&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INVALID_DEVICE_REQUEST;</highlight></codeline>
<codeline lineno="323"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="325"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嶈宓侀惄顔界垼閻樿埖鈧礁鎷伴弸浣光偓褏鈥樼€规艾绱╅懘姘偓?<sp/><sp/><sp/><sp/>switch<sp/>(State)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="326"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kindref="member">GpioStateOn</ref>:</highlight></codeline>
<codeline lineno="327"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref><sp/>=<sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.Polarity<sp/>==<sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" kindref="member">GpioPolarityActiveHigh</ref>)<sp/>?<sp/>TRUE<sp/>:<sp/>FALSE;</highlight></codeline>
<codeline lineno="328"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="329"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="330"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" kindref="member">GpioStateOff</ref>:</highlight></codeline>
<codeline lineno="331"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref><sp/>=<sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.Polarity<sp/>==<sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" kindref="member">GpioPolarityActiveHigh</ref>)<sp/>?<sp/>FALSE<sp/>:<sp/>TRUE;</highlight></codeline>
<codeline lineno="332"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="333"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="334"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kindref="member">GpioStateUnknown</ref>:</highlight></codeline>
<codeline lineno="335"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" kindref="member">GpioStateError</ref>:</highlight></codeline>
<codeline lineno="336"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="337"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight></codeline>
<codeline lineno="338"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>state<sp/>requested:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>State);</highlight></codeline>
<codeline lineno="339"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="340"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="341"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="342"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佸墽鐤咷PIO瀵洝鍓奸崐?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>GPIOSetValue(Device,<sp/>deviceContext-&gt;Config.PinNumber,<sp/>pinValue);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="343"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="344"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentState<sp/>=<sp/>State;</highlight></codeline>
<codeline lineno="345"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>state<sp/>changed<sp/>to<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>State);</highlight></codeline>
<codeline lineno="346"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="347"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夊▔銊ュ斀娴滃棗娲栫拫鍐跨礉鐠嬪啰鏁ら崶鐐剁殶閸戣姤鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="348"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;EventCallback<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="349"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" kindref="member">GPIO_DEVICE_EVENT</ref><sp/></highlight><highlight class="keyword">event</highlight><highlight class="normal"><sp/>=<sp/>(State<sp/>==<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kindref="member">GpioStateOn</ref>)<sp/>?<sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d" kindref="member">GpioEventOn</ref><sp/>:<sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652" kindref="member">GpioEventOff</ref>;</highlight></codeline>
<codeline lineno="350"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="351"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;EventCallback(</highlight></codeline>
<codeline lineno="352"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Device,</highlight></codeline>
<codeline lineno="353"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>event,</highlight></codeline>
<codeline lineno="354"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kindref="member">pinValue</ref>,</highlight></codeline>
<codeline lineno="355"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CallbackContext);</highlight></codeline>
<codeline lineno="356"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="357"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="358"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>set<sp/>GPIO<sp/>pin<sp/>value&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="359"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="360"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="361"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="362"><highlight class="normal">}</highlight></codeline>
<codeline lineno="363"><highlight class="normal"></highlight></codeline>
<codeline lineno="366"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="367" refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" kindref="member">GpioDeviceGetState</ref>(</highlight></codeline>
<codeline lineno="368"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="369"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PGPIO_DEVICE_STATE<sp/>State</highlight></codeline>
<codeline lineno="370"><highlight class="normal">)</highlight></codeline>
<codeline lineno="371"><highlight class="normal">{</highlight></codeline>
<codeline lineno="372"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="373"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="374"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Getting<sp/>GPIO<sp/>device<sp/>state&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="375"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="376"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>State<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="377"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="378"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="379"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="380"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="381"><highlight class="normal"><sp/><sp/><sp/><sp/>*State<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentState;</highlight></codeline>
<codeline lineno="382"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Current<sp/>GPIO<sp/>device<sp/>state:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>*State);</highlight></codeline>
<codeline lineno="383"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="384"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="385"><highlight class="normal">}</highlight></codeline>
<codeline lineno="386"><highlight class="normal"></highlight></codeline>
<codeline lineno="390"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="391" refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" kindref="member">GpioDeviceRegisterCallback</ref>(</highlight></codeline>
<codeline lineno="392"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="393"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kindref="member">GPIO_DEVICE_EVENT_CALLBACK</ref><sp/>Callback,</highlight></codeline>
<codeline lineno="394"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="395"><highlight class="normal">)</highlight></codeline>
<codeline lineno="396"><highlight class="normal">{</highlight></codeline>
<codeline lineno="397"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="398"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="399"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Registering<sp/>GPIO<sp/>device<sp/>event<sp/>callback&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="400"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="401"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>Callback<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="402"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="403"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="404"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="405"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="406"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞夈劌鍞介崶鐐剁殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="407"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;EventCallback<sp/>=<sp/>Callback;</highlight></codeline>
<codeline lineno="408"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CallbackContext<sp/>=<sp/>Context;</highlight></codeline>
<codeline lineno="409"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="410"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>event<sp/>callback<sp/>registered<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="411"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="412"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="413"><highlight class="normal">}</highlight></codeline>
<codeline lineno="414"><highlight class="normal"></highlight></codeline>
<codeline lineno="418"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="419" refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" kindref="member">GpioDeviceUnregisterCallback</ref>(</highlight></codeline>
<codeline lineno="420"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="421"><highlight class="normal">)</highlight></codeline>
<codeline lineno="422"><highlight class="normal">{</highlight></codeline>
<codeline lineno="423"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="424"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="425"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Unregistering<sp/>GPIO<sp/>device<sp/>event<sp/>callback&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="426"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="427"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="428"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="429"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="430"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="431"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="432"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞撳懘娅庨崶鐐剁殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="433"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;EventCallback<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="434"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CallbackContext<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="435"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="436"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>device<sp/>event<sp/>callback<sp/>unregistered&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="437"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="438"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="439"><highlight class="normal">}</highlight></codeline>
<codeline lineno="440"><highlight class="normal"></highlight></codeline>
<codeline lineno="444"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="445" refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" refkind="member"><highlight class="normal"><ref refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" kindref="member">GpioDevicePulse</ref>(</highlight></codeline>
<codeline lineno="446"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="447"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>PulseDurationMs</highlight></codeline>
<codeline lineno="448"><highlight class="normal">)</highlight></codeline>
<codeline lineno="449"><highlight class="normal">{</highlight></codeline>
<codeline lineno="450"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="451"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_CONTEXT<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetGpioDeviceContext(Device);</highlight></codeline>
<codeline lineno="452"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/>originalState;</highlight></codeline>
<codeline lineno="453"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/>oppositeState;</highlight></codeline>
<codeline lineno="454"><highlight class="normal"><sp/><sp/><sp/><sp/>LARGE_INTEGER<sp/>delay;</highlight></codeline>
<codeline lineno="455"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="456"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Generating<sp/>GPIO<sp/>device<sp/>pulse<sp/>for<sp/>%d<sp/>ms&quot;</highlight><highlight class="normal">,<sp/>PulseDurationMs);</highlight></codeline>
<codeline lineno="457"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="458"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="459"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="460"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="461"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="462"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="463"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濡偓閺屻儱绱╅懘姘Ц閸氾缚璐熸潏鎾冲毉濡€崇础</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="464"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config.Direction<sp/>!=<sp/><ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" kindref="member">GpioDirectionOut</ref>)<sp/>{</highlight></codeline>
<codeline lineno="465"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_OPERATION,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight></codeline>
<codeline lineno="466"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="stringliteral">&quot;Cannot<sp/>generate<sp/>pulse<sp/>on<sp/>input<sp/>GPIO<sp/>pin&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="467"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INVALID_DEVICE_REQUEST;</highlight></codeline>
<codeline lineno="468"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="469"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="470"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娣囨繂鐡ㄩ崢鐔奉潗閻樿埖鈧?<sp/><sp/><sp/><sp/>originalState<sp/>=<sp/>deviceContext-&gt;CurrentState;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="471"><highlight class="normal"><sp/><sp/><sp/><sp/>oppositeState<sp/>=<sp/>(originalState<sp/>==<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kindref="member">GpioStateOn</ref>)<sp/>?<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" kindref="member">GpioStateOff</ref><sp/>:<sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kindref="member">GpioStateOn</ref>;</highlight></codeline>
<codeline lineno="472"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="473"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佸墽鐤嗘稉铏规祲閸欏秶濮搁幀?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>GpioDeviceSetState(Device,<sp/>oppositeState);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="474"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="475"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>set<sp/>opposite<sp/>state&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="476"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="477"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="478"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="479"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>瀵よ埖妞傞幐鍥х暰閻ㄥ嫭妞傞梻?<sp/><sp/><sp/><sp/>delay.QuadPart<sp/>=<sp/>(LONGLONG)(-10)<sp/>*<sp/>1000<sp/>*<sp/>PulseDurationMs;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="480"><highlight class="normal"><sp/><sp/><sp/><sp/>KeDelayExecutionThread(KernelMode,<sp/>FALSE,<sp/>&amp;delay);</highlight></codeline>
<codeline lineno="481"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="482"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹垹顦查崢鐔奉潗閻樿埖鈧?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>GpioDeviceSetState(Device,<sp/>originalState);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="483"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="484"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>restore<sp/>original<sp/>state&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="485"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="486"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="487"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="488"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>pulse<sp/>completed<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="489"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="490"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="491"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/devices/gpio_device.c"/>
  </compounddef>
</doxygen>
