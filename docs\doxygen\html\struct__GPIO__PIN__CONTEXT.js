var struct__GPIO__PIN__CONTEXT =
[
    [ "GPIOInitialize", "struct__GPIO__PIN__CONTEXT.html#ab343a956248ebb07de6a72eaeb55ec35", null ],
    [ "if", "struct__GPIO__PIN__CONTEXT.html#acd3cbb2291f76aabd30090072d539050", null ],
    [ "LogError", "struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454", null ],
    [ "WdfSpinLockRelease", "struct__GPIO__PIN__CONTEXT.html#a846739f679fc9ddea85cb263aa72dc9f", null ],
    [ "EvtGpioInterruptDpc", "struct__GPIO__PIN__CONTEXT.html#a44db5b39e5c14ce83ac0637da253fb47", null ],
    [ "EvtGpioInterruptIsr", "struct__GPIO__PIN__CONTEXT.html#a4b4c364430ee4f37c01413a6552df5d0", null ],
    [ "Lock", "struct__GPIO__PIN__CONTEXT.html#ae1602e3f0f02ccfe7b30fdaa89b017a7", null ],
    [ "Pins", "struct__GPIO__PIN__CONTEXT.html#a4dd51420a9c389d77c721c25d4349927", null ],
    [ "SpbIoTarget", "struct__GPIO__PIN__CONTEXT.html#a81f98266b74a3a8d80a632eead026240", null ],
    [ "STATUS_INVALID_PARAMETER", "struct__GPIO__PIN__CONTEXT.html#ad95efe470c1ee3b4b0f0d81c7f3c53fb", null ],
    [ "WdfDevice", "struct__GPIO__PIN__CONTEXT.html#a9e971cde7006142d4b5ac56689228e0b", null ]
];