<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li class="current"><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_i.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>I2C_ADDRESS&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">kmdf_i2c.h</a></li>
<li>I2C_ADDRESS_10BIT&#160;:&#160;<a class="el" href="i2c__device_8h.html#a64738b9f6c1ba2f1f919ef7a61ad1e35">i2c_device.h</a></li>
<li>I2C_ADDRESS_7BIT&#160;:&#160;<a class="el" href="i2c__device_8h.html#ab65b9830c26b77346e0f420c643d63b9">i2c_device.h</a></li>
<li>I2C_CONFIG&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">kmdf_i2c.h</a></li>
<li>I2C_DEFAULT_TIMEOUT&#160;:&#160;<a class="el" href="i2c__device_8c.html#a4bbdac9bba21cb3959a7355001ea590f">i2c_device.c</a></li>
<li>I2C_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="i2c__core_8c.html#abad9f6d84bae65aada7d4a1cfcc2ba12">i2c_core.c</a>, <a class="el" href="i2c__device_8c.html#aa89f192944d335c9a60e5858325ed89a">i2c_device.c</a></li>
<li>I2C_STATISTICS&#160;:&#160;<a class="el" href="i2c__device_8h.html#a7d8f35e89fbf4832545b3dfab3bd1ae1">i2c_device.h</a></li>
<li>I2C_TRANSFER_PACKET&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">kmdf_i2c.h</a>, <a class="el" href="i2c__device_8h.html#af8486d1fd9d3904ca8eb4408ec81d9c4">i2c_device.h</a></li>
<li>I2C_TRANSFER_READ&#160;:&#160;<a class="el" href="i2c__device_8h.html#ad6922f686b3fc13f8365467975aba1d7">i2c_device.h</a></li>
<li>I2C_TRANSFER_TYPE&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b">kmdf_i2c.h</a></li>
<li>I2cConfig&#160;:&#160;<a class="el" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">i2c_device.c</a></li>
<li>I2cDeviceCleanup()&#160;:&#160;<a class="el" href="i2c__device_8c.html#a5da67a960d3cf99caa6874438a84629b">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a5da67a960d3cf99caa6874438a84629b">i2c_device.h</a></li>
<li>I2cDeviceGetStatistics()&#160;:&#160;<a class="el" href="i2c__device_8c.html#a709aca0009ccfb39adebbdd9ce97e252">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a709aca0009ccfb39adebbdd9ce97e252">i2c_device.h</a></li>
<li>I2cDeviceInitialize()&#160;:&#160;<a class="el" href="i2c__device_8c.html#ab0c3b778b5a363d418c3d768cdb1e2d4">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#ab0c3b778b5a363d418c3d768cdb1e2d4">i2c_device.h</a></li>
<li>I2cDeviceRead()&#160;:&#160;<a class="el" href="i2c__device_8c.html#a6576f1e3485d12c22c444244044c1d30">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a6576f1e3485d12c22c444244044c1d30">i2c_device.h</a></li>
<li>I2cDeviceTransfer()&#160;:&#160;<a class="el" href="i2c__device_8c.html#ad84f26684684313ff193803d1d9c7c32">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#ad84f26684684313ff193803d1d9c7c32">i2c_device.h</a></li>
<li>I2cDeviceWrite()&#160;:&#160;<a class="el" href="i2c__device_8c.html#a580f2434082501937a3d8bc4d5591866">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a580f2434082501937a3d8bc4d5591866">i2c_device.h</a></li>
<li>I2CInitialize()&#160;:&#160;<a class="el" href="i2c__core_8c.html#a3730a6f611cf9feba7ba954330f41a6c">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#a5467da0184a8f514f9ff43ab28f7d2d0">kmdf_i2c.h</a></li>
<li>I2CRead&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e">kmdf_i2c.h</a></li>
<li>I2CReadRegister()&#160;:&#160;<a class="el" href="i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#aad5c9145daea9c25554b814bfed47756">kmdf_i2c.h</a></li>
<li>I2CScanBus()&#160;:&#160;<a class="el" href="i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#a1b937c9865418ca9d50b16766c8ceb66">kmdf_i2c.h</a></li>
<li>I2CTransferAsynchronous()&#160;:&#160;<a class="el" href="i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#a363c4a8b2ee1e16d8a6aaf35b0e67722">kmdf_i2c.h</a></li>
<li>I2CTransferSynchronous()&#160;:&#160;<a class="el" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#ae74bb3af98d6a79ba5774f9c3a480ca7">kmdf_i2c.h</a></li>
<li>I2CTransferTimerExpired&#160;:&#160;<a class="el" href="i2c__core_8c.html#a465308d666d8357287980a516e216910">i2c_core.c</a></li>
<li>I2CUninitialize()&#160;:&#160;<a class="el" href="i2c__core_8c.html#ae1622080dc9f8424bde67b829ee735c7">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#aa8f6531c5b52bc6d04ca38fbaab3c223">kmdf_i2c.h</a></li>
<li>I2CWrite&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630">kmdf_i2c.h</a></li>
<li>I2CWriteRead&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7">kmdf_i2c.h</a></li>
<li>I2CWriteRegister()&#160;:&#160;<a class="el" href="i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb">i2c_core.c</a>, <a class="el" href="kmdf__i2c_8h.html#aa4838a1894b94b950fc4a7e73624d7ed">kmdf_i2c.h</a></li>
<li>if()&#160;:&#160;<a class="el" href="gpio__core_8c.html#a977bbe3e09136dd34381e7f1b889a570">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b">spi_device.c</a></li>
<li>IN&#160;:&#160;<a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">precomp.h</a></li>
<li>INITGUID&#160;:&#160;<a class="el" href="device__manager_8c.html#af11aade3f3741fb554915d10d3f514eb">device_manager.c</a>, <a class="el" href="driver__core_8c.html#af11aade3f3741fb554915d10d3f514eb">driver_core.c</a></li>
<li>Initialized&#160;:&#160;<a class="el" href="gpio__core_8c.html#a33c00fdea3f12acb400049b8ef710ea9">gpio_core.c</a></li>
<li>InitializeDevice()&#160;:&#160;<a class="el" href="driver__core_8h.html#afc516515541c17e0ba39b5ac97a01636">driver_core.h</a></li>
<li>InitializeListHead&#160;:&#160;<a class="el" href="core__types_8h.html#acec93a755836f578590339c921773e21">core_types.h</a></li>
<li>InsertHeadList&#160;:&#160;<a class="el" href="core__types_8h.html#a14f3c1fe642e4927959b4beed2852e2a">core_types.h</a></li>
<li>InsertTailList&#160;:&#160;<a class="el" href="core__types_8h.html#a0bf4f8c1a40d587fc04e48b16db5a8c3">core_types.h</a></li>
<li>interruptConfig&#160;:&#160;<a class="el" href="gpio__core_8c.html#ac6c56d4f54252f6088c0d841efbc597e">gpio_core.c</a></li>
<li>InterruptTranslated&#160;:&#160;<a class="el" href="gpio__core_8c.html#a4983b2b08534e2a12b6e0c30d87594f0">gpio_core.c</a></li>
<li>IOCTL_DEVICE_SPECIFIC_COMMAND&#160;:&#160;<a class="el" href="device__manager_8h.html#a06bac98d6f690509acee10555e212f41">device_manager.h</a></li>
<li>IOCTL_DRIVER_BASE&#160;:&#160;<a class="el" href="driver__core_8h.html#a0785b75b63fb30d328b99d809b6ef8d9">driver_core.h</a></li>
<li>IOCTL_DRIVER_GET_STATISTICS&#160;:&#160;<a class="el" href="driver__core_8h.html#a6d4bdae2f23ab96032a14098fb28b1f2">driver_core.h</a></li>
<li>IOCTL_DRIVER_GET_VERSION&#160;:&#160;<a class="el" href="driver__core_8h.html#a07c561db6f9baf7d9f5452605546aed9">driver_core.h</a></li>
<li>IOCTL_DRIVER_RESET&#160;:&#160;<a class="el" href="driver__core_8h.html#a9cc7d1b6ef52a7692138d85aa247f989">driver_core.h</a></li>
<li>IOCTL_DRIVER_SET_LOGGING&#160;:&#160;<a class="el" href="driver__core_8h.html#a3cfaff7b63b2bbdd9583818a4f090d3b">driver_core.h</a></li>
<li>IOCTL_GET_DEVICE_INFO&#160;:&#160;<a class="el" href="device__manager_8h.html#a5d922ac9f0d09258cd06cb7d8d7160af">device_manager.h</a></li>
<li>IOCTL_GPIO_DEVICE_BASE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a0ab51b588e38783851968f26ed81d41e">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_GET_STATE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a531a7dc21007b6e72d524a9fa29de43d">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_REGISTER_CALLBACK&#160;:&#160;<a class="el" href="gpio__device_8h.html#ada8c56039bf34983b27bd0338982f351">gpio_device.h</a></li>
<li>IOCTL_GPIO_DEVICE_SET_STATE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a15338fabc6809209e45575e75f4867e4">gpio_device.h</a></li>
<li>IOCTL_GPIO_GET_VALUE&#160;:&#160;<a class="el" href="gpio__core_8c.html#a09573d341b2d8f94a213241de2444b0b">gpio_core.c</a></li>
<li>IOCTL_GPIO_SET_DIRECTION&#160;:&#160;<a class="el" href="gpio__core_8c.html#aa5235f4dd44bf922bf5befb2ef0b3b4b">gpio_core.c</a></li>
<li>IOCTL_GPIO_SET_VALUE&#160;:&#160;<a class="el" href="gpio__core_8c.html#a921358974fe0b0cbe1288fd8bdc34196">gpio_core.c</a></li>
<li>IOCTL_I2C_GET_STATISTICS&#160;:&#160;<a class="el" href="i2c__device_8h.html#a23164ebc8fc22800438176c588caa941">i2c_device.h</a></li>
<li>IOCTL_I2C_RESET&#160;:&#160;<a class="el" href="i2c__device_8h.html#a69ebe19cf050058019d84f005052cc00">i2c_device.h</a></li>
<li>IOCTL_I2C_SET_BUS_SPEED&#160;:&#160;<a class="el" href="i2c__device_8h.html#a9c3881592ae1c10fbd5ba1b9ae7e85ec">i2c_device.h</a></li>
<li>IOCTL_I2C_TRANSFER&#160;:&#160;<a class="el" href="i2c__device_8h.html#a15204e5c2582622fc3ef40f01fe93322">i2c_device.h</a></li>
<li>IOCTL_I2C_TRANSFER_SEQUENCE&#160;:&#160;<a class="el" href="i2c__device_8h.html#a478905e3b3f7f700dd9e0975f42fe1a3">i2c_device.h</a></li>
<li>IOCTL_RESET_DEVICE&#160;:&#160;<a class="el" href="device__manager_8h.html#a2f0ab8f1ea9c5b1dbd09bf73f736a7b6">device_manager.h</a></li>
<li>IOCTL_SPI_BASE&#160;:&#160;<a class="el" href="spi__device_8h.html#aad9cdee9a56a867985b5110add53ed94">spi_device.h</a></li>
<li>IOCTL_SPI_GET_STATISTICS&#160;:&#160;<a class="el" href="spi__device_8h.html#a97fe5a41276df38e46922527c4b9baf9">spi_device.h</a></li>
<li>IOCTL_SPI_RESET&#160;:&#160;<a class="el" href="spi__device_8h.html#a8442695a715b85f6516ff535bc5d1409">spi_device.h</a></li>
<li>IOCTL_SPI_SET_BUS_SPEED&#160;:&#160;<a class="el" href="spi__device_8h.html#aaba9d20f35713a3c0dd088bfdb433a0b">spi_device.h</a></li>
<li>IOCTL_SPI_SET_MODE&#160;:&#160;<a class="el" href="spi__device_8h.html#a471b24a3583fd2212e30e4619ae701be">spi_device.h</a></li>
<li>IOCTL_SPI_TRANSFER&#160;:&#160;<a class="el" href="spi__device_8h.html#a72feec97101aca3be161a59ffe40cc2c">spi_device.h</a></li>
<li>IOCTL_SPI_TRANSFER_FULL_DUPLEX&#160;:&#160;<a class="el" href="spi__device_8h.html#a21382b2df65b9cb8a11da17114ab9491">spi_device.h</a></li>
<li>IOCTL_TOUCH_GET_DATA&#160;:&#160;<a class="el" href="ioctl_8h.html#ad40a8f5f93a2d0fdabdc3b13510850b6">ioctl.h</a></li>
<li>IsListEmpty&#160;:&#160;<a class="el" href="core__types_8h.html#a1f65a0c67211ca77b491405e7c0d539e">core_types.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
