<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="src_2core_2log_2driver__log_8h" kind="file" language="C++">
    <compoundname>driver_log.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includedby refid="driver__log_8c" local="yes">C:/KMDF Driver1/src/core/log/driver_log.c</includedby>
    <incdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/src/core/log/driver_log.h</label>
        <link refid="src_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="2">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/core/log/driver_log.h</label>
        <link refid="src_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__LOG__CONFIG" prot="public">_LOG_CONFIG</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1aa1911455782e83f3b06fab600be0e43e" prot="public" static="no">
        <name>LOG_ALERT</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" kindref="member">LogLevelAlert</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="128" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="128" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a4c42b3fa94110619ab8458eb672d189d" prot="public" static="no">
        <name>LOG_ALERT_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" kindref="member">LogLevelAlert</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="147" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="147" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1abc03884460a6987df33fea0d5cae8302" prot="public" static="no">
        <name>LOG_CRITICAL</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" kindref="member">LogLevelCritical</ref>,  __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="129" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="129" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1ab48ce4a2ee7f0b5f74153fedf6ad7c25" prot="public" static="no">
        <name>LOG_CRITICAL_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" kindref="member">LogLevelCritical</ref>,  __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="148" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="148" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1acfe39a25e08737b535dc881071ebf149" prot="public" static="no">
        <name>LOG_DEBUG</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kindref="member">LogLevelDebug</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="134" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="134" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1ae930e4b3ae4e59dc6a7b6a4feefb116f" prot="public" static="no">
        <name>LOG_DEBUG_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kindref="member">LogLevelDebug</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="153" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="153" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1ad706db1253940848e01bbc71ede868ef" prot="public" static="no">
        <name>LOG_EMERGENCY</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" kindref="member">LogLevelEmergency</ref>, __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="127" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="127" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a570937723f42dd301b24b631ec455b58" prot="public" static="no">
        <name>LOG_EMERGENCY_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" kindref="member">LogLevelEmergency</ref>, __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="146" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="146" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" prot="public" static="no">
        <name>LOG_ERROR</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kindref="member">LogLevelError</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="130" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="130" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
        <referencedby refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__main_8c" startline="106" endline="141">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1aefdc06b9d942e6b102424a8a81c0be8a" compoundref="i2c__core_8c" startline="437" endline="447">I2CTransferTimerExpired</referencedby>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1aff0a0cc082f6b2fad9ed0979da6e8a9b" prot="public" static="no">
        <name>LOG_ERROR_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kindref="member">LogLevelError</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="149" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="149" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" prot="public" static="no">
        <name>LOG_INFO</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>,      __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="133" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="133" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" compoundref="device__manager_8c" startline="316" endline="354">DeviceConfigureIoQueue</referencedby>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" compoundref="device__manager_8c" startline="957" endline="976">DeviceD0Entry</referencedby>
        <referencedby refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" compoundref="device__manager_8c" startline="982" endline="1001">DeviceD0Exit</referencedby>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" compoundref="device__manager_8c" startline="258" endline="272">DeviceInterruptDisable</referencedby>
        <referencedby refid="device__manager_8c_1a7a7512003b2efe8ec1d3412af1b7c0b3" compoundref="device__manager_8c" startline="215" endline="231">DeviceInterruptDpc</referencedby>
        <referencedby refid="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" compoundref="device__manager_8c" startline="237" endline="252">DeviceInterruptEnable</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" compoundref="device__manager_8c" startline="876" endline="951">DeviceReleaseHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
        <referencedby refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__main_8c" startline="106" endline="141">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__main_8c" startline="146" endline="158">EvtDriverUnload</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" compoundref="i2c__core_8c" startline="111" endline="132">I2CUninitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a94d7f96857344352ffbc6ee65e9f2390" prot="public" static="no">
        <name>LOG_INFO_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>,      __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="152" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="152" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a05bf2404451e701f51d18409e72321fd" prot="public" static="no">
        <name>LOG_NOTICE</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" kindref="member">LogLevelNotice</ref>,    __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="132" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="132" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a6cf4240fc51cea71e901acf9df797b98" prot="public" static="no">
        <name>LOG_NOTICE_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" kindref="member">LogLevelNotice</ref>,    __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="151" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="151" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a0972af62c9ad7b688924604669d7d762" prot="public" static="no">
        <name>LOG_TRACE</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" kindref="member">LogLevelTrace</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="135" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="135" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a8793c218a97ef927e72271b80a872495" prot="public" static="no">
        <name>LOG_TRACE_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" kindref="member">LogLevelTrace</ref>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="154" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="154" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" prot="public" static="no">
        <name>LOG_WARNING</name>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref>,   __FUNCTION__, __LINE__, Format, __VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="131" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="131" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" compoundref="i2c__core_8c" startline="111" endline="132">I2CUninitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="src_2core_2log_2driver__log_8h_1a238f142a1b0fcbd8378c38d99b233baa" prot="public" static="no">
        <name>LOG_WARNING_IF</name>
        <param><defname>Condition</defname></param>
        <param><defname>Format</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (Condition) { <ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref>,   __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="150" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="150" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" prot="public" static="no" strong="no">
        <type></type>
        <name>_LOG_LEVEL</name>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" prot="public">
          <name>LogLevelEmergency</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" prot="public">
          <name>LogLevelAlert</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" prot="public">
          <name>LogLevelCritical</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" prot="public">
          <name>LogLevelError</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" prot="public">
          <name>LogLevelWarning</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" prot="public">
          <name>LogLevelNotice</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" prot="public">
          <name>LogLevelInfo</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" prot="public">
          <name>LogLevelDebug</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" prot="public">
          <name>LogLevelTrace</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" prot="public">
          <name>LogLevelMax</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="17" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="17" bodyend="28"/>
      </memberdef>
      <memberdef kind="enum" id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5" prot="public" static="no" strong="no">
        <type></type>
        <name>_LOG_TYPES</name>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1" prot="public">
          <name>LogTypeNone</name>
          <initializer>= 0x00</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" prot="public">
          <name>LogTypeDebugger</name>
          <initializer>= 0x01</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" prot="public">
          <name>LogTypeFile</name>
          <initializer>= 0x02</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" prot="public">
          <name>LogTypeETW</name>
          <initializer>= 0x04</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" prot="public">
          <name>LogTypeWPP</name>
          <initializer>= 0x08</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" prot="public">
          <name>LogTypeAll</name>
          <initializer>= 0xFF</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="34" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="34" bodyend="42"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" prot="public" static="no">
        <type>struct <ref refid="struct__LOG__CONFIG" kindref="compound">_LOG_CONFIG</ref></type>
        <definition>typedef struct _LOG_CONFIG LOG_CONFIG</definition>
        <argsstring></argsstring>
        <name>LOG_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="55" column="12"/>
      </memberdef>
      <memberdef kind="typedef" id="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" prot="public" static="no">
        <type>enum <ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kindref="member">_LOG_LEVEL</ref></type>
        <definition>typedef enum _LOG_LEVEL LOG_LEVEL</definition>
        <argsstring></argsstring>
        <name>LOG_LEVEL</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="28" column="11"/>
      </memberdef>
      <memberdef kind="typedef" id="src_2core_2log_2driver__log_8h_1a5c3fab47ae6bd7de107b55a48ff20591" prot="public" static="no">
        <type>enum <ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5" kindref="member">_LOG_TYPES</ref></type>
        <definition>typedef enum _LOG_TYPES LOG_TYPES</definition>
        <argsstring></argsstring>
        <name>LOG_TYPES</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="42" column="11"/>
      </memberdef>
      <memberdef kind="typedef" id="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" prot="public" static="no">
        <type><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref> *</type>
        <definition>typedef LOG_CONFIG* PLOG_CONFIG</definition>
        <argsstring></argsstring>
        <name>PLOG_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="57" column="20" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.h" bodystart="57" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="src_2core_2log_2driver__log_8h_1af114289be71fc27e5ce43d55d4d6622c" prot="public" static="no" extern="yes" mutable="no">
        <type><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref></type>
        <definition>LOG_CONFIG g_LogConfig</definition>
        <argsstring></argsstring>
        <name>g_LogConfig</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="63" column="19" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="16" bodyend="16" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="63" declcolumn="19"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" compoundref="driver__log_8c" startline="103" endline="129">LogMessageVA</referencedby>
      </memberdef>
      <memberdef kind="variable" id="src_2core_2log_2driver__log_8h_1acd7f22e672d8bbb5ac97c70459b869fb" prot="public" static="no" extern="yes" mutable="no">
        <type>BOOLEAN</type>
        <definition>BOOLEAN g_LogInitialized</definition>
        <argsstring></argsstring>
        <name>g_LogInitialized</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="64" column="16" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="17" bodyend="-1" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="64" declcolumn="16"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" compoundref="driver__log_8c" startline="103" endline="129">LogMessageVA</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3" prot="public" static="no" extern="yes" mutable="no">
        <type>const PCSTR</type>
        <definition>const PCSTR g_LogLevelNames[LogLevelMax]</definition>
        <argsstring>[LogLevelMax]</argsstring>
        <name>g_LogLevelNames</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="65" column="20" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="65" declcolumn="20"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="src_2core_2log_2driver__log_8h_1aa7f5f3b01615029c1cb54753c0b03175" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogConfigInit</definition>
        <argsstring>(_Out_ PLOG_CONFIG LogConfig)</argsstring>
        <name>LogConfigInit</name>
        <param>
          <type>_Out_ <ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref></type>
          <declname>LogConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="89" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="90" bodyend="100" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="89" declcolumn="1"/>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" compoundref="src_2core_2log_2driver__log_8h" startline="24">LogLevelInfo</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" compoundref="src_2core_2log_2driver__log_8h" startline="36">LogTypeDebugger</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
      </memberdef>
      <memberdef kind="function" id="src_2core_2log_2driver__log_8h_1aa2e9424857371175fc265253fbabcc5d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS LogInitialize</definition>
        <argsstring>(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig)</argsstring>
        <name>LogInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>DriverObject</declname>
        </param>
        <param>
          <type>_In_opt_ CONST <ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref> *</type>
          <declname>InitialConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="82" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="135" bodyend="241" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="82" declcolumn="1"/>
        <references refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" compoundref="driver__log_8c" startline="16" endline="16">g_LogConfig</references>
        <references refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" compoundref="driver__log_8c" startline="33">g_LogFileHandle</references>
        <references refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" compoundref="driver__log_8c" startline="34">g_LogFilePath</references>
        <references refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" compoundref="driver__log_8c" startline="35">g_LogFilePathBuffer</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3">g_LogLevelNames</references>
        <references refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" compoundref="driver__log_8c" startline="18">g_LogLock</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" compoundref="src_2core_2log_2driver__log_8h" startline="24">LogLevelInfo</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" compoundref="src_2core_2log_2driver__log_8h" startline="27">LogLevelMax</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" compoundref="src_2core_2log_2driver__log_8h" startline="37">LogTypeFile</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="core__types_8h_1aa4e1fcf1f71277fe7bbb792e4bc4f1f8" compoundref="core__types_8h" startline="168">STATUS_ALREADY_INITIALIZED</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="function" id="src_2core_2log_2driver__log_8h_1a8e8711da6408af7b3b313f892121215e" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogMessageVA</definition>
        <argsstring>(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args)</argsstring>
        <name>LogMessageVA</name>
        <param>
          <type>_In_ <ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref></type>
          <declname>Level</declname>
        </param>
        <param>
          <type>_In_ PCSTR</type>
          <declname>Function</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Line</declname>
        </param>
        <param>
          <type>_In_ PCSTR</type>
          <declname>Format</declname>
        </param>
        <param>
          <type>_In_ va_list</type>
          <declname>Args</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="109" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="103" bodyend="129" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="109" declcolumn="1"/>
        <references refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" compoundref="driver__log_8c" startline="16" endline="16">g_LogConfig</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" compoundref="src_2core_2log_2driver__log_8h" startline="36">LogTypeDebugger</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="src_2core_2log_2driver__log_8h_1aab8bcb7121136bc236fe5d55778fbaf2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogUninitialize</definition>
        <argsstring>(VOID)</argsstring>
        <name>LogUninitialize</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.h" line="96" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="45" bodyend="87" declfile="C:/KMDF Driver1/src/core/log/driver_log.h" declline="96" declcolumn="1"/>
        <references refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" compoundref="driver__log_8c" startline="33">g_LogFileHandle</references>
        <references refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" compoundref="driver__log_8c" startline="34">g_LogFilePath</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" compoundref="driver__log_8c" startline="18">g_LogLock</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="preprocessor">#ifndef<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>Required<sp/>for<sp/>NTSTATUS,<sp/>etc.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Disable<sp/>warning<sp/>C4324<sp/>(structure<sp/>was<sp/>padded<sp/>due<sp/>to<sp/>alignment<sp/>specifier)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight><highlight class="comment">//<sp/>specifically<sp/>for<sp/>WDF<sp/>headers,<sp/>as<sp/>this<sp/>warning<sp/>is<sp/>often<sp/>triggered<sp/>by<sp/>them</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight><highlight class="comment">//<sp/>and<sp/>can<sp/>be<sp/>treated<sp/>as<sp/>an<sp/>error<sp/>(C2220)<sp/>in<sp/>some<sp/>build<sp/>configurations.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>warning(push)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>warning(disable:<sp/>4324)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Required<sp/>for<sp/>WDF_OBJECT_ATTRIBUTES,<sp/>WDFSPINLOCK,<sp/>etc.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>warning(pop)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Log<sp/>Level<sp/>Enum</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Defines<sp/>the<sp/>severity<sp/>of<sp/>the<sp/>log<sp/>message.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kindref="member">_LOG_LEVEL</ref><sp/>{</highlight></codeline>
<codeline lineno="18" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" kindref="member">LogLevelEmergency</ref><sp/>=<sp/>0,<sp/></highlight><highlight class="comment">//<sp/>System<sp/>is<sp/>unusable.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" kindref="member">LogLevelAlert</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Action<sp/>must<sp/>be<sp/>taken<sp/>immediately.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" kindref="member">LogLevelCritical</ref>,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Critical<sp/>conditions.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kindref="member">LogLevelError</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Error<sp/>conditions.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Warning<sp/>conditions.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" kindref="member">LogLevelNotice</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Normal<sp/>but<sp/>significant<sp/>condition.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Informational<sp/>messages.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kindref="member">LogLevelDebug</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Debug-level<sp/>messages.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" kindref="member">LogLevelTrace</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Trace-level<sp/>messages<sp/>(more<sp/>verbose<sp/>than<sp/>debug).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" kindref="member">LogLevelMax</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Max<sp/>log<sp/>level,<sp/>used<sp/>for<sp/>array<sp/>sizing<sp/>and<sp/>validation.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" refkind="member"><highlight class="normal">}<sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref>;</highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Log<sp/>Types<sp/>Enum<sp/>(Bitmask)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Defines<sp/>where<sp/>the<sp/>log<sp/>message<sp/>should<sp/>be<sp/>sent.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5" kindref="member">_LOG_TYPES</ref><sp/>{</highlight></codeline>
<codeline lineno="35" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1" kindref="member">LogTypeNone</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/>=<sp/>0x00,<sp/></highlight><highlight class="comment">//<sp/>No<sp/>logging<sp/>output.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kindref="member">LogTypeDebugger</ref><sp/><sp/><sp/>=<sp/>0x01,<sp/></highlight><highlight class="comment">//<sp/>Output<sp/>to<sp/>kernel<sp/>debugger<sp/>(e.g.,<sp/>KdPrint).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kindref="member">LogTypeFile</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/>=<sp/>0x02,<sp/></highlight><highlight class="comment">//<sp/>Output<sp/>to<sp/>a<sp/>log<sp/>file.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" kindref="member">LogTypeETW</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>=<sp/>0x04,<sp/></highlight><highlight class="comment">//<sp/>Output<sp/>to<sp/>ETW<sp/>(Event<sp/>Tracing<sp/>for<sp/>Windows).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" kindref="member">LogTypeWPP</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>=<sp/>0x08,<sp/></highlight><highlight class="comment">//<sp/>Output<sp/>to<sp/>WPP<sp/>Software<sp/>Tracing.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Add<sp/>more<sp/>types<sp/>as<sp/>needed,<sp/>e.g.,<sp/>LogTypeSyslog<sp/>=<sp/>0x10;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" kindref="member">LogTypeAll</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>=<sp/>0xFF<sp/><sp/></highlight><highlight class="comment">//<sp/>Log<sp/>to<sp/>all<sp/>supported<sp/>types<sp/>(convenience).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="src_2core_2log_2driver__log_8h_1a5c3fab47ae6bd7de107b55a48ff20591" refkind="member"><highlight class="normal">}<sp/><ref refid="src_2core_2log_2driver__log_8h_1a5c3fab47ae6bd7de107b55a48ff20591" kindref="member">LOG_TYPES</ref>;</highlight></codeline>
<codeline lineno="43"><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Log<sp/>Configuration<sp/>Structure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Holds<sp/>the<sp/>current<sp/>configuration<sp/>for<sp/>the<sp/>logging<sp/>system.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="struct__LOG__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__LOG__CONFIG" kindref="compound">_LOG_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="49" refid="struct__LOG__CONFIG_1ac0ad1887a3c94cf0998d9c5359900a4f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/><ref refid="struct__LOG__CONFIG_1ac0ad1887a3c94cf0998d9c5359900a4f" kindref="member">MinLevel</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Minimum<sp/>log<sp/>level<sp/>to<sp/>record.<sp/>Messages<sp/>below<sp/>this<sp/>level<sp/>will<sp/>be<sp/>ignored.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="struct__LOG__CONFIG_1aa4471d5ed80ebce68218e751798de8dc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a5c3fab47ae6bd7de107b55a48ff20591" kindref="member">LOG_TYPES</ref><sp/><ref refid="struct__LOG__CONFIG_1aa4471d5ed80ebce68218e751798de8dc" kindref="member">LogTargets</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Bitmask<sp/>of<sp/>log<sp/>targets<sp/>(Debugger,<sp/>File,<sp/>ETW,<sp/>WPP).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Future<sp/>extensions:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WCHAR<sp/>LogFilePath[256];<sp/>//<sp/>Path<sp/>for<sp/>LogTypeFile,<sp/>if<sp/>configurable<sp/>at<sp/>runtime.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>ULONG<sp/>MaxLogFileSize;<sp/><sp/><sp/>//<sp/>Max<sp/>size<sp/>for<sp/>LogTypeFile<sp/>before<sp/>rotation.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>INT<sp/>MaxBackupFiles;<sp/><sp/><sp/><sp/><sp/>//<sp/>Number<sp/>of<sp/>backup<sp/>files<sp/>for<sp/>LogTypeFile.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55" refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" refkind="member"><highlight class="normal">}<sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>;</highlight></codeline>
<codeline lineno="56"><highlight class="normal"></highlight></codeline>
<codeline lineno="57" refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>*<sp/><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref>;</highlight></codeline>
<codeline lineno="58"><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="60"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Extern<sp/>declarations<sp/>for<sp/>global<sp/>variables<sp/>defined<sp/>in<sp/>driver_log.c.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="61"><highlight class="normal"></highlight><highlight class="comment">//<sp/>These<sp/>allow<sp/>other<sp/>.c<sp/>files<sp/>that<sp/>include<sp/>this<sp/>header<sp/>to<sp/>access<sp/>these<sp/>global<sp/>settings.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="62"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="63"><highlight class="normal"></highlight><highlight class="keyword">extern</highlight><highlight class="normal"><sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref><sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Global<sp/>logging<sp/>configuration<sp/>settings.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight><highlight class="keyword">extern</highlight><highlight class="normal"><sp/>BOOLEAN<sp/><ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Flag<sp/>indicating<sp/>if<sp/>the<sp/>logging<sp/>system<sp/>has<sp/>been<sp/>initialized.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="65"><highlight class="normal"></highlight><highlight class="keyword">extern</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">const</highlight><highlight class="normal"><sp/>PCSTR<sp/><ref refid="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3" kindref="member">g_LogLevelNames</ref>[<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" kindref="member">LogLevelMax</ref>];<sp/></highlight><highlight class="comment">//<sp/>Array<sp/>of<sp/>human-readable<sp/>names<sp/>for<sp/>log<sp/>levels.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"></highlight></codeline>
<codeline lineno="67"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="68"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Public<sp/>Logging<sp/>Function<sp/>Prototypes</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="69"><highlight class="normal"></highlight><highlight class="comment">//<sp/>These<sp/>are<sp/>the<sp/>functions<sp/>that<sp/>other<sp/>parts<sp/>of<sp/>the<sp/>driver<sp/>can<sp/>call.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"></highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Initializes<sp/>the<sp/>logging<sp/>system.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight><highlight class="comment">//<sp/>This<sp/>function<sp/>must<sp/>be<sp/>called<sp/>(e.g.,<sp/>in<sp/>DriverEntry)<sp/>before<sp/>any<sp/>logging<sp/>can<sp/>occur.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Parameters:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>DriverObject:<sp/><sp/>Pointer<sp/>to<sp/>the<sp/>WDFDRIVER<sp/>object.<sp/>This<sp/>can<sp/>be<sp/>used<sp/>to<sp/>associate</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="76"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF<sp/>resources<sp/>like<sp/>spinlocks<sp/>with<sp/>the<sp/>driver&apos;s<sp/>lifetime.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="77"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>InitialConfig:<sp/>Optional<sp/>pointer<sp/>to<sp/>a<sp/>LOG_CONFIG<sp/>structure<sp/>to<sp/>override<sp/>default<sp/>settings.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="78"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>If<sp/>NULL,<sp/>default<sp/>logging<sp/>settings<sp/>will<sp/>be<sp/>used.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="79"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Return<sp/>Value:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="80"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>STATUS_SUCCESS<sp/>if<sp/>initialization<sp/>was<sp/>successful,<sp/>otherwise<sp/>an<sp/>NTSTATUS<sp/>error<sp/>code.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="81"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1ae2910293c9c672800cca68427812b7c9" kindref="member">LogInitialize</ref>(</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>DriverObject,</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>CONST<sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>*<sp/>InitialConfig</highlight></codeline>
<codeline lineno="85"><highlight class="normal">);</highlight></codeline>
<codeline lineno="86"><highlight class="normal"></highlight></codeline>
<codeline lineno="87"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Initializes<sp/>the<sp/>logging<sp/>configuration<sp/>structure<sp/>with<sp/>default<sp/>values.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="88"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1ab4caba1729c833f0c7cce2e72c20e30a" kindref="member">LogConfigInit</ref>(</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref><sp/>LogConfig</highlight></codeline>
<codeline lineno="91"><highlight class="normal">);</highlight></codeline>
<codeline lineno="92"><highlight class="normal"></highlight></codeline>
<codeline lineno="93"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Uninitializes<sp/>the<sp/>logging<sp/>system<sp/>and<sp/>releases<sp/>any<sp/>allocated<sp/>resources.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"></highlight><highlight class="comment">//<sp/>This<sp/>function<sp/>should<sp/>be<sp/>called<sp/>when<sp/>the<sp/>driver<sp/>is<sp/>unloading<sp/>(e.g.,<sp/>in<sp/>EvtDriverUnload).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="95"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><ref refid="src_2core_2log_2driver__log_8h_1aab8bcb7121136bc236fe5d55778fbaf2" kindref="member">LogUninitialize</ref>(</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="98"><highlight class="normal">);</highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight></codeline>
<codeline lineno="100"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Core<sp/>logging<sp/>function<sp/>that<sp/>takes<sp/>a<sp/>va_list<sp/>for<sp/>variable<sp/>arguments.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="101"><highlight class="normal"></highlight><highlight class="comment">//<sp/>This<sp/>is<sp/>typically<sp/>called<sp/>by<sp/>user-friendly<sp/>macros<sp/>rather<sp/>than<sp/>directly.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="102"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Parameters:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="103"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>Level:<sp/><sp/><sp/><sp/>The<sp/>severity<sp/>level<sp/>of<sp/>the<sp/>message.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="104"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>Function:<sp/>The<sp/>name<sp/>of<sp/>the<sp/>function<sp/>where<sp/>the<sp/>log<sp/>message<sp/>originated.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="105"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>Line:<sp/><sp/><sp/><sp/><sp/>The<sp/>line<sp/>number<sp/>in<sp/>the<sp/>source<sp/>file<sp/>where<sp/>the<sp/>log<sp/>message<sp/>originated.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="106"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>Format:<sp/><sp/><sp/>The<sp/>printf-style<sp/>format<sp/>string<sp/>for<sp/>the<sp/>message.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="107"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>Args:<sp/><sp/><sp/><sp/><sp/>A<sp/>va_list<sp/>containing<sp/>the<sp/>arguments<sp/>for<sp/>the<sp/>format<sp/>string.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="108"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><ref refid="src_2core_2log_2driver__log_8h_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/>Level,</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PCSTR<sp/>Function,</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Line,</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PCSTR<sp/>Format,</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>va_list<sp/>Args</highlight></codeline>
<codeline lineno="115"><highlight class="normal">);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"></highlight></codeline>
<codeline lineno="117"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="118"><highlight class="normal"></highlight><highlight class="comment">//<sp/>User-friendly<sp/>Logging<sp/>Macros<sp/>(Recommended<sp/>for<sp/>use<sp/>in<sp/>driver<sp/>code)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="119"><highlight class="normal"></highlight><highlight class="comment">//<sp/>These<sp/>macros<sp/>simplify<sp/>logging<sp/>by<sp/>automatically<sp/>providing<sp/>the<sp/>function<sp/>name<sp/>(__FUNCTION__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="120"><highlight class="normal"></highlight><highlight class="comment">//<sp/>and<sp/>line<sp/>number<sp/>(__LINE__),<sp/>and<sp/>handling<sp/>the<sp/>variable<sp/>arguments<sp/>(...).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="121"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Usage<sp/>Example:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="123"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>LOG_INFO(&quot;Device<sp/>initialized<sp/>successfully,<sp/>status:<sp/>0x%X&quot;,<sp/>status);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="124"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>LOG_ERROR(&quot;Failed<sp/>to<sp/>allocate<sp/>memory<sp/>of<sp/>size<sp/>%zu<sp/>bytes.&quot;,<sp/>requiredSize);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="125"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="126"><highlight class="normal"></highlight></codeline>
<codeline lineno="127" refid="src_2core_2log_2driver__log_8h_1ad706db1253940848e01bbc71ede868ef" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_EMERGENCY(Format,<sp/>...)<sp/>LogMessageVA(LogLevelEmergency,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="128" refid="src_2core_2log_2driver__log_8h_1aa1911455782e83f3b06fab600be0e43e" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_ALERT(Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>LogMessageVA(LogLevelAlert,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="129" refid="src_2core_2log_2driver__log_8h_1abc03884460a6987df33fea0d5cae8302" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_CRITICAL(Format,<sp/>...)<sp/><sp/>LogMessageVA(LogLevelCritical,<sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="130" refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_ERROR(Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>LogMessageVA(LogLevelError,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="131" refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_WARNING(Format,<sp/>...)<sp/><sp/><sp/>LogMessageVA(LogLevelWarning,<sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="132" refid="src_2core_2log_2driver__log_8h_1a05bf2404451e701f51d18409e72321fd" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_NOTICE(Format,<sp/>...)<sp/><sp/><sp/><sp/>LogMessageVA(LogLevelNotice,<sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="133" refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_INFO(Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/><sp/>LogMessageVA(LogLevelInfo,<sp/><sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="134" refid="src_2core_2log_2driver__log_8h_1acfe39a25e08737b535dc881071ebf149" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_DEBUG(Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>LogMessageVA(LogLevelDebug,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="135" refid="src_2core_2log_2driver__log_8h_1a0972af62c9ad7b688924604669d7d762" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_TRACE(Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>LogMessageVA(LogLevelTrace,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="136"><highlight class="normal"></highlight></codeline>
<codeline lineno="137"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="138"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Conditional<sp/>Logging<sp/>Macros<sp/>(Log<sp/>only<sp/>if<sp/>a<sp/>specified<sp/>condition<sp/>is<sp/>true)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="139"><highlight class="normal"></highlight><highlight class="comment">//<sp/>These<sp/>macros<sp/>help<sp/>reduce<sp/>log<sp/>spam<sp/>by<sp/>only<sp/>logging<sp/>when<sp/>a<sp/>particular<sp/>condition<sp/>is<sp/>met.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="140"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="141"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Usage<sp/>Example:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="142"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>NTSTATUS<sp/>status<sp/>=<sp/>SomeFunction();</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="143"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/>LOG_ERROR_IF(status<sp/>!=<sp/>STATUS_SUCCESS,<sp/>&quot;SomeFunction<sp/>failed<sp/>with<sp/>status:<sp/>0x%X&quot;,<sp/>status);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="144"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="145"><highlight class="normal"></highlight></codeline>
<codeline lineno="146" refid="src_2core_2log_2driver__log_8h_1a570937723f42dd301b24b631ec455b58" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_EMERGENCY_IF(Condition,<sp/>Format,<sp/>...)<sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelEmergency,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="147" refid="src_2core_2log_2driver__log_8h_1a4c42b3fa94110619ab8458eb672d189d" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_ALERT_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelAlert,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="148" refid="src_2core_2log_2driver__log_8h_1ab48ce4a2ee7f0b5f74153fedf6ad7c25" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_CRITICAL_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelCritical,<sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="149" refid="src_2core_2log_2driver__log_8h_1aff0a0cc082f6b2fad9ed0979da6e8a9b" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_ERROR_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelError,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="150" refid="src_2core_2log_2driver__log_8h_1a238f142a1b0fcbd8378c38d99b233baa" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_WARNING_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelWarning,<sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="151" refid="src_2core_2log_2driver__log_8h_1a6cf4240fc51cea71e901acf9df797b98" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_NOTICE_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelNotice,<sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="152" refid="src_2core_2log_2driver__log_8h_1a94d7f96857344352ffbc6ee65e9f2390" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_INFO_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelInfo,<sp/><sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="153" refid="src_2core_2log_2driver__log_8h_1ae930e4b3ae4e59dc6a7b6a4feefb116f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_DEBUG_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelDebug,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="154" refid="src_2core_2log_2driver__log_8h_1a8793c218a97ef927e72271b80a872495" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_TRACE_IF(Condition,<sp/>Format,<sp/>...)<sp/><sp/><sp/><sp/><sp/>do<sp/>{<sp/>if<sp/>(Condition)<sp/>{<sp/>LogMessageVA(LogLevelTrace,<sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>__LINE__,<sp/>Format,<sp/>__VA_ARGS__);<sp/>}<sp/>}<sp/>while(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="155"><highlight class="normal"></highlight></codeline>
<codeline lineno="156"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/core/log/driver_log.h"/>
  </compounddef>
</doxygen>
