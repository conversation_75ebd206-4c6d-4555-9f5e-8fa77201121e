<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/core/log/driver_log.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('include_2core_2log_2driver__log_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">driver_log.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for driver_log.h:</div>
<div class="dyncontent">
<div class="center"><img src="include_2core_2log_2driver__log_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8h" id="aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" title=" " alt="" coords="16,96,80,123"/>
<area shape="poly" title=" " alt="" coords="81,50,64,83,59,81,76,47"/>
<area shape="rect" title=" " alt="" coords="104,96,158,123"/>
<area shape="poly" title=" " alt="" coords="103,47,120,81,115,83,98,50"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="include_2core_2log_2driver__log_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8hdep" id="aC_1_2KMDF_01Driver1_2include_2core_2log_2driver__log_8hdep">
<area shape="rect" title=" " alt="" coords="747,5,916,48"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="328,96,497,139"/>
<area shape="poly" title=" " alt="" coords="732,52,498,101,497,96,731,46"/>
<area shape="rect" href="driver__core_8c.html" title=" " alt="" coords="219,187,364,245"/>
<area shape="poly" title=" " alt="" coords="731,31,619,35,497,45,439,54,388,65,347,80,318,98,304,117,296,140,292,187,286,186,291,139,300,115,315,94,345,75,387,60,438,49,496,40,619,29,731,25"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="175,301,320,344"/>
<area shape="poly" title=" " alt="" coords="787,59,638,147,543,199,446,248,375,278,308,303,306,298,373,273,444,243,540,194,635,143,784,54"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="683,293,828,352"/>
<area shape="poly" title=" " alt="" coords="809,62,790,97,774,150,765,204,758,293,753,293,760,203,769,149,785,95,804,59"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="852,301,997,344"/>
<area shape="poly" title=" " alt="" coords="827,64,822,102,821,149,826,198,840,244,863,275,891,299,888,303,859,279,836,247,820,199,815,149,817,102,822,63"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="1021,301,1167,344"/>
<area shape="poly" title=" " alt="" coords="839,63,859,164,874,212,890,243,942,265,1035,298,1034,303,941,270,887,247,869,214,854,165,834,64"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1191,301,1336,344"/>
<area shape="poly" title=" " alt="" coords="858,58,892,98,934,137,1008,188,1085,233,1217,299,1214,303,1082,238,1005,193,931,141,889,101,854,62"/>
<area shape="rect" href="driver__entry_8h.html" title="Brief description." alt="" coords="26,195,194,237"/>
<area shape="poly" title=" " alt="" coords="732,30,636,34,524,44,405,65,346,80,290,98,244,120,201,146,135,196,131,192,198,141,242,115,288,94,345,75,404,60,523,39,636,28,731,24"/>
<area shape="rect" href="error__handling_8c.html" title=" " alt="" coords="944,96,1121,139"/>
<area shape="poly" title=" " alt="" coords="893,52,986,93,984,98,891,57"/>
<area shape="rect" href="gpio__device_8c.html" title=" " alt="" coords="1145,96,1320,139"/>
<area shape="poly" title=" " alt="" coords="931,47,1145,94,1144,99,930,52"/>
<area shape="rect" href="i2c__device_8c.html" title=" " alt="" coords="1344,96,1513,139"/>
<area shape="poly" title=" " alt="" coords="931,37,1118,61,1333,93,1344,95,1343,101,1332,99,1117,66,931,42"/>
<area shape="rect" href="spi__device_8c.html" title=" " alt="" coords="1536,96,1705,139"/>
<area shape="poly" title=" " alt="" coords="931,32,1200,54,1363,72,1525,93,1536,95,1536,100,1524,99,1362,77,1200,59,931,37"/>
<area shape="poly" title=" " alt="" coords="377,150,329,188,325,184,374,146"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="5,293,151,352"/>
<area shape="poly" title=" " alt="" coords="414,155,404,202,393,227,377,247,351,268,326,280,302,285,277,287,225,287,196,289,163,296,152,299,150,294,161,291,195,284,225,282,277,282,301,280,324,275,348,263,373,243,389,224,399,201,409,153"/>
<area shape="poly" title=" " alt="" coords="428,153,435,199,433,224,423,247,407,258,378,273,303,303,301,298,376,268,405,254,419,244,427,223,430,199,423,154"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="572,195,717,237"/>
<area shape="poly" title=" " alt="" coords="477,143,596,192,594,197,475,147"/>
<area shape="poly" title=" " alt="" coords="557,232,343,264,163,296,152,299,150,294,161,291,342,258,556,227"/>
<area shape="poly" title=" " alt="" coords="566,245,553,248,442,271,332,296,307,303,306,298,331,291,441,266,552,243,564,239"/>
<area shape="poly" title=" " alt="" coords="679,246,727,291,723,295,676,250"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,301,489,344"/>
<area shape="poly" title=" " alt="" coords="587,247,463,303,461,298,585,242"/>
<area shape="poly" title=" " alt="" coords="715,241,870,298,869,303,713,246"/>
<area shape="poly" title=" " alt="" coords="733,230,865,256,1011,291,1036,298,1034,303,1009,296,863,261,732,235"/>
<area shape="poly" title=" " alt="" coords="733,225,974,257,1093,274,1180,291,1207,298,1206,303,1179,296,1092,280,973,262,733,231"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="513,301,659,344"/>
<area shape="poly" title=" " alt="" coords="628,252,600,302,595,300,623,250"/>
<area shape="poly" title=" " alt="" coords="102,253,89,294,84,292,97,252"/>
<area shape="poly" title=" " alt="" coords="151,245,222,299,219,303,148,249"/>
</map>
</div>
</div>
<p><a href="include_2core_2log_2driver__log_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FLOG_5FCONFIG_5Fstruct_5F_5FLOG_5F_5FCONFIG" id="r__5FLOG_5FCONFIG_5Fstruct_5F_5FLOG_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__LOG__CONFIG">_LOG_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ac56df030fb93601c871fd894e289601a" id="r_ac56df030fb93601c871fd894e289601a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac56df030fb93601c871fd894e289601a">FUNCTION_ENTRY</a>()</td></tr>
<tr class="memitem:a347d8e7da8a8e1d1cebfd45f4055a4a8" id="r_a347d8e7da8a8e1d1cebfd45f4055a4a8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a347d8e7da8a8e1d1cebfd45f4055a4a8">FUNCTION_EXIT</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>)</td></tr>
<tr class="memitem:abd0b0523397fb05f0ed46fc217fb630f" id="r_abd0b0523397fb05f0ed46fc217fb630f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abd0b0523397fb05f0ed46fc217fb630f">LOG_DEBUG</a>(format, ...)</td></tr>
<tr class="memitem:abffaf9cecb61026cac6db71a16ace9c5" id="r_abffaf9cecb61026cac6db71a16ace9c5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abffaf9cecb61026cac6db71a16ace9c5">LOG_ERROR</a>(format, ...)</td></tr>
<tr class="memitem:a89681da4efde0b54dc7f2839665082c8" id="r_a89681da4efde0b54dc7f2839665082c8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a89681da4efde0b54dc7f2839665082c8">LOG_INFO</a>(format, ...)</td></tr>
<tr class="memitem:a6594ece0df59e19da1473edfc079fd45" id="r_a6594ece0df59e19da1473edfc079fd45"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6594ece0df59e19da1473edfc079fd45">LOG_VERBOSE</a>(format, ...)</td></tr>
<tr class="memitem:a1c60134b1702d179d9b86bc618f416fe" id="r_a1c60134b1702d179d9b86bc618f416fe"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1c60134b1702d179d9b86bc618f416fe">LOG_WARNING</a>(format, ...)</td></tr>
<tr class="memitem:a1f2322291188e5d028f82ff2f340e5e3" id="r_a1f2322291188e5d028f82ff2f340e5e3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1f2322291188e5d028f82ff2f340e5e3">LogInfo</a>(function,  line,  format, ...)</td></tr>
<tr class="memitem:aa47a100aaaa86f29c113feda40125d64" id="r_aa47a100aaaa86f29c113feda40125d64"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa47a100aaaa86f29c113feda40125d64">LogWarning</a>(function,  line,  format, ...)</td></tr>
<tr class="memitem:a3adf7dc8e9dcfe1da6e33aa8043a80c3" id="r_a3adf7dc8e9dcfe1da6e33aa8043a80c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3adf7dc8e9dcfe1da6e33aa8043a80c3">MAX_LOG_MESSAGE_LENGTH</a>&#160;&#160;&#160;512</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a565ca8d986ea85865e5e0e69c0fccc9d" id="r_a565ca8d986ea85865e5e0e69c0fccc9d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__LOG__CONFIG">_LOG_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></td></tr>
<tr class="memitem:aa90925833aff044f4ba03f43f8084bf7" id="r_aa90925833aff044f4ba03f43f8084bf7"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td></tr>
<tr class="memitem:a00e4548dd1db35b54cbeb1ee0fe45f66" id="r_a00e4548dd1db35b54cbeb1ee0fe45f66"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a050bae65361e276b294f785581894867">_LOG_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a></td></tr>
<tr class="memitem:a1736725db1db6ce0d7662411d9fbf587" id="r_a1736725db1db6ce0d7662411d9fbf587"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__LOG__CONFIG">_LOG_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1736725db1db6ce0d7662411d9fbf587">PLOG_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a7898a2c3a87496daad04bfb45321843f" id="r_a7898a2c3a87496daad04bfb45321843f"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> { <br />
&#160;&#160;<a class="el" href="#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16">LogLevelDisabled</a> = 0
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a> = 1
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a> = 2
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a> = 3
, <br />
&#160;&#160;<a class="el" href="#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">LogLevelVerbose</a> = 4
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a> = 5
<br />
 }</td></tr>
<tr class="memitem:a050bae65361e276b294f785581894867" id="r_a050bae65361e276b294f785581894867"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a050bae65361e276b294f785581894867">_LOG_TYPE</a> { <br />
&#160;&#160;<a class="el" href="#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543">LogTypeKdPrint</a> = 0
, <a class="el" href="#a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a> = 1
, <a class="el" href="#a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a> = 2
, <a class="el" href="#a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a> = 3
, <br />
&#160;&#160;<a class="el" href="#a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a> = 0xFF
<br />
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a93b035f39214ba5782080d504ae3ebc7" id="r_a93b035f39214ba5782080d504ae3ebc7"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a93b035f39214ba5782080d504ae3ebc7">LogCleanup</a> (VOID)</td></tr>
<tr class="memitem:ab4caba1729c833f0c7cce2e72c20e30a" id="r_ab4caba1729c833f0c7cce2e72c20e30a"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab4caba1729c833f0c7cce2e72c20e30a">LogConfigInit</a> (<a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig)</td></tr>
<tr class="memitem:a8b05bbaba9e1fe6f53057c15e4a53a81" id="r_a8b05bbaba9e1fe6f53057c15e4a53a81"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8b05bbaba9e1fe6f53057c15e4a53a81">LogFunctionEntry</a> (PCSTR FunctionName)</td></tr>
<tr class="memitem:a2ebea8a6c7cbdde9ba74e856c73a2740" id="r_a2ebea8a6c7cbdde9ba74e856c73a2740"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2ebea8a6c7cbdde9ba74e856c73a2740">LogFunctionExit</a> (PCSTR FunctionName, NTSTATUS Status)</td></tr>
<tr class="memitem:ae2910293c9c672800cca68427812b7c9" id="r_ae2910293c9c672800cca68427812b7c9"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae2910293c9c672800cca68427812b7c9">LogInitialize</a> (PDRIVER_OBJECT DriverObject, <a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig)</td></tr>
<tr class="memitem:ae483585a71d174709d7049cc4b4758e1" id="r_ae483585a71d174709d7049cc4b4758e1"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a> (<a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level, PCSTR Function, ULONG Line, PCSTR Format,...)</td></tr>
<tr class="memitem:abac3042c22899daa9d6987d7f15e0185" id="r_abac3042c22899daa9d6987d7f15e0185"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abac3042c22899daa9d6987d7f15e0185">LogSetLevel</a> (<a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__LOG__CONFIG" id="struct__LOG__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__LOG__CONFIG">&#9670;&#160;</a></span>_LOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _LOG_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ac82d96af8adf8a1862d02ac0be15939d" name="ac82d96af8adf8a1862d02ac0be15939d"></a>BOOLEAN</td>
<td class="fieldname">
IncludeComponentName</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a860b2d89bf1aff272da84d5029dcb0b1" name="a860b2d89bf1aff272da84d5029dcb0b1"></a>BOOLEAN</td>
<td class="fieldname">
IncludeTimestamp</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a21ca685eeee730a0d8a179892b840d5f" name="a21ca685eeee730a0d8a179892b840d5f"></a>PWSTR</td>
<td class="fieldname">
LogFilePath</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab67683f62a123026ec78fe405cdbb177" name="ab67683f62a123026ec78fe405cdbb177"></a><a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
<td class="fieldname">
LogLevel</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aa4471d5ed80ebce68218e751798de8dc" name="aa4471d5ed80ebce68218e751798de8dc"></a><a class="el" href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a></td>
<td class="fieldname">
LogTargets</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a4b99237eff4b17c1d4fbfa9697528f70" name="a4b99237eff4b17c1d4fbfa9697528f70"></a><a class="el" href="#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a></td>
<td class="fieldname">
LogTypes</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a7aa67c4af6a6176801a2b2af65ac3cbb" name="a7aa67c4af6a6176801a2b2af65ac3cbb"></a>ULONG</td>
<td class="fieldname">
MaxLogFileSize</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ac0ad1887a3c94cf0998d9c5359900a4f" name="ac0ad1887a3c94cf0998d9c5359900a4f"></a><a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
<td class="fieldname">
MinLevel</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="ac56df030fb93601c871fd894e289601a" name="ac56df030fb93601c871fd894e289601a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac56df030fb93601c871fd894e289601a">&#9670;&#160;</a></span>FUNCTION_ENTRY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define FUNCTION_ENTRY</td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#a8b05bbaba9e1fe6f53057c15e4a53a81">LogFunctionEntry</a>(__FUNCTION__)</div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a8b05bbaba9e1fe6f53057c15e4a53a81"><div class="ttname"><a href="#a8b05bbaba9e1fe6f53057c15e4a53a81">LogFunctionEntry</a></div><div class="ttdeci">VOID LogFunctionEntry(PCSTR FunctionName)</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a347d8e7da8a8e1d1cebfd45f4055a4a8" name="a347d8e7da8a8e1d1cebfd45f4055a4a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a347d8e7da8a8e1d1cebfd45f4055a4a8">&#9670;&#160;</a></span>FUNCTION_EXIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define FUNCTION_EXIT</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em><a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#a2ebea8a6c7cbdde9ba74e856c73a2740">LogFunctionExit</a>(__FUNCTION__, <a class="code hl_variable" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>)</div>
<div class="ttc" id="agpio__core_8c_html_a9611b3a00430a86619b5923de30f9fdb"><div class="ttname"><a href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a></div><div class="ttdeci">status</div><div class="ttdef"><b>Definition</b> gpio_core.c:127</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a2ebea8a6c7cbdde9ba74e856c73a2740"><div class="ttname"><a href="#a2ebea8a6c7cbdde9ba74e856c73a2740">LogFunctionExit</a></div><div class="ttdeci">VOID LogFunctionExit(PCSTR FunctionName, NTSTATUS Status)</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="abd0b0523397fb05f0ed46fc217fb630f" name="abd0b0523397fb05f0ed46fc217fb630f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd0b0523397fb05f0ed46fc217fb630f">&#9670;&#160;</a></span>LOG_DEBUG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_DEBUG</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ae483585a71d174709d7049cc4b4758e1"><div class="ttname"><a href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a></div><div class="ttdeci">VOID LogMessage(LOG_LEVEL Level, PCSTR Function, ULONG Line, PCSTR Format,...)</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a></div><div class="ttdeci">@ LogLevelDebug</div><div class="ttdef"><b>Definition</b> driver_log.h:25</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="abffaf9cecb61026cac6db71a16ace9c5" name="abffaf9cecb61026cac6db71a16ace9c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abffaf9cecb61026cac6db71a16ace9c5">&#9670;&#160;</a></span>LOG_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_ERROR</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a></div><div class="ttdeci">@ LogLevelError</div><div class="ttdef"><b>Definition</b> driver_log.h:21</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a89681da4efde0b54dc7f2839665082c8" name="a89681da4efde0b54dc7f2839665082c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a89681da4efde0b54dc7f2839665082c8">&#9670;&#160;</a></span>LOG_INFO</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_INFO</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a></div><div class="ttdeci">@ LogLevelInfo</div><div class="ttdef"><b>Definition</b> driver_log.h:24</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a6594ece0df59e19da1473edfc079fd45" name="a6594ece0df59e19da1473edfc079fd45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6594ece0df59e19da1473edfc079fd45">&#9670;&#160;</a></span>LOG_VERBOSE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_VERBOSE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">LogLevelVerbose</a>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">LogLevelVerbose</a></div><div class="ttdeci">@ LogLevelVerbose</div><div class="ttdef"><b>Definition</b> driver_log.h:14</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a1c60134b1702d179d9b86bc618f416fe" name="a1c60134b1702d179d9b86bc618f416fe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c60134b1702d179d9b86bc618f416fe">&#9670;&#160;</a></span>LOG_WARNING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_WARNING</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a></div><div class="ttdeci">@ LogLevelWarning</div><div class="ttdef"><b>Definition</b> driver_log.h:22</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a1f2322291188e5d028f82ff2f340e5e3" name="a1f2322291188e5d028f82ff2f340e5e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f2322291188e5d028f82ff2f340e5e3">&#9670;&#160;</a></span>LogInfo</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LogInfo</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>function</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>line</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>, function, line, format, ##__VA_ARGS__)</div>
</div><!-- fragment -->
</div>
</div>
<a id="aa47a100aaaa86f29c113feda40125d64" name="aa47a100aaaa86f29c113feda40125d64"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa47a100aaaa86f29c113feda40125d64">&#9670;&#160;</a></span>LogWarning</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LogWarning</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>function</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>line</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <a class="code hl_function" href="#ae483585a71d174709d7049cc4b4758e1">LogMessage</a>(<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>, function, line, format, ##__VA_ARGS__)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a3adf7dc8e9dcfe1da6e33aa8043a80c3" name="a3adf7dc8e9dcfe1da6e33aa8043a80c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3adf7dc8e9dcfe1da6e33aa8043a80c3">&#9670;&#160;</a></span>MAX_LOG_MESSAGE_LENGTH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MAX_LOG_MESSAGE_LENGTH&#160;&#160;&#160;512</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a565ca8d986ea85865e5e0e69c0fccc9d" name="a565ca8d986ea85865e5e0e69c0fccc9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a565ca8d986ea85865e5e0e69c0fccc9d">&#9670;&#160;</a></span>LOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__LOG__CONFIG">_LOG_CONFIG</a> <a class="el" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa90925833aff044f4ba03f43f8084bf7" name="aa90925833aff044f4ba03f43f8084bf7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa90925833aff044f4ba03f43f8084bf7">&#9670;&#160;</a></span>LOG_LEVEL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> <a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a00e4548dd1db35b54cbeb1ee0fe45f66" name="a00e4548dd1db35b54cbeb1ee0fe45f66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a00e4548dd1db35b54cbeb1ee0fe45f66">&#9670;&#160;</a></span>LOG_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a050bae65361e276b294f785581894867">_LOG_TYPE</a> <a class="el" href="#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1736725db1db6ce0d7662411d9fbf587" name="a1736725db1db6ce0d7662411d9fbf587"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1736725db1db6ce0d7662411d9fbf587">&#9670;&#160;</a></span>PLOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__LOG__CONFIG">_LOG_CONFIG</a> * <a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="a7898a2c3a87496daad04bfb45321843f" name="a7898a2c3a87496daad04bfb45321843f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7898a2c3a87496daad04bfb45321843f">&#9670;&#160;</a></span>_LOG_LEVEL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16" name="a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16"></a>LogLevelDisabled&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" name="a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"></a>LogLevelError&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" name="a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"></a>LogLevelWarning&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" name="a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"></a>LogLevelInfo&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" name="a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b"></a>LogLevelVerbose&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" name="a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"></a>LogLevelDebug&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a050bae65361e276b294f785581894867" name="a050bae65361e276b294f785581894867"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a050bae65361e276b294f785581894867">&#9670;&#160;</a></span>_LOG_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a050bae65361e276b294f785581894867">_LOG_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" name="a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543"></a>LogTypeKdPrint&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7" name="a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7"></a>LogTypeWPP&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3" name="a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3"></a>LogTypeETW&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d" name="a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d"></a>LogTypeFile&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee" name="a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee"></a>LogTypeAll&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a93b035f39214ba5782080d504ae3ebc7" name="a93b035f39214ba5782080d504ae3ebc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93b035f39214ba5782080d504ae3ebc7">&#9670;&#160;</a></span>LogCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogCleanup </td>
          <td>(</td>
          <td class="paramtype">VOID</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="include_2core_2log_2driver__log_8h_a93b035f39214ba5782080d504ae3ebc7_icgraph.png" border="0" usemap="#ainclude_2core_2log_2driver__log_8h_a93b035f39214ba5782080d504ae3ebc7_icgraph" loading="lazy" alt=""/></div>
<map name="ainclude_2core_2log_2driver__log_8h_a93b035f39214ba5782080d504ae3ebc7_icgraph" id="ainclude_2core_2log_2driver__log_8h_a93b035f39214ba5782080d504ae3ebc7_icgraph">
<area shape="rect" title=" " alt="" coords="306,5,396,32"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,5,93,32"/>
<area shape="poly" title=" " alt="" coords="290,21,93,21,93,16,290,16"/>
<area shape="rect" href="driver__entry_8c.html#a075700d7117ddde115f3bb0db54b619e" title=" " alt="" coords="141,31,258,57"/>
<area shape="poly" title=" " alt="" coords="291,31,259,37,258,32,290,26"/>
<area shape="poly" title=" " alt="" coords="125,34,93,29,93,23,126,29"/>
</map>
</div>

</div>
</div>
<a id="ab4caba1729c833f0c7cce2e72c20e30a" name="ab4caba1729c833f0c7cce2e72c20e30a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4caba1729c833f0c7cce2e72c20e30a">&#9670;&#160;</a></span>LogConfigInit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogConfigInit </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>LogConfig</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8b05bbaba9e1fe6f53057c15e4a53a81" name="a8b05bbaba9e1fe6f53057c15e4a53a81"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b05bbaba9e1fe6f53057c15e4a53a81">&#9670;&#160;</a></span>LogFunctionEntry()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogFunctionEntry </td>
          <td>(</td>
          <td class="paramtype">PCSTR</td>          <td class="paramname"><span class="paramname"><em>FunctionName</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2ebea8a6c7cbdde9ba74e856c73a2740" name="a2ebea8a6c7cbdde9ba74e856c73a2740"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ebea8a6c7cbdde9ba74e856c73a2740">&#9670;&#160;</a></span>LogFunctionExit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogFunctionExit </td>
          <td>(</td>
          <td class="paramtype">PCSTR</td>          <td class="paramname"><span class="paramname"><em>FunctionName</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">NTSTATUS</td>          <td class="paramname"><span class="paramname"><em>Status</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae2910293c9c672800cca68427812b7c9" name="ae2910293c9c672800cca68427812b7c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2910293c9c672800cca68427812b7c9">&#9670;&#160;</a></span>LogInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogInitialize </td>
          <td>(</td>
          <td class="paramtype">PDRIVER_OBJECT</td>          <td class="paramname"><span class="paramname"><em>DriverObject</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>LogConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae483585a71d174709d7049cc4b4758e1" name="ae483585a71d174709d7049cc4b4758e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae483585a71d174709d7049cc4b4758e1">&#9670;&#160;</a></span>LogMessage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogMessage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>          <td class="paramname"><span class="paramname"><em>Level</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">PCSTR</td>          <td class="paramname"><span class="paramname"><em>Function</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ULONG</td>          <td class="paramname"><span class="paramname"><em>Line</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">PCSTR</td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>...</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="include_2core_2log_2driver__log_8h_ae483585a71d174709d7049cc4b4758e1_icgraph.png" border="0" usemap="#ainclude_2core_2log_2driver__log_8h_ae483585a71d174709d7049cc4b4758e1_icgraph" loading="lazy" alt=""/></div>
<map name="ainclude_2core_2log_2driver__log_8h_ae483585a71d174709d7049cc4b4758e1_icgraph" id="ainclude_2core_2log_2driver__log_8h_ae483585a71d174709d7049cc4b4758e1_icgraph">
<area shape="rect" title=" " alt="" coords="696,791,791,817"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="577,791,648,817"/>
<area shape="poly" title=" " alt="" coords="680,807,648,807,648,801,680,801"/>
<area shape="rect" href="driver__core_8c.html#abc51f0e6ed5304a27afddf92da4720e6" title=" " alt="" coords="355,5,503,32"/>
<area shape="poly" title=" " alt="" coords="607,775,599,615,582,385,558,170,543,93,535,68,527,52,502,34,505,29,531,49,540,66,549,92,563,169,587,385,604,614,613,775"/>
<area shape="rect" href="driver__entry_8c.html#a0776c179fdcbdd09df07ee264e7e78e6" title=" " alt="" coords="141,56,281,83"/>
<area shape="poly" title=" " alt="" coords="607,775,599,624,583,409,572,303,559,210,544,140,536,117,527,103,501,82,470,67,437,57,403,52,334,51,273,58,272,53,334,45,403,46,438,52,472,62,503,78,531,100,541,114,549,138,564,209,577,302,588,409,604,623,613,775"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,107,93,133"/>
<area shape="poly" title=" " alt="" coords="607,775,598,632,582,431,558,246,543,181,536,159,528,147,496,129,449,117,392,110,327,108,197,111,93,118,93,113,197,105,327,102,392,105,450,112,498,125,531,143,540,157,549,179,563,245,587,431,604,631,612,775"/>
<area shape="rect" href="driver__core_8c.html#a24d4768bc415635465e00a5f5a3b4187" title=" " alt="" coords="356,157,502,184"/>
<area shape="poly" title=" " alt="" coords="609,775,606,652,595,482,585,394,571,315,552,249,540,224,527,205,501,186,504,181,531,201,545,221,557,247,576,314,590,394,600,481,611,652,614,775"/>
<area shape="rect" href="driver__core_8c.html#acdb452dbcae039af8967376463c758b9" title=" " alt="" coords="145,183,277,209"/>
<area shape="poly" title=" " alt="" coords="609,776,606,660,596,501,585,421,571,348,552,288,540,265,527,249,511,237,486,227,421,213,346,205,277,200,278,195,347,199,422,208,488,222,513,232,531,245,545,263,557,286,576,346,591,420,601,501,611,660,614,776"/>
<area shape="rect" href="driver__core_8c.html#a89627789114e389118ee51bda8684ab6" title=" " alt="" coords="342,259,516,285"/>
<area shape="poly" title=" " alt="" coords="608,775,603,670,591,527,580,455,566,389,549,335,527,299,513,288,517,284,531,296,554,333,572,388,585,454,596,526,608,670,613,775"/>
<area shape="rect" href="gpio__device_8c.html#a3df79e59d8cd67d1b0ea2bdd7b7e2662" title=" " alt="" coords="348,309,510,336"/>
<area shape="poly" title=" " alt="" coords="607,776,601,680,587,552,577,488,563,429,547,382,527,350,509,336,512,332,531,346,552,380,569,428,582,487,593,551,606,679,613,775"/>
<area shape="rect" href="i2c__device_8c.html#a50d1319f95a5bfb01ed5c3ab0f60bf8b" title=" " alt="" coords="368,360,490,387"/>
<area shape="poly" title=" " alt="" coords="607,776,599,689,584,577,560,470,545,428,527,400,510,388,490,380,491,375,513,383,531,397,550,426,565,468,589,576,604,689,612,775"/>
<area shape="rect" href="gpio__device_8c.html#a0bd26e3410adfbb26bba326602f8fec6" title=" " alt="" coords="358,411,500,437"/>
<area shape="poly" title=" " alt="" coords="606,776,596,700,580,602,557,510,543,475,527,451,514,441,499,434,501,429,517,437,531,448,548,472,562,509,586,601,602,699,611,775"/>
<area shape="rect" href="gpio__device_8c.html#a21428e126bf8c8996ea3405e6a8be2f2" title=" " alt="" coords="360,461,498,488"/>
<area shape="poly" title=" " alt="" coords="610,775,608,718,597,644,587,605,572,568,553,533,527,502,514,492,497,484,499,479,516,487,531,498,557,530,577,565,592,604,602,643,613,717,615,775"/>
<area shape="rect" href="gpio__device_8c.html#a1e860d4292f8df84e5d3101f8a415d6c" title=" " alt="" coords="368,512,490,539"/>
<area shape="poly" title=" " alt="" coords="608,775,603,727,590,666,566,605,549,577,528,553,510,541,490,533,492,528,513,536,531,549,553,574,571,603,595,665,608,726,613,775"/>
<area shape="rect" href="gpio__device_8c.html#a6448a7e48d735f67501f3273b75485fd" title=" " alt="" coords="334,563,524,589"/>
<area shape="poly" title=" " alt="" coords="605,776,597,737,583,690,560,643,545,622,528,603,510,592,513,588,531,599,550,618,565,640,588,688,603,736,610,775"/>
<area shape="rect" href="gpio__device_8c.html#a731812dec996a670e7d557a282535d3d" title=" " alt="" coords="359,613,499,640"/>
<area shape="poly" title=" " alt="" coords="601,777,575,714,554,682,528,654,499,639,501,634,531,650,559,678,580,712,606,775"/>
<area shape="rect" href="gpio__device_8c.html#afbd2bd91a99c594504ca275fd8b45825" title=" " alt="" coords="329,664,529,691"/>
<area shape="poly" title=" " alt="" coords="594,779,566,740,548,721,528,705,505,694,507,689,531,700,552,717,570,737,598,776"/>
<area shape="rect" href="i2c__device_8c.html#a5da67a960d3cf99caa6874438a84629b" title=" " alt="" coords="365,715,493,741"/>
<area shape="poly" title=" " alt="" coords="579,784,528,756,493,744,495,739,530,751,581,779"/>
<area shape="rect" href="i2c__device_8c.html#a709aca0009ccfb39adebbdd9ce97e252" title=" " alt="" coords="351,765,507,792"/>
<area shape="poly" title=" " alt="" coords="561,800,507,792,508,787,562,794"/>
<area shape="rect" href="i2c__device_8c.html#ab0c3b778b5a363d418c3d768cdb1e2d4" title=" " alt="" coords="365,816,493,843"/>
<area shape="poly" title=" " alt="" coords="563,814,494,823,493,818,562,808"/>
<area shape="rect" href="i2c__device_8c.html#a6576f1e3485d12c22c444244044c1d30" title=" " alt="" coords="374,867,485,893"/>
<area shape="poly" title=" " alt="" coords="581,829,530,857,486,872,485,867,528,852,579,824"/>
<area shape="rect" href="i2c__device_8c.html#ad84f26684684313ff193803d1d9c7c32" title=" " alt="" coords="365,917,493,944"/>
<area shape="poly" title=" " alt="" coords="598,832,570,871,552,891,531,908,495,923,493,918,528,903,548,887,566,868,594,829"/>
<area shape="rect" href="i2c__device_8c.html#a580f2434082501937a3d8bc4d5591866" title=" " alt="" coords="374,968,484,995"/>
<area shape="poly" title=" " alt="" coords="606,833,580,896,559,930,531,958,509,971,486,978,484,973,507,966,528,954,554,926,575,894,601,831"/>
<area shape="rect" href="gpio__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641" title=" " alt="" coords="410,1019,448,1045"/>
<area shape="poly" title=" " alt="" coords="610,833,603,872,588,920,565,968,550,990,531,1009,511,1021,489,1029,449,1035,448,1029,488,1024,509,1016,528,1005,545,986,560,965,583,918,597,871,605,832"/>
<area shape="rect" href="i2c__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b" title=" " alt="" coords="410,1069,448,1096"/>
<area shape="poly" title=" " alt="" coords="613,833,608,882,595,943,571,1005,553,1034,531,1059,512,1072,490,1080,449,1086,448,1080,488,1075,509,1068,528,1055,549,1031,566,1003,590,942,603,881,608,833"/>
<area shape="rect" href="gpio__device_8c.html#ad94ec7eba667568bcd9afe3483282304" title=" " alt="" coords="410,1120,448,1147"/>
<area shape="poly" title=" " alt="" coords="615,833,613,891,602,965,592,1004,577,1043,557,1078,531,1110,512,1123,490,1131,449,1136,448,1131,489,1126,510,1119,528,1106,553,1075,572,1040,587,1003,597,964,608,890,610,833"/>
<area shape="rect" href="i2c__device_8c.html#adfac0a96ec8249c69bd820670db7f2cd" title=" " alt="" coords="410,1171,448,1197"/>
<area shape="poly" title=" " alt="" coords="611,833,602,909,586,1007,562,1099,548,1136,531,1161,512,1174,490,1182,449,1187,449,1182,489,1177,510,1169,527,1157,543,1133,557,1098,580,1006,596,908,606,832"/>
<area shape="rect" href="spi__device_8c.html#afff80b1a0000ef578da0277667a994ff" title=" " alt="" coords="410,1221,448,1248"/>
<area shape="poly" title=" " alt="" coords="612,833,604,919,589,1032,565,1140,550,1182,531,1211,512,1225,490,1233,449,1238,448,1233,489,1228,510,1220,527,1208,545,1180,560,1138,584,1031,599,919,607,832"/>
<area shape="rect" href="spi__device_8c.html#a052b57a96b994325a574bcb9f3db837a" title=" " alt="" coords="364,1272,494,1299"/>
<area shape="poly" title=" " alt="" coords="613,833,606,929,593,1057,582,1121,569,1180,552,1228,531,1262,515,1275,495,1283,493,1278,512,1270,527,1258,547,1226,563,1179,577,1120,587,1056,601,928,607,832"/>
<area shape="rect" href="spi__device_8c.html#ae2be7c6b48ddf5b08876e1115879469d" title=" " alt="" coords="350,1323,508,1349"/>
<area shape="poly" title=" " alt="" coords="613,833,608,938,596,1082,585,1154,572,1220,554,1275,531,1312,510,1328,507,1323,527,1309,549,1273,566,1219,580,1153,591,1081,603,938,608,833"/>
<area shape="rect" href="spi__device_8c.html#a6939e12311ec72f975bcd03a4250a3e2" title=" " alt="" coords="364,1373,494,1400"/>
<area shape="poly" title=" " alt="" coords="614,833,610,948,599,1106,589,1187,575,1261,556,1321,544,1345,531,1363,515,1376,495,1384,493,1379,512,1372,527,1359,540,1342,551,1319,569,1259,583,1186,594,1106,605,948,608,833"/>
<area shape="rect" href="spi__device_8c.html#a3bc98267d67ee8988179bde952efaa87" title=" " alt="" coords="373,1424,485,1451"/>
<area shape="poly" title=" " alt="" coords="614,833,612,957,602,1131,592,1220,577,1301,558,1368,545,1394,531,1414,511,1429,487,1437,485,1432,508,1424,527,1410,541,1391,553,1366,572,1299,587,1219,597,1130,607,957,609,833"/>
<area shape="rect" href="spi__device_8c.html#a2428921b9d71ab9d24f34e0a7b23487c" title=" " alt="" coords="364,1475,494,1501"/>
<area shape="poly" title=" " alt="" coords="614,833,614,966,605,1155,595,1252,580,1341,560,1414,546,1442,531,1464,515,1478,495,1486,493,1481,512,1473,527,1461,542,1440,555,1412,575,1339,590,1251,600,1155,609,966,609,833"/>
<area shape="rect" href="spi__device_8c.html#ae90ccf3d865bebb54c2c76e10fcbcaa8" title=" " alt="" coords="374,1525,485,1552"/>
<area shape="poly" title=" " alt="" coords="612,833,604,985,587,1201,563,1403,548,1475,540,1500,531,1515,511,1531,486,1539,485,1534,508,1526,527,1512,535,1497,543,1474,558,1402,582,1201,598,985,607,833"/>
<area shape="rect" href="spi__core_8c.html#a685d8d7731e750c1512b975df16cc030" title=" " alt="" coords="384,1600,475,1627"/>
<area shape="poly" title=" " alt="" coords="614,833,614,982,605,1200,596,1312,581,1417,560,1504,547,1538,531,1566,507,1589,477,1604,474,1599,503,1585,527,1562,542,1536,555,1502,575,1416,590,1312,600,1199,609,982,609,833"/>
<area shape="poly" title=" " alt="" coords="354,39,272,58,271,53,353,34"/>
<area shape="poly" title=" " alt="" coords="152,90,94,109,92,104,150,85"/>
<area shape="poly" title=" " alt="" coords="165,178,78,136,80,131,168,173"/>
<area shape="poly" title=" " alt="" coords="460,1587,450,1581,429,1579,410,1581,400,1585,398,1591,402,1598,398,1601,392,1591,396,1582,409,1575,429,1573,451,1576,463,1582"/>
</map>
</div>

</div>
</div>
<a id="abac3042c22899daa9d6987d7f15e0185" name="abac3042c22899daa9d6987d7f15e0185"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abac3042c22899daa9d6987d7f15e0185">&#9670;&#160;</a></span>LogSetLevel()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogSetLevel </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>          <td class="paramname"><span class="paramname"><em>Level</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_3d69f64eaf81436fe2b22361382717e5.html">core</a></li><li class="navelem"><a href="dir_ebc4183974da10596d83fbd9697e69e1.html">log</a></li><li class="navelem"><a href="include_2core_2log_2driver__log_8h.html">driver_log.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
