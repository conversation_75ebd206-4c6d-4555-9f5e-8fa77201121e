graph TB
    subgraph "应用层 Application Layer"
        APP[用户应用程序]
        API[Windows API]
    end

    subgraph "系统层 System Layer"
        KERNEL[Windows Kernel]
        KMDF[KMDF Framework]
    end

    subgraph "KMDF Driver1 核心架构"
        subgraph "入口层 Entry Layer"
            ENTRY[DriverEntry]
            MAIN[driver_main.c]
        end

        subgraph "模块宇宙 Module Universe"
            REGISTRY[中央注册表]
            INTERFACE[I模块接口]
        end

        subgraph "主语层 Subject Layer - 核心实体"
            CORE[CoreManager]
            DEVICE[DeviceManager]
            DRIVER[DriverCore]
        end

        subgraph "动词层 Verb Layer - 操作模块"
            IO[I/O Operations]
            POWER[Power Management]
            ERROR[Error Handling]
            LOG[Logging System]
        end

        subgraph "宾语层 Object Layer - 数据对象"
            CONFIG[Configuration]
            STATE[Device State]
            BUFFER[Data Buffers]
            STATS[Statistics]
        end

        subgraph "出口接口层 Interface Layer"
            HAL[Hardware Abstraction Layer]
            BUS[Bus Interfaces]
        end
    end

    subgraph "硬件抽象层 HAL"
        I2C[I2C Controller]
        SPI[SPI Interface]
        USB[USB Manager]
        GPIO[GPIO Controller]
    end

    subgraph "智能优化系统 AI Optimization"
        ML[Machine Learning Engine]
        SIMD[SIMD Accelerator]
        SCHEDULER[Adaptive Scheduler]
        PREDICTOR[Performance Predictor]
    end

    subgraph "测试框架 Testing Framework"
        UNIT[Unit Tests]
        INTEGRATION[Integration Tests]
        PERFORMANCE[Performance Tests]
        SECURITY[Security Tests]
    end

    subgraph "开发工具链 Development Tools"
        BUILD[Build System]
        ANALYZE[Code Analyzer]
        DEBUG[Debug Tools]
        DOCS[Documentation Generator]
    end

    subgraph "物理硬件 Hardware"
        HW1[I2C Devices]
        HW2[SPI Devices]
        HW3[USB Devices]
        HW4[GPIO Pins]
    end

    %% 连接关系
    APP --> API
    API --> KERNEL
    KERNEL --> KMDF
    KMDF --> ENTRY

    ENTRY --> REGISTRY
    REGISTRY --> INTERFACE
    INTERFACE --> CORE
    INTERFACE --> DEVICE
    INTERFACE --> DRIVER

    CORE --> IO
    DEVICE --> POWER
    DRIVER --> ERROR
    ERROR --> LOG

    IO --> CONFIG
    POWER --> STATE
    ERROR --> BUFFER
    LOG --> STATS

    CONFIG --> HAL
    STATE --> BUS
    BUFFER --> HAL
    STATS --> BUS

    HAL --> I2C
    HAL --> SPI
    HAL --> USB
    HAL --> GPIO

    BUS --> I2C
    BUS --> SPI
    BUS --> USB
    BUS --> GPIO

    I2C --> HW1
    SPI --> HW2
    USB --> HW3
    GPIO --> HW4

    %% AI优化系统连接
    CORE --> ML
    DEVICE --> SCHEDULER
    IO --> SIMD
    POWER --> PREDICTOR

    %% 测试框架连接
    CORE -.-> UNIT
    DEVICE -.-> INTEGRATION
    IO -.-> PERFORMANCE
    ERROR -.-> SECURITY

    %% 开发工具连接
    ENTRY -.-> BUILD
    CORE -.-> ANALYZE
    ERROR -.-> DEBUG
    LOG -.-> DOCS

    %% 样式定义
    classDef entryLayer fill:#e1f5fe
    classDef coreLayer fill:#f3e5f5
    classDef verbLayer fill:#e8f5e8
    classDef objectLayer fill:#fff3e0
    classDef interfaceLayer fill:#fce4ec
    classDef halLayer fill:#f1f8e9
    classDef aiLayer fill:#e0f2f1
    classDef testLayer fill:#fff8e1
    classDef toolLayer fill:#f9fbe7
    classDef hardwareLayer fill:#efebe9

    class ENTRY,MAIN entryLayer
    class CORE,DEVICE,DRIVER coreLayer
    class IO,POWER,ERROR,LOG verbLayer
    class CONFIG,STATE,BUFFER,STATS objectLayer
    class HAL,BUS interfaceLayer
    class I2C,SPI,USB,GPIO halLayer
    class ML,SIMD,SCHEDULER,PREDICTOR aiLayer
    class UNIT,INTEGRATION,PERFORMANCE,SECURITY testLayer
    class BUILD,ANALYZE,DEBUG,DOCS toolLayer
    class HW1,HW2,HW3,HW4 hardwareLayer
