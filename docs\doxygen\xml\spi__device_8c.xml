<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="spi__device_8c" kind="file" language="C++">
    <compoundname>spi_device.c</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="hal__interface_8h" local="yes">../../../include/hal/hal_interface.h</includes>
    <includes refid="kmdf__spi_8h" local="yes">../../../include/hal/bus/kmdf_spi.h</includes>
    <includes refid="spi__device_8h" local="yes">../../../include/hal/devices/spi_device.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="5">
        <label>../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="9">
        <label>../../../include/core/log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="7">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>../../../include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="7" relation="include">
        </childnode>
      </node>
      <node id="8">
        <label>../../../include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../../../include/hal/hal_interface.h</label>
        <link refid="hal__interface_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="var">
      <memberdef kind="variable" id="spi__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" prot="public" static="no" mutable="no">
        <type>Exit</type>
        <definition>Exit __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="92" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="95" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" prot="public" static="no" mutable="no">
        <type>WDFMEMORY</type>
        <definition>deviceContext ConfigurationMemory</definition>
        <argsstring></argsstring>
        <name>ConfigurationMemory</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="19" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="19" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet DelayInMicroseconds</definition>
        <argsstring></argsstring>
        <name>DelayInMicroseconds</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="226" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="226" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1ae49d231a428d107c888f925e845daf62" prot="public" static="no" mutable="no">
        <type>BOOLEAN</type>
        <definition>Statistics DeviceInitialized</definition>
        <argsstring></argsstring>
        <name>DeviceInitialized</name>
        <initializer>= FALSE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="22" column="13" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="22" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1abf852046373359fb294f66a784b38263" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig DeviceType</definition>
        <argsstring></argsstring>
        <name>DeviceType</name>
        <initializer>= <ref refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab" kindref="member">HalDeviceSPI</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="78" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="78" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a0544c3fe466e421738dae463968b70ba" prot="public" static="no" mutable="no">
        <type></type>
        <definition>else</definition>
        <argsstring></argsstring>
        <name>else</name>
        <initializer>{
        InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" kindref="member">TransactionCount</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="182" column="6" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="182" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
        <definition>Statistics ErrorCount</definition>
        <argsstring></argsstring>
        <name>ErrorCount</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="57" column="18" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="57" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>packet Flags</definition>
        <argsstring></argsstring>
        <name>Flags</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="79" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="79" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" prot="public" static="no" mutable="no">
        <type><ref refid="hal__interface_8h_1a2f4ba870132c1fd57e2d74ba94e39805" kindref="member">HAL_DEVICE_HANDLE</ref></type>
        <definition>HAL_DEVICE_HANDLE HalHandle</definition>
        <argsstring></argsstring>
        <name>HalHandle</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="21" column="23" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="21" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig PrivateData</definition>
        <argsstring></argsstring>
        <name>PrivateData</name>
        <initializer>= <ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="80" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="80" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig PrivateDataSize</definition>
        <argsstring></argsstring>
        <name>PrivateDataSize</name>
        <initializer>= sizeof(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="81" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="81" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a2d4e25dc12a54c28261d5ba390e3aa19" prot="public" static="no" mutable="no">
        <type>*</type>
        <definition>* PSPI_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>PSPI_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="23" column="21" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="23" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1ac2677e024009c29e2bcee99e0c32c735" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet ReadBuffer</definition>
        <argsstring></argsstring>
        <name>ReadBuffer</name>
        <initializer>= Buffer</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="223" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="223" bodyend="-1"/>
        <referencedby refid="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f">SPIWriteRead</referencedby>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a1d0f6e5a27e5a31ee40de06efe3ba233" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet ReadLength</definition>
        <argsstring></argsstring>
        <name>ReadLength</name>
        <initializer>= Length</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="224" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="224" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1aa226b0d93154d552caefa2ca1550155c" prot="public" static="no" mutable="no">
        <type></type>
        <definition>SPI_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>SPI_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="23" column="2" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="23" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" prot="public" static="no" mutable="no">
        <type><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref></type>
        <definition>deviceContext SpiConfig</definition>
        <argsstring></argsstring>
        <name>SpiConfig</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="20" column="17" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="20" bodyend="-1"/>
        <referencedby refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5">RtlCopyMemory</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a9611b3a00430a86619b5923de30f9fdb" prot="public" static="no" mutable="no">
        <type></type>
        <definition>return status</definition>
        <argsstring></argsstring>
        <name>status</name>
        <initializer>= WdfMemoryCreate(
        WDF_NO_OBJECT_ATTRIBUTES,
        NonPagedPoolNx,
        0,
        sizeof(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>),
        &amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kindref="member">ConfigurationMemory</ref>,
        (PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>
    )</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="60" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="60" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a77b4762318f24dff847f94f382cfeea6" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return STATUS_SUCCESS</definition>
        <argsstring></argsstring>
        <name>STATUS_SUCCESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="326" column="12" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="326" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a63212990a463669c2face6cfbfd28d26" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
        <definition>Statistics TransactionCount</definition>
        <argsstring></argsstring>
        <name>TransactionCount</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="56" column="18" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="56" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>deviceContext WdfDevice</definition>
        <argsstring></argsstring>
        <name>WdfDevice</name>
        <initializer>= Device</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="18" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="18" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1ad26ded9b73e8b14b4117614b39440d86" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet WriteBuffer</definition>
        <argsstring></argsstring>
        <name>WriteBuffer</name>
        <initializer>= <ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="221" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="221" bodyend="-1"/>
        <referencedby refid="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f">SPIWriteRead</referencedby>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" prot="public" static="no" mutable="no">
        <type></type>
        <definition>RtlCopyMemory&amp;[1] writeBuffer</definition>
        <argsstring>[0]</argsstring>
        <name>writeBuffer</name>
        <initializer>= RegisterAddress</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="219" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="219" bodyend="-1"/>
        <referencedby refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
        <referencedby refid="spi__device_8c_1afff80b1a0000ef578da0277667a994ff" compoundref="spi__device_8c" startline="267" endline="270">if</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
      </memberdef>
      <memberdef kind="variable" id="spi__device_8c_1a530eca3d7e36c5dde60c5e49dd7b2b34" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet WriteLength</definition>
        <argsstring></argsstring>
        <name>WriteLength</name>
        <initializer>= 1</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="222" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="222" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>ExFreePoolWithTag</definition>
        <argsstring>(writeBuffer, &apos;SPIW&apos;)</argsstring>
        <name>ExFreePoolWithTag</name>
        <param>
          <type><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref></type>
        </param>
        <param>
          <type>&apos;SPIW&apos;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="289" column="5" declfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" declline="289" declcolumn="5"/>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(!NT_SUCCESS(status))</argsstring>
        <name>if</name>
        <param>
          <type>!</type>
          <declname>NT_SUCCESS</declname>
          <array>status</array>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="69" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="69" bodyend="72"/>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1ab29d05a3528131be0d35fe785e85590f" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;HalHandle !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kindref="member">HalHandle</ref> !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="100" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="100" bodyend="103"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1ac40f83943701ccbf4235e0c238583dfb" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;SpiConfig !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref> !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="321" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="321" bodyend="324"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext==NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="48" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="48" bodyend="51"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a4b5c92f0859e4be1ead5d71edc903427" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(NT_SUCCESS(status) &amp;&amp;BytesRead !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>) &amp;&amp;BytesRead !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="230" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="230" bodyend="232"/>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a164e77dd43f69d29ea926ae0ec42969b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(NT_SUCCESS(status) &amp;&amp;BytesWritten !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>) &amp;&amp;BytesWritten !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="284" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="284" bodyend="286"/>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1afff80b1a0000ef578da0277667a994ff" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(writeBuffer==NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref></type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="267" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="267" bodyend="270"/>
        <references refid="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" compoundref="error__codes_8h" startline="65">ERROR_NOT_ENOUGH_MEMORY</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a92b63e772873a034bea01d26f382ed57" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;SPI device initialized successfully&quot;)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;SPI device initialized successfully&quot;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="90" column="5" declfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" declline="90" declcolumn="5"/>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1aef91cc299dacb19ea73324acf8c8ff2c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;SPI transfer succeeded&quot;)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;SPI transfer succeeded&quot;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="184" column="9" declfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" declline="184" declcolumn="9"/>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>RtlCopyMemory</definition>
        <argsstring>(deviceContext-&gt;SpiConfig, SpiConfig, sizeof(SPI_CONFIG))</argsstring>
        <name>RtlCopyMemory</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;</type>
          <declname>SpiConfig</declname>
        </param>
        <param>
          <type><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></type>
        </param>
        <param>
          <type>sizeof(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>)</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="75" column="5" declfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" declline="75" declcolumn="5"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" compoundref="spi__device_8c" startline="20">SpiConfig</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID SpiDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>SpiDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceCleanup - u6e05u7406SPIu8bbeu5907u8d44u6e90</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="113" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="113" bodyend="142"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceGetStatistics</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PSPI_STATISTICS Statistics)</argsstring>
        <name>SpiDeviceGetStatistics</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ <ref refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kindref="member">PSPI_STATISTICS</ref></type>
          <declname>Statistics</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceGetStatistics - u83b7u53d6SPIu8bbeu5907u7edfu8ba1u4fe1u606f</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Statistics</parametername>
</parameternamelist>
<parameterdescription>
<para>u7edfu8ba1u4fe1u606fu7ed3u6784</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="298" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="298" bodyend="308"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</argsstring>
        <name>SpiDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref></type>
          <declname>SpiConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceInitialize - u521du59cbu5316SPIu8bbeu5907</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">SpiConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>SPIu914du7f6e</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="31" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="31" bodyend="45"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" compoundref="spi__device_8c" startline="20">SpiConfig</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceRead</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</argsstring>
        <name>SpiDeviceRead</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_writes_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesRead</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceRead - u4eceSu5907u8bfbu53d6u6570u636e</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>u5bc4u5b58u5668u5730u5740 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>u6570u636eu7f13u51b2u533a </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>u7f13u51b2u533au957fu5ea6 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesRead</parametername>
</parameternamelist>
<parameterdescription>
<para>u5b9eu9645u8bfbu53d6u7684u5b57u8282u6570</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="194" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="194" bodyend="210"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceTransfer</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_DEVICE_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</argsstring>
        <name>SpiDeviceTransfer</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kindref="member">PSPI_DEVICE_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceTransfer - u6267u884cSPIu4f20u8f93</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TransferPacket</parametername>
</parameternamelist>
<parameterdescription>
<para>u4f20u8f93u6570u636eu5305 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TimeoutMs</parametername>
</parameternamelist>
<parameterdescription>
<para>u8d85u65f6u65f6u95f4(u6bebu79d2)</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="148" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="148" bodyend="160"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceWrite</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</argsstring>
        <name>SpiDeviceWrite</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_reads_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesWritten</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceWrite - u5411SPIu8bbeu5907u5199u5165u6570u636e</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>u5bc4u5b58u5668u5730u5740 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>u6570u636eu7f13u51b2u533a </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>u7f13u51b2u533au957fu5ea6 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesWritten</parametername>
</parameternamelist>
<parameterdescription>
<para>u5b9eu9645u5199u5165u7684u5b57u8282u6570</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c" line="241" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="241" bodyend="257"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>spi_device.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>SPI鐠佹儳顦す鍗炲З鐎圭偟骞?</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>閹绘劒绶甸柅姘辨暏SPI鐠佹儳顦惃鍕徔娴ｆ挸鐤勯悳甯礉閸╄桨绨幋鎴滄粦閻ㄥ嫭膩閸ф瀵查弸鑸电€?</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="hal__interface_8h" kindref="compound">../../../include/hal/hal_interface.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__spi_8h" kindref="compound">../../../include/hal/bus/kmdf_spi.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="spi__device_8h" kindref="compound">../../../include/hal/devices/spi_device.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI鐠佹儳顦稉濠佺瑓閺傚洨绮ㄩ弸?typedef<sp/>struct<sp/>_SPI_DEVICE_CONTEXT<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="spi__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF鐠佹儳顦€电钖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="spi__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFMEMORY<sp/><ref refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kindref="member">ConfigurationMemory</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹儳顦柊宥囩枂閸愬懎鐡?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPI闁板秶鐤?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="spi__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a2f4ba870132c1fd57e2d74ba94e39805" kindref="member">HAL_DEVICE_HANDLE</ref><sp/><ref refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kindref="member">HalHandle</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>HAL鐠佹儳顦崣銉︾労</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="spi__device_8c_1ae49d231a428d107c888f925e845daf62" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="i2c__device_8c_1ae49d231a428d107c888f925e845daf62" kindref="member">DeviceInitialized</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹儳顦崚婵嗩潗閸栨牗鐖ｈ箛?<sp/><sp/><sp/><sp/>ULONG<sp/>TransactionCount;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>閹绨ㄩ崝陇顓搁弫?<sp/><sp/><sp/><sp/>ULONG<sp/>ErrorCount;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>闁挎瑨顕ょ拋鈩冩殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="spi__device_8c_1a2d4e25dc12a54c28261d5ba390e3aa19" refkind="member"><highlight class="normal">}<sp/><ref refid="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" kindref="member">SPI_DEVICE_CONTEXT</ref>,<sp/>*<ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref>;</highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//<sp/>娴犲豆DF鐠佹儳顦懢宄板絿SPI鐠佹儳顦稉濠佺瑓閺?WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(SPI_DEVICE_CONTEXT,<sp/>GetSpiDeviceContext)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="28"><highlight class="comment"><sp/>*<sp/>SpiDeviceInitialize<sp/>-<sp/>閸掓繂顫愰崠鏈I鐠佹儳顦?</highlight></codeline>
<codeline lineno="29"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="31" refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" kindref="member">SpiDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="32"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="33"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></highlight></codeline>
<codeline lineno="34"><highlight class="normal">)</highlight></codeline>
<codeline lineno="35"><highlight class="normal">{</highlight></codeline>
<codeline lineno="36"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="37"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="38"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a9de221d82717e7709cdfc025378bc222" kindref="member">HAL_DEVICE_CONFIG</ref><sp/>halConfig<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="39"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="40"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>SPI<sp/>device&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="41"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="42"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>SpiConfig<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="spi__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_UNSUCCESSFUL;</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍啎婢跺洣绗傛稉瀣瀮</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="56" refid="spi__device_8c_1a63212990a463669c2face6cfbfd28d26" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="57" refid="spi__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂揝PI闁板秶鐤嗛崘鍛摠閸擃垱婀?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="60" refid="spi__device_8c_1a9611b3a00430a86619b5923de30f9fdb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfMemoryCreate(</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_NO_OBJECT_ATTRIBUTES,</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NonPagedPoolNx,</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0,</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>),</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory,</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="69" refid="spi__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>configuration<sp/>memory&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢跺秴鍩桽PI闁板秶鐤?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75" refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5" kindref="member">RtlCopyMemory</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig,<sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>));</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>闁板秶鐤咹AL鐠佹儳顦?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="78" refid="spi__device_8c_1abf852046373359fb294f66a784b38263" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.DeviceType<sp/>=<sp/><ref refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab" kindref="member">HalDeviceSPI</ref>;</highlight></codeline>
<codeline lineno="79" refid="spi__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.Flags<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="80" refid="spi__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.PrivateData<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig;</highlight></codeline>
<codeline lineno="81" refid="spi__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.PrivateDataSize<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>);</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍啎婢跺槗AL鐏?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>HalDeviceOpen(Device,<sp/>&amp;halConfig,<sp/>&amp;deviceContext-&gt;HalHandle);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>open<sp/>HAL<sp/>device&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐎瑰本鍨氶崚婵嗩潗閸?<sp/><sp/><sp/><sp/>deviceContext-&gt;DeviceInitialized<sp/>=<sp/>TRUE;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="90" refid="spi__device_8c_1a92b63e772873a034bea01d26f382ed57" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>device<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="92" refid="spi__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" refkind="member"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="100" refid="spi__device_8c_1ab29d05a3528131be0d35fe785e85590f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189" kindref="member">HalDeviceClose</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle);</highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="107"><highlight class="normal">}</highlight></codeline>
<codeline lineno="108"><highlight class="normal"></highlight></codeline>
<codeline lineno="109"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="110"><highlight class="comment"><sp/>*<sp/>SpiDeviceCleanup<sp/>-<sp/>濞撳懐鎮奡PI鐠佹儳顦挧鍕爱</highlight></codeline>
<codeline lineno="111"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="112"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="113" refid="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" kindref="member">SpiDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="115"><highlight class="normal">)</highlight></codeline>
<codeline lineno="116"><highlight class="normal">{</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>SPI<sp/>device<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸忔娊妫碒AL鐠佹儳顦崣銉︾労</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189" kindref="member">HalDeviceClose</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle);</highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掔娀娅庨柊宥囩枂閸愬懎鐡?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="135"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory);</highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>device<sp/>cleanup<sp/>completed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="142"><highlight class="normal">}</highlight></codeline>
<codeline lineno="143"><highlight class="normal"></highlight></codeline>
<codeline lineno="144"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="145"><highlight class="comment"><sp/>*<sp/>SpiDeviceTransfer<sp/>-<sp/>閹笛嗩攽SPI娴肩姾绶?</highlight></codeline>
<codeline lineno="146"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="147"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="148" refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" kindref="member">SpiDeviceTransfer</ref>(</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kindref="member">PSPI_DEVICE_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="152"><highlight class="normal">)</highlight></codeline>
<codeline lineno="153"><highlight class="normal">{</highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>TransferPacket<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽SPI娴肩姾绶?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="hal__interface_8h_1ab3ade1341d2d05db754ab4a9e3ee7198" kindref="member">HalDeviceIoControl</ref>(</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle,</highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8h_1a72feec97101aca3be161a59ffe40cc2c" kindref="member">IOCTL_SPI_TRANSFER</ref>,</highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" kindref="member">SPI_DEVICE_TRANSFER_PACKET</ref>),</highlight></codeline>
<codeline lineno="174"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,</highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0,</highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL</highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="180"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount);</highlight></codeline>
<codeline lineno="181"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>transfer<sp/>failed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="182" refid="spi__device_8c_1a0544c3fe466e421738dae463968b70ba" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="183"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount);</highlight></codeline>
<codeline lineno="184" refid="spi__device_8c_1aef91cc299dacb19ea73324acf8c8ff2c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>transfer<sp/>succeeded&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="185"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="186"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="188"><highlight class="normal">}</highlight></codeline>
<codeline lineno="189"><highlight class="normal"></highlight></codeline>
<codeline lineno="190"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="191"><highlight class="comment"><sp/>*<sp/>SpiDeviceRead<sp/>-<sp/>娴犲洞PI鐠佹儳顦拠璇插絿閺佺増宓?</highlight></codeline>
<codeline lineno="192"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="193"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="194" refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" kindref="member">SpiDeviceRead</ref>(</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesRead</highlight></codeline>
<codeline lineno="200"><highlight class="normal">)</highlight></codeline>
<codeline lineno="201"><highlight class="normal">{</highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" kindref="member">SPI_DEVICE_TRANSFER_PACKET</ref><sp/>packet<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[1];</highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Buffer<sp/>==<sp/>NULL<sp/>||<sp/>Length<sp/>==<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="210"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="212"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="213"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="217"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="218"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸戝棗顦崘娆戠处閸愭彃灏崪灞肩炊鏉堟挸瀵?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="219" refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[0]<sp/>=<sp/>RegisterAddress;</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="221" refid="spi__device_8c_1ad26ded9b73e8b14b4117614b39440d86" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.WriteBuffer<sp/>=<sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>;</highlight></codeline>
<codeline lineno="222" refid="spi__device_8c_1a530eca3d7e36c5dde60c5e49dd7b2b34" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.WriteLength<sp/>=<sp/>1;</highlight></codeline>
<codeline lineno="223" refid="spi__device_8c_1ac2677e024009c29e2bcee99e0c32c735" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.ReadBuffer<sp/>=<sp/>Buffer;</highlight></codeline>
<codeline lineno="224" refid="spi__device_8c_1a1d0f6e5a27e5a31ee40de06efe3ba233" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.ReadLength<sp/>=<sp/>Length;</highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Flags<sp/>=<sp/><ref refid="spi__device_8h_1a935c3756801c209968fb16a7be795396" kindref="member">SPI_TRANSFER_READ</ref>;</highlight></codeline>
<codeline lineno="226" refid="spi__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DelayInMicroseconds<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" kindref="member">SpiDeviceTransfer</ref>(Device,<sp/>&amp;packet,<sp/>1000);</highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="230" refid="spi__device_8c_1a4b5c92f0859e4be1ead5d71edc903427" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>&amp;&amp;<sp/>BytesRead<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>*BytesRead<sp/>=<sp/>Length;</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="235"><highlight class="normal">}</highlight></codeline>
<codeline lineno="236"><highlight class="normal"></highlight></codeline>
<codeline lineno="237"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="238"><highlight class="comment"><sp/>*<sp/>SpiDeviceWrite<sp/>-<sp/>閸氭叀PI鐠佹儳顦崘娆忓弳閺佺増宓?</highlight></codeline>
<codeline lineno="239"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="240"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="241" refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" kindref="member">SpiDeviceWrite</ref>(</highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="244"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="245"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesWritten</highlight></codeline>
<codeline lineno="247"><highlight class="normal">)</highlight></codeline>
<codeline lineno="248"><highlight class="normal">{</highlight></codeline>
<codeline lineno="249"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="250"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="251"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" kindref="member">SPI_DEVICE_TRANSFER_PACKET</ref><sp/>packet<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="252"><highlight class="normal"><sp/><sp/><sp/><sp/>PUCHAR<sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="253"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="254"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Buffer<sp/>==<sp/>NULL<sp/>||<sp/>Length<sp/>==<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="255"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="256"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="261"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="264"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="265"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掑棝鍘ら崘娆戠处閸愭彃灏?(閸栧懎鎯堢€靛嫬鐡ㄩ崳銊ユ勾閸р偓閸滃本鏆熼幑?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="266"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref><sp/>=<sp/>ExAllocatePool2(POOL_FLAG_NON_PAGED,<sp/>Length<sp/>+<sp/>1,<sp/></highlight><highlight class="stringliteral">&apos;SPIW&apos;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="267" refid="spi__device_8c_1afff80b1a0000ef578da0277667a994ff" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="268"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" kindref="member">ERROR_NOT_ENOUGH_MEMORY</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>allocate<sp/>write<sp/>buffer&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="269"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INSUFFICIENT_RESOURCES;</highlight></codeline>
<codeline lineno="270"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="272"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵夘偄鍘栫紓鎾冲暱閸?<sp/><sp/><sp/><sp/>writeBuffer[0]<sp/>=<sp/>RegisterAddress;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="273"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[1],<sp/>Buffer,<sp/>Length);</highlight></codeline>
<codeline lineno="274"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸戝棗顦导鐘虹翻閸?<sp/><sp/><sp/><sp/>packet.WriteBuffer<sp/>=<sp/>writeBuffer;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.WriteLength<sp/>=<sp/>Length<sp/>+<sp/>1;</highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.ReadBuffer<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="278"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.ReadLength<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="279"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Flags<sp/>=<sp/><ref refid="spi__device_8h_1af27a6537c3222c6796876ff953298b42" kindref="member">SPI_TRANSFER_WRITE</ref>;</highlight></codeline>
<codeline lineno="280"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DelayInMicroseconds<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="281"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="282"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" kindref="member">SpiDeviceTransfer</ref>(Device,<sp/>&amp;packet,<sp/>1000);</highlight></codeline>
<codeline lineno="283"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="284" refid="spi__device_8c_1a164e77dd43f69d29ea926ae0ec42969b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>&amp;&amp;<sp/>BytesWritten<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="285"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>*BytesWritten<sp/>=<sp/>Length;</highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="288"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞撳懐鎮婄挧鍕爱</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="289" refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>,<sp/></highlight><highlight class="stringliteral">&apos;SPIW&apos;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="290"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="292"><highlight class="normal">}</highlight></codeline>
<codeline lineno="293"><highlight class="normal"></highlight></codeline>
<codeline lineno="294"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="295"><highlight class="comment"><sp/>*<sp/>SpiDeviceGetStatistics<sp/>-<sp/>閼惧嘲褰嘢PI鐠佹儳顦紒鐔活吀娣団剝浼?</highlight></codeline>
<codeline lineno="296"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="297"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="298" refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" refkind="member"><highlight class="normal"><ref refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" kindref="member">SpiDeviceGetStatistics</ref>(</highlight></codeline>
<codeline lineno="299"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="300"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kindref="member">PSPI_STATISTICS</ref><sp/>Statistics</highlight></codeline>
<codeline lineno="301"><highlight class="normal">)</highlight></codeline>
<codeline lineno="302"><highlight class="normal">{</highlight></codeline>
<codeline lineno="303"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="305"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Statistics<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="306"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="308"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="309"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="310"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetSpiDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="311"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="312"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="313"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_UNSUCCESSFUL;</highlight></codeline>
<codeline lineno="314"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="315"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="316"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵夘偄鍘栫紒鐔活吀娣団剝浼?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="317"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;TransactionCount<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount;</highlight></codeline>
<codeline lineno="318"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;ErrorCount<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount;</highlight></codeline>
<codeline lineno="319"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;DeviceInitialized<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized;</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="321" refid="spi__device_8c_1ac40f83943701ccbf4235e0c238583dfb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Statistics-&gt;ClockFrequency<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig-&gt;MaxClockFrequency;</highlight></codeline>
<codeline lineno="323"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Statistics-&gt;Mode<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpiConfig-&gt;Mode;</highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="325"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="326" refid="spi__device_8c_1a77b4762318f24dff847f94f382cfeea6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="327"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/devices/spi_device.c"/>
  </compounddef>
</doxygen>
