<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="gpio__device_8h" kind="file" language="C++">
    <compoundname>gpio_device.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="kmdf__gpio_8h" local="yes">../bus/kmdf_gpio.h</includes>
    <includedby refid="gpio__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/gpio_device.c</includedby>
    <incdepgraph>
      <node id="6">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__GPIO__DEVICE__CONFIG" prot="public">_GPIO_DEVICE_CONFIG</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" prot="public" static="no">
        <name>IOCTL_GPIO_DEVICE_BASE</name>
        <initializer>0x8300</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="67" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="67" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="gpio__device_8h_1a531a7dc21007b6e72d524a9fa29de43d" prot="public" static="no">
        <name>IOCTL_GPIO_DEVICE_GET_STATE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" kindref="member">IOCTL_GPIO_DEVICE_BASE</ref> + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="69" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="69" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="gpio__device_8h_1ada8c56039bf34983b27bd0338982f351" prot="public" static="no">
        <name>IOCTL_GPIO_DEVICE_REGISTER_CALLBACK</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" kindref="member">IOCTL_GPIO_DEVICE_BASE</ref> + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="70" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="70" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="gpio__device_8h_1a15338fabc6809209e45575e75f4867e4" prot="public" static="no">
        <name>IOCTL_GPIO_DEVICE_SET_STATE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" kindref="member">IOCTL_GPIO_DEVICE_BASE</ref> + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="68" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="68" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0f" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_DEVICE_EVENT</name>
        <enumvalue id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fade992ff1fe1b3d791821a97aaafd5789" prot="public">
          <name>GpioEventPress</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa3b928ecf3b3a5198bb6a552f62814d3e" prot="public">
          <name>GpioEventRelease</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d" prot="public">
          <name>GpioEventOn</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652" prot="public">
          <name>GpioEventOff</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa71136dfd97b017cb0b6288ef5889cf71" prot="public">
          <name>GpioEventChange</name>
          <initializer>= 4</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="48" column="1" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="48" bodyend="54"/>
      </memberdef>
      <memberdef kind="enum" id="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_DEVICE_STATE</name>
        <enumvalue id="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" prot="public">
          <name>GpioStateUnknown</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" prot="public">
          <name>GpioStateOn</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" prot="public">
          <name>GpioStateOff</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" prot="public">
          <name>GpioStateError</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="28" column="1" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="28" bodyend="33"/>
      </memberdef>
      <memberdef kind="enum" id="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_DEVICE_TYPE</name>
        <enumvalue id="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" prot="public">
          <name>GpioTypeGeneric</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a9f11eddd6db1aa56c00b47541c54802f" prot="public">
          <name>GpioTypeButton</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" prot="public">
          <name>GpioTypeLed</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6af84e71ef287fe86c36e80f06a7826d7f" prot="public">
          <name>GpioTypeSensor</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="20" column="1" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="20" bodyend="25"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" prot="public" static="no">
        <type>struct <ref refid="struct__GPIO__DEVICE__CONFIG" kindref="compound">_GPIO_DEVICE_CONFIG</ref></type>
        <definition>typedef struct _GPIO_DEVICE_CONFIG GPIO_DEVICE_CONFIG</definition>
        <argsstring></argsstring>
        <name>GPIO_DEVICE_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="45" column="20"/>
      </memberdef>
      <memberdef kind="typedef" id="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" prot="public" static="no">
        <type>enum <ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0f" kindref="member">_GPIO_DEVICE_EVENT</ref></type>
        <definition>typedef enum _GPIO_DEVICE_EVENT GPIO_DEVICE_EVENT</definition>
        <argsstring></argsstring>
        <name>GPIO_DEVICE_EVENT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="54" column="19"/>
      </memberdef>
      <memberdef kind="typedef" id="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" prot="public" static="no">
        <type>VOID(*</type>
        <definition>typedef VOID(* GPIO_DEVICE_EVENT_CALLBACK) (_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT Event, _In_ ULONG PinValue, _In_opt_ PVOID Context)</definition>
        <argsstring>)(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT Event, _In_ ULONG PinValue, _In_opt_ PVOID Context)</argsstring>
        <name>GPIO_DEVICE_EVENT_CALLBACK</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="58" column="1" bodyfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" bodystart="59" bodyend="-1"/>
      </memberdef>
      <memberdef kind="typedef" id="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" prot="public" static="no">
        <type>enum <ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286" kindref="member">_GPIO_DEVICE_STATE</ref></type>
        <definition>typedef enum _GPIO_DEVICE_STATE GPIO_DEVICE_STATE</definition>
        <argsstring></argsstring>
        <name>GPIO_DEVICE_STATE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="33" column="19"/>
      </memberdef>
      <memberdef kind="typedef" id="gpio__device_8h_1a2ddd51479fb5937e5f4ba2afa3e1504b" prot="public" static="no">
        <type>enum <ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6" kindref="member">_GPIO_DEVICE_TYPE</ref></type>
        <definition>typedef enum _GPIO_DEVICE_TYPE GPIO_DEVICE_TYPE</definition>
        <argsstring></argsstring>
        <name>GPIO_DEVICE_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="25" column="18"/>
      </memberdef>
      <memberdef kind="typedef" id="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" prot="public" static="no">
        <type>struct <ref refid="struct__GPIO__DEVICE__CONFIG" kindref="compound">_GPIO_DEVICE_CONFIG</ref> *</type>
        <definition>typedef struct _GPIO_DEVICE_CONFIG * PGPIO_DEVICE_CONFIG</definition>
        <argsstring></argsstring>
        <name>PGPIO_DEVICE_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="45" column="41"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="gpio__device_8h_1ac3d3347067d7be6497e71b6ba4b389c2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID GpioDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>GpioDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceCleanup - \u6e05\u7406GPIO\u8bbe\u5907\u8d44\u6e90</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61</para>
</parameterdescription>
</parameteritem>
</parameterlist>
GpioDeviceCleanup - 濞撳懐鎮奊PIO鐠佹儳顦挧鍕爱 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="96" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="271" bodyend="286" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="96" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1a0bd26e3410adfbb26bba326602f8fec6" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceGetState</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PGPIO_DEVICE_STATE State)</argsstring>
        <name>GpioDeviceGetState</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ PGPIO_DEVICE_STATE</type>
          <declname>State</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceGetState - \u83b7\u53d6GPIO\u8bbe\u5907\u72b6\u6001</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">State</parametername>
</parameternamelist>
<parameterdescription>
<para>\u7528\u4e8e\u5b58\u50a8\u72b6\u6001\u7684\u53d8\u91cf</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDeviceGetState - 閼惧嘲褰嘒PIO鐠佹儳顦悩鑸碘偓? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="123" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="367" bodyend="385" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="123" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1a21428e126bf8c8996ea3405e6a8be2f2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PGPIO_DEVICE_CONFIG GpioConfig)</argsstring>
        <name>GpioDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kindref="member">PGPIO_DEVICE_CONFIG</ref></type>
          <declname>GpioConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceInitialize - \u521d\u59cb\u5316GPIO\u8bbe\u5907</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">GpioConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>GPIO\u914d\u7f6e</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDeviceInitialize - 閸掓繂顫愰崠鏈慞IO鐠佹儳顦? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="85" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="160" bodyend="177" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="85" declcolumn="1"/>
        <references refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" compoundref="gpio__core_8c" startline="176">attributes</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54">pinConfig</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1a1e860d4292f8df84e5d3101f8a415d6c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDevicePulse</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PulseDurationMs)</argsstring>
        <name>GpioDevicePulse</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PulseDurationMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDevicePulse - \u8bbe\u5907\u53d1\u51fa\u8131\u51b2\u8131\u51b2</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">PulseDurationMs</parametername>
</parameternamelist>
<parameterdescription>
<para>\u8131\u51b2\u6301\u7eed\u65f6\u95f4(ms)</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDevicePulse - 鐠佹儳顦崣鎴濆毉閼村鍟块懘澶婂暱 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="165" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="445" bodyend="491" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="165" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" compoundref="kmdf__gpio_8h" startline="16">GpioDirectionOut</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" compoundref="gpio__device_8h" startline="31">GpioStateOff</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" compoundref="gpio__device_8h" startline="30">GpioStateOn</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1a6448a7e48d735f67501f3273b75485fd" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceRegisterCallback</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT_CALLBACK Callback, _In_opt_ PVOID Context)</argsstring>
        <name>GpioDeviceRegisterCallback</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kindref="member">GPIO_DEVICE_EVENT_CALLBACK</ref></type>
          <declname>Callback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceRegisterCallback - \u6ce8\u518cGPIO\u8bbe\u5907\u4e8b\u4ef6\u56de\u8c03</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Callback</parametername>
</parameternamelist>
<parameterdescription>
<para>\u56de\u8c03\u51fd\u6570 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Context</parametername>
</parameternamelist>
<parameterdescription>
<para>\u56de\u8c03\u4e0a\u4e0b\u6587</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDeviceRegisterCallback - 濞夈劌鍞紾PIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="138" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="391" bodyend="413" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="138" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1a731812dec996a670e7d557a282535d3d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceSetState</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_STATE State)</argsstring>
        <name>GpioDeviceSetState</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref></type>
          <declname>State</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceSetState - \u8bbe\u7f6eGPIO\u8bbe\u5907\u72b6\u6001</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">State</parametername>
</parameternamelist>
<parameterdescription>
<para>\u8981\u8bbe\u7f6e\u7684\u72b6\u6001</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDeviceSetState - 鐠佸墽鐤咷PIO鐠佹儳顦悩鑸碘偓? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="109" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="302" bodyend="340" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="109" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" compoundref="kmdf__gpio_8h" startline="16">GpioDirectionOut</references>
        <references refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" compoundref="kmdf__gpio_8h" startline="22">GpioPolarityActiveHigh</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" compoundref="gpio__device_8h" startline="32">GpioStateError</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" compoundref="gpio__device_8h" startline="31">GpioStateOff</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" compoundref="gpio__device_8h" startline="30">GpioStateOn</references>
        <references refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" compoundref="gpio__device_8h" startline="29">GpioStateUnknown</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" compoundref="gpio__device_8c" startline="245">pinValue</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="gpio__device_8h_1afbd2bd91a99c594504ca275fd8b45825" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS GpioDeviceUnregisterCallback</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>GpioDeviceUnregisterCallback</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>GpioDeviceUnregisterCallback - \u53d6\u6d88\u6ce8\u518cGPIO\u8bbe\u5907\u4e8b\u4ef6\u56de\u8c03</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF\u8bbe\u5907\u5bf9\u8c61</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS \u72b6\u6001\u7801</para>
</simplesect>
GpioDeviceUnregisterCallback - 閸欐牗绉峰▔銊ュ斀GPIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h" line="152" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/gpio_device.c" bodystart="419" bodyend="439" declfile="C:/KMDF Driver1/include/hal/devices/gpio_device.h" declline="152" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>gpio_device.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>GPIO\u8bbe\u5907\u9a71\u52a8\u63a5\u53e3\u5934\u6587\u4ef6</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>\u63d0\u4f9bGPIO\u8bbe\u5907\u64cd\u4f5c\u7684\u7edf\u4e00\u63a5\u53e3</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>GPIO_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>GPIO_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__gpio_8h" kindref="compound">../bus/kmdf_gpio.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="comment">//<sp/>\u5e38\u91cf\u548c\u7ed3\u6784\u5b9a\u4e49</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u8bbe\u5907\u7c7b\u578b</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6" kindref="member">_GPIO_DEVICE_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="21" refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" kindref="member">GpioTypeGeneric</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u901a\u7528GPIO\u8bbe\u5907</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a9f11eddd6db1aa56c00b47541c54802f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a9f11eddd6db1aa56c00b47541c54802f" kindref="member">GpioTypeButton</ref><sp/>=<sp/>1,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u6309\u94ae</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" kindref="member">GpioTypeLed</ref><sp/>=<sp/>2,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>LED</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6af84e71ef287fe86c36e80f06a7826d7f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6af84e71ef287fe86c36e80f06a7826d7f" kindref="member">GpioTypeSensor</ref><sp/>=<sp/>3<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u4f20\u611f\u5668</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="gpio__device_8h_1a2ddd51479fb5937e5f4ba2afa3e1504b" refkind="member"><highlight class="normal">}<sp/><ref refid="gpio__device_8h_1a2ddd51479fb5937e5f4ba2afa3e1504b" kindref="member">GPIO_DEVICE_TYPE</ref>;</highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u8bbe\u5907\u72b6\u6001</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286" kindref="member">_GPIO_DEVICE_STATE</ref><sp/>{</highlight></codeline>
<codeline lineno="29" refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kindref="member">GpioStateUnknown</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u672a\u77e5\u72b6\u6001</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kindref="member">GpioStateOn</ref><sp/>=<sp/>1,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5f00\u542f\u72b6\u6001</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" kindref="member">GpioStateOff</ref><sp/>=<sp/>2,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5173\u95ed\u72b6\u6001</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" kindref="member">GpioStateError</ref><sp/>=<sp/>3<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u9519\u8bef\u72b6\u6001</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33" refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" refkind="member"><highlight class="normal">}<sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref>;</highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u8bbe\u5907\u914d\u7f6e\u7ed3\u6784</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__GPIO__DEVICE__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__GPIO__DEVICE__CONFIG" kindref="compound">_GPIO_DEVICE_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="37" refid="struct__GPIO__DEVICE__CONFIG_1a6a8cbfb763fb3333f78514eb57a6f9d1" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1a2ddd51479fb5937e5f4ba2afa3e1504b" kindref="member">GPIO_DEVICE_TYPE</ref><sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1a6a8cbfb763fb3333f78514eb57a6f9d1" kindref="member">DeviceType</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u8bbe\u5907\u7c7b\u578b</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__GPIO__DEVICE__CONFIG_1a8070a58e69d1707fa6295dbe6709ea58" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1a8070a58e69d1707fa6295dbe6709ea58" kindref="member">PinNumber</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>GPIO\u5f15\u811a\u7f16\u53f7</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="struct__GPIO__DEVICE__CONFIG_1afd346bb1662b8fd0787e95a58459801b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kindref="member">GPIO_PIN_DIRECTION</ref><sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1afd346bb1662b8fd0787e95a58459801b" kindref="member">Direction</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5f15\u811a\u65b9\u5411</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="struct__GPIO__DEVICE__CONFIG_1a612f8dcb2e2376c49891b7f8f80460ce" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" kindref="member">GPIO_PIN_POLARITY</ref><sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1a612f8dcb2e2376c49891b7f8f80460ce" kindref="member">Polarity</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u6781\u6027</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="struct__GPIO__DEVICE__CONFIG_1aa569540357376717aca1aa0d6c94a6c4" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1aa569540357376717aca1aa0d6c94a6c4" kindref="member">EnableInterrupt</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u662f\u5426\u542f\u7528\u4e2d\u65ad</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="struct__GPIO__DEVICE__CONFIG_1aed18d39f9d22f9f8fb5c65dc0c1887d8" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kindref="member">GPIO_INTERRUPT_TYPE</ref><sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1aed18d39f9d22f9f8fb5c65dc0c1887d8" kindref="member">InterruptType</ref>;<sp/></highlight><highlight class="comment">//<sp/>\u4e2d\u65ad\u7c7b\u578b</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="struct__GPIO__DEVICE__CONFIG_1aede019215cfc30d3e4610984d3903eb5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1aede019215cfc30d3e4610984d3903eb5" kindref="member">DebounceTime</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u53bb\u6296\u65f6\u95f4(ms)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="struct__GPIO__DEVICE__CONFIG_1a664ba2cda132fa14533710f1c3f822b4" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__GPIO__DEVICE__CONFIG_1a664ba2cda132fa14533710f1c3f822b4" kindref="member">DeviceContext</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u8bbe\u5907\u7279\u5b9a\u4e0a\u4e0b\u6587</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" refkind="member"><highlight class="normal">}<sp/><ref refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kindref="member">GPIO_DEVICE_CONFIG</ref>,<sp/>*<ref refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kindref="member">PGPIO_DEVICE_CONFIG</ref>;</highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u4e8b\u4ef6\u7c7b\u578b</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0f" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0f" kindref="member">_GPIO_DEVICE_EVENT</ref><sp/>{</highlight></codeline>
<codeline lineno="49" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fade992ff1fe1b3d791821a97aaafd5789" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fade992ff1fe1b3d791821a97aaafd5789" kindref="member">GpioEventPress</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u6309\u4e0b\u4e8b\u4ef6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa3b928ecf3b3a5198bb6a552f62814d3e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa3b928ecf3b3a5198bb6a552f62814d3e" kindref="member">GpioEventRelease</ref><sp/>=<sp/>1,<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u91ca\u653e\u4e8b\u4ef6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d" kindref="member">GpioEventOn</ref><sp/>=<sp/>2,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5f00\u542f\u4e8b\u4ef6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652" kindref="member">GpioEventOff</ref><sp/>=<sp/>3,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5173\u95ed\u4e8b\u4ef6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53" refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa71136dfd97b017cb0b6288ef5889cf71" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa71136dfd97b017cb0b6288ef5889cf71" kindref="member">GpioEventChange</ref><sp/>=<sp/>4<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u72b6\u6001\u53d8\u5316\u4e8b\u4ef6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54" refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" refkind="member"><highlight class="normal">}<sp/><ref refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" kindref="member">GPIO_DEVICE_EVENT</ref>;</highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u8bbe\u5907\u4e8b\u4ef6\u56de\u8c03</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58" refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" refkind="member"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="59"><highlight class="normal">(*<ref refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kindref="member">GPIO_DEVICE_EVENT_CALLBACK</ref>)(</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" kindref="member">GPIO_DEVICE_EVENT</ref><sp/>Event,</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>PinValue,</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="65"><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO<sp/>IOCTL<sp/>\u63a7\u5236\u7801</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="67" refid="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_DEVICE_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0x8300</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="68" refid="gpio__device_8h_1a15338fabc6809209e45575e75f4867e4" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_DEVICE_SET_STATE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_GPIO_DEVICE_BASE<sp/>+<sp/>0,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="69" refid="gpio__device_8h_1a531a7dc21007b6e72d524a9fa29de43d" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_DEVICE_GET_STATE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_GPIO_DEVICE_BASE<sp/>+<sp/>1,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="70" refid="gpio__device_8h_1ada8c56039bf34983b27bd0338982f351" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_DEVICE_REGISTER_CALLBACK<sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_GPIO_DEVICE_BASE<sp/>+<sp/>2,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"></highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u8bbe\u5907\u63a5\u53e3\u51fd\u6570\u58f0\u660e</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"></highlight></codeline>
<codeline lineno="84"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><ref refid="gpio__device_8h_1a21428e126bf8c8996ea3405e6a8be2f2" kindref="member">GpioDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kindref="member">PGPIO_DEVICE_CONFIG</ref><sp/>GpioConfig</highlight></codeline>
<codeline lineno="88"><highlight class="normal">);</highlight></codeline>
<codeline lineno="89"><highlight class="normal"></highlight></codeline>
<codeline lineno="95"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><ref refid="gpio__device_8h_1ac3d3347067d7be6497e71b6ba4b389c2" kindref="member">GpioDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="98"><highlight class="normal">);</highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight></codeline>
<codeline lineno="108"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><ref refid="gpio__device_8h_1a731812dec996a670e7d557a282535d3d" kindref="member">GpioDeviceSetState</ref>(</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kindref="member">GPIO_DEVICE_STATE</ref><sp/>State</highlight></codeline>
<codeline lineno="112"><highlight class="normal">);</highlight></codeline>
<codeline lineno="113"><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><ref refid="gpio__device_8h_1a0bd26e3410adfbb26bba326602f8fec6" kindref="member">GpioDeviceGetState</ref>(</highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PGPIO_DEVICE_STATE<sp/>State</highlight></codeline>
<codeline lineno="126"><highlight class="normal">);</highlight></codeline>
<codeline lineno="127"><highlight class="normal"></highlight></codeline>
<codeline lineno="137"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><ref refid="gpio__device_8h_1a6448a7e48d735f67501f3273b75485fd" kindref="member">GpioDeviceRegisterCallback</ref>(</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kindref="member">GPIO_DEVICE_EVENT_CALLBACK</ref><sp/>Callback,</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="142"><highlight class="normal">);</highlight></codeline>
<codeline lineno="143"><highlight class="normal"></highlight></codeline>
<codeline lineno="151"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="152"><highlight class="normal"><ref refid="gpio__device_8h_1afbd2bd91a99c594504ca275fd8b45825" kindref="member">GpioDeviceUnregisterCallback</ref>(</highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="154"><highlight class="normal">);</highlight></codeline>
<codeline lineno="155"><highlight class="normal"></highlight></codeline>
<codeline lineno="164"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><ref refid="gpio__device_8h_1a1e860d4292f8df84e5d3101f8a415d6c" kindref="member">GpioDevicePulse</ref>(</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>PulseDurationMs</highlight></codeline>
<codeline lineno="168"><highlight class="normal">);</highlight></codeline>
<codeline lineno="169"><highlight class="normal"></highlight></codeline>
<codeline lineno="170"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>GPIO_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/devices/gpio_device.h"/>
  </compounddef>
</doxygen>
