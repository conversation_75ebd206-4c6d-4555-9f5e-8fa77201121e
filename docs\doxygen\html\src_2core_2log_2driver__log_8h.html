<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/core/log/driver_log.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('src_2core_2log_2driver__log_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">driver_log.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for driver_log.h:</div>
<div class="dyncontent">
<div class="center"><img src="src_2core_2log_2driver__log_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8h" id="aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8h">
<area shape="rect" title=" " alt="" coords="6,5,152,48"/>
<area shape="rect" title=" " alt="" coords="5,96,70,123"/>
<area shape="poly" title=" " alt="" coords="71,50,53,83,49,81,66,47"/>
<area shape="rect" title=" " alt="" coords="94,96,147,123"/>
<area shape="poly" title=" " alt="" coords="92,47,109,81,104,83,87,50"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="src_2core_2log_2driver__log_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8hdep" id="aC_1_2KMDF_01Driver1_2src_2core_2log_2driver__log_8hdep">
<area shape="rect" title=" " alt="" coords="259,5,404,48"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="5,195,151,237"/>
<area shape="poly" title=" " alt="" coords="293,60,108,196,104,192,290,55"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="513,96,659,139"/>
<area shape="poly" title=" " alt="" coords="406,51,527,93,525,98,404,56"/>
<area shape="poly" title=" " alt="" coords="498,131,341,154,163,189,136,197,135,192,161,184,340,148,497,126"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="175,187,320,245"/>
<area shape="poly" title=" " alt="" coords="499,143,332,189,321,193,320,187,331,184,497,138"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="344,187,489,245"/>
<area shape="poly" title=" " alt="" coords="538,149,468,188,466,184,535,144"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="513,195,659,237"/>
<area shape="poly" title=" " alt="" coords="589,154,589,194,583,194,583,154"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="683,195,828,237"/>
<area shape="poly" title=" " alt="" coords="637,144,720,192,718,196,634,149"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="852,195,997,237"/>
<area shape="poly" title=" " alt="" coords="675,138,841,184,865,191,863,197,840,189,673,143"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1021,195,1167,237"/>
<area shape="poly" title=" " alt="" coords="675,126,832,148,1011,184,1037,192,1036,197,1009,189,831,154,674,131"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="1191,195,1336,237"/>
<area shape="poly" title=" " alt="" coords="674,120,904,140,1043,159,1180,184,1208,191,1206,197,1179,189,1042,164,904,145,674,126"/>
</map>
</div>
</div>
<p><a href="src_2core_2log_2driver__log_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FLOG_5FCONFIG_5Fstruct_5F_5FLOG_5F_5FCONFIG" id="r__5FLOG_5FCONFIG_5Fstruct_5F_5FLOG_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aa1911455782e83f3b06fab600be0e43e" id="r_aa1911455782e83f3b06fab600be0e43e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa1911455782e83f3b06fab600be0e43e">LOG_ALERT</a>(Format, ...)</td></tr>
<tr class="memitem:a4c42b3fa94110619ab8458eb672d189d" id="r_a4c42b3fa94110619ab8458eb672d189d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4c42b3fa94110619ab8458eb672d189d">LOG_ALERT_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:abc03884460a6987df33fea0d5cae8302" id="r_abc03884460a6987df33fea0d5cae8302"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abc03884460a6987df33fea0d5cae8302">LOG_CRITICAL</a>(Format, ...)</td></tr>
<tr class="memitem:ab48ce4a2ee7f0b5f74153fedf6ad7c25" id="r_ab48ce4a2ee7f0b5f74153fedf6ad7c25"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab48ce4a2ee7f0b5f74153fedf6ad7c25">LOG_CRITICAL_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:acfe39a25e08737b535dc881071ebf149" id="r_acfe39a25e08737b535dc881071ebf149"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acfe39a25e08737b535dc881071ebf149">LOG_DEBUG</a>(Format, ...)</td></tr>
<tr class="memitem:ae930e4b3ae4e59dc6a7b6a4feefb116f" id="r_ae930e4b3ae4e59dc6a7b6a4feefb116f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae930e4b3ae4e59dc6a7b6a4feefb116f">LOG_DEBUG_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:ad706db1253940848e01bbc71ede868ef" id="r_ad706db1253940848e01bbc71ede868ef"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad706db1253940848e01bbc71ede868ef">LOG_EMERGENCY</a>(Format, ...)</td></tr>
<tr class="memitem:a570937723f42dd301b24b631ec455b58" id="r_a570937723f42dd301b24b631ec455b58"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a570937723f42dd301b24b631ec455b58">LOG_EMERGENCY_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:a29e75b488d8e8ef5641c5bd16709faec" id="r_a29e75b488d8e8ef5641c5bd16709faec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a29e75b488d8e8ef5641c5bd16709faec">LOG_ERROR</a>(Format, ...)</td></tr>
<tr class="memitem:aff0a0cc082f6b2fad9ed0979da6e8a9b" id="r_aff0a0cc082f6b2fad9ed0979da6e8a9b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aff0a0cc082f6b2fad9ed0979da6e8a9b">LOG_ERROR_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:a7748b322eafa9e058c518fef49b110cb" id="r_a7748b322eafa9e058c518fef49b110cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7748b322eafa9e058c518fef49b110cb">LOG_INFO</a>(Format, ...)</td></tr>
<tr class="memitem:a94d7f96857344352ffbc6ee65e9f2390" id="r_a94d7f96857344352ffbc6ee65e9f2390"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a94d7f96857344352ffbc6ee65e9f2390">LOG_INFO_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:a05bf2404451e701f51d18409e72321fd" id="r_a05bf2404451e701f51d18409e72321fd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a05bf2404451e701f51d18409e72321fd">LOG_NOTICE</a>(Format, ...)</td></tr>
<tr class="memitem:a6cf4240fc51cea71e901acf9df797b98" id="r_a6cf4240fc51cea71e901acf9df797b98"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6cf4240fc51cea71e901acf9df797b98">LOG_NOTICE_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:a0972af62c9ad7b688924604669d7d762" id="r_a0972af62c9ad7b688924604669d7d762"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0972af62c9ad7b688924604669d7d762">LOG_TRACE</a>(Format, ...)</td></tr>
<tr class="memitem:a8793c218a97ef927e72271b80a872495" id="r_a8793c218a97ef927e72271b80a872495"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8793c218a97ef927e72271b80a872495">LOG_TRACE_IF</a>(Condition,  Format, ...)</td></tr>
<tr class="memitem:a1dd05e1ef2b66fc68251edacaa75e9f7" id="r_a1dd05e1ef2b66fc68251edacaa75e9f7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1dd05e1ef2b66fc68251edacaa75e9f7">LOG_WARNING</a>(Format, ...)</td></tr>
<tr class="memitem:a238f142a1b0fcbd8378c38d99b233baa" id="r_a238f142a1b0fcbd8378c38d99b233baa"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a238f142a1b0fcbd8378c38d99b233baa">LOG_WARNING_IF</a>(Condition,  Format, ...)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a565ca8d986ea85865e5e0e69c0fccc9d" id="r_a565ca8d986ea85865e5e0e69c0fccc9d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></td></tr>
<tr class="memitem:aa90925833aff044f4ba03f43f8084bf7" id="r_aa90925833aff044f4ba03f43f8084bf7"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td></tr>
<tr class="memitem:a5c3fab47ae6bd7de107b55a48ff20591" id="r_a5c3fab47ae6bd7de107b55a48ff20591"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a></td></tr>
<tr class="memitem:ab99d8d17b06b190b7fecbbadd3d6b7df" id="r_ab99d8d17b06b190b7fecbbadd3d6b7df"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a7898a2c3a87496daad04bfb45321843f" id="r_a7898a2c3a87496daad04bfb45321843f"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> { <br />
&#160;&#160;<a class="el" href="#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a> = 0
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a>
, <br />
&#160;&#160;<a class="el" href="#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a>
, <br />
&#160;&#160;<a class="el" href="#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a>
, <a class="el" href="#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a>
<br />
 }</td></tr>
<tr class="memitem:a7f7a95369342d65f5886d79f0c1845e5" id="r_a7f7a95369342d65f5886d79f0c1845e5"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a> { <br />
&#160;&#160;<a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1">LogTypeNone</a> = 0x00
, <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127">LogTypeDebugger</a> = 0x01
, <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a> = 0x02
, <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a> = 0x04
, <br />
&#160;&#160;<a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a> = 0x08
, <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a> = 0xFF
<br />
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aa7f5f3b01615029c1cb54753c0b03175" id="r_aa7f5f3b01615029c1cb54753c0b03175"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa7f5f3b01615029c1cb54753c0b03175">LogConfigInit</a> (_Out_ <a class="el" href="#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig)</td></tr>
<tr class="memitem:aa2e9424857371175fc265253fbabcc5d" id="r_aa2e9424857371175fc265253fbabcc5d"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa2e9424857371175fc265253fbabcc5d">LogInitialize</a> (_In_ <a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a> DriverObject, _In_opt_ CONST <a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a> *InitialConfig)</td></tr>
<tr class="memitem:a8e8711da6408af7b3b313f892121215e" id="r_a8e8711da6408af7b3b313f892121215e"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a> (_In_ <a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args)</td></tr>
<tr class="memitem:aab8bcb7121136bc236fe5d55778fbaf2" id="r_aab8bcb7121136bc236fe5d55778fbaf2"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aab8bcb7121136bc236fe5d55778fbaf2">LogUninitialize</a> (VOID)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:af114289be71fc27e5ce43d55d4d6622c" id="r_af114289be71fc27e5ce43d55d4d6622c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af114289be71fc27e5ce43d55d4d6622c">g_LogConfig</a></td></tr>
<tr class="memitem:acd7f22e672d8bbb5ac97c70459b869fb" id="r_acd7f22e672d8bbb5ac97c70459b869fb"><td class="memItemLeft" align="right" valign="top">BOOLEAN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acd7f22e672d8bbb5ac97c70459b869fb">g_LogInitialized</a></td></tr>
<tr class="memitem:ad2099051d14962ced91f03017c7021f3" id="r_ad2099051d14962ced91f03017c7021f3"><td class="memItemLeft" align="right" valign="top">const PCSTR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad2099051d14962ced91f03017c7021f3">g_LogLevelNames</a> [<a class="el" href="#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a>]</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__LOG__CONFIG" id="struct__LOG__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__LOG__CONFIG">&#9670;&#160;</a></span>_LOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _LOG_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ac82d96af8adf8a1862d02ac0be15939d" name="ac82d96af8adf8a1862d02ac0be15939d"></a>BOOLEAN</td>
<td class="fieldname">
IncludeComponentName</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a860b2d89bf1aff272da84d5029dcb0b1" name="a860b2d89bf1aff272da84d5029dcb0b1"></a>BOOLEAN</td>
<td class="fieldname">
IncludeTimestamp</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a21ca685eeee730a0d8a179892b840d5f" name="a21ca685eeee730a0d8a179892b840d5f"></a>PWSTR</td>
<td class="fieldname">
LogFilePath</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab67683f62a123026ec78fe405cdbb177" name="ab67683f62a123026ec78fe405cdbb177"></a><a class="el" href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
<td class="fieldname">
LogLevel</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aa4471d5ed80ebce68218e751798de8dc" name="aa4471d5ed80ebce68218e751798de8dc"></a><a class="el" href="#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a></td>
<td class="fieldname">
LogTargets</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a4b99237eff4b17c1d4fbfa9697528f70" name="a4b99237eff4b17c1d4fbfa9697528f70"></a><a class="el" href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">LOG_TYPE</a></td>
<td class="fieldname">
LogTypes</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a7aa67c4af6a6176801a2b2af65ac3cbb" name="a7aa67c4af6a6176801a2b2af65ac3cbb"></a>ULONG</td>
<td class="fieldname">
MaxLogFileSize</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ac0ad1887a3c94cf0998d9c5359900a4f" name="ac0ad1887a3c94cf0998d9c5359900a4f"></a><a class="el" href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
<td class="fieldname">
MinLevel</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="aa1911455782e83f3b06fab600be0e43e" name="aa1911455782e83f3b06fab600be0e43e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa1911455782e83f3b06fab600be0e43e">&#9670;&#160;</a></span>LOG_ALERT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_ALERT</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="adriver__log_8c_html_a8e8711da6408af7b3b313f892121215e"><div class="ttname"><a href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a></div><div class="ttdeci">VOID LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args)</div><div class="ttdef"><b>Definition</b> driver_log.c:103</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a></div><div class="ttdeci">@ LogLevelAlert</div><div class="ttdef"><b>Definition</b> driver_log.h:19</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a4c42b3fa94110619ab8458eb672d189d" name="a4c42b3fa94110619ab8458eb672d189d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c42b3fa94110619ab8458eb672d189d">&#9670;&#160;</a></span>LOG_ALERT_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_ALERT_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="abc03884460a6987df33fea0d5cae8302" name="abc03884460a6987df33fea0d5cae8302"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc03884460a6987df33fea0d5cae8302">&#9670;&#160;</a></span>LOG_CRITICAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_CRITICAL</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a>,  __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a></div><div class="ttdeci">@ LogLevelCritical</div><div class="ttdef"><b>Definition</b> driver_log.h:20</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="ab48ce4a2ee7f0b5f74153fedf6ad7c25" name="ab48ce4a2ee7f0b5f74153fedf6ad7c25"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab48ce4a2ee7f0b5f74153fedf6ad7c25">&#9670;&#160;</a></span>LOG_CRITICAL_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_CRITICAL_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a>,  __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="acfe39a25e08737b535dc881071ebf149" name="acfe39a25e08737b535dc881071ebf149"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acfe39a25e08737b535dc881071ebf149">&#9670;&#160;</a></span>LOG_DEBUG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_DEBUG</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a></div><div class="ttdeci">@ LogLevelDebug</div><div class="ttdef"><b>Definition</b> driver_log.h:25</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="ae930e4b3ae4e59dc6a7b6a4feefb116f" name="ae930e4b3ae4e59dc6a7b6a4feefb116f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae930e4b3ae4e59dc6a7b6a4feefb116f">&#9670;&#160;</a></span>LOG_DEBUG_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_DEBUG_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="ad706db1253940848e01bbc71ede868ef" name="ad706db1253940848e01bbc71ede868ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad706db1253940848e01bbc71ede868ef">&#9670;&#160;</a></span>LOG_EMERGENCY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_EMERGENCY</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a>, __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a></div><div class="ttdeci">@ LogLevelEmergency</div><div class="ttdef"><b>Definition</b> driver_log.h:18</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a570937723f42dd301b24b631ec455b58" name="a570937723f42dd301b24b631ec455b58"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a570937723f42dd301b24b631ec455b58">&#9670;&#160;</a></span>LOG_EMERGENCY_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_EMERGENCY_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a>, __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a29e75b488d8e8ef5641c5bd16709faec" name="a29e75b488d8e8ef5641c5bd16709faec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29e75b488d8e8ef5641c5bd16709faec">&#9670;&#160;</a></span>LOG_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_ERROR</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a></div><div class="ttdeci">@ LogLevelError</div><div class="ttdef"><b>Definition</b> driver_log.h:21</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="aff0a0cc082f6b2fad9ed0979da6e8a9b" name="aff0a0cc082f6b2fad9ed0979da6e8a9b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff0a0cc082f6b2fad9ed0979da6e8a9b">&#9670;&#160;</a></span>LOG_ERROR_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_ERROR_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a7748b322eafa9e058c518fef49b110cb" name="a7748b322eafa9e058c518fef49b110cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7748b322eafa9e058c518fef49b110cb">&#9670;&#160;</a></span>LOG_INFO</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_INFO</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>,      __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a></div><div class="ttdeci">@ LogLevelInfo</div><div class="ttdef"><b>Definition</b> driver_log.h:24</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a94d7f96857344352ffbc6ee65e9f2390" name="a94d7f96857344352ffbc6ee65e9f2390"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94d7f96857344352ffbc6ee65e9f2390">&#9670;&#160;</a></span>LOG_INFO_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_INFO_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>,      __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a05bf2404451e701f51d18409e72321fd" name="a05bf2404451e701f51d18409e72321fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05bf2404451e701f51d18409e72321fd">&#9670;&#160;</a></span>LOG_NOTICE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_NOTICE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a>,    __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a></div><div class="ttdeci">@ LogLevelNotice</div><div class="ttdef"><b>Definition</b> driver_log.h:23</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a6cf4240fc51cea71e901acf9df797b98" name="a6cf4240fc51cea71e901acf9df797b98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6cf4240fc51cea71e901acf9df797b98">&#9670;&#160;</a></span>LOG_NOTICE_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_NOTICE_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a>,    __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a0972af62c9ad7b688924604669d7d762" name="a0972af62c9ad7b688924604669d7d762"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0972af62c9ad7b688924604669d7d762">&#9670;&#160;</a></span>LOG_TRACE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_TRACE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a></div><div class="ttdeci">@ LogLevelTrace</div><div class="ttdef"><b>Definition</b> driver_log.h:26</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a8793c218a97ef927e72271b80a872495" name="a8793c218a97ef927e72271b80a872495"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8793c218a97ef927e72271b80a872495">&#9670;&#160;</a></span>LOG_TRACE_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_TRACE_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a>,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a1dd05e1ef2b66fc68251edacaa75e9f7" name="a1dd05e1ef2b66fc68251edacaa75e9f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1dd05e1ef2b66fc68251edacaa75e9f7">&#9670;&#160;</a></span>LOG_WARNING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_WARNING</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>,   __FUNCTION__, __LINE__, Format, __VA_ARGS__)</div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"><div class="ttname"><a href="#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a></div><div class="ttdeci">@ LogLevelWarning</div><div class="ttdef"><b>Definition</b> driver_log.h:22</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a238f142a1b0fcbd8378c38d99b233baa" name="a238f142a1b0fcbd8378c38d99b233baa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a238f142a1b0fcbd8378c38d99b233baa">&#9670;&#160;</a></span>LOG_WARNING_IF</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_WARNING_IF</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { <span class="keywordflow">if</span> (Condition) { <a class="code hl_function" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(<a class="code hl_enumvalue" href="#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>,   __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } <span class="keywordflow">while</span>(0)</div>
</div><!-- fragment -->
</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a565ca8d986ea85865e5e0e69c0fccc9d" name="a565ca8d986ea85865e5e0e69c0fccc9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a565ca8d986ea85865e5e0e69c0fccc9d">&#9670;&#160;</a></span>LOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a> <a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa90925833aff044f4ba03f43f8084bf7" name="aa90925833aff044f4ba03f43f8084bf7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa90925833aff044f4ba03f43f8084bf7">&#9670;&#160;</a></span>LOG_LEVEL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> <a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a5c3fab47ae6bd7de107b55a48ff20591" name="a5c3fab47ae6bd7de107b55a48ff20591"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c3fab47ae6bd7de107b55a48ff20591">&#9670;&#160;</a></span>LOG_TYPES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a> <a class="el" href="#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab99d8d17b06b190b7fecbbadd3d6b7df" name="ab99d8d17b06b190b7fecbbadd3d6b7df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab99d8d17b06b190b7fecbbadd3d6b7df">&#9670;&#160;</a></span>PLOG_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>* <a class="el" href="#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="a7898a2c3a87496daad04bfb45321843f" name="a7898a2c3a87496daad04bfb45321843f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7898a2c3a87496daad04bfb45321843f">&#9670;&#160;</a></span>_LOG_LEVEL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" name="a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131"></a>LogLevelEmergency&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" name="a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c"></a>LogLevelAlert&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" name="a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408"></a>LogLevelCritical&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" name="a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"></a>LogLevelError&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" name="a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"></a>LogLevelWarning&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" name="a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0"></a>LogLevelNotice&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" name="a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"></a>LogLevelInfo&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" name="a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"></a>LogLevelDebug&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" name="a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816"></a>LogLevelTrace&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" name="a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f"></a>LogLevelMax&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a7f7a95369342d65f5886d79f0c1845e5" name="a7f7a95369342d65f5886d79f0c1845e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f7a95369342d65f5886d79f0c1845e5">&#9670;&#160;</a></span>_LOG_TYPES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1" name="a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1"></a>LogTypeNone&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" name="a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127"></a>LogTypeDebugger&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" name="a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d"></a>LogTypeFile&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" name="a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3"></a>LogTypeETW&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" name="a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7"></a>LogTypeWPP&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" name="a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee"></a>LogTypeAll&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="aa7f5f3b01615029c1cb54753c0b03175" name="aa7f5f3b01615029c1cb54753c0b03175"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7f5f3b01615029c1cb54753c0b03175">&#9670;&#160;</a></span>LogConfigInit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogConfigInit </td>
          <td>(</td>
          <td class="paramtype">_Out_ <a class="el" href="#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>LogConfig</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="src_2core_2log_2driver__log_8h_aa7f5f3b01615029c1cb54753c0b03175_cgraph.png" border="0" usemap="#asrc_2core_2log_2driver__log_8h_aa7f5f3b01615029c1cb54753c0b03175_cgraph" loading="lazy" alt=""/></div>
<map name="asrc_2core_2log_2driver__log_8h_aa7f5f3b01615029c1cb54753c0b03175_cgraph" id="asrc_2core_2log_2driver__log_8h_aa7f5f3b01615029c1cb54753c0b03175_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,104,32"/>
<area shape="rect" href="gpio__core_8c.html#aa5ccd638c5bf670b734784f2601b7ec7" title=" " alt="" coords="152,5,263,32"/>
<area shape="poly" title=" " alt="" coords="104,16,136,16,136,21,104,21"/>
</map>
</div>

</div>
</div>
<a id="aa2e9424857371175fc265253fbabcc5d" name="aa2e9424857371175fc265253fbabcc5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa2e9424857371175fc265253fbabcc5d">&#9670;&#160;</a></span>LogInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS LogInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a></td>          <td class="paramname"><span class="paramname"><em>DriverObject</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ CONST <a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a> *</td>          <td class="paramname"><span class="paramname"><em>InitialConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="src_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_cgraph.png" border="0" usemap="#asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_cgraph" loading="lazy" alt=""/></div>
<map name="asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_cgraph" id="asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,96,56"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="144,29,260,56"/>
<area shape="poly" title=" " alt="" coords="96,40,129,40,129,45,96,45"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="308,29,413,56"/>
<area shape="poly" title=" " alt="" coords="260,40,292,40,292,45,260,45"/>
<area shape="poly" title=" " alt="" coords="333,30,328,21,332,11,343,5,361,3,380,5,390,12,387,16,378,10,361,8,344,10,336,15,334,20,338,28"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="src_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_icgraph.png" border="0" usemap="#asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_icgraph" loading="lazy" alt=""/></div>
<map name="asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_icgraph" id="asrc_2core_2log_2driver__log_8h_aa2e9424857371175fc265253fbabcc5d_icgraph">
<area shape="rect" title=" " alt="" coords="141,5,231,32"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,5,93,32"/>
<area shape="poly" title=" " alt="" coords="125,21,93,21,93,16,125,16"/>
</map>
</div>

</div>
</div>
<a id="a8e8711da6408af7b3b313f892121215e" name="a8e8711da6408af7b3b313f892121215e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e8711da6408af7b3b313f892121215e">&#9670;&#160;</a></span>LogMessageVA()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogMessageVA </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></td>          <td class="paramname"><span class="paramname"><em>Level</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ PCSTR</td>          <td class="paramname"><span class="paramname"><em>Function</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Line</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ PCSTR</td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ va_list</td>          <td class="paramname"><span class="paramname"><em>Args</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aab8bcb7121136bc236fe5d55778fbaf2" name="aab8bcb7121136bc236fe5d55778fbaf2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab8bcb7121136bc236fe5d55778fbaf2">&#9670;&#160;</a></span>LogUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID LogUninitialize </td>
          <td>(</td>
          <td class="paramtype">VOID</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="af114289be71fc27e5ce43d55d4d6622c" name="af114289be71fc27e5ce43d55d4d6622c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af114289be71fc27e5ce43d55d4d6622c">&#9670;&#160;</a></span>g_LogConfig</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a> g_LogConfig</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a id="acd7f22e672d8bbb5ac97c70459b869fb" name="acd7f22e672d8bbb5ac97c70459b869fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd7f22e672d8bbb5ac97c70459b869fb">&#9670;&#160;</a></span>g_LogInitialized</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">BOOLEAN g_LogInitialized</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a id="ad2099051d14962ced91f03017c7021f3" name="ad2099051d14962ced91f03017c7021f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2099051d14962ced91f03017c7021f3">&#9670;&#160;</a></span>g_LogLevelNames</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const PCSTR g_LogLevelNames[<a class="el" href="#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a>]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_aebb8dcc11953d78e620bbef0b9e2183.html">core</a></li><li class="navelem"><a href="dir_2aa9c0e397d40306fad4535cf762fffd.html">log</a></li><li class="navelem"><a href="src_2core_2log_2driver__log_8h.html">driver_log.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
