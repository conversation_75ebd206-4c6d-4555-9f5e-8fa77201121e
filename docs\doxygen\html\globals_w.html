<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li class="current"><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_w.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_w" name="index_w"></a>- w -</h3><ul>
<li>WDF_NO_HANDLE&#160;:&#160;<a class="el" href="core__types_8h.html#aa5171e0d8c548a75e9b42333beb6390e">core_types.h</a></li>
<li>WDF_PNP_DEVICE_STATE&#160;:&#160;<a class="el" href="core__types_8h.html#a221c7751a97d3a74ef06fca4c21df7be">core_types.h</a></li>
<li>WDFAPI&#160;:&#160;<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">core_types.h</a></li>
<li>WDFDEVICE&#160;:&#160;<a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">core_types.h</a></li>
<li>WdfDevice&#160;:&#160;<a class="el" href="gpio__core_8c.html#a11ec07dcb5c1cea421134a0b149443a5">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#a11ec07dcb5c1cea421134a0b149443a5">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a11ec07dcb5c1cea421134a0b149443a5">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a11ec07dcb5c1cea421134a0b149443a5">spi_device.c</a></li>
<li>WDFDRIVER&#160;:&#160;<a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">core_types.h</a></li>
<li>WDFOBJECT&#160;:&#160;<a class="el" href="core__types_8h.html#a3921100f8f21c9e6503c02e51eb84644">core_types.h</a></li>
<li>WdfPnpDeviceStateDeleted&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4a2f3d900775841ff5752d051840df67f9">core_types.h</a></li>
<li>WdfPnpDeviceStateDisabled&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4a8d4da388b3e90c23c1439b64af155f46">core_types.h</a></li>
<li>WdfPnpDeviceStateInvalid&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4a32e2c31db9c18bee67b905ee7790fce9">core_types.h</a></li>
<li>WdfPnpDeviceStateRemoved&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4a57e65bf1809d0b1ce961289483e25673">core_types.h</a></li>
<li>WdfPnpDeviceStateSurpriseRemoval&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4a4ec4b8f4e5fb775bb55c33aea51210f9">core_types.h</a></li>
<li>WdfPnpDeviceStateWorking&#160;:&#160;<a class="el" href="core__types_8h.html#abc45cb0e4da10d450f8b522e479c1ea4aa810832bd1c384c1b65dc16cefb6d746">core_types.h</a></li>
<li>WDFQUEUE&#160;:&#160;<a class="el" href="core__types_8h.html#afd7c139f363e4f6879a9f8d21938be91">core_types.h</a></li>
<li>WDFREQUEST&#160;:&#160;<a class="el" href="core__types_8h.html#a5bbb7f7db295e12f4f24ca6ed92554f3">core_types.h</a></li>
<li>WDFSPINLOCK&#160;:&#160;<a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">core_types.h</a></li>
<li>WdfSpinLockRelease()&#160;:&#160;<a class="el" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab">gpio_core.c</a></li>
<li>WINVER&#160;:&#160;<a class="el" href="precomp_8h.html#a966cd377b9f3fdeb1432460c33352af1">precomp.h</a></li>
<li>WriteBuffer&#160;:&#160;<a class="el" href="spi__device_8c.html#ad26ded9b73e8b14b4117614b39440d86">spi_device.c</a></li>
<li>writeBuffer&#160;:&#160;<a class="el" href="spi__device_8c.html#a7a33fa49b57196f5722a55916cff0a52">spi_device.c</a></li>
<li>WriteLength&#160;:&#160;<a class="el" href="spi__device_8c.html#a530eca3d7e36c5dde60c5e49dd7b2b34">spi_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
