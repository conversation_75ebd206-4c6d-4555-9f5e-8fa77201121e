<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__bus__common_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_bus_common.h</div></div>
</div><!--header-->
<div class="contents">
<a href="kmdf__bus__common_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * kmdf_bus_common.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * Common definitions for bus interfaces</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * Provides shared interfaces and data structures for all bus types</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> */</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span> </div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#ifndef KMDF_BUS_COMMON_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#define KMDF_BUS_COMMON_H</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span> </div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment">// #include &lt;wdm.h&gt; // Removed, wdf.h should suffice or ntddk.h via other headers if in kernel mode</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="preprocessor">#include &quot;<a class="code" href="error__codes_8h.html">../../core/error/error_codes.h</a>&quot;</span> <span class="comment">// This includes ntddk.h (conditionally) and wdf.h</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span> </div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">// Bus type enumeration</span></div>
<div class="foldopen" id="foldopen00016" data-start="{" data-end="};">
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123">   16</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a> {</div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584">   17</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584">BusTypeI2C</a>,  <span class="comment">// I2C bus</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60">   18</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60">BusTypeSPI</a>,  <span class="comment">// SPI bus</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc">   19</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc">BusTypeUSB</a>,  <span class="comment">// USB bus</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38">   20</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38">BusTypeMax</a>   <span class="comment">// Enum boundary check value</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a070973cba55289cdfb72a260dc529d59">   21</a></span>} <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a>;</div>
</div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment">// Bus transfer status</span></div>
<div class="foldopen" id="foldopen00024" data-start="{" data-end="};">
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52f">   24</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a> {</div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947">   25</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947">BusTransferSuccess</a> = 0,       <span class="comment">// Transfer successful</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51">   26</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51">BusTransferFailed</a>,           <span class="comment">// Transfer failed</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726">   27</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726">BusTransferCancelled</a>,        <span class="comment">// Transfer cancelled</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c">   28</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c">BusTransferInvalidParameter</a>, <span class="comment">// Invalid parameter</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b">   29</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b">BusTransferDeviceNotReady</a>,   <span class="comment">// Device not ready</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519">   30</a></span>    <a class="code hl_enumvalue" href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519">BusTransferTimeout</a>           <span class="comment">// Transfer timeout</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ab61a790fb09aa3a337c89ea002b5a76f">   31</a></span>} <a class="code hl_typedef" href="kmdf__bus__common_8h.html#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a>;</div>
</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span> </div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="comment">// Common bus configuration structure</span></div>
<div class="foldopen" id="foldopen00034" data-start="{" data-end="};">
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html">   34</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__bus__common_8h.html#struct__BUS__CONFIG">_BUS_CONFIG</a> {</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ab3667ea857ae85e39b62c3a39b8d6761">   35</a></span>    <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a> <a class="code hl_variable" href="kmdf__bus__common_8h.html#ab3667ea857ae85e39b62c3a39b8d6761">BusType</a>;                 <span class="comment">// Bus type</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a4dc4b392465c874dc7b75256fa2ac00a">   36</a></span>    ULONG <a class="code hl_variable" href="kmdf__bus__common_8h.html#a4dc4b392465c874dc7b75256fa2ac00a">Speed</a>;                      <span class="comment">// Bus speed</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a76efcd63df259ff464d8b6e9be040134">   37</a></span>    ULONG <a class="code hl_variable" href="kmdf__bus__common_8h.html#a76efcd63df259ff464d8b6e9be040134">Flags</a>;                      <span class="comment">// Configuration flags</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ae19319720b3c056dc60261c26e1dd43e">   38</a></span>    PVOID <a class="code hl_variable" href="kmdf__bus__common_8h.html#ae19319720b3c056dc60261c26e1dd43e">BusSpecificConfig</a>;          <span class="comment">// Bus specific configuration</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a90f573bacff80fd27b824b98e3e4fb9a">   39</a></span>} <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a90f573bacff80fd27b824b98e3e4fb9a">BUS_CONFIG</a>, *<a class="code hl_typedef" href="kmdf__bus__common_8h.html#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a>;</div>
</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="comment">// Common bus transfer packet structure</span></div>
<div class="foldopen" id="foldopen00042" data-start="{" data-end="};">
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html">   42</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__bus__common_8h.html#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a> {</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ab2d70f7d13f1b96499d1ed9fa11b881b">   43</a></span>    PVOID <a class="code hl_variable" href="kmdf__bus__common_8h.html#ab2d70f7d13f1b96499d1ed9fa11b881b">Buffer</a>;                     <span class="comment">// Data buffer</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#acd85870608d805b91015bd3e0c4302c4">   44</a></span>    SIZE_T <a class="code hl_variable" href="kmdf__bus__common_8h.html#acd85870608d805b91015bd3e0c4302c4">BufferLength</a>;             <span class="comment">// Buffer length</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ab3b945cbfe042fe6d4cf96ab9517ae6a">   45</a></span>    PVOID <a class="code hl_variable" href="kmdf__bus__common_8h.html#ab3b945cbfe042fe6d4cf96ab9517ae6a">Context</a>;                   <span class="comment">// Transfer context</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a66430abda4905c786c0b4e542757c518">   46</a></span>    <a class="code hl_typedef" href="kmdf__bus__common_8h.html#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a> <a class="code hl_variable" href="kmdf__bus__common_8h.html#a66430abda4905c786c0b4e542757c518">Status</a>;      <span class="comment">// Transfer status</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">   47</a></span>} <a class="code hl_typedef" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a>, *<a class="code hl_typedef" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a>;</div>
</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span> </div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="comment">// Bus operation callback function type</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">   50</a></span><span class="keyword">typedef</span> VOID (*<a class="code hl_typedef" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a>)(</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>    <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span>);</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span> </div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="comment">// Common bus interface function declarations</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#ad5aa0e171a9d72e28ab08d06935ab2f5">   56</a></span><a class="code hl_function" href="kmdf__bus__common_8h.html#ad5aa0e171a9d72e28ab08d06935ab2f5">BusInitialize</a>(</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>    _In_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a> BusConfig</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>);</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span> </div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a2878b61cfb78224301b7d25175aa243e">   62</a></span><a class="code hl_function" href="kmdf__bus__common_8h.html#a2878b61cfb78224301b7d25175aa243e">BusUninitialize</a>(</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>);</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span> </div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a79b8f6be307f48971e34bb6cabfba958">   67</a></span><a class="code hl_function" href="kmdf__bus__common_8h.html#a79b8f6be307f48971e34bb6cabfba958">BusTransferSynchronous</a>(</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>    _Inout_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    _In_ ULONG Timeout</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>);</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span> </div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#af23a4d40f37f1cf45dd8b2fc40cf6dff">   74</a></span><a class="code hl_function" href="kmdf__bus__common_8h.html#af23a4d40f37f1cf45dd8b2fc40cf6dff">BusTransferAsynchronous</a>(</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>    _Inout_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span>    _In_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback,</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>    _In_opt_ PVOID Context</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>);</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span> </div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno"><a class="line" href="kmdf__bus__common_8h.html#a4f54d258241aeda6b5d01ee12110870f">   82</a></span><a class="code hl_function" href="kmdf__bus__common_8h.html#a4f54d258241aeda6b5d01ee12110870f">BusCancelTransfer</a>(</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>    _In_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>);</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span> </div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span><span class="preprocessor">#endif </span><span class="comment">// KMDF_BUS_COMMON_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="acore__types_8h_html_a1cb14808a3eba8cd3fcc47bd1207a805"><div class="ttname"><a href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a></div><div class="ttdeci">#define WDFAPI</div><div class="ttdef"><b>Definition</b> core_types.h:21</div></div>
<div class="ttc" id="aerror__codes_8h_html"><div class="ttname"><a href="error__codes_8h.html">error_codes.h</a></div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a070973cba55289cdfb72a260dc529d59"><div class="ttname"><a href="kmdf__bus__common_8h.html#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a></div><div class="ttdeci">enum _BUS_TYPE BUS_TYPE</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a2878b61cfb78224301b7d25175aa243e"><div class="ttname"><a href="kmdf__bus__common_8h.html#a2878b61cfb78224301b7d25175aa243e">BusUninitialize</a></div><div class="ttdeci">WDFAPI VOID BusUninitialize(_In_ WDFDEVICE Device)</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a3709500586d6c79d8df0693c133a3f2d"><div class="ttname"><a href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></div><div class="ttdeci">VOID(* BUS_OPERATION_CALLBACK)(PBUS_TRANSFER_PACKET TransferPacket)</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:50</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a40bc08609f724c5347a097e2bd6a1123"><div class="ttname"><a href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a></div><div class="ttdeci">_BUS_TYPE</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:16</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38"><div class="ttname"><a href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38">BusTypeMax</a></div><div class="ttdeci">@ BusTypeMax</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:20</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584"><div class="ttname"><a href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584">BusTypeI2C</a></div><div class="ttdeci">@ BusTypeI2C</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:17</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60"><div class="ttname"><a href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60">BusTypeSPI</a></div><div class="ttdeci">@ BusTypeSPI</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:18</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc"><div class="ttname"><a href="kmdf__bus__common_8h.html#a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc">BusTypeUSB</a></div><div class="ttdeci">@ BusTypeUSB</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:19</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a41f78cc35bd82acd5a7e7fd5fc51d00d"><div class="ttname"><a href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></div><div class="ttdeci">struct _BUS_TRANSFER_PACKET * PBUS_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a4dc4b392465c874dc7b75256fa2ac00a"><div class="ttname"><a href="kmdf__bus__common_8h.html#a4dc4b392465c874dc7b75256fa2ac00a">_BUS_CONFIG::Speed</a></div><div class="ttdeci">ULONG Speed</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:36</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a4f54d258241aeda6b5d01ee12110870f"><div class="ttname"><a href="kmdf__bus__common_8h.html#a4f54d258241aeda6b5d01ee12110870f">BusCancelTransfer</a></div><div class="ttdeci">WDFAPI NTSTATUS BusCancelTransfer(_In_ WDFDEVICE Device, _In_ PBUS_TRANSFER_PACKET TransferPacket)</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52f"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a></div><div class="ttdeci">_BUS_TRANSFER_STATUS</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:24</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c">BusTransferInvalidParameter</a></div><div class="ttdeci">@ BusTransferInvalidParameter</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:28</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726">BusTransferCancelled</a></div><div class="ttdeci">@ BusTransferCancelled</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:27</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519">BusTransferTimeout</a></div><div class="ttdeci">@ BusTransferTimeout</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:30</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947">BusTransferSuccess</a></div><div class="ttdeci">@ BusTransferSuccess</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:25</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b">BusTransferDeviceNotReady</a></div><div class="ttdeci">@ BusTransferDeviceNotReady</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:29</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51"><div class="ttname"><a href="kmdf__bus__common_8h.html#a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51">BusTransferFailed</a></div><div class="ttdeci">@ BusTransferFailed</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:26</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a66430abda4905c786c0b4e542757c518"><div class="ttname"><a href="kmdf__bus__common_8h.html#a66430abda4905c786c0b4e542757c518">_BUS_TRANSFER_PACKET::Status</a></div><div class="ttdeci">BUS_TRANSFER_STATUS Status</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:46</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a76efcd63df259ff464d8b6e9be040134"><div class="ttname"><a href="kmdf__bus__common_8h.html#a76efcd63df259ff464d8b6e9be040134">_BUS_CONFIG::Flags</a></div><div class="ttdeci">ULONG Flags</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:37</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a79b8f6be307f48971e34bb6cabfba958"><div class="ttname"><a href="kmdf__bus__common_8h.html#a79b8f6be307f48971e34bb6cabfba958">BusTransferSynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS BusTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PBUS_TRANSFER_PACKET TransferPacket, _In_ ULONG Timeout)</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a90f573bacff80fd27b824b98e3e4fb9a"><div class="ttname"><a href="kmdf__bus__common_8h.html#a90f573bacff80fd27b824b98e3e4fb9a">BUS_CONFIG</a></div><div class="ttdeci">struct _BUS_CONFIG BUS_CONFIG</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_aac06c68a58c9667998bbe0975aa78c51"><div class="ttname"><a href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></div><div class="ttdeci">struct _BUS_TRANSFER_PACKET BUS_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ab2d70f7d13f1b96499d1ed9fa11b881b"><div class="ttname"><a href="kmdf__bus__common_8h.html#ab2d70f7d13f1b96499d1ed9fa11b881b">_BUS_TRANSFER_PACKET::Buffer</a></div><div class="ttdeci">PVOID Buffer</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:43</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ab3667ea857ae85e39b62c3a39b8d6761"><div class="ttname"><a href="kmdf__bus__common_8h.html#ab3667ea857ae85e39b62c3a39b8d6761">_BUS_CONFIG::BusType</a></div><div class="ttdeci">BUS_TYPE BusType</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:35</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ab3b945cbfe042fe6d4cf96ab9517ae6a"><div class="ttname"><a href="kmdf__bus__common_8h.html#ab3b945cbfe042fe6d4cf96ab9517ae6a">_BUS_TRANSFER_PACKET::Context</a></div><div class="ttdeci">PVOID Context</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:45</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ab61a790fb09aa3a337c89ea002b5a76f"><div class="ttname"><a href="kmdf__bus__common_8h.html#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a></div><div class="ttdeci">enum _BUS_TRANSFER_STATUS BUS_TRANSFER_STATUS</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_acd85870608d805b91015bd3e0c4302c4"><div class="ttname"><a href="kmdf__bus__common_8h.html#acd85870608d805b91015bd3e0c4302c4">_BUS_TRANSFER_PACKET::BufferLength</a></div><div class="ttdeci">SIZE_T BufferLength</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:44</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ad5aa0e171a9d72e28ab08d06935ab2f5"><div class="ttname"><a href="kmdf__bus__common_8h.html#ad5aa0e171a9d72e28ab08d06935ab2f5">BusInitialize</a></div><div class="ttdeci">WDFAPI NTSTATUS BusInitialize(_In_ WDFDEVICE Device, _In_ PBUS_CONFIG BusConfig)</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_ae19319720b3c056dc60261c26e1dd43e"><div class="ttname"><a href="kmdf__bus__common_8h.html#ae19319720b3c056dc60261c26e1dd43e">_BUS_CONFIG::BusSpecificConfig</a></div><div class="ttdeci">PVOID BusSpecificConfig</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:38</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_aeca4f45b7bb946c601f53bb52b08b170"><div class="ttname"><a href="kmdf__bus__common_8h.html#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a></div><div class="ttdeci">struct _BUS_CONFIG * PBUS_CONFIG</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_af23a4d40f37f1cf45dd8b2fc40cf6dff"><div class="ttname"><a href="kmdf__bus__common_8h.html#af23a4d40f37f1cf45dd8b2fc40cf6dff">BusTransferAsynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS BusTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PBUS_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_struct__BUS__CONFIG"><div class="ttname"><a href="kmdf__bus__common_8h.html#struct__BUS__CONFIG">_BUS_CONFIG</a></div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:34</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_struct__BUS__TRANSFER__PACKET"><div class="ttname"><a href="kmdf__bus__common_8h.html#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a></div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:42</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
