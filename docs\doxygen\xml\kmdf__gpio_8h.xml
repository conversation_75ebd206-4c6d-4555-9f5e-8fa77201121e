<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="kmdf__gpio_8h" kind="file" language="C++">
    <compoundname>kmdf_gpio.h</compoundname>
    <includes refid="kmdf__bus__common_8h" local="yes">kmdf_bus_common.h</includes>
    <includedby refid="gpio__device_8h" local="yes">C:/KMDF Driver1/include/hal/devices/gpio_device.h</includedby>
    <includedby refid="gpio__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/gpio_core.c</includedby>
    <incdepgraph>
      <node id="4">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__GPIO__PIN__CONFIG" prot="public">_GPIO_PIN_CONFIG</innerclass>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_INTERRUPT_TYPE</name>
        <enumvalue id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76" prot="public">
          <name>GpioInterruptNone</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560" prot="public">
          <name>GpioInterruptRising</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84" prot="public">
          <name>GpioInterruptFalling</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64" prot="public">
          <name>GpioInterruptBoth</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42" prot="public">
          <name>GpioInterruptLevel</name>
          <initializer>= 4</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="26" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" bodystart="26" bodyend="32"/>
      </memberdef>
      <memberdef kind="enum" id="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4b" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_PIN_DIRECTION</name>
        <enumvalue id="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2" prot="public">
          <name>GpioDirectionIn</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" prot="public">
          <name>GpioDirectionOut</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="14" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" bodystart="14" bodyend="17"/>
      </memberdef>
      <memberdef kind="enum" id="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070" prot="public" static="no" strong="no">
        <type></type>
        <name>_GPIO_PIN_POLARITY</name>
        <enumvalue id="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" prot="public">
          <name>GpioPolarityActiveLow</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" prot="public">
          <name>GpioPolarityActiveHigh</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="20" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" bodystart="20" bodyend="23"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" prot="public" static="no">
        <type>VOID(*</type>
        <definition>typedef VOID(* GPIO_INTERRUPT_CALLBACK) (_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</definition>
        <argsstring>)(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</argsstring>
        <name>GPIO_INTERRUPT_CALLBACK</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="50" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" bodystart="51" bodyend="-1"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" prot="public" static="no">
        <type>enum <ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33" kindref="member">_GPIO_INTERRUPT_TYPE</ref></type>
        <definition>typedef enum _GPIO_INTERRUPT_TYPE GPIO_INTERRUPT_TYPE</definition>
        <argsstring></argsstring>
        <name>GPIO_INTERRUPT_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="32" column="21"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" prot="public" static="no">
        <type>struct <ref refid="struct__GPIO__PIN__CONFIG" kindref="compound">_GPIO_PIN_CONFIG</ref></type>
        <definition>typedef struct _GPIO_PIN_CONFIG GPIO_PIN_CONFIG</definition>
        <argsstring></argsstring>
        <name>GPIO_PIN_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="46" column="17"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" prot="public" static="no">
        <type>enum <ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4b" kindref="member">_GPIO_PIN_DIRECTION</ref></type>
        <definition>typedef enum _GPIO_PIN_DIRECTION GPIO_PIN_DIRECTION</definition>
        <argsstring></argsstring>
        <name>GPIO_PIN_DIRECTION</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="17" column="20"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" prot="public" static="no">
        <type>enum <ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070" kindref="member">_GPIO_PIN_POLARITY</ref></type>
        <definition>typedef enum _GPIO_PIN_POLARITY GPIO_PIN_POLARITY</definition>
        <argsstring></argsstring>
        <name>GPIO_PIN_POLARITY</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="23" column="19"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" prot="public" static="no">
        <type>struct <ref refid="struct__GPIO__PIN__CONFIG" kindref="compound">_GPIO_PIN_CONFIG</ref> *</type>
        <definition>typedef struct _GPIO_PIN_CONFIG * PGPIO_PIN_CONFIG</definition>
        <argsstring></argsstring>
        <name>PGPIO_PIN_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="46" column="35"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID GPIODisableInterrupt</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber)</argsstring>
        <name>GPIODisableInterrupt</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="102" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="102" declcolumn="1"/>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
        <referencedby refid="gpio__device_8c_1aac20ced732c198d7484287c9eb39e413" compoundref="gpio__device_8c" startline="289" endline="291">if</referencedby>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOEnableInterrupt</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ GPIO_INTERRUPT_TYPE InterruptType, _In_ GPIO_INTERRUPT_CALLBACK Callback, _In_opt_ PVOID Context)</argsstring>
        <name>GPIOEnableInterrupt</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kindref="member">GPIO_INTERRUPT_TYPE</ref></type>
          <declname>InterruptType</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" kindref="member">GPIO_INTERRUPT_CALLBACK</ref></type>
          <declname>Callback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="93" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="93" declcolumn="1"/>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOGetValue</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _Out_ PBOOLEAN Value)</argsstring>
        <name>GPIOGetValue</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_Out_ PBOOLEAN</type>
          <declname>Value</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="86" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="86" declcolumn="1"/>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1a0a73c23c89291af0e81cef7098d10e29" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig)</argsstring>
        <name>GPIOInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref></type>
          <declname>GpioConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="60" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="140" bodyend="159" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="60" declcolumn="1"/>
        <references refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" compoundref="gpio__core_8c" startline="43">GPIO_POOL_TAG</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1ade1cb652014fc4a3984567ff49900d81" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOPulse</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ ULONG PulseDurationMs)</argsstring>
        <name>GPIOPulse</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PulseDurationMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="108" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="108" declcolumn="1"/>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOSetDirection</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ GPIO_PIN_DIRECTION Direction)</argsstring>
        <name>GPIOSetDirection</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kindref="member">GPIO_PIN_DIRECTION</ref></type>
          <declname>Direction</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="72" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="72" declcolumn="1"/>
        <references refid="gpio__device_8c_1a130124259198fee8d71747e31a529e96" compoundref="gpio__device_8c" startline="213">Direction</references>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS GPIOSetValue</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN Value)</argsstring>
        <name>GPIOSetValue</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <param>
          <type>_In_ BOOLEAN</type>
          <declname>Value</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="79" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="79" declcolumn="1"/>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__gpio_8h_1a785f00e9c0879fb478077d2cdce99906" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID GPIOUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber)</argsstring>
        <name>GPIOUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" line="66" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="225" bodyend="279" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h" declline="66" declcolumn="1"/>
        <references refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</references>
        <references refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" compoundref="gpio__core_8c" startline="43">GPIO_POOL_TAG</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>kmdf_gpio.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>GPIO总线接口头文件</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>提供GPIO特定的接口和数据结构</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_GPIO_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_GPIO_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__bus__common_8h" kindref="compound">kmdf_bus_common.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO总线特定函数声明<sp/>GPIO事件回调<sp/>GPIO特定配置结构体<sp/>GPIO中断类型<sp/>GPIO高低电平有效或低电平有效<sp/>GPIO方向</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14" refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4b" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4b" kindref="member">_GPIO_PIN_DIRECTION</ref><sp/>{</highlight></codeline>
<codeline lineno="15" refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2" kindref="member">GpioDirectionIn</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>输入</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" kindref="member">GpioDirectionOut</ref><sp/>=<sp/>1,<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>输出</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kindref="member">GPIO_PIN_DIRECTION</ref>;</highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070" kindref="member">_GPIO_PIN_POLARITY</ref><sp/>{</highlight></codeline>
<codeline lineno="21" refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" kindref="member">GpioPolarityActiveLow</ref><sp/>=<sp/>0,<sp/><sp/></highlight><highlight class="comment">//<sp/>低电平有效</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" kindref="member">GpioPolarityActiveHigh</ref><sp/>=<sp/>1<sp/><sp/></highlight><highlight class="comment">//<sp/>高电平有效</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" kindref="member">GPIO_PIN_POLARITY</ref>;</highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33" kindref="member">_GPIO_INTERRUPT_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="27" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76" kindref="member">GpioInterruptNone</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>无中断</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560" kindref="member">GpioInterruptRising</ref><sp/>=<sp/>1,<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>上升沿中断</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84" kindref="member">GpioInterruptFalling</ref><sp/>=<sp/>2,<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>下降沿中断</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64" kindref="member">GpioInterruptBoth</ref><sp/>=<sp/>3,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>双沿中断</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42" kindref="member">GpioInterruptLevel</ref><sp/>=<sp/>4<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>电平中断</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kindref="member">GPIO_INTERRUPT_TYPE</ref>;</highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="struct__GPIO__PIN__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__GPIO__PIN__CONFIG" kindref="compound">_GPIO_PIN_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="36" refid="struct__GPIO__PIN__CONFIG_1acc85fb800144aa8336d910544113014c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__GPIO__PIN__CONFIG_1acc85fb800144aa8336d910544113014c" kindref="member">PinNumber</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>GPIO引脚编号</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__GPIO__PIN__CONFIG_1aa3d8c873130efce02f77b07f742a05ff" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kindref="member">GPIO_PIN_DIRECTION</ref><sp/><ref refid="struct__GPIO__PIN__CONFIG_1aa3d8c873130efce02f77b07f742a05ff" kindref="member">Direction</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>输入/输出</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__GPIO__PIN__CONFIG_1a3298588b14792c3fdd790313352c673e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" kindref="member">GPIO_PIN_POLARITY</ref><sp/><ref refid="struct__GPIO__PIN__CONFIG_1a3298588b14792c3fdd790313352c673e" kindref="member">Polarity</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>极性</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="struct__GPIO__PIN__CONFIG_1a16e25974fff79d31225fbb4402059d42" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kindref="member">GPIO_INTERRUPT_TYPE</ref><sp/><ref refid="struct__GPIO__PIN__CONFIG_1a16e25974fff79d31225fbb4402059d42" kindref="member">InterruptType</ref>;<sp/></highlight><highlight class="comment">//<sp/>中断类型</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="struct__GPIO__PIN__CONFIG_1a8d011afe85a8ab79a1f0ab8a0aeeee9f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__GPIO__PIN__CONFIG_1a8d011afe85a8ab79a1f0ab8a0aeeee9f" kindref="member">DebounceTime</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>去抖时间(ms)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="struct__GPIO__PIN__CONFIG_1a0de26b1a7ce28b0b9aa018fe961fdf60" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__GPIO__PIN__CONFIG_1a0de26b1a7ce28b0b9aa018fe961fdf60" kindref="member">PullUp</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>启用上拉</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="struct__GPIO__PIN__CONFIG_1afd66b730c3f6bec3b15b28ccd0832f85" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__GPIO__PIN__CONFIG_1afd66b730c3f6bec3b15b28ccd0832f85" kindref="member">PullDown</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>启用下拉</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="struct__GPIO__PIN__CONFIG_1abd8ea283a387003ee58595a12fb2b5ec" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__GPIO__PIN__CONFIG_1abd8ea283a387003ee58595a12fb2b5ec" kindref="member">OpenDrain</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>开漏模式</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="struct__GPIO__PIN__CONFIG_1aec4597ae991a215e755336600b15e60a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__GPIO__PIN__CONFIG_1aec4597ae991a215e755336600b15e60a" kindref="member">SpbDeviceObject</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB设备对象</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="struct__GPIO__PIN__CONFIG_1a6065b7e884557238880cacf2002110d3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>LARGE_INTEGER<sp/><ref refid="struct__GPIO__PIN__CONFIG_1a6065b7e884557238880cacf2002110d3" kindref="member">SpbConnectionId</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB连接ID</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46" refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref>,<sp/>*<ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref>;</highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight></codeline>
<codeline lineno="48"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" refkind="member"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="51"><highlight class="normal">(*<ref refid="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" kindref="member">GPIO_INTERRUPT_CALLBACK</ref>)(</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>BOOLEAN<sp/>PinValue,</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight></codeline>
<codeline lineno="58"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><ref refid="kmdf__gpio_8h_1a0a73c23c89291af0e81cef7098d10e29" kindref="member">GPIOInitialize</ref>(</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref><sp/>GpioConfig</highlight></codeline>
<codeline lineno="63"><highlight class="normal">);</highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight></codeline>
<codeline lineno="65"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><ref refid="kmdf__gpio_8h_1a785f00e9c0879fb478077d2cdce99906" kindref="member">GPIOUninitialize</ref>(</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref></highlight></codeline>
<codeline lineno="69"><highlight class="normal">);</highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="72" refid="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4" kindref="member">GPIOSetDirection</ref>(</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kindref="member">GPIO_PIN_DIRECTION</ref><sp/><ref refid="gpio__device_8c_1a130124259198fee8d71747e31a529e96" kindref="member">Direction</ref></highlight></codeline>
<codeline lineno="76"><highlight class="normal">);</highlight></codeline>
<codeline lineno="77"><highlight class="normal"></highlight></codeline>
<codeline lineno="78"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="79" refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" kindref="member">GPIOSetValue</ref>(</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>BOOLEAN<sp/>Value</highlight></codeline>
<codeline lineno="83"><highlight class="normal">);</highlight></codeline>
<codeline lineno="84"><highlight class="normal"></highlight></codeline>
<codeline lineno="85"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="86" refid="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb" kindref="member">GPIOGetValue</ref>(</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PBOOLEAN<sp/>Value</highlight></codeline>
<codeline lineno="90"><highlight class="normal">);</highlight></codeline>
<codeline lineno="91"><highlight class="normal"></highlight></codeline>
<codeline lineno="92"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="93" refid="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48" kindref="member">GPIOEnableInterrupt</ref>(</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kindref="member">GPIO_INTERRUPT_TYPE</ref><sp/>InterruptType,</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" kindref="member">GPIO_INTERRUPT_CALLBACK</ref><sp/>Callback,</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="99"><highlight class="normal">);</highlight></codeline>
<codeline lineno="100"><highlight class="normal"></highlight></codeline>
<codeline lineno="101"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="102" refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4" kindref="member">GPIODisableInterrupt</ref>(</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref></highlight></codeline>
<codeline lineno="105"><highlight class="normal">);</highlight></codeline>
<codeline lineno="106"><highlight class="normal"></highlight></codeline>
<codeline lineno="107"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="108" refid="kmdf__gpio_8h_1ade1cb652014fc4a3984567ff49900d81" refkind="member"><highlight class="normal"><ref refid="kmdf__gpio_8h_1ade1cb652014fc4a3984567ff49900d81" kindref="member">GPIOPulse</ref>(</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>,</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>PulseDurationMs</highlight></codeline>
<codeline lineno="112"><highlight class="normal">);</highlight></codeline>
<codeline lineno="113"><highlight class="normal"></highlight></codeline>
<codeline lineno="114"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_GPIO_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h"/>
  </compounddef>
</doxygen>
