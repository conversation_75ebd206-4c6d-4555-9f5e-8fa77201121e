# KMDF Driver Code Quality Enforcement
# u8fd9u4e2au6a21u5757u5b9eu73b0u4e86u4ee3u7801u8d28u91cfu5de5u5177u548cu89c4u8303u68c0u67e5

# u542fu7528u4ee3u7801u89c4u8303u68c0u67e5
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#
function(enable_code_standards)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs "")
    cmake_parse_arguments(STANDARDS "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT STANDARDS_TARGET)
        message(FATAL_ERROR "Code standards TARGET not specified")
    endif()
    
    # u6dfbu52a0u7f16u8bd1u5668u8b66u544a
    if(MSVC)
        target_compile_options(${STANDARDS_TARGET} PRIVATE
            /W4    # u8b66u544au7ea7u522b4
            /WX    # u5c06u8b66u544au89c6u4e3au9519u8bef
            /utf-8 # UTF-8u7f16u7801
            /permissive- # u4e25u683cu7684u6807u51c6u5408u89c4u6027
        )
    else()
        target_compile_options(${STANDARDS_TARGET} PRIVATE
            -Wall
            -Wextra
            -Werror
            -pedantic
        )
    endif()
    
    # u6dfbu52a0u9879u76eeu89c4u8303u5b9au4e49
    target_compile_definitions(${STANDARDS_TARGET} PRIVATE
        -DKMDF_CODING_STANDARDS_ENABLED
    )
    
    # u6dfbu52a0u9879u76eeu7f16u8bd1u5668u5b8fu5b9au4e49
    target_compile_definitions(${STANDARDS_TARGET} PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:_UNICODE UNICODE STRICT USE_UTF8>
    )
    
    message(STATUS "Enabled code standards for: ${STANDARDS_TARGET}")
endfunction()

# u542fu7528u9759u6001u4ee3u7801u5206u6790
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#   HEADERS - u5934u6587u4ef6u5e93
#
function(enable_static_analysis)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs HEADERS)
    cmake_parse_arguments(ANALYSIS "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT ANALYSIS_TARGET)
        message(FATAL_ERROR "Static analysis TARGET not specified")
    endif()
    
    # MSVCu9759u6001u5206u6790
    if(MSVC)
        # u542fu7528Code Analysisu5de5u5177
        target_compile_options(${ANALYSIS_TARGET} PRIVATE
            /analyze
            /analyze:WX- # u4e0du5c06u5206u6790u8b66u544au89c6u4e3au9519u8bef
            /analyze:max_paths 256
        )
    endif()
    
    # u6dfbu52a0u9759u6001u5206u6790u81eau5b9au4e49u76eeu6807
    add_custom_target(${ANALYSIS_TARGET}_analyze
        COMMAND ${CMAKE_COMMAND} -E echo "Running static analysis for ${ANALYSIS_TARGET}"
        COMMAND ${CMAKE_COMMAND} -E echo "Using compiler for analysis"
        # u8fd9u91ccu53efu4ee5u6dfbu52a0clang-tidy, cppcheck u7b49u5de5u5177
        DEPENDS ${ANALYSIS_TARGET}
        COMMENT "Running static code analysis on ${ANALYSIS_TARGET}"
    )
    
    message(STATUS "Enabled static analysis for: ${ANALYSIS_TARGET}")
endfunction()

# u68c0u67e5u5934u6587u4ef6u7ecfu89c4u683cu5f0f
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#   HEADERS - u5934u6587u4ef6u5217u8868
#
function(check_header_format)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs HEADERS)
    cmake_parse_arguments(FORMAT "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT FORMAT_TARGET)
        message(FATAL_ERROR "Header format check TARGET not specified")
    endif()
    
    if(NOT FORMAT_HEADERS)
        message(FATAL_ERROR "Header format check HEADERS not specified")
    endif()
    
    # u6dfbu52a0u5934u6587u4ef6u683cu5f0fu68c0u67e5u76eeu6807
    add_custom_target(${FORMAT_TARGET}_check_headers
        COMMAND ${CMAKE_COMMAND} -E echo "Checking header format for ${FORMAT_TARGET}"
        COMMAND ${Python_EXECUTABLE} ${CMAKE_SOURCE_DIR}/tools/fix_header_guards.py
        DEPENDS ${FORMAT_HEADERS}
        COMMENT "Checking header format for ${FORMAT_TARGET}"
    )
    
    message(STATUS "Added header format check for: ${FORMAT_TARGET}")
endfunction()

# u68c0u67e5u6e90u6587u4ef6u7f16u7801u548cu7b26u53f7u89c4u8303
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#   SOURCES - u6e90u6587u4ef6u5217u8868
#
function(check_source_encoding)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs SOURCES)
    cmake_parse_arguments(ENCODING "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT ENCODING_TARGET)
        message(FATAL_ERROR "Source encoding check TARGET not specified")
    endif()
    
    if(NOT ENCODING_SOURCES)
        message(FATAL_ERROR "Source encoding check SOURCES not specified")
    endif()
    
    # u521bu5efau4e00u4e2au76eeu6807u68c0u67e5u5e76u4feeu590du6e90u6587u4ef6u7f16u7801
    add_custom_target(${ENCODING_TARGET}_check_encoding
        COMMAND ${CMAKE_COMMAND} -E echo "Checking encoding for ${ENCODING_TARGET}"
        COMMAND ${Python_EXECUTABLE} ${CMAKE_SOURCE_DIR}/tools/fix_coding_style.py --dir src
        DEPENDS ${ENCODING_SOURCES}
        COMMENT "Checking source file encoding for ${ENCODING_TARGET}"
    )
    
    # u68c0u67e5u7c7bu578bu662fu5426u7b26u5408u547du540du7c7bu4e3b
    add_custom_target(${ENCODING_TARGET}_check_naming
        COMMAND ${CMAKE_COMMAND} -E echo "Checking naming conventions for ${ENCODING_TARGET}"
        COMMAND ${Python_EXECUTABLE} ${CMAKE_SOURCE_DIR}/tools/fix_source_standards.py --dir src
        DEPENDS ${ENCODING_SOURCES}
        COMMENT "Checking naming conventions for ${ENCODING_TARGET}"
    )
    
    message(STATUS "Added source encoding and naming checks for: ${ENCODING_TARGET}")
endfunction()

# u6dfbu52a0u6240u6709u4ee3u7801u68c0u67e5
#
# u53c2u6570:
#   TARGET - u76eeu6807u540du79f0
#
function(add_all_code_checks)
    # u5b9au4e49u51fdu6570u53c2u6570
    set(options "")
    set(oneValueArgs TARGET)
    set(multiValueArgs "")
    cmake_parse_arguments(CHECKS "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    # u9a8cu8bc1u5fc5u8981u53c2u6570
    if(NOT CHECKS_TARGET)
        message(FATAL_ERROR "Code checks TARGET not specified")
    endif()
    
    # u83b7u53d6u76eeu6807u6e90u6587u4ef6
    get_target_property(sources ${CHECKS_TARGET} SOURCES)
    
    # u542fu7528u6240u6709u4ee3u7801u68c0u67e5
    enable_code_standards(TARGET ${CHECKS_TARGET})
    enable_static_analysis(TARGET ${CHECKS_TARGET})
    
    # u521bu5efau4e00u4e2au805au5408u6240u6709u68c0u67e5u7684u8d85u7ea7u76eeu6807
    add_custom_target(${CHECKS_TARGET}_all_checks ALL
        DEPENDS 
            ${CHECKS_TARGET}
            ${CHECKS_TARGET}_analyze
            ${CHECKS_TARGET}_check_encoding
            ${CHECKS_TARGET}_check_headers
            ${CHECKS_TARGET}_check_naming
        COMMENT "Running all code checks for ${CHECKS_TARGET}"
    )
    
    message(STATUS "Added all code checks for: ${CHECKS_TARGET}")
endfunction()
