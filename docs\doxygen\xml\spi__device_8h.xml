<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="spi__device_8h" kind="file" language="C++">
    <compoundname>spi_device.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="kmdf__spi_8h" local="yes">../bus/kmdf_spi.h</includes>
    <includedby refid="spi__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/spi_device.c</includedby>
    <incdepgraph>
      <node id="6">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__SPI__DEVICE__TRANSFER__PACKET" prot="public">_SPI_DEVICE_TRANSFER_PACKET</innerclass>
    <innerclass refid="struct__SPI__STATISTICS" prot="public">_SPI_STATISTICS</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" prot="public" static="no">
        <name>IOCTL_SPI_BASE</name>
        <initializer>0x8200</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="45" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="45" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a97fe5a41276df38e46922527c4b9baf9" prot="public" static="no">
        <name>IOCTL_SPI_GET_STATISTICS</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="48" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="48" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a8442695a715b85f6516ff535bc5d1409" prot="public" static="no">
        <name>IOCTL_SPI_RESET</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="49" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="49" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1aaba9d20f35713a3c0dd088bfdb433a0b" prot="public" static="no">
        <name>IOCTL_SPI_SET_BUS_SPEED</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="50" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="50" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a471b24a3583fd2212e30e4619ae701be" prot="public" static="no">
        <name>IOCTL_SPI_SET_MODE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 5, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="51" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="51" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a72feec97101aca3be161a59ffe40cc2c" prot="public" static="no">
        <name>IOCTL_SPI_TRANSFER</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="46" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="46" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a21382b2df65b9cb8a11da17114ab9491" prot="public" static="no">
        <name>IOCTL_SPI_TRANSFER_FULL_DUPLEX</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, <ref refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kindref="member">IOCTL_SPI_BASE</ref> + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="47" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="47" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1aa7a1a825b415e6aa12c47463eefc0bb7" prot="public" static="no">
        <name>SPI_TRANSFER_FULL_DUPLEX</name>
        <initializer>0x00000004</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="22" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="22" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a8595e49b5f8ddb021462587455bd2ff5" prot="public" static="no">
        <name>SPI_TRANSFER_NO_CHIPSEL</name>
        <initializer>0x00000008</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="23" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="23" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1a935c3756801c209968fb16a7be795396" prot="public" static="no">
        <name>SPI_TRANSFER_READ</name>
        <initializer>0x00000001</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="20" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="20" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="spi__device_8h_1af27a6537c3222c6796876ff953298b42" prot="public" static="no">
        <name>SPI_TRANSFER_WRITE</name>
        <initializer>0x00000002</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="21" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" bodystart="21" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__DEVICE__TRANSFER__PACKET" kindref="compound">_SPI_DEVICE_TRANSFER_PACKET</ref> *</type>
        <definition>typedef struct _SPI_DEVICE_TRANSFER_PACKET * PSPI_DEVICE_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>PSPI_DEVICE_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="33" column="57"/>
      </memberdef>
      <memberdef kind="typedef" id="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__STATISTICS" kindref="compound">_SPI_STATISTICS</ref> *</type>
        <definition>typedef struct _SPI_STATISTICS * PSPI_STATISTICS</definition>
        <argsstring></argsstring>
        <name>PSPI_STATISTICS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="42" column="33"/>
      </memberdef>
      <memberdef kind="typedef" id="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__DEVICE__TRANSFER__PACKET" kindref="compound">_SPI_DEVICE_TRANSFER_PACKET</ref></type>
        <definition>typedef struct _SPI_DEVICE_TRANSFER_PACKET SPI_DEVICE_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>SPI_DEVICE_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="33" column="28"/>
      </memberdef>
      <memberdef kind="typedef" id="spi__device_8h_1aa83effe237db8be37288dceaeeab8d57" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__STATISTICS" kindref="compound">_SPI_STATISTICS</ref></type>
        <definition>typedef struct _SPI_STATISTICS SPI_STATISTICS</definition>
        <argsstring></argsstring>
        <name>SPI_STATISTICS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="42" column="16"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="spi__device_8h_1a052b57a96b994325a574bcb9f3db837a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID SpiDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>SpiDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceCleanup - u6e05u7406SPIu8bbeu5907u8d44u6e90</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="77" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="113" bodyend="142" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="77" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1ae2be7c6b48ddf5b08876e1115879469d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceGetStatistics</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PSPI_STATISTICS Statistics)</argsstring>
        <name>SpiDeviceGetStatistics</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ <ref refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kindref="member">PSPI_STATISTICS</ref></type>
          <declname>Statistics</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceGetStatistics - u83b7u53d6SPIu8bbeu5907u7edfu8ba1u4fe1u606f</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Statistics</parametername>
</parameternamelist>
<parameterdescription>
<para>u7edfu8ba1u4fe1u606fu7ed3u6784</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="146" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="298" bodyend="308" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="146" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1a6939e12311ec72f975bcd03a4250a3e2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</argsstring>
        <name>SpiDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref></type>
          <declname>SpiConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceInitialize - u521du59cbu5316SPIu8bbeu5907</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">SpiConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>SPIu914du7f6e</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="66" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="31" bodyend="45" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="66" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" compoundref="spi__device_8c" startline="20">SpiConfig</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1a3bc98267d67ee8988179bde952efaa87" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceRead</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</argsstring>
        <name>SpiDeviceRead</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_writes_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesRead</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceRead - u4eceSu5907u8bfbu53d6u6570u636e</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>u5bc4u5b58u5668u5730u5740 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>u6570u636eu7f13u51b2u533a </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>u7f13u51b2u533au957fu5ea6 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesRead</parametername>
</parameternamelist>
<parameterdescription>
<para>u5b9eu9645u8bfbu53d6u7684u5b57u8282u6570</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="109" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="194" bodyend="210" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="109" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1a30b9d7f482d2a1343e50a60ea8d4135a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceSetClockFrequency</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG ClockFrequency)</argsstring>
        <name>SpiDeviceSetClockFrequency</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>ClockFrequency</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceSetClockFrequency - u8bbeu7f6eSPIu8bbeu5907u65f6u949fu9891u7387</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">ClockFrequency</parametername>
</parameternamelist>
<parameterdescription>
<para>u65f6u949fu9891u7387(Hz)</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="174" column="1" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="174" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1a3c91b33450d309fa46affa22959f8607" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceSetMode</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ SPI_MODE Mode)</argsstring>
        <name>SpiDeviceSetMode</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kindref="member">SPI_MODE</ref></type>
          <declname>Mode</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceSetMode - u8bbeu7f6eSPIu8bbeu5907u5de5u4f5cu6a21u5f0f</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Mode</parametername>
</parameternamelist>
<parameterdescription>
<para>SPIu6a21u5f0f</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="160" column="1" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="160" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1a2428921b9d71ab9d24f34e0a7b23487c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceTransfer</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_DEVICE_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</argsstring>
        <name>SpiDeviceTransfer</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kindref="member">PSPI_DEVICE_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceTransfer - u6267u884cSPIu4f20u8f93</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TransferPacket</parametername>
</parameternamelist>
<parameterdescription>
<para>u4f20u8f93u6570u636eu5305 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TimeoutMs</parametername>
</parameternamelist>
<parameterdescription>
<para>u8d85u65f6u65f6u95f4(u6bebu79d2)</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="91" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="148" bodyend="160" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="91" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="spi__device_8h_1ae90ccf3d865bebb54c2c76e10fcbcaa8" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS SpiDeviceWrite</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</argsstring>
        <name>SpiDeviceWrite</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_reads_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesWritten</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>SpiDeviceWrite - u5411SPIu8bbeu5907u5199u5165u6570u636e</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDFu8bbeu5907u5bf9u8c61 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>u5bc4u5b58u5668u5730u5740 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>u6570u636eu7f13u51b2u533a </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>u7f13u51b2u533au957fu5ea6 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesWritten</parametername>
</parameternamelist>
<parameterdescription>
<para>u5b9eu9645u5199u5165u7684u5b57u8282u6570</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS u72b6u6001u7801 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h" line="129" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/spi_device.c" bodystart="241" bodyend="257" declfile="C:/KMDF Driver1/include/hal/devices/spi_device.h" declline="129" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>spi_device.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>SPIu8bbeu5907u9a71u52a8u63a5u53e3u5934u6587u4ef6</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>u63d0u4f9bSPIu8bbeu5907u64cdu4f5cu7684u7edfu4e00u63a5u53e3</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>SPI_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SPI_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__spi_8h" kindref="compound">../bus/kmdf_spi.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="comment">//<sp/>u5e38u91cfu548cu7ed3u6784u5b9au4e49</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPIu4f20u8f93u6807u5fd7</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="spi__device_8h_1a935c3756801c209968fb16a7be795396" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SPI_TRANSFER_READ<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0x00000001<sp/><sp/></highlight><highlight class="comment">//<sp/>u8bfbu4f20u8f93</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="spi__device_8h_1af27a6537c3222c6796876ff953298b42" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SPI_TRANSFER_WRITE<sp/><sp/><sp/><sp/><sp/><sp/><sp/>0x00000002<sp/><sp/></highlight><highlight class="comment">//<sp/>u5199u4f20u8f93</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="spi__device_8h_1aa7a1a825b415e6aa12c47463eefc0bb7" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SPI_TRANSFER_FULL_DUPLEX<sp/>0x00000004<sp/><sp/></highlight><highlight class="comment">//<sp/>u5168u53ccu5de5u4f20u8f93</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="spi__device_8h_1a8595e49b5f8ddb021462587455bd2ff5" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>SPI_TRANSFER_NO_CHIPSEL<sp/><sp/>0x00000008<sp/><sp/></highlight><highlight class="comment">//<sp/>u4e0du64cdu4f5cu7247u9009u4fe1u53f7</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPIu4f20u8f93u6570u636eu5305u7ed3u6784</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="struct__SPI__DEVICE__TRANSFER__PACKET" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET" kindref="compound">_SPI_DEVICE_TRANSFER_PACKET</ref><sp/>{</highlight></codeline>
<codeline lineno="27" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ace00b8a3ea9807f1b300e84fd6a8d7be" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ace00b8a3ea9807f1b300e84fd6a8d7be" kindref="member">WriteBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u5199u6570u636eu7f13u51b2u533a</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a8575b3351fe18405d3eb99caf11e503b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a8575b3351fe18405d3eb99caf11e503b" kindref="member">WriteLength</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u5199u6570u636eu957fu5ea6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a182672e24da62e3c9fcbc4baf9b285a1" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a182672e24da62e3c9fcbc4baf9b285a1" kindref="member">ReadBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u8bfbu6570u636eu7f13u51b2u533a</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ab93e815b31b5919f672691f7f01b06af" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ab93e815b31b5919f672691f7f01b06af" kindref="member">ReadLength</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u8bfbu6570u636eu957fu5ea6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a5ce41e65ae89fce3ef5107acc4fb11d2" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a5ce41e65ae89fce3ef5107acc4fb11d2" kindref="member">Flags</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u4f20u8f93u6807u5fd7</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a744606c6d58de9e0a6e0fe4e4acfbec6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a744606c6d58de9e0a6e0fe4e4acfbec6" kindref="member">DelayInMicroseconds</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u4f20u8f93u540eu7684u5ef6u8fdfuff08u5faeu79d2uff09</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33" refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" refkind="member"><highlight class="normal">}<sp/><ref refid="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" kindref="member">SPI_DEVICE_TRANSFER_PACKET</ref>,<sp/>*<ref refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kindref="member">PSPI_DEVICE_TRANSFER_PACKET</ref>;</highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPIu7edfu8ba1u4fe1u606fu7ed3u6784</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__SPI__STATISTICS" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__SPI__STATISTICS" kindref="compound">_SPI_STATISTICS</ref><sp/>{</highlight></codeline>
<codeline lineno="37" refid="struct__SPI__STATISTICS_1aed0da9f11638910222e8bcf387a1d38f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__STATISTICS_1aed0da9f11638910222e8bcf387a1d38f" kindref="member">TransactionCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u4f20u8f93u603bu6570</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__SPI__STATISTICS_1af7be253804f923ed13df884c7c93cdeb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__STATISTICS_1af7be253804f923ed13df884c7c93cdeb" kindref="member">ErrorCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u9519u8befu603bu6570</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="struct__SPI__STATISTICS_1a254521d63fa6f195accebd432ad9a2c9" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__SPI__STATISTICS_1a254521d63fa6f195accebd432ad9a2c9" kindref="member">DeviceInitialized</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u8bbeu5907u662fu5426u5df2u521du59cbu5316</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="struct__SPI__STATISTICS_1aea910d0fe28ff0978ec2ecfec60b8c52" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__STATISTICS_1aea910d0fe28ff0978ec2ecfec60b8c52" kindref="member">ClockFrequency</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u603bu7ebfu65f6u949fu9891u7387</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="struct__SPI__STATISTICS_1aaddc6c47a910b922a46c02072a72b0ef" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kindref="member">SPI_MODE</ref><sp/><ref refid="struct__SPI__STATISTICS_1aaddc6c47a910b922a46c02072a72b0ef" kindref="member">Mode</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPIu6a21u5f0f</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" refkind="member"><highlight class="normal">}<sp/><ref refid="spi__device_8h_1aa83effe237db8be37288dceaeeab8d57" kindref="member">SPI_STATISTICS</ref>,<sp/>*<ref refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kindref="member">PSPI_STATISTICS</ref>;</highlight></codeline>
<codeline lineno="43"><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI<sp/>IOCTL<sp/>u63a7u5236u7801</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0x8200</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46" refid="spi__device_8h_1a72feec97101aca3be161a59ffe40cc2c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_TRANSFER<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>0,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47" refid="spi__device_8h_1a21382b2df65b9cb8a11da17114ab9491" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_TRANSFER_FULL_DUPLEX<sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>1,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="spi__device_8h_1a97fe5a41276df38e46922527c4b9baf9" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_GET_STATISTICS<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>2,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49" refid="spi__device_8h_1a8442695a715b85f6516ff535bc5d1409" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_RESET<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>3,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="spi__device_8h_1aaba9d20f35713a3c0dd088bfdb433a0b" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_SET_BUS_SPEED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>4,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51" refid="spi__device_8h_1a471b24a3583fd2212e30e4619ae701be" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_SPI_SET_MODE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_SPI_BASE<sp/>+<sp/>5,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52"><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPIu8bbeu5907u63a5u53e3u51fdu6570u58f0u660e</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal"></highlight></codeline>
<codeline lineno="65"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><ref refid="spi__device_8h_1a6939e12311ec72f975bcd03a4250a3e2" kindref="member">SpiDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></highlight></codeline>
<codeline lineno="69"><highlight class="normal">);</highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight></codeline>
<codeline lineno="76"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><ref refid="spi__device_8h_1a052b57a96b994325a574bcb9f3db837a" kindref="member">SpiDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="79"><highlight class="normal">);</highlight></codeline>
<codeline lineno="80"><highlight class="normal"></highlight></codeline>
<codeline lineno="90"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><ref refid="spi__device_8h_1a2428921b9d71ab9d24f34e0a7b23487c" kindref="member">SpiDeviceTransfer</ref>(</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kindref="member">PSPI_DEVICE_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="95"><highlight class="normal">);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"></highlight></codeline>
<codeline lineno="108"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><ref refid="spi__device_8h_1a3bc98267d67ee8988179bde952efaa87" kindref="member">SpiDeviceRead</ref>(</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesRead</highlight></codeline>
<codeline lineno="115"><highlight class="normal">);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"></highlight></codeline>
<codeline lineno="128"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="129"><highlight class="normal"><ref refid="spi__device_8h_1ae90ccf3d865bebb54c2c76e10fcbcaa8" kindref="member">SpiDeviceWrite</ref>(</highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesWritten</highlight></codeline>
<codeline lineno="135"><highlight class="normal">);</highlight></codeline>
<codeline lineno="136"><highlight class="normal"></highlight></codeline>
<codeline lineno="145"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="146"><highlight class="normal"><ref refid="spi__device_8h_1ae2be7c6b48ddf5b08876e1115879469d" kindref="member">SpiDeviceGetStatistics</ref>(</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kindref="member">PSPI_STATISTICS</ref><sp/>Statistics</highlight></codeline>
<codeline lineno="149"><highlight class="normal">);</highlight></codeline>
<codeline lineno="150"><highlight class="normal"></highlight></codeline>
<codeline lineno="159"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="160" refid="spi__device_8h_1a3c91b33450d309fa46affa22959f8607" refkind="member"><highlight class="normal"><ref refid="spi__device_8h_1a3c91b33450d309fa46affa22959f8607" kindref="member">SpiDeviceSetMode</ref>(</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kindref="member">SPI_MODE</ref><sp/>Mode</highlight></codeline>
<codeline lineno="163"><highlight class="normal">);</highlight></codeline>
<codeline lineno="164"><highlight class="normal"></highlight></codeline>
<codeline lineno="173"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="174" refid="spi__device_8h_1a30b9d7f482d2a1343e50a60ea8d4135a" refkind="member"><highlight class="normal"><ref refid="spi__device_8h_1a30b9d7f482d2a1343e50a60ea8d4135a" kindref="member">SpiDeviceSetClockFrequency</ref>(</highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>ClockFrequency</highlight></codeline>
<codeline lineno="177"><highlight class="normal">);</highlight></codeline>
<codeline lineno="178"><highlight class="normal"></highlight></codeline>
<codeline lineno="179"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>SPI_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/devices/spi_device.h"/>
  </compounddef>
</doxygen>
