digraph "C:/KMDF Driver1/include/core/error/error_codes.h"
{
 // LATEX_PDF_SIZE
  bgcolor="transparent";
  edge [fontname=Helvetica,fontsize=10,labelfontname=Helvetica,labelfontsize=10];
  node [fontname=Helvetica,fontsize=10,shape=box,height=0.2,width=0.4];
  Node1 [id="Node000001",label="C:/KMDF Driver1/include\l/core/error/error_codes.h",height=0.2,width=0.4,color="gray40", fillcolor="grey60", style="filled", fontcolor="black",tooltip=" "];
  Node1 -> Node2 [id="edge1_Node000001_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 [id="Node000002",label="C:/KMDF Driver1/include\l/core/device/device_manager.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8h.html",tooltip="Brief description."];
  Node2 -> Node3 [id="edge2_Node000002_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node3 [id="Node000003",label="C:/KMDF Driver1/include\l/core/driver/driver_entry.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8h.html",tooltip="Brief description."];
  Node3 -> Node4 [id="edge3_Node000003_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node4 [id="Node000004",label="C:/KMDF Driver1/src\l/core/driver/driver\l_entry.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8c.html",tooltip=" "];
  Node3 -> Node5 [id="edge4_Node000003_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node5 [id="Node000005",label="C:/KMDF Driver1/src\l/driver_main.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__main_8c.html",tooltip=" "];
  Node2 -> Node6 [id="edge5_Node000002_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 [id="Node000006",label="C:/KMDF Driver1/src\l/core/device/device\l_manager.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8c.html",tooltip=" "];
  Node2 -> Node7 [id="edge6_Node000002_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node7 [id="Node000007",label="C:/KMDF Driver1/src\l/core/driver/driver\l_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8c.html",tooltip=" "];
  Node2 -> Node5 [id="edge7_Node000002_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 -> Node8 [id="edge8_Node000002_Node000008",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 [id="Node000008",label="C:/KMDF Driver1/src\l/precomp.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8h.html",tooltip=" "];
  Node8 -> Node6 [id="edge9_Node000008_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node4 [id="edge10_Node000008_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node9 [id="edge11_Node000008_Node000009",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node9 [id="Node000009",label="C:/KMDF Driver1/src\l/core/log/driver_log.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__log_8c.html",tooltip=" "];
  Node8 -> Node5 [id="edge12_Node000008_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node10 [id="edge13_Node000008_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node10 [id="Node000010",label="C:/KMDF Driver1/src\l/hal/bus/gpio_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__core_8c.html",tooltip=" "];
  Node8 -> Node11 [id="edge14_Node000008_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node11 [id="Node000011",label="C:/KMDF Driver1/src\l/hal/bus/i2c_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__core_8c.html",tooltip=" "];
  Node8 -> Node12 [id="edge15_Node000008_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node12 [id="Node000012",label="C:/KMDF Driver1/src\l/hal/bus/spi_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__core_8c.html",tooltip=" "];
  Node8 -> Node13 [id="edge16_Node000008_Node000013",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node13 [id="Node000013",label="C:/KMDF Driver1/src\l/precomp.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8c.html",tooltip=" "];
  Node1 -> Node14 [id="edge17_Node000001_Node000014",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 [id="Node000014",label="C:/KMDF Driver1/include\l/core/driver/driver_core.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8h.html",tooltip=" "];
  Node14 -> Node7 [id="edge18_Node000014_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 -> Node4 [id="edge19_Node000014_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 -> Node5 [id="edge20_Node000014_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 -> Node8 [id="edge21_Node000014_Node000008",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node3 [id="edge22_Node000001_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node15 [id="edge23_Node000001_Node000015",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node15 [id="Node000015",label="C:/KMDF Driver1/include\l/core/error/error_handling.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$error__handling_8h.html",tooltip="驱动程序错误处理和断言宏定义"];
  Node1 -> Node16 [id="edge24_Node000001_Node000016",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 [id="Node000016",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_bus_common.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__bus__common_8h.html",tooltip=" "];
  Node16 -> Node2 [id="edge25_Node000016_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 -> Node3 [id="edge26_Node000016_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 -> Node17 [id="edge27_Node000016_Node000017",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node17 [id="Node000017",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_gpio.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__gpio_8h.html",tooltip=" "];
  Node17 -> Node18 [id="edge28_Node000017_Node000018",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node18 [id="Node000018",label="C:/KMDF Driver1/include\l/hal/devices/gpio_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__device_8h.html",tooltip=" "];
  Node18 -> Node19 [id="edge29_Node000018_Node000019",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node19 [id="Node000019",label="C:/KMDF Driver1/src\l/hal/devices/gpio_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__device_8c.html",tooltip=" "];
  Node17 -> Node10 [id="edge30_Node000017_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 -> Node20 [id="edge31_Node000016_Node000020",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node20 [id="Node000020",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_i2c.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__i2c_8h.html",tooltip=" "];
  Node20 -> Node21 [id="edge32_Node000020_Node000021",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node21 [id="Node000021",label="C:/KMDF Driver1/include\l/hal/devices/i2c_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__device_8h.html",tooltip=" "];
  Node20 -> Node5 [id="edge33_Node000020_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node20 -> Node11 [id="edge34_Node000020_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node20 -> Node22 [id="edge35_Node000020_Node000022",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node22 [id="Node000022",label="C:/KMDF Driver1/src\l/hal/devices/i2c_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__device_8c.html",tooltip=" "];
  Node16 -> Node23 [id="edge36_Node000016_Node000023",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node23 [id="Node000023",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_spi.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__spi_8h.html",tooltip=" "];
  Node23 -> Node24 [id="edge37_Node000023_Node000024",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node24 [id="Node000024",label="C:/KMDF Driver1/include\l/hal/devices/spi_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__device_8h.html",tooltip=" "];
  Node24 -> Node25 [id="edge38_Node000024_Node000025",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node25 [id="Node000025",label="C:/KMDF Driver1/src\l/hal/devices/spi_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__device_8c.html",tooltip=" "];
  Node23 -> Node12 [id="edge39_Node000023_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node23 -> Node25 [id="edge40_Node000023_Node000025",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 -> Node5 [id="edge41_Node000016_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node26 [id="edge42_Node000001_Node000026",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node26 [id="Node000026",label="C:/KMDF Driver1/include\l/hal/hal_interface.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$hal__interface_8h.html",tooltip=" "];
  Node26 -> Node22 [id="edge43_Node000026_Node000022",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node26 -> Node25 [id="edge44_Node000026_Node000025",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node6 [id="edge45_Node000001_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node7 [id="edge46_Node000001_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node27 [id="edge47_Node000001_Node000027",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node27 [id="Node000027",label="C:/KMDF Driver1/src\l/core/error/error_handling.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$error__handling_8c.html",tooltip=" "];
  Node1 -> Node5 [id="edge48_Node000001_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node10 [id="edge49_Node000001_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node11 [id="edge50_Node000001_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node12 [id="edge51_Node000001_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node19 [id="edge52_Node000001_Node000019",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node22 [id="edge53_Node000001_Node000022",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node25 [id="edge54_Node000001_Node000025",dir="back",color="steelblue1",style="solid",tooltip=" "];
}
