<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: _GPIO_PIN_CONTEXT Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('struct__GPIO__PIN__CONTEXT.html','','struct__GPIO__PIN__CONTEXT-members'); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">_GPIO_PIN_CONTEXT Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-pub-methods" class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab343a956248ebb07de6a72eaeb55ec35" id="r_ab343a956248ebb07de6a72eaeb55ec35"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab343a956248ebb07de6a72eaeb55ec35">GPIOInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a> GpioConfig)</td></tr>
<tr class="memitem:acd3cbb2291f76aabd30090072d539050" id="r_acd3cbb2291f76aabd30090072d539050"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acd3cbb2291f76aabd30090072d539050">if</a> (gpioManager==NULL)</td></tr>
<tr class="memitem:a38aa560da9bc169cb9b45c3df5ea4454" id="r_a38aa560da9bc169cb9b45c3df5ea4454"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a38aa560da9bc169cb9b45c3df5ea4454">LogError</a> (ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;<a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a846739f679fc9ddea85cb263aa72dc9f" id="r_a846739f679fc9ddea85cb263aa72dc9f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a846739f679fc9ddea85cb263aa72dc9f">WdfSpinLockRelease</a> (gpioManager-&gt;<a class="el" href="#ae1602e3f0f02ccfe7b30fdaa89b017a7">Lock</a>)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-pub-attribs" class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a44db5b39e5c14ce83ac0637da253fb47" id="r_a44db5b39e5c14ce83ac0637da253fb47"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_DPC&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a44db5b39e5c14ce83ac0637da253fb47">EvtGpioInterruptDpc</a></td></tr>
<tr class="memitem:a4b4c364430ee4f37c01413a6552df5d0" id="r_a4b4c364430ee4f37c01413a6552df5d0"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_ISR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4b4c364430ee4f37c01413a6552df5d0">EvtGpioInterruptIsr</a></td></tr>
<tr class="memitem:ae1602e3f0f02ccfe7b30fdaa89b017a7" id="r_ae1602e3f0f02ccfe7b30fdaa89b017a7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae1602e3f0f02ccfe7b30fdaa89b017a7">Lock</a></td></tr>
<tr class="memitem:a4dd51420a9c389d77c721c25d4349927" id="r_a4dd51420a9c389d77c721c25d4349927"><td class="memItemLeft" align="right" valign="top">PGPIO_PIN_CONTEXT&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4dd51420a9c389d77c721c25d4349927">Pins</a> [256]</td></tr>
<tr class="memitem:a81f98266b74a3a8d80a632eead026240" id="r_a81f98266b74a3a8d80a632eead026240"><td class="memItemLeft" align="right" valign="top">WDFIOTARGET&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a81f98266b74a3a8d80a632eead026240">SpbIoTarget</a></td></tr>
<tr class="memitem:ad95efe470c1ee3b4b0f0d81c7f3c53fb" id="r_ad95efe470c1ee3b4b0f0d81c7f3c53fb"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad95efe470c1ee3b4b0f0d81c7f3c53fb">STATUS_INVALID_PARAMETER</a></td></tr>
<tr class="memitem:a9e971cde7006142d4b5ac56689228e0b" id="r_a9e971cde7006142d4b5ac56689228e0b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9e971cde7006142d4b5ac56689228e0b">WdfDevice</a></td></tr>
</table>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Member Function Documentation</h2>
<a id="ab343a956248ebb07de6a72eaeb55ec35" name="ab343a956248ebb07de6a72eaeb55ec35"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab343a956248ebb07de6a72eaeb55ec35">&#9670;&#160;</a></span>GPIOInitialize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS _GPIO_PIN_CONTEXT::GPIOInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>GpioConfig</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="struct__GPIO__PIN__CONTEXT_ab343a956248ebb07de6a72eaeb55ec35_cgraph.png" border="0" usemap="#astruct__GPIO__PIN__CONTEXT_ab343a956248ebb07de6a72eaeb55ec35_cgraph" loading="lazy" alt=""/></div>
<map name="astruct__GPIO__PIN__CONTEXT_ab343a956248ebb07de6a72eaeb55ec35_cgraph" id="astruct__GPIO__PIN__CONTEXT_ab343a956248ebb07de6a72eaeb55ec35_cgraph">
<area shape="rect" title=" " alt="" coords="5,47,175,89"/>
<area shape="rect" href="struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454" title=" " alt="" coords="223,5,392,48"/>
<area shape="poly" title=" " alt="" coords="174,49,207,43,208,48,175,55"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="265,96,350,123"/>
<area shape="poly" title=" " alt="" coords="175,81,251,96,250,101,174,87"/>
<area shape="poly" title=" " alt="" coords="271,97,264,87,269,77,283,71,307,69,333,72,347,78,345,83,332,77,307,75,285,77,272,81,269,87,275,94"/>
</map>
</div>

</div>
</div>
<a id="acd3cbb2291f76aabd30090072d539050" name="acd3cbb2291f76aabd30090072d539050"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd3cbb2291f76aabd30090072d539050">&#9670;&#160;</a></span>if()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">_GPIO_PIN_CONTEXT::if </td>
          <td>(</td>
          <td class="paramtype">gpioManager</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="struct__GPIO__PIN__CONTEXT_acd3cbb2291f76aabd30090072d539050_cgraph.png" border="0" usemap="#astruct__GPIO__PIN__CONTEXT_acd3cbb2291f76aabd30090072d539050_cgraph" loading="lazy" alt=""/></div>
<map name="astruct__GPIO__PIN__CONTEXT_acd3cbb2291f76aabd30090072d539050_cgraph" id="astruct__GPIO__PIN__CONTEXT_acd3cbb2291f76aabd30090072d539050_cgraph">
<area shape="rect" title=" " alt="" coords="5,13,182,40"/>
<area shape="rect" href="struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454" title=" " alt="" coords="230,5,399,48"/>
<area shape="poly" title=" " alt="" coords="182,24,214,24,214,29,182,29"/>
</map>
</div>

</div>
</div>
<a id="a38aa560da9bc169cb9b45c3df5ea4454" name="a38aa560da9bc169cb9b45c3df5ea4454"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38aa560da9bc169cb9b45c3df5ea4454">&#9670;&#160;</a></span>LogError()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">_GPIO_PIN_CONTEXT::LogError </td>
          <td>(</td>
          <td class="paramtype">ERROR_PIN_ALREADY_REGISTERED</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;Pin %d is already configured or out of range&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">GpioConfig-&gt;</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="struct__GPIO__PIN__CONTEXT_a38aa560da9bc169cb9b45c3df5ea4454_icgraph.png" border="0" usemap="#astruct__GPIO__PIN__CONTEXT_a38aa560da9bc169cb9b45c3df5ea4454_icgraph" loading="lazy" alt=""/></div>
<map name="astruct__GPIO__PIN__CONTEXT_a38aa560da9bc169cb9b45c3df5ea4454_icgraph" id="astruct__GPIO__PIN__CONTEXT_a38aa560da9bc169cb9b45c3df5ea4454_icgraph">
<area shape="rect" title=" " alt="" coords="230,35,399,77"/>
<area shape="rect" href="struct__GPIO__PIN__CONTEXT.html#ab343a956248ebb07de6a72eaeb55ec35" title=" " alt="" coords="9,5,178,48"/>
<area shape="poly" title=" " alt="" coords="214,45,178,41,179,35,215,40"/>
<area shape="rect" href="struct__GPIO__PIN__CONTEXT.html#acd3cbb2291f76aabd30090072d539050" title=" " alt="" coords="5,72,182,99"/>
<area shape="poly" title=" " alt="" coords="215,72,183,76,182,71,214,67"/>
</map>
</div>

</div>
</div>
<a id="a846739f679fc9ddea85cb263aa72dc9f" name="a846739f679fc9ddea85cb263aa72dc9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a846739f679fc9ddea85cb263aa72dc9f">&#9670;&#160;</a></span>WdfSpinLockRelease()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">_GPIO_PIN_CONTEXT::WdfSpinLockRelease </td>
          <td>(</td>
          <td class="paramtype">gpioManager-&gt;</td>          <td class="paramname"><span class="paramname"><em>Lock</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-variable-members" id="doc-variable-members"></a><h2 id="header-doc-variable-members" class="groupheader">Member Data Documentation</h2>
<a id="a44db5b39e5c14ce83ac0637da253fb47" name="a44db5b39e5c14ce83ac0637da253fb47"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44db5b39e5c14ce83ac0637da253fb47">&#9670;&#160;</a></span>EvtGpioInterruptDpc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_INTERRUPT_DPC _GPIO_PIN_CONTEXT::EvtGpioInterruptDpc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4b4c364430ee4f37c01413a6552df5d0" name="a4b4c364430ee4f37c01413a6552df5d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4b4c364430ee4f37c01413a6552df5d0">&#9670;&#160;</a></span>EvtGpioInterruptIsr</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_INTERRUPT_ISR _GPIO_PIN_CONTEXT::EvtGpioInterruptIsr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae1602e3f0f02ccfe7b30fdaa89b017a7" name="ae1602e3f0f02ccfe7b30fdaa89b017a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1602e3f0f02ccfe7b30fdaa89b017a7">&#9670;&#160;</a></span>Lock</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a> _GPIO_PIN_CONTEXT::Lock</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4dd51420a9c389d77c721c25d4349927" name="a4dd51420a9c389d77c721c25d4349927"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4dd51420a9c389d77c721c25d4349927">&#9670;&#160;</a></span>Pins</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PGPIO_PIN_CONTEXT _GPIO_PIN_CONTEXT::Pins[256]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a81f98266b74a3a8d80a632eead026240" name="a81f98266b74a3a8d80a632eead026240"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81f98266b74a3a8d80a632eead026240">&#9670;&#160;</a></span>SpbIoTarget</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">WDFIOTARGET _GPIO_PIN_CONTEXT::SpbIoTarget</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad95efe470c1ee3b4b0f0d81c7f3c53fb" name="ad95efe470c1ee3b4b0f0d81c7f3c53fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad95efe470c1ee3b4b0f0d81c7f3c53fb">&#9670;&#160;</a></span>STATUS_INVALID_PARAMETER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return _GPIO_PIN_CONTEXT::STATUS_INVALID_PARAMETER</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9e971cde7006142d4b5ac56689228e0b" name="a9e971cde7006142d4b5ac56689228e0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9e971cde7006142d4b5ac56689228e0b">&#9670;&#160;</a></span>WdfDevice</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> _GPIO_PIN_CONTEXT::WdfDevice</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>C:/KMDF Driver1/src/hal/bus/<a class="el" href="gpio__core_8c.html">gpio_core.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
