{"name": "Gemini API", "provider": "gemini", "api_key": "zaSyB66wIbZKJJR4fgrgf59CKkaELXG67WDdE", "base_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "api_base": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "model": "gemini-2.0-flash", "alternate_models": ["gemini-2.5-pro-exp-03-25:free", "gemini-1.5-pro"], "max_tokens": 8192, "temperature": 0.7, "timeout": 60, "retry_count": 3, "retry_delay": 2, "system_prompt": "你是一个擅长代码分析的Gemini大模型，请为Windows驱动程序提供准确深入的源码解读和中文注释，并指出潜在的问题和优化方向。"}