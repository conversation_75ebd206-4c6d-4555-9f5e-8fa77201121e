{"name": "DeepSeek Coder", "provider": "deepseek", "api_key": "***********************************", "base_url": "https://api.deepseek.com/v1/chat/completions", "api_base": "https://api.deepseek.com/v1/chat/completions", "model": "deepseek-coder", "alternate_models": ["deepseek-chat"], "max_tokens": 8192, "temperature": 0.2, "top_p": 0.8, "timeout": 120, "retry_count": 3, "retry_delay": 5, "system_prompt": "你是DeepSeek Coder模型，一个专注于分析复杂代码的专家。请利用你对系统编程和底层架构的专业知识，对Windows驱动程序代码进行全面、深入的分析。\n\n关注以下关键方面：\n1. 驱动程序框架和架构设计\n2. 重要的注册调用和回调函数\n3. I/O处理和缓冲区管理\n4. 硬件交互和断点处理\n5. 内核模式与用户模式的过渡\n\n请提供详细的中文注释和解释，包括代码的意图、执行过程、可能的边界条件和错误处理机制。如果发现潜在问题或优化空间，请给出具体的改进建议。", "code_optimization": true}