<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/bus/i2c_core.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('i2c__core_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">i2c_core.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="precomp_8h_source.html">../../precomp.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="kmdf__i2c_8h_source.html">../../../include/hal/bus/kmdf_i2c.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for i2c_core.c:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2bus_2i2c__core_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2i2c__core_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2i2c__core_8c">
<area shape="rect" title=" " alt="" coords="1013,5,1158,48"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="836,96,942,123"/>
<area shape="poly" title=" " alt="" coords="1036,51,935,92,933,87,1034,46"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="902,427,1061,453"/>
<area shape="poly" title=" " alt="" coords="1013,33,753,47,587,59,418,75,260,95,129,120,78,134,40,150,16,166,10,174,8,183,8,268,10,277,16,286,40,305,77,321,127,337,253,364,402,385,556,401,699,413,886,424,886,430,698,418,555,406,401,390,252,369,126,342,76,326,37,309,12,290,5,279,3,268,3,182,5,172,12,162,37,145,77,129,128,115,260,90,418,70,587,54,753,42,1012,28"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="1687,344,1809,371"/>
<area shape="poly" title=" " alt="" coords="1159,32,1266,47,1400,73,1545,112,1618,138,1686,168,1743,197,1763,215,1781,244,1787,267,1784,291,1767,332,1762,330,1779,289,1781,267,1776,246,1759,218,1740,201,1684,173,1616,143,1544,117,1399,78,1265,52,1158,38"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="999,253,1121,280"/>
<area shape="poly" title=" " alt="" coords="1107,47,1131,76,1153,114,1165,156,1164,178,1157,199,1134,227,1105,248,1102,244,1130,223,1152,196,1159,177,1160,157,1148,116,1127,79,1103,50"/>
<area shape="rect" title=" " alt="" coords="144,171,227,197"/>
<area shape="poly" title=" " alt="" coords="836,116,584,134,417,150,243,173,242,168,416,145,584,129,835,111"/>
<area shape="rect" title=" " alt="" coords="949,501,1014,528"/>
<area shape="poly" title=" " alt="" coords="943,109,1105,114,1320,124,1533,141,1620,153,1686,168,1728,181,1761,194,1789,212,1819,244,1835,266,1843,287,1851,336,1855,358,1850,380,1827,412,1801,431,1770,444,1731,456,1669,471,1586,483,1385,501,1181,511,1029,515,1029,510,1181,506,1384,496,1586,478,1668,466,1730,451,1769,438,1799,427,1823,409,1846,378,1850,358,1845,336,1838,288,1830,269,1815,247,1786,217,1758,198,1726,186,1685,173,1619,159,1532,147,1320,129,1105,119,943,114"/>
<area shape="rect" title=" " alt="" coords="1109,427,1163,453"/>
<area shape="poly" title=" " alt="" coords="943,112,1002,119,1066,131,1121,147,1142,157,1157,169,1174,198,1183,231,1186,265,1184,299,1169,364,1151,414,1146,412,1164,362,1179,299,1181,265,1178,232,1169,200,1153,172,1139,161,1119,152,1064,136,1001,125,942,118"/>
<area shape="rect" title=" " alt="" coords="252,171,335,197"/>
<area shape="poly" title=" " alt="" coords="836,117,627,137,491,152,351,173,350,168,491,147,626,131,835,112"/>
<area shape="rect" title=" " alt="" coords="359,171,441,197"/>
<area shape="poly" title=" " alt="" coords="836,119,671,140,457,173,456,168,670,135,835,114"/>
<area shape="rect" href="src_2core_2log_2driver__log_8h.html" title=" " alt="" coords="439,344,577,371"/>
<area shape="poly" title=" " alt="" coords="836,113,660,116,433,124,322,131,227,142,156,156,133,164,119,173,106,191,100,208,100,223,107,238,119,252,136,265,182,288,239,307,303,323,424,344,423,349,301,328,238,312,180,293,133,269,115,256,102,241,95,225,94,207,101,188,116,169,130,159,155,150,226,137,322,126,432,119,660,110,836,108"/>
<area shape="rect" href="error__handling_8h.html" title="驱动程序错误处理和断言宏定义" alt="" coords="652,344,825,371"/>
<area shape="poly" title=" " alt="" coords="836,113,663,116,440,124,332,132,239,142,170,156,147,164,134,172,126,186,128,199,137,211,155,225,211,250,286,273,375,294,468,313,638,340,637,345,467,318,374,299,285,278,209,255,152,229,133,215,123,200,121,185,130,169,145,159,168,151,238,137,332,127,440,119,663,111,836,108"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="1649,245,1767,288"/>
<area shape="poly" title=" " alt="" coords="943,109,1115,115,1338,126,1542,143,1614,155,1656,168,1671,181,1683,196,1700,229,1695,232,1679,199,1667,185,1653,173,1613,160,1541,149,1338,131,1115,120,943,115"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="794,245,924,288"/>
<area shape="poly" title=" " alt="" coords="900,122,916,156,920,177,917,198,907,218,894,236,890,232,903,215,912,197,915,177,911,158,896,124"/>
<area shape="rect" title=" " alt="" coords="465,171,599,197"/>
<area shape="poly" title=" " alt="" coords="836,124,610,170,609,164,835,119"/>
<area shape="rect" title=" " alt="" coords="623,171,750,197"/>
<area shape="poly" title=" " alt="" coords="855,126,737,167,736,162,853,121"/>
<area shape="rect" title=" " alt="" coords="775,171,902,197"/>
<area shape="poly" title=" " alt="" coords="883,125,858,160,854,157,878,122"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="1472,171,1643,197"/>
<area shape="poly" title=" " alt="" coords="943,114,1448,168,1457,169,1456,174,1448,173,943,119"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="978,171,1142,197"/>
<area shape="poly" title=" " alt="" coords="920,121,1017,162,1015,167,918,126"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1271,171,1436,197"/>
<area shape="poly" title=" " alt="" coords="943,116,1257,165,1256,170,943,122"/>
<area shape="poly" title=" " alt="" coords="518,370,549,411,571,433,597,451,624,462,661,471,753,488,934,507,934,512,753,493,660,477,623,467,595,456,568,437,545,415,514,373"/>
<area shape="poly" title=" " alt="" coords="578,367,640,376,857,399,952,408,1074,424,1095,428,1094,433,1073,429,952,413,857,404,640,381,577,372"/>
<area shape="poly" title=" " alt="" coords="734,372,721,411,721,433,729,452,742,462,761,472,815,488,877,499,934,506,933,512,876,504,814,493,759,477,739,467,725,455,716,434,716,410,729,370"/>
<area shape="poly" title=" " alt="" coords="802,369,1094,428,1093,433,801,374"/>
<area shape="poly" title=" " alt="" coords="777,369,930,419,928,424,776,374"/>
<area shape="rect" title=" " alt="" coords="739,427,877,453"/>
<area shape="poly" title=" " alt="" coords="752,369,790,413,785,417,747,373"/>
<area shape="poly" title=" " alt="" coords="984,454,984,486,979,486,979,454"/>
<area shape="poly" title=" " alt="" coords="1672,291,1592,338,1565,361,1552,372,1534,381,1487,398,1438,412,1338,430,1248,439,1178,442,1178,436,1248,434,1338,425,1436,407,1485,393,1532,376,1549,367,1561,357,1589,334,1669,286"/>
<area shape="poly" title=" " alt="" coords="1649,285,1050,425,1049,420,1648,279"/>
<area shape="rect" title=" " alt="" coords="1603,344,1664,371"/>
<area shape="poly" title=" " alt="" coords="1693,290,1656,334,1652,330,1689,287"/>
<area shape="poly" title=" " alt="" coords="1720,287,1738,329,1734,331,1715,290"/>
<area shape="poly" title=" " alt="" coords="1714,374,1590,414,1433,456,1320,477,1208,493,1029,512,1029,507,1207,488,1319,472,1431,451,1589,409,1712,369"/>
<area shape="poly" title=" " alt="" coords="1708,374,1677,381,1534,404,1393,421,1178,439,1178,434,1392,416,1533,399,1675,376,1707,369"/>
<area shape="poly" title=" " alt="" coords="793,271,753,277,711,289,673,308,642,338,637,347,636,356,642,378,664,415,695,445,732,467,774,485,817,497,860,504,934,511,933,516,859,510,816,502,772,490,730,472,692,449,660,418,638,380,630,357,632,346,638,334,670,304,709,284,752,272,792,266"/>
<area shape="poly" title=" " alt="" coords="872,287,902,332,923,356,947,376,973,389,1012,403,1095,426,1093,431,1010,408,971,394,944,381,919,360,898,336,867,290"/>
<area shape="poly" title=" " alt="" coords="864,288,874,331,884,355,897,377,918,399,943,416,940,421,915,403,893,380,879,358,869,333,859,289"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="1001,336,1122,379"/>
<area shape="poly" title=" " alt="" coords="907,286,1001,327,999,332,905,291"/>
<area shape="poly" title=" " alt="" coords="1083,377,1116,413,1112,417,1079,381"/>
<area shape="poly" title=" " alt="" coords="1043,381,1006,417,1003,414,1039,377"/>
<area shape="poly" title=" " alt="" coords="1644,189,1715,207,1749,222,1780,243,1813,274,1835,308,1840,325,1840,343,1835,362,1823,380,1800,403,1767,423,1726,440,1679,455,1571,479,1449,496,1325,507,1208,513,1029,516,1029,511,1208,507,1325,501,1449,490,1570,474,1678,450,1724,435,1764,418,1796,399,1819,377,1830,359,1835,343,1835,326,1830,310,1809,278,1777,247,1747,227,1713,212,1643,194"/>
<area shape="poly" title=" " alt="" coords="1561,198,1559,241,1551,267,1538,290,1499,328,1453,359,1404,383,1353,403,1256,427,1178,438,1178,433,1255,422,1352,397,1402,378,1451,354,1495,323,1534,286,1547,265,1553,241,1556,198"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="1394,253,1523,280"/>
<area shape="poly" title=" " alt="" coords="1544,200,1487,246,1484,242,1540,196"/>
<area shape="poly" title=" " alt="" coords="1402,283,1138,342,1136,337,1401,278"/>
<area shape="poly" title=" " alt="" coords="977,195,835,212,669,240,590,259,520,282,465,308,444,322,429,338,411,367,406,393,413,415,429,435,455,452,488,466,572,488,669,501,770,509,934,512,934,517,769,514,669,507,571,493,486,471,452,456,426,439,408,418,401,393,406,365,425,334,440,318,462,303,518,277,588,254,668,235,834,207,977,190"/>
<area shape="poly" title=" " alt="" coords="1037,200,1010,219,989,246,983,272,981,311,983,350,992,377,1007,390,1033,403,1096,425,1094,430,1031,408,1004,394,987,380,978,351,976,311,978,271,984,244,1006,216,1034,196"/>
<area shape="poly" title=" " alt="" coords="1063,198,1063,238,1057,238,1057,198"/>
<area shape="poly" title=" " alt="" coords="1063,281,1063,320,1058,321,1058,281"/>
<area shape="poly" title=" " alt="" coords="1417,195,1489,216,1519,229,1538,244,1547,266,1538,289,1515,321,1488,350,1426,399,1356,437,1283,465,1209,486,1140,500,1029,514,1029,508,1139,495,1208,481,1281,460,1354,432,1423,394,1484,346,1511,318,1534,287,1541,266,1534,247,1517,233,1488,221,1416,200"/>
<area shape="poly" title=" " alt="" coords="1356,198,1351,240,1344,266,1333,289,1299,332,1257,369,1215,399,1177,421,1174,417,1212,394,1254,365,1295,329,1328,287,1339,264,1346,239,1351,197"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="1196,253,1318,280"/>
<area shape="poly" title=" " alt="" coords="1340,200,1285,245,1282,241,1337,196"/>
<area shape="poly" title=" " alt="" coords="1230,283,1122,332,1120,327,1228,278"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FI2C_5FDEVICE_5FCONTEXT_5Fstruct_5F_5FI2C_5F_5FDEVICE_5F_5FCONTEXT" id="r__5FI2C_5FDEVICE_5FCONTEXT_5Fstruct_5F_5FI2C_5F_5FDEVICE_5F_5FCONTEXT"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__I2C__DEVICE__CONTEXT">_I2C_DEVICE_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:abad9f6d84bae65aada7d4a1cfcc2ba12" id="r_abad9f6d84bae65aada7d4a1cfcc2ba12"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__DEVICE__CONTEXT">_I2C_DEVICE_CONTEXT</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abad9f6d84bae65aada7d4a1cfcc2ba12">I2C_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:a9f0733f4be9833c3a734164c7711fbe5" id="r_a9f0733f4be9833c3a734164c7711fbe5"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__DEVICE__CONTEXT">_I2C_DEVICE_CONTEXT</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f0733f4be9833c3a734164c7711fbe5">PI2C_DEVICE_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3730a6f611cf9feba7ba954330f41a6c" id="r_a3730a6f611cf9feba7ba954330f41a6c"><td class="memItemLeft" align="right" valign="top">绗竴姝ワ細I2CInitialize 鍒濆鍖朓2C鎬荤嚎鎺ュ彛 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3730a6f611cf9feba7ba954330f41a6c">I2CInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="el" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>)</td></tr>
<tr class="memitem:a0dc1e54406b75f4efa145bbb512f87fe" id="r_a0dc1e54406b75f4efa145bbb512f87fe"><td class="memItemLeft" align="right" valign="top">绗叚姝ワ細I2CReadRegister 璇诲彇I2C璁惧瀵勫瓨鍣 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0dc1e54406b75f4efa145bbb512f87fe">I2CReadRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:a4440e6d849d5de8720702c225f6bd83b" id="r_a4440e6d849d5de8720702c225f6bd83b"><td class="memItemLeft" align="right" valign="top">绗竷姝ワ細I2CScanBus 鎵弿I2C鎬荤嚎鏌ユ壘璁惧 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4440e6d849d5de8720702c225f6bd83b">I2CScanBus</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <a class="el" href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a> DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount)</td></tr>
<tr class="memitem:ac03ee248114c6e0f051a792462609cb4" id="r_ac03ee248114c6e0f051a792462609cb4"><td class="memItemLeft" align="right" valign="top">绗洓姝ワ細I2CTransferAsynchronous 鎵ц寮傛I2C浼犺緭 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac03ee248114c6e0f051a792462609cb4">I2CTransferAsynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket, _In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a83e1937f01cd4ec9a8e227bd544a0f06" id="r_a83e1937f01cd4ec9a8e227bd544a0f06"><td class="memItemLeft" align="right" valign="top">绗笁姝ワ細I2CTransferSynchronous 鎵ц鍚屾I2C浼犺緭 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a83e1937f01cd4ec9a8e227bd544a0f06">I2CTransferSynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:aefdc06b9d942e6b102424a8a81c0be8a" id="r_aefdc06b9d942e6b102424a8a81c0be8a"><td class="memItemLeft" align="right" valign="top">I2C浼犺緭瓒呮椂澶勭悊鍥炶皟 *VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aefdc06b9d942e6b102424a8a81c0be8a">I2CTransferTimerExpired</a> (_In_ WDFTIMER Timer)</td></tr>
<tr class="memitem:ae1622080dc9f8424bde67b829ee735c7" id="r_ae1622080dc9f8424bde67b829ee735c7"><td class="memItemLeft" align="right" valign="top">绗簩姝ワ細I2CUninitialize 娓呯悊I2C鎬荤嚎鎺ュ彛 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae1622080dc9f8424bde67b829ee735c7">I2CUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a7e9d20258e5842242cf0a532b4d60deb" id="r_a7e9d20258e5842242cf0a532b4d60deb"><td class="memItemLeft" align="right" valign="top">绗簲姝ワ細I2CWriteRegister 鍐欏叆I2C璁惧瀵勫瓨鍣 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7e9d20258e5842242cf0a532b4d60deb">I2CWriteRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aee3e2abd9dd27ddfc3e158a9ffce1746" id="r_aee3e2abd9dd27ddfc3e158a9ffce1746"><td class="memItemLeft" align="right" valign="top">Exit&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aee3e2abd9dd27ddfc3e158a9ffce1746">__pad0__</a></td></tr>
<tr class="memitem:a465308d666d8357287980a516e216910" id="r_a465308d666d8357287980a516e216910"><td class="memItemLeft" align="right" valign="top">EVT_WDF_TIMER&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a465308d666d8357287980a516e216910">I2CTransferTimerExpired</a></td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__I2C__DEVICE__CONTEXT" id="struct__I2C__DEVICE__CONTEXT"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__I2C__DEVICE__CONTEXT">&#9670;&#160;</a></span>_I2C_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _I2C_DEVICE_CONTEXT</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><div class="dynheader">
Collaboration diagram for _I2C_DEVICE_CONTEXT:</div>
<div class="dyncontent">
<div class="center"><img src="struct__I2C__DEVICE__CONTEXT__coll__graph.png" border="0" usemap="#a__I2C__DEVICE__CONTEXT_coll__map" loading="lazy" alt="Collaboration graph"/></div>
<map name="a__I2C__DEVICE__CONTEXT_coll__map" id="a__I2C__DEVICE__CONTEXT_coll__map">
<area shape="rect" title=" " alt="" coords="5,104,184,131"/>
<area shape="rect" href="kmdf__i2c_8h.html#struct__I2C__CONFIG" title=" " alt="" coords="40,5,149,32"/>
<area shape="poly" title=" " alt="" coords="97,48,97,104,92,104,92,48"/>
</map>
<center><span class="legend">[<a target="top" href="graph_legend.html">legend</a>]</span></center></div>
<table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a4096eb1e4fb3e430840ed07109bec38b" name="a4096eb1e4fb3e430840ed07109bec38b"></a><a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a></td>
<td class="fieldname">
Config</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a3897e76d5d5a45297f03bdaa74c2280c" name="a3897e76d5d5a45297f03bdaa74c2280c"></a><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a></td>
<td class="fieldname">
TransferLock</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="acf0fff6904f00f7611172d1e8b9c2bcd" name="acf0fff6904f00f7611172d1e8b9c2bcd"></a><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>
<td class="fieldname">
WdfDevice</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="abad9f6d84bae65aada7d4a1cfcc2ba12" name="abad9f6d84bae65aada7d4a1cfcc2ba12"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abad9f6d84bae65aada7d4a1cfcc2ba12">&#9670;&#160;</a></span>I2C_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__DEVICE__CONTEXT">_I2C_DEVICE_CONTEXT</a> <a class="el" href="#abad9f6d84bae65aada7d4a1cfcc2ba12">I2C_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9f0733f4be9833c3a734164c7711fbe5" name="a9f0733f4be9833c3a734164c7711fbe5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f0733f4be9833c3a734164c7711fbe5">&#9670;&#160;</a></span>PI2C_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__DEVICE__CONTEXT">_I2C_DEVICE_CONTEXT</a> * <a class="el" href="#a9f0733f4be9833c3a734164c7711fbe5">PI2C_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a3730a6f611cf9feba7ba954330f41a6c" name="a3730a6f611cf9feba7ba954330f41a6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3730a6f611cf9feba7ba954330f41a6c">&#9670;&#160;</a></span>I2CInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗竴姝ワ細I2CInitialize 鍒濆鍖朓2C鎬荤嚎鎺ュ彛 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>I2cConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_cgraph.png" border="0" usemap="#ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_cgraph" id="ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,96,56"/>
<area shape="poly" title=" " alt="" coords="24,30,20,21,23,11,33,5,51,3,69,5,79,12,76,16,67,10,50,8,35,10,27,14,25,20,29,28"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="308,29,413,56"/>
<area shape="poly" title=" " alt="" coords="96,40,292,40,292,45,96,45"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="144,55,260,81"/>
<area shape="poly" title=" " alt="" coords="96,48,129,53,128,58,96,53"/>
<area shape="poly" title=" " alt="" coords="333,30,328,21,332,11,343,5,361,3,380,5,390,12,387,16,378,10,361,8,344,10,336,15,334,20,338,28"/>
<area shape="poly" title=" " alt="" coords="260,56,292,51,293,56,261,61"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_icgraph.png" border="0" usemap="#ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_icgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_icgraph" id="ai2c__core_8c_a3730a6f611cf9feba7ba954330f41a6c_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,96,56"/>
<area shape="poly" title=" " alt="" coords="64,15,59,10,50,8,42,10,37,14,36,20,39,28,34,30,31,20,33,12,40,5,51,3,62,5,68,12"/>
</map>
</div>

</div>
</div>
<a id="a0dc1e54406b75f4efa145bbb512f87fe" name="a0dc1e54406b75f4efa145bbb512f87fe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0dc1e54406b75f4efa145bbb512f87fe">&#9670;&#160;</a></span>I2CReadRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗叚姝ワ細I2CReadRegister 璇诲彇I2C璁惧瀵勫瓨鍣 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CReadRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>SlaveAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PUCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a0dc1e54406b75f4efa145bbb512f87fe_cgraph.png" border="0" usemap="#ai2c__core_8c_a0dc1e54406b75f4efa145bbb512f87fe_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a0dc1e54406b75f4efa145bbb512f87fe_cgraph" id="ai2c__core_8c_a0dc1e54406b75f4efa145bbb512f87fe_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,128,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="176,55,342,81"/>
<area shape="poly" title=" " alt="" coords="128,65,160,65,160,71,128,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="411,29,516,56"/>
<area shape="poly" title=" " alt="" coords="342,55,395,48,396,54,343,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="390,80,537,107"/>
<area shape="poly" title=" " alt="" coords="343,76,375,80,375,85,342,81"/>
<area shape="poly" title=" " alt="" coords="435,30,430,21,434,11,445,5,464,3,483,5,494,12,491,16,482,10,464,8,447,10,438,15,436,20,440,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="585,80,670,107"/>
<area shape="poly" title=" " alt="" coords="538,91,570,91,570,96,538,96"/>
<area shape="poly" title=" " alt="" coords="599,81,594,71,598,62,609,56,628,53,647,56,658,62,655,67,646,61,627,59,611,61,602,65,599,71,604,78"/>
</map>
</div>

</div>
</div>
<a id="a4440e6d849d5de8720702c225f6bd83b" name="a4440e6d849d5de8720702c225f6bd83b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4440e6d849d5de8720702c225f6bd83b">&#9670;&#160;</a></span>I2CScanBus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗竷姝ワ細I2CScanBus 鎵弿I2C鎬荤嚎鏌ユ壘璁惧 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CScanBus </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <a class="el" href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>DeviceAddresses</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>MaxDeviceAddresses</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PULONG</td>          <td class="paramname"><span class="paramname"><em>DeviceCount</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a4440e6d849d5de8720702c225f6bd83b_cgraph.png" border="0" usemap="#ai2c__core_8c_a4440e6d849d5de8720702c225f6bd83b_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a4440e6d849d5de8720702c225f6bd83b_cgraph" id="ai2c__core_8c_a4440e6d849d5de8720702c225f6bd83b_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,101,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="384,29,489,56"/>
<area shape="poly" title=" " alt="" coords="101,62,368,44,368,50,101,68"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="149,80,315,107"/>
<area shape="poly" title=" " alt="" coords="101,72,133,77,133,82,101,77"/>
<area shape="poly" title=" " alt="" coords="408,30,403,21,407,11,418,5,437,3,456,5,467,12,464,16,455,10,437,8,420,10,411,15,409,20,413,28"/>
<area shape="poly" title=" " alt="" coords="288,77,368,57,369,62,290,82"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="363,80,510,107"/>
<area shape="poly" title=" " alt="" coords="315,91,348,91,348,96,315,96"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="558,80,643,107"/>
<area shape="poly" title=" " alt="" coords="511,91,543,91,543,96,511,96"/>
<area shape="poly" title=" " alt="" coords="572,81,567,71,571,62,582,56,601,53,620,56,631,62,628,67,619,61,600,59,584,61,575,65,572,71,577,78"/>
</map>
</div>

</div>
</div>
<a id="ac03ee248114c6e0f051a792462609cb4" name="ac03ee248114c6e0f051a792462609cb4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac03ee248114c6e0f051a792462609cb4">&#9670;&#160;</a></span>I2CTransferAsynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗洓姝ワ細I2CTransferAsynchronous 鎵ц寮傛I2C浼犺緭 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CTransferAsynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>CompletionCallback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_ac03ee248114c6e0f051a792462609cb4_cgraph.png" border="0" usemap="#ai2c__core_8c_ac03ee248114c6e0f051a792462609cb4_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_ac03ee248114c6e0f051a792462609cb4_cgraph" id="ai2c__core_8c_ac03ee248114c6e0f051a792462609cb4_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,179,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="227,55,393,81"/>
<area shape="poly" title=" " alt="" coords="179,65,211,65,211,71,179,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="462,29,567,56"/>
<area shape="poly" title=" " alt="" coords="393,55,446,48,447,54,394,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="441,80,588,107"/>
<area shape="poly" title=" " alt="" coords="394,76,426,80,426,85,393,81"/>
<area shape="poly" title=" " alt="" coords="486,30,481,21,485,11,496,5,515,3,534,5,545,12,542,16,533,10,515,8,498,10,489,15,487,20,491,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="636,80,721,107"/>
<area shape="poly" title=" " alt="" coords="589,91,621,91,621,96,589,96"/>
<area shape="poly" title=" " alt="" coords="650,81,645,71,649,62,660,56,679,53,698,56,709,62,706,67,697,61,678,59,662,61,653,65,650,71,655,78"/>
</map>
</div>

</div>
</div>
<a id="a83e1937f01cd4ec9a8e227bd544a0f06" name="a83e1937f01cd4ec9a8e227bd544a0f06"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a83e1937f01cd4ec9a8e227bd544a0f06">&#9670;&#160;</a></span>I2CTransferSynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗笁姝ワ細I2CTransferSynchronous 鎵ц鍚屾I2C浼犺緭 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CTransferSynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_cgraph.png" border="0" usemap="#ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_cgraph" id="ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,172,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="241,29,346,56"/>
<area shape="poly" title=" " alt="" coords="172,55,225,48,225,54,172,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="220,80,367,107"/>
<area shape="poly" title=" " alt="" coords="172,76,205,80,204,85,172,81"/>
<area shape="poly" title=" " alt="" coords="265,30,260,21,263,11,275,5,293,3,313,5,324,12,321,16,311,10,293,8,276,10,267,15,265,20,270,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="415,80,499,107"/>
<area shape="poly" title=" " alt="" coords="367,91,399,91,399,96,367,96"/>
<area shape="poly" title=" " alt="" coords="429,81,424,71,427,62,439,56,457,53,477,56,488,62,485,67,475,61,457,59,440,61,431,65,429,71,433,78"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_icgraph.png" border="0" usemap="#ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_icgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_icgraph" id="ai2c__core_8c_a83e1937f01cd4ec9a8e227bd544a0f06_icgraph">
<area shape="rect" title=" " alt="" coords="227,81,393,108"/>
<area shape="rect" href="i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe" title=" " alt="" coords="31,5,153,32"/>
<area shape="poly" title=" " alt="" coords="260,78,178,47,142,35,144,30,180,41,262,73"/>
<area shape="rect" href="i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b" title=" " alt="" coords="44,56,140,83"/>
<area shape="poly" title=" " alt="" coords="211,86,140,77,140,72,212,81"/>
<area shape="rect" href="i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4" title=" " alt="" coords="5,107,179,133"/>
<area shape="poly" title=" " alt="" coords="212,109,179,113,179,107,211,103"/>
<area shape="rect" href="i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb" title=" " alt="" coords="31,157,153,184"/>
<area shape="poly" title=" " alt="" coords="262,117,180,148,144,159,142,154,178,143,260,112"/>
</map>
</div>

</div>
</div>
<a id="aefdc06b9d942e6b102424a8a81c0be8a" name="aefdc06b9d942e6b102424a8a81c0be8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefdc06b9d942e6b102424a8a81c0be8a">&#9670;&#160;</a></span>I2CTransferTimerExpired()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">I2C浼犺緭瓒呮椂澶勭悊鍥炶皟 *VOID I2CTransferTimerExpired </td>
          <td>(</td>
          <td class="paramtype">_In_ WDFTIMER</td>          <td class="paramname"><span class="paramname"><em>Timer</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_aefdc06b9d942e6b102424a8a81c0be8a_cgraph.png" border="0" usemap="#ai2c__core_8c_aefdc06b9d942e6b102424a8a81c0be8a_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_aefdc06b9d942e6b102424a8a81c0be8a_cgraph" id="ai2c__core_8c_aefdc06b9d942e6b102424a8a81c0be8a_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,173,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="221,29,326,56"/>
<area shape="poly" title=" " alt="" coords="173,40,205,40,205,45,173,45"/>
<area shape="poly" title=" " alt="" coords="242,30,236,21,240,11,253,5,273,3,295,5,307,12,305,16,294,10,273,8,254,10,244,15,241,20,246,27"/>
</map>
</div>

</div>
</div>
<a id="ae1622080dc9f8424bde67b829ee735c7" name="ae1622080dc9f8424bde67b829ee735c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1622080dc9f8424bde67b829ee735c7">&#9670;&#160;</a></span>I2CUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗簩姝ワ細I2CUninitialize 娓呯悊I2C鎬荤嚎鎺ュ彛 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID I2CUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_ae1622080dc9f8424bde67b829ee735c7_cgraph.png" border="0" usemap="#ai2c__core_8c_ae1622080dc9f8424bde67b829ee735c7_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_ae1622080dc9f8424bde67b829ee735c7_cgraph" id="ai2c__core_8c_ae1622080dc9f8424bde67b829ee735c7_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,112,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="160,29,265,56"/>
<area shape="poly" title=" " alt="" coords="112,40,144,40,144,45,112,45"/>
<area shape="poly" title=" " alt="" coords="186,30,181,21,184,11,195,5,212,3,231,5,241,12,238,16,229,10,212,8,197,10,188,14,186,20,190,28"/>
</map>
</div>

</div>
</div>
<a id="a7e9d20258e5842242cf0a532b4d60deb" name="a7e9d20258e5842242cf0a532b4d60deb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e9d20258e5842242cf0a532b4d60deb">&#9670;&#160;</a></span>I2CWriteRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">绗簲姝ワ細I2CWriteRegister 鍐欏叆I2C璁惧瀵勫瓨鍣 *<a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CWriteRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>SlaveAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__core_8c_a7e9d20258e5842242cf0a532b4d60deb_cgraph.png" border="0" usemap="#ai2c__core_8c_a7e9d20258e5842242cf0a532b4d60deb_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__core_8c_a7e9d20258e5842242cf0a532b4d60deb_cgraph" id="ai2c__core_8c_a7e9d20258e5842242cf0a532b4d60deb_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,127,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="175,55,341,81"/>
<area shape="poly" title=" " alt="" coords="127,65,159,65,159,71,127,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="410,29,515,56"/>
<area shape="poly" title=" " alt="" coords="341,55,394,48,395,54,342,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="389,80,536,107"/>
<area shape="poly" title=" " alt="" coords="342,76,374,80,374,85,341,81"/>
<area shape="poly" title=" " alt="" coords="434,30,429,21,433,11,444,5,463,3,482,5,493,12,490,16,481,10,463,8,446,10,437,15,435,20,439,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="584,80,669,107"/>
<area shape="poly" title=" " alt="" coords="537,91,569,91,569,96,537,96"/>
<area shape="poly" title=" " alt="" coords="598,81,593,71,597,62,608,56,627,53,646,56,657,62,654,67,645,61,626,59,610,61,601,65,598,71,603,78"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="aee3e2abd9dd27ddfc3e158a9ffce1746" name="aee3e2abd9dd27ddfc3e158a9ffce1746"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee3e2abd9dd27ddfc3e158a9ffce1746">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Exit __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a465308d666d8357287980a516e216910" name="a465308d666d8357287980a516e216910"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a465308d666d8357287980a516e216910">&#9670;&#160;</a></span>I2CTransferTimerExpired</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_TIMER I2CTransferTimerExpired</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_43b43b2f79854d1934869d5a4aaeb79e.html">bus</a></li><li class="navelem"><a href="i2c__core_8c.html">i2c_core.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
