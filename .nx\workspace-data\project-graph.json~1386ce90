{"nodes": {}, "externalNodes": {"npm:@azure/abort-controller": {"type": "npm", "name": "npm:@azure/abort-controller", "data": {"version": "2.1.2", "packageName": "@azure/abort-controller", "hash": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA=="}}, "npm:@azure/core-auth": {"type": "npm", "name": "npm:@azure/core-auth", "data": {"version": "1.9.0", "packageName": "@azure/core-auth", "hash": "sha512-****************************************************+pBsYcBa88B2NGO/SEnYPGhyBqNlE8ilSw=="}}, "npm:@azure/core-client": {"type": "npm", "name": "npm:@azure/core-client", "data": {"version": "1.9.4", "packageName": "@azure/core-client", "hash": "sha512-f7IxTD15Qdux30s2qFARH+JxgwxWLG2Rlr4oSkPGuLWm+1p5y1+C04XGLA0vmX6EtqfutmjvpNmAfgwVIS5hpw=="}}, "npm:@azure/core-rest-pipeline": {"type": "npm", "name": "npm:@azure/core-rest-pipeline", "data": {"version": "1.20.0", "packageName": "@azure/core-rest-pipeline", "hash": "sha512-ASoP8uqZBS3H/8N8at/XwFr6vYrRP3syTK0EUjDXQy0Y1/AUS+QeIRThKmTNJO2RggvBBxaXDPM7YoIwDGeA0g=="}}, "npm:@azure/core-tracing": {"type": "npm", "name": "npm:@azure/core-tracing", "data": {"version": "1.2.0", "packageName": "@azure/core-tracing", "hash": "sha512-UKTiEJPkWcESPYJz3X5uKRYyOcJD+4nYph+KpfdPRnQJVrZfk0KJgdnaAWKfhsBBtAf/D58Az4AvCJEmWgIBAg=="}}, "npm:@azure/core-util": {"type": "npm", "name": "npm:@azure/core-util", "data": {"version": "1.12.0", "packageName": "@azure/core-util", "hash": "sha512-13IyjTQgABPARvG90+N2dXpC+hwp466XCdQXPCRlbWHgd3SJd5Q1VvaBGv6k1BIa4MQm6hAF1UBU1m8QUxV8sQ=="}}, "npm:@azure/identity": {"type": "npm", "name": "npm:@azure/identity", "data": {"version": "4.10.0", "packageName": "@azure/identity", "hash": "sha512-iT53Sre2NJK6wzMWnvpjNiR3md597LZ3uK/5kQD2TkrY9vqhrY5bt2KwELNjkOWQ9n8S/92knj/QEykTtjMNqQ=="}}, "npm:@azure/logger": {"type": "npm", "name": "npm:@azure/logger", "data": {"version": "1.2.0", "packageName": "@azure/logger", "hash": "sha512-0hKEzLhpw+ZTAfNJyRrn6s+V0nDWzXk9OjBr2TiGIu0OfMr5s2V4FpKLTAK3Ca5r5OKLbf4hkOGDPyiRjie/jA=="}}, "npm:@azure/msal-browser": {"type": "npm", "name": "npm:@azure/msal-browser", "data": {"version": "4.12.0", "packageName": "@azure/msal-browser", "hash": "sha512-WD1lmVWchg7wn1mI7Tr4v7QPyTwK+8Nuyje3jRpOFENLRLEBsdK8VVdTw3C+TypZmYn4cOAdj3zREnuFXgvfIA=="}}, "npm:@azure/msal-common": {"type": "npm", "name": "npm:@azure/msal-common", "data": {"version": "15.6.0", "packageName": "@azure/msal-common", "hash": "sha512-EotmBz42apYGjqiIV9rDUdptaMptpTn4TdGf3JfjLvFvinSe9BJ6ywU92K9ky+t/b0ghbeTSe9RfqlgLh8f2jA=="}}, "npm:@azure/msal-node": {"type": "npm", "name": "npm:@azure/msal-node", "data": {"version": "3.5.3", "packageName": "@azure/msal-node", "hash": "sha512-c5mifzHX5mwm5JqMIlURUyp6LEEdKF1a8lmcNRLBo0lD7zpSYPHupa4jHyhJyg9ccLwszLguZJdk2h3ngnXwNw=="}}, "npm:@babel/code-frame": {"type": "npm", "name": "npm:@babel/code-frame", "data": {"version": "7.27.1", "packageName": "@babel/code-frame", "hash": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="}}, "npm:@babel/helper-validator-identifier": {"type": "npm", "name": "npm:@babel/helper-validator-identifier", "data": {"version": "7.27.1", "packageName": "@babel/helper-validator-identifier", "hash": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}}, "npm:@emnapi/core": {"type": "npm", "name": "npm:@emnapi/core", "data": {"version": "1.4.3", "packageName": "@emnapi/core", "hash": "sha512-4m62DuCE07lw01soJwPiBGC0nAww0Q+RY70VZ+n49yDIO13yyinhbWCeNnaob0lakDtWQzSdtNWzJeOJt2ma+g=="}}, "npm:@emnapi/runtime": {"type": "npm", "name": "npm:@emnapi/runtime", "data": {"version": "1.4.3", "packageName": "@emnapi/runtime", "hash": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ=="}}, "npm:@emnapi/wasi-threads": {"type": "npm", "name": "npm:@emnapi/wasi-threads", "data": {"version": "1.0.2", "packageName": "@emnapi/wasi-threads", "hash": "sha512-5n3nTJblwRi8LlXkJ9eBzu+kZR8Yxcc7ubakyQTFzPMtIhFpUBRbsnc2Dv88IZDIbCDlBiWrknhB4Lsz7mg6BA=="}}, "npm:@isaacs/cliui": {"type": "npm", "name": "npm:@isaacs/cliui", "data": {"version": "8.0.2", "packageName": "@isaacs/cliui", "hash": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA=="}}, "npm:ansi-regex@6.1.0": {"type": "npm", "name": "npm:ansi-regex@6.1.0", "data": {"version": "6.1.0", "packageName": "ansi-regex", "hash": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA=="}}, "npm:ansi-regex": {"type": "npm", "name": "npm:ansi-regex", "data": {"version": "5.0.1", "packageName": "ansi-regex", "hash": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}}, "npm:ansi-styles@6.2.1": {"type": "npm", "name": "npm:ansi-styles@6.2.1", "data": {"version": "6.2.1", "packageName": "ansi-styles", "hash": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug=="}}, "npm:ansi-styles": {"type": "npm", "name": "npm:ansi-styles", "data": {"version": "4.3.0", "packageName": "ansi-styles", "hash": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="}}, "npm:ansi-styles@5.2.0": {"type": "npm", "name": "npm:ansi-styles@5.2.0", "data": {"version": "5.2.0", "packageName": "ansi-styles", "hash": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="}}, "npm:emoji-regex@9.2.2": {"type": "npm", "name": "npm:emoji-regex@9.2.2", "data": {"version": "9.2.2", "packageName": "emoji-regex", "hash": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg=="}}, "npm:emoji-regex": {"type": "npm", "name": "npm:emoji-regex", "data": {"version": "8.0.0", "packageName": "emoji-regex", "hash": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}}, "npm:string-width@5.1.2": {"type": "npm", "name": "npm:string-width@5.1.2", "data": {"version": "5.1.2", "packageName": "string-width", "hash": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA=="}}, "npm:string-width": {"type": "npm", "name": "npm:string-width", "data": {"version": "4.2.3", "packageName": "string-width", "hash": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="}}, "npm:strip-ansi@7.1.0": {"type": "npm", "name": "npm:strip-ansi@7.1.0", "data": {"version": "7.1.0", "packageName": "strip-ansi", "hash": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ=="}}, "npm:strip-ansi": {"type": "npm", "name": "npm:strip-ansi", "data": {"version": "6.0.1", "packageName": "strip-ansi", "hash": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="}}, "npm:wrap-ansi@8.1.0": {"type": "npm", "name": "npm:wrap-ansi@8.1.0", "data": {"version": "8.1.0", "packageName": "wrap-ansi", "hash": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ=="}}, "npm:wrap-ansi": {"type": "npm", "name": "npm:wrap-ansi", "data": {"version": "7.0.0", "packageName": "wrap-ansi", "hash": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="}}, "npm:@jest/schemas": {"type": "npm", "name": "npm:@jest/schemas", "data": {"version": "29.6.3", "packageName": "@jest/schemas", "hash": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA=="}}, "npm:@modelcontextprotocol/sdk": {"type": "npm", "name": "npm:@modelcontextprotocol/sdk", "data": {"version": "1.0.1", "packageName": "@modelcontextprotocol/sdk", "hash": "sha512-slLdFaxQJ9AlRg+hw28iiTtGvShAOgOKXcD0F91nUcRYiOMuS9ZBYjcdNZRXW9G5JQ511GRTdUy1zQVZDpJ+4w=="}}, "npm:@modelcontextprotocol/sdk@0.5.0": {"type": "npm", "name": "npm:@modelcontextprotocol/sdk@0.5.0", "data": {"version": "0.5.0", "packageName": "@modelcontextprotocol/sdk", "hash": "sha512-RXgulUX6ewvxjAG0kOpLMEdXXWkzWgaoCGaA2CwNW7cQCIphjpJhjpHSiaPdVCnisjRF/0Cm9KWHUuIoeiAblQ=="}}, "npm:@modelcontextprotocol/sdk@1.11.4": {"type": "npm", "name": "npm:@modelcontextprotocol/sdk@1.11.4", "data": {"version": "1.11.4", "packageName": "@modelcontextprotocol/sdk", "hash": "sha512-OTbhe5slIjiOtLxXhKalkKGhIQrwvhgCDs/C2r8kcBTy5HR/g43aDQU0l7r8O0VGbJPTNJvDc7ZdQMdQDJXmbw=="}}, "npm:@modelcontextprotocol/sdk@1.11.2": {"type": "npm", "name": "npm:@modelcontextprotocol/sdk@1.11.2", "data": {"version": "1.11.2", "packageName": "@modelcontextprotocol/sdk", "hash": "sha512-H9vwztj5OAqHg9GockCQC06k1natgcxWQSRpQcPJf6i5+MWBzfKkRtxGbjQf0X2ihii0ffLZCRGbYV2f2bjNCQ=="}}, "npm:@modelcontextprotocol/server-filesystem": {"type": "npm", "name": "npm:@modelcontextprotocol/server-filesystem", "data": {"version": "2025.3.28", "packageName": "@modelcontextprotocol/server-filesystem", "hash": "sha512-1AMqM0EZnF7n6L5njMASDR12ppyvtj89HinePbvB8UtT5JKWQ6LJJcbsTYIt/gerFNssf17gH5qXvqSM+eCQSg=="}}, "npm:@modelcontextprotocol/server-github": {"type": "npm", "name": "npm:@modelcontextprotocol/server-github", "data": {"version": "2025.4.8", "packageName": "@modelcontextprotocol/server-github", "hash": "sha512-8N43bQw9MlUB0piTZHK2JMh8kYPKxH57d4Z7Wb8PS4by2MkZ0FzI5xPImg3xumpev82VZw2VWHQJJJYp+WkwEw=="}}, "npm:@modelcontextprotocol/server-memory": {"type": "npm", "name": "npm:@modelcontextprotocol/server-memory", "data": {"version": "2025.4.25", "packageName": "@modelcontextprotocol/server-memory", "hash": "sha512-n6qZVRj3uHpysZVn3Q0IKqIPwQkVZVkcIfE65WRJkiSG4/Xli1xaVh+qOH4duXGC4a9I82391jsl9OdeQFlZuA=="}}, "npm:@modelcontextprotocol/server-puppeteer": {"type": "npm", "name": "npm:@modelcontextprotocol/server-puppeteer", "data": {"version": "2025.5.12", "packageName": "@modelcontextprotocol/server-puppeteer", "hash": "sha512-uG5QIxnnAkL6m7snXcsgC3k8ZnkYlikYZN016re6TEwtXwKUoJjRMzsqbwqw45IqP+7pXvN9vuPJ/OuHu70jow=="}}, "npm:@modelcontextprotocol/server-sequential-thinking": {"type": "npm", "name": "npm:@modelcontextprotocol/server-sequential-thinking", "data": {"version": "0.6.2", "packageName": "@modelcontextprotocol/server-sequential-thinking", "hash": "sha512-CgYRG6PPPldPk60Oi/jmPNKQ8hUg1V2rqlBRWsRvB5/QIgb+kyd6dySh0WfEoWC5kT+7avM2qDD8SKEBBHRkOQ=="}}, "npm:@napi-rs/wasm-runtime": {"type": "npm", "name": "npm:@napi-rs/wasm-runtime", "data": {"version": "0.2.4", "packageName": "@napi-rs/wasm-runtime", "hash": "sha512-9zESzOO5aDByvhIAsOy9TbpZ0Ur2AJbUI7UT73kcUTS2mxAMHOBaa1st/jAymNoCtvrit99kkzT1FZuXVcgfIQ=="}}, "npm:@nx/nx-darwin-arm64": {"type": "npm", "name": "npm:@nx/nx-darwin-arm64", "data": {"version": "21.0.4", "packageName": "@nx/nx-darwin-arm64", "hash": "sha512-d6B0L+P8+cbh7rHXvb+ty/kqlAa70cBgRdsEw4BnKAiptSkeJ9uSCzoDbLzYhMnfzX9nqi2G7COa2+Idw0tjkA=="}}, "npm:@nx/nx-darwin-x64": {"type": "npm", "name": "npm:@nx/nx-darwin-x64", "data": {"version": "21.0.4", "packageName": "@nx/nx-darwin-x64", "hash": "sha512-O7vdmQoGn2btM1TxzFZV4VZ0GyKKA5qQkfsjqWv7iwmsya/eLQjnLO1jSfitM5QHccyRminnMP61hIxSLge8+A=="}}, "npm:@nx/nx-freebsd-x64": {"type": "npm", "name": "npm:@nx/nx-freebsd-x64", "data": {"version": "21.0.4", "packageName": "@nx/nx-freebsd-x64", "hash": "sha512-ttWUfbmBW8owQ19RKmKoQNvSB3s/uR2aALDqBsv1N/HdHkbBvba6DbA2UThAlP13QM6LN5cC50Pu9kfRThPG+w=="}}, "npm:@nx/nx-linux-arm-gnueabihf": {"type": "npm", "name": "npm:@nx/nx-linux-arm-gnueabihf", "data": {"version": "21.0.4", "packageName": "@nx/nx-linux-arm-gnueabihf", "hash": "sha512-OPa9o3K/8z8H4VTfjRxurFe1r0S1YuS2a/CAAzSFMYpw+Myzq83qE5BeC7u0K5LDCidymxxSdVCg5eKTqfif6w=="}}, "npm:@nx/nx-linux-arm64-gnu": {"type": "npm", "name": "npm:@nx/nx-linux-arm64-gnu", "data": {"version": "21.0.4", "packageName": "@nx/nx-linux-arm64-gnu", "hash": "sha512-FFFypXlb3HBvTgeW04Cs3pgYZ7hKQNcS3oXSV02pVIgRx+jx+tVwfKGUKa8wAzp3W1NBne1PR3ro+NbRrB93yw=="}}, "npm:@nx/nx-linux-arm64-musl": {"type": "npm", "name": "npm:@nx/nx-linux-arm64-musl", "data": {"version": "21.0.4", "packageName": "@nx/nx-linux-arm64-musl", "hash": "sha512-+ResLuIoxMBim5LM6tvK/m5yX49i2quHfrcBIWfniF3mbOoeOvILIpBf8PeE/3Kng7fo+/Qt2e6qpRAJD8NR1g=="}}, "npm:@nx/nx-linux-x64-gnu": {"type": "npm", "name": "npm:@nx/nx-linux-x64-gnu", "data": {"version": "21.0.4", "packageName": "@nx/nx-linux-x64-gnu", "hash": "sha512-BtFORmVikj4tLtRZpGoOG/58yaEItDMWJmRRWKAGX1ut8x0OqQhPjRYYnXZJ9/5Vl5r77Ut8wKgVIRu3Hg1iZA=="}}, "npm:@nx/nx-linux-x64-musl": {"type": "npm", "name": "npm:@nx/nx-linux-x64-musl", "data": {"version": "21.0.4", "packageName": "@nx/nx-linux-x64-musl", "hash": "sha512-GD5Wvh2YDsw/JxE6+Mr05107WG78cj7eQFyUNsXeuApHCEOxC+jJZxMa/PiLNDq9Q0+aBeB7AAgQfBsut5rQKQ=="}}, "npm:@nx/nx-win32-arm64-msvc": {"type": "npm", "name": "npm:@nx/nx-win32-arm64-msvc", "data": {"version": "21.0.4", "packageName": "@nx/nx-win32-arm64-msvc", "hash": "sha512-ypCwZPOFA0xS10LYCOjn/1Vv1WwVM4KGfUAsw1EhrBBmTIaBzXI0HdCCSB+kvdp1ikkU6+iSuCqyGm98rmSBwg=="}}, "npm:@nx/nx-win32-x64-msvc": {"type": "npm", "name": "npm:@nx/nx-win32-x64-msvc", "data": {"version": "21.0.4", "packageName": "@nx/nx-win32-x64-msvc", "hash": "sha512-HF9+8TV1rTMTyuYJozxz7SKCslVRWvDioKPoNh4yXYpexZ79ylv4IOQ+21mBzMeFxgEFi73TsW+i7807rhbWoA=="}}, "npm:@pkgjs/parseargs": {"type": "npm", "name": "npm:@pkgjs/parseargs", "data": {"version": "0.11.0", "packageName": "@pkgjs/parseargs", "hash": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg=="}}, "npm:@puppeteer/browsers": {"type": "npm", "name": "npm:@puppeteer/browsers", "data": {"version": "2.6.1", "packageName": "@puppeteer/browsers", "hash": "sha512-aBSREisdsGH890S2rQqK82qmQYU3uFpSH8wcZWHgHzl3LfzsxAKbLNiAG9mO8v1Y0UICBeClICxPJvyr0rcuxg=="}}, "npm:@sinclair/typebox": {"type": "npm", "name": "npm:@sinclair/typebox", "data": {"version": "0.27.8", "packageName": "@sinclair/typebox", "hash": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA=="}}, "npm:@tiberriver256/mcp-server-azure-devops": {"type": "npm", "name": "npm:@tiberriver256/mcp-server-azure-devops", "data": {"version": "0.1.37", "packageName": "@tiberriver256/mcp-server-azure-devops", "hash": "sha512-iaLi/woQsiUioBtZFZtOCWaZHvZ0p4gvTs7iLubb/ZuDXLKda56WBs5oYonW1j5moJkUfZqze20S00U1iNfKTQ=="}}, "npm:@tootallnate/quickjs-emscripten": {"type": "npm", "name": "npm:@tootallnate/quickjs-emscripten", "data": {"version": "0.23.0", "packageName": "@tootallnate/quickjs-emscripten", "hash": "sha512-C5Mc6rdnsaJDjO3UpGW/CQTHtCKaYlScZTly4JIu97Jxo/odCiH0ITnDXSJPTOrEKk/ycSZ0AOgTmkDtkOsvIA=="}}, "npm:@tybys/wasm-util": {"type": "npm", "name": "npm:@tybys/wasm-util", "data": {"version": "0.9.0", "packageName": "@tybys/wasm-util", "hash": "sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw=="}}, "npm:@types/node": {"type": "npm", "name": "npm:@types/node", "data": {"version": "22.15.18", "packageName": "@types/node", "hash": "sha512-v1DKRfUdyW+jJhZNEI1PYy29S2YRxMV5AOO/x/SjKmW0acCIOqmbj6Haf9eHAhsPmrhlHSxEhv/1WszcLWV4cg=="}}, "npm:@types/node-fetch": {"type": "npm", "name": "npm:@types/node-fetch", "data": {"version": "2.6.12", "packageName": "@types/node-fetch", "hash": "sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA=="}}, "npm:@types/yauzl": {"type": "npm", "name": "npm:@types/yauzl", "data": {"version": "2.10.3", "packageName": "@types/yauzl", "hash": "sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q=="}}, "npm:@typespec/ts-http-runtime": {"type": "npm", "name": "npm:@typespec/ts-http-runtime", "data": {"version": "0.2.2", "packageName": "@typespec/ts-http-runtime", "hash": "sha512-Gz/Sm64+Sq/vklJu1tt9t+4R2lvnud8NbTD/ZfpZtMiUX7YeVpCA8j6NSW8ptwcoLL+NmYANwqP8DV0q/bwl2w=="}}, "npm:@upstash/context7-mcp": {"type": "npm", "name": "npm:@upstash/context7-mcp", "data": {"version": "1.0.9", "packageName": "@upstash/context7-mcp", "hash": "sha512-cdy4zs1PRpEyOVoZP/wRkG3rS45OEWQASB/riqGuewQVQpzH4nSHCnvzwHyCxRLNUlEhjBGlmBOWcwQdt1bw2A=="}}, "npm:@yarnpkg/lockfile": {"type": "npm", "name": "npm:@yarnpkg/lockfile", "data": {"version": "1.1.0", "packageName": "@yarnpkg/lockfile", "hash": "sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ=="}}, "npm:@yarnpkg/parsers": {"type": "npm", "name": "npm:@yarnpkg/parsers", "data": {"version": "3.0.2", "packageName": "@yarnpkg/parsers", "hash": "sha512-/HcYgtUSiJiot/XWGLOlGxPYUG65+/31V8oqk17vZLW1xlCoR4PampyePljOxY2n8/3jz9+tIFzICsyGujJZoA=="}}, "npm:argparse@1.0.10": {"type": "npm", "name": "npm:argparse@1.0.10", "data": {"version": "1.0.10", "packageName": "<PERSON><PERSON><PERSON><PERSON>", "hash": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg=="}}, "npm:argparse": {"type": "npm", "name": "npm:a<PERSON><PERSON><PERSON>", "data": {"version": "2.0.1", "packageName": "<PERSON><PERSON><PERSON><PERSON>", "hash": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="}}, "npm:js-yaml@3.14.1": {"type": "npm", "name": "npm:js-yaml@3.14.1", "data": {"version": "3.14.1", "packageName": "js-yaml", "hash": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="}}, "npm:js-yaml": {"type": "npm", "name": "npm:js-yaml", "data": {"version": "4.1.0", "packageName": "js-yaml", "hash": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="}}, "npm:sprintf-js@1.0.3": {"type": "npm", "name": "npm:sprintf-js@1.0.3", "data": {"version": "1.0.3", "packageName": "sprintf-js", "hash": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g=="}}, "npm:sprintf-js": {"type": "npm", "name": "npm:sprintf-js", "data": {"version": "1.1.3", "packageName": "sprintf-js", "hash": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA=="}}, "npm:@zkochan/js-yaml": {"type": "npm", "name": "npm:@zkochan/js-yaml", "data": {"version": "0.0.7", "packageName": "@zkochan/js-yaml", "hash": "sha512-nrUSn7hzt7J6JWgWGz78ZYI8wj+gdIJdk0Ynjpp8l+trkn58Uqsf6RYrYkEK+3X18EX+TNdtJI0WxAtc+L84SQ=="}}, "npm:accepts": {"type": "npm", "name": "npm:accepts", "data": {"version": "2.0.0", "packageName": "accepts", "hash": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng=="}}, "npm:agent-base": {"type": "npm", "name": "npm:agent-base", "data": {"version": "7.1.3", "packageName": "agent-base", "hash": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw=="}}, "npm:ajv": {"type": "npm", "name": "npm:ajv", "data": {"version": "8.17.1", "packageName": "ajv", "hash": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g=="}}, "npm:ansi-colors": {"type": "npm", "name": "npm:ansi-colors", "data": {"version": "4.1.3", "packageName": "ansi-colors", "hash": "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="}}, "npm:ast-types": {"type": "npm", "name": "npm:ast-types", "data": {"version": "0.13.4", "packageName": "ast-types", "hash": "sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w=="}}, "npm:asynckit": {"type": "npm", "name": "npm:asynckit", "data": {"version": "0.4.0", "packageName": "asynckit", "hash": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}}, "npm:axios": {"type": "npm", "name": "npm:axios", "data": {"version": "1.9.0", "packageName": "axios", "hash": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg=="}}, "npm:azure-devops-node-api": {"type": "npm", "name": "npm:azure-devops-node-api", "data": {"version": "13.0.0", "packageName": "azure-devops-node-api", "hash": "sha512-T/i3pt2Dxb2//1+TJT05Ff5heUmQEWKwa8sdguIhdRYT3Zge9FYw98zpfFvCD7CZsz6AN74SKGgqF3ISVN2TGg=="}}, "npm:b4a": {"type": "npm", "name": "npm:b4a", "data": {"version": "1.6.7", "packageName": "b4a", "hash": "sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg=="}}, "npm:balanced-match": {"type": "npm", "name": "npm:balanced-match", "data": {"version": "1.0.2", "packageName": "balanced-match", "hash": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}}, "npm:bare-events": {"type": "npm", "name": "npm:bare-events", "data": {"version": "2.5.4", "packageName": "bare-events", "hash": "sha512-+gFfDkR8pj4/TrWCGUGWmJIkBwuxPS5F+a5yWjOHQt2hHvNZd5YLzadjmDUtFmMM4y429bnKLa8bYBMHcYdnQA=="}}, "npm:bare-fs": {"type": "npm", "name": "npm:bare-fs", "data": {"version": "4.1.5", "packageName": "bare-fs", "hash": "sha512-1zccWBMypln0jEE05LzZt+V/8y8AQsQQqxtklqaIyg5nu6OAYFhZxPXinJTSG+kU5qyNmeLgcn9AW7eHiCHVLA=="}}, "npm:bare-os": {"type": "npm", "name": "npm:bare-os", "data": {"version": "3.6.1", "packageName": "bare-os", "hash": "sha512-uaIjxokhFidJP+bmmvKSgiMzj2sV5GPHaZVAIktcxcpCyBFFWO+YlikVAdhmUo2vYFvFhOXIAlldqV29L8126g=="}}, "npm:bare-path": {"type": "npm", "name": "npm:bare-path", "data": {"version": "3.0.0", "packageName": "bare-path", "hash": "sha512-tyfW2cQcB5NN8Saijrhqn0Zh7AnFNsnczRcuWODH0eYAXBsJ5gVxAUuNr7tsHSC6IZ77cA0SitzT+s47kot8Mw=="}}, "npm:bare-stream": {"type": "npm", "name": "npm:bare-stream", "data": {"version": "2.6.5", "packageName": "bare-stream", "hash": "sha512-jSmxKJNJmHySi6hC42zlZnq00rga4jjxcgNZjY9N5WlOe/iOoGRtdwGsHzQv2RlH2KOYMwGUXhf2zXd32BA9RA=="}}, "npm:base64-js": {"type": "npm", "name": "npm:base64-js", "data": {"version": "1.5.1", "packageName": "base64-js", "hash": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="}}, "npm:basic-ftp": {"type": "npm", "name": "npm:basic-ftp", "data": {"version": "5.0.5", "packageName": "basic-ftp", "hash": "sha512-4Bcg1P8xhUuqcii/S0Z9wiHIrQVPMermM1any+MX5GeGD7faD3/msQUDGLol9wOcz4/jbg/WJnGqoJF6LiBdtg=="}}, "npm:bl": {"type": "npm", "name": "npm:bl", "data": {"version": "4.1.0", "packageName": "bl", "hash": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="}}, "npm:body-parser": {"type": "npm", "name": "npm:body-parser", "data": {"version": "2.2.0", "packageName": "body-parser", "hash": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg=="}}, "npm:brace-expansion": {"type": "npm", "name": "npm:brace-expansion", "data": {"version": "2.0.1", "packageName": "brace-expansion", "hash": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="}}, "npm:buffer": {"type": "npm", "name": "npm:buffer", "data": {"version": "5.7.1", "packageName": "buffer", "hash": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="}}, "npm:buffer-crc32": {"type": "npm", "name": "npm:buffer-crc32", "data": {"version": "0.2.13", "packageName": "buffer-crc32", "hash": "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ=="}}, "npm:buffer-equal-constant-time": {"type": "npm", "name": "npm:buffer-equal-constant-time", "data": {"version": "1.0.1", "packageName": "buffer-equal-constant-time", "hash": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="}}, "npm:bundle-name": {"type": "npm", "name": "npm:bundle-name", "data": {"version": "4.1.0", "packageName": "bundle-name", "hash": "sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q=="}}, "npm:bytes": {"type": "npm", "name": "npm:bytes", "data": {"version": "3.1.2", "packageName": "bytes", "hash": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="}}, "npm:call-bind-apply-helpers": {"type": "npm", "name": "npm:call-bind-apply-helpers", "data": {"version": "1.0.2", "packageName": "call-bind-apply-helpers", "hash": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ=="}}, "npm:call-bound": {"type": "npm", "name": "npm:call-bound", "data": {"version": "1.0.4", "packageName": "call-bound", "hash": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg=="}}, "npm:callsites": {"type": "npm", "name": "npm:callsites", "data": {"version": "3.1.0", "packageName": "callsites", "hash": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="}}, "npm:chalk": {"type": "npm", "name": "npm:chalk", "data": {"version": "5.4.1", "packageName": "chalk", "hash": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w=="}}, "npm:chalk@4.1.2": {"type": "npm", "name": "npm:chalk@4.1.2", "data": {"version": "4.1.2", "packageName": "chalk", "hash": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="}}, "npm:chromium-bidi": {"type": "npm", "name": "npm:chromium-bidi", "data": {"version": "0.11.0", "packageName": "chromium-bidi", "hash": "sha512-6CJWHkNRoyZyjV9Rwv2lYONZf1Xm0IuDyNq97nwSsxxP3wf5Bwy15K5rOvVKMtJ127jJBmxFUanSAOjgFRxgrA=="}}, "npm:zod@3.23.8": {"type": "npm", "name": "npm:zod@3.23.8", "data": {"version": "3.23.8", "packageName": "zod", "hash": "sha512-XBx9AXhXktjUqnepgTiE5flcKIYWi/rme0Eaj+5Y0lftuGBq+jyRu/md4WnuxqgP1ubdpNCsYEYPxrzVHD8d6g=="}}, "npm:zod": {"type": "npm", "name": "npm:zod", "data": {"version": "3.24.4", "packageName": "zod", "hash": "sha512-OdqJE9UDRPwWsrHjLN2F8bPxvwJBK22EHLWtanu0LSYr5YqzsaaW3RMgmjwr8Rypg5k+meEJdSPXJZXE/yqOMg=="}}, "npm:cli-cursor": {"type": "npm", "name": "npm:cli-cursor", "data": {"version": "3.1.0", "packageName": "cli-cursor", "hash": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw=="}}, "npm:cli-spinners": {"type": "npm", "name": "npm:cli-spinners", "data": {"version": "2.6.1", "packageName": "cli-spinners", "hash": "sha512-x/5fWmGMnbKQAaNwN+UZlV79qBLM9JFnJuJ03gIi5whrob0xV0ofNVHy9DhwGdsMJQc2OKv0oGmLzvaqvAVv+g=="}}, "npm:cliui": {"type": "npm", "name": "npm:cliui", "data": {"version": "8.0.1", "packageName": "cliui", "hash": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ=="}}, "npm:clone": {"type": "npm", "name": "npm:clone", "data": {"version": "1.0.4", "packageName": "clone", "hash": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg=="}}, "npm:color-convert": {"type": "npm", "name": "npm:color-convert", "data": {"version": "2.0.1", "packageName": "color-convert", "hash": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="}}, "npm:color-name": {"type": "npm", "name": "npm:color-name", "data": {"version": "1.1.4", "packageName": "color-name", "hash": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}}, "npm:combined-stream": {"type": "npm", "name": "npm:combined-stream", "data": {"version": "1.0.8", "packageName": "combined-stream", "hash": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="}}, "npm:content-disposition": {"type": "npm", "name": "npm:content-disposition", "data": {"version": "1.0.0", "packageName": "content-disposition", "hash": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg=="}}, "npm:content-type": {"type": "npm", "name": "npm:content-type", "data": {"version": "1.0.5", "packageName": "content-type", "hash": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="}}, "npm:cookie": {"type": "npm", "name": "npm:cookie", "data": {"version": "0.7.2", "packageName": "cookie", "hash": "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w=="}}, "npm:cookie-signature": {"type": "npm", "name": "npm:cookie-signature", "data": {"version": "1.2.2", "packageName": "cookie-signature", "hash": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg=="}}, "npm:cors": {"type": "npm", "name": "npm:cors", "data": {"version": "2.8.5", "packageName": "cors", "hash": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="}}, "npm:cosmiconfig": {"type": "npm", "name": "npm:cosmiconfig", "data": {"version": "9.0.0", "packageName": "cosmiconfig", "hash": "sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg=="}}, "npm:cross-spawn": {"type": "npm", "name": "npm:cross-spawn", "data": {"version": "7.0.6", "packageName": "cross-spawn", "hash": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="}}, "npm:data-uri-to-buffer": {"type": "npm", "name": "npm:data-uri-to-buffer", "data": {"version": "6.0.2", "packageName": "data-uri-to-buffer", "hash": "sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw=="}}, "npm:data-uri-to-buffer@4.0.1": {"type": "npm", "name": "npm:data-uri-to-buffer@4.0.1", "data": {"version": "4.0.1", "packageName": "data-uri-to-buffer", "hash": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A=="}}, "npm:debug": {"type": "npm", "name": "npm:debug", "data": {"version": "4.4.1", "packageName": "debug", "hash": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="}}, "npm:default-browser": {"type": "npm", "name": "npm:default-browser", "data": {"version": "5.2.1", "packageName": "default-browser", "hash": "sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg=="}}, "npm:default-browser-id": {"type": "npm", "name": "npm:default-browser-id", "data": {"version": "5.0.0", "packageName": "default-browser-id", "hash": "sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA=="}}, "npm:defaults": {"type": "npm", "name": "npm:defaults", "data": {"version": "1.0.4", "packageName": "defaults", "hash": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A=="}}, "npm:define-lazy-prop": {"type": "npm", "name": "npm:define-lazy-prop", "data": {"version": "3.0.0", "packageName": "define-lazy-prop", "hash": "sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg=="}}, "npm:define-lazy-prop@2.0.0": {"type": "npm", "name": "npm:define-lazy-prop@2.0.0", "data": {"version": "2.0.0", "packageName": "define-lazy-prop", "hash": "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og=="}}, "npm:degenerator": {"type": "npm", "name": "npm:degenerator", "data": {"version": "5.0.1", "packageName": "degenerator", "hash": "sha512-TllpMR/t0M5sqCXfj85i4XaAzxmS5tVA16dqvdkMwGmzI+dXLXnw3J+3Vdv7VKw+ThlTMboK6i9rnZ6Nntj5CQ=="}}, "npm:delayed-stream": {"type": "npm", "name": "npm:delayed-stream", "data": {"version": "1.0.0", "packageName": "delayed-stream", "hash": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}}, "npm:depd": {"type": "npm", "name": "npm:depd", "data": {"version": "2.0.0", "packageName": "depd", "hash": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="}}, "npm:devtools-protocol": {"type": "npm", "name": "npm:devtools-protocol", "data": {"version": "0.0.1367902", "packageName": "devtools-protocol", "hash": "sha512-XxtPuC3PGakY6PD7dG66/o8KwJ/LkH2/EKe19Dcw58w53dv4/vSQEkn/SzuyhHE2q4zPgCkxQBxus3VV4ql+Pg=="}}, "npm:diff": {"type": "npm", "name": "npm:diff", "data": {"version": "5.2.0", "packageName": "diff", "hash": "sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A=="}}, "npm:diff-sequences": {"type": "npm", "name": "npm:diff-sequences", "data": {"version": "29.6.3", "packageName": "diff-sequences", "hash": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q=="}}, "npm:dotenv": {"type": "npm", "name": "npm:dotenv", "data": {"version": "16.5.0", "packageName": "dotenv", "hash": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg=="}}, "npm:dotenv@16.4.7": {"type": "npm", "name": "npm:dotenv@16.4.7", "data": {"version": "16.4.7", "packageName": "dotenv", "hash": "sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ=="}}, "npm:dotenv-expand": {"type": "npm", "name": "npm:dotenv-expand", "data": {"version": "11.0.7", "packageName": "dotenv-expand", "hash": "sha512-zIHwmZPRshsCdpMDyVsqGmgyP0yT8GAgXUnkdAoJisxvf33k7yO6OuoKmcTGuXPWSsm8Oh88nZicRLA9Y0rUeA=="}}, "npm:dunder-proto": {"type": "npm", "name": "npm:dunder-proto", "data": {"version": "1.0.1", "packageName": "dunder-proto", "hash": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A=="}}, "npm:eastasianwidth": {"type": "npm", "name": "npm:eastasianwidth", "data": {"version": "0.2.0", "packageName": "eastasianwidth", "hash": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA=="}}, "npm:ecdsa-sig-formatter": {"type": "npm", "name": "npm:ecdsa-sig-formatter", "data": {"version": "1.0.11", "packageName": "ecdsa-sig-formatter", "hash": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ=="}}, "npm:ee-first": {"type": "npm", "name": "npm:ee-first", "data": {"version": "1.1.1", "packageName": "ee-first", "hash": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="}}, "npm:encodeurl": {"type": "npm", "name": "npm:encodeurl", "data": {"version": "2.0.0", "packageName": "encodeurl", "hash": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg=="}}, "npm:end-of-stream": {"type": "npm", "name": "npm:end-of-stream", "data": {"version": "1.4.4", "packageName": "end-of-stream", "hash": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="}}, "npm:enquirer": {"type": "npm", "name": "npm:enquirer", "data": {"version": "2.3.6", "packageName": "enquirer", "hash": "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg=="}}, "npm:env-paths": {"type": "npm", "name": "npm:env-paths", "data": {"version": "2.2.1", "packageName": "env-paths", "hash": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A=="}}, "npm:error-ex": {"type": "npm", "name": "npm:error-ex", "data": {"version": "1.3.2", "packageName": "error-ex", "hash": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="}}, "npm:es-define-property": {"type": "npm", "name": "npm:es-define-property", "data": {"version": "1.0.1", "packageName": "es-define-property", "hash": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="}}, "npm:es-errors": {"type": "npm", "name": "npm:es-errors", "data": {"version": "1.3.0", "packageName": "es-errors", "hash": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="}}, "npm:es-object-atoms": {"type": "npm", "name": "npm:es-object-atoms", "data": {"version": "1.1.1", "packageName": "es-object-atoms", "hash": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA=="}}, "npm:es-set-tostringtag": {"type": "npm", "name": "npm:es-set-tostringtag", "data": {"version": "2.1.0", "packageName": "es-set-tostringtag", "hash": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA=="}}, "npm:escalade": {"type": "npm", "name": "npm:escalade", "data": {"version": "3.2.0", "packageName": "escalade", "hash": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="}}, "npm:escape-html": {"type": "npm", "name": "npm:escape-html", "data": {"version": "1.0.3", "packageName": "escape-html", "hash": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="}}, "npm:escape-string-regexp": {"type": "npm", "name": "npm:escape-string-regexp", "data": {"version": "1.0.5", "packageName": "escape-string-regexp", "hash": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg=="}}, "npm:escodegen": {"type": "npm", "name": "npm:escodegen", "data": {"version": "2.1.0", "packageName": "escodegen", "hash": "sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w=="}}, "npm:esprima": {"type": "npm", "name": "npm:esprima", "data": {"version": "4.0.1", "packageName": "esprima", "hash": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}}, "npm:estraverse": {"type": "npm", "name": "npm:estraverse", "data": {"version": "5.3.0", "packageName": "estraverse", "hash": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="}}, "npm:esutils": {"type": "npm", "name": "npm:esutils", "data": {"version": "2.0.3", "packageName": "esutils", "hash": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="}}, "npm:etag": {"type": "npm", "name": "npm:etag", "data": {"version": "1.8.1", "packageName": "etag", "hash": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="}}, "npm:eventsource": {"type": "npm", "name": "npm:eventsource", "data": {"version": "3.0.7", "packageName": "eventsource", "hash": "sha512-CRT1WTyuQoD771GW56XEZFQ/ZoSfWid1alKGDYMmkt2yl8UXrVR4pspqWNEcqKvVIzg6PAltWjxcSSPrboA4iA=="}}, "npm:eventsource-parser": {"type": "npm", "name": "npm:eventsource-parser", "data": {"version": "3.0.1", "packageName": "eventsource-parser", "hash": "sha512-VARTJ9CYeuQYb0pZEPbzi740OWFgpHe7AYJ2WFZVnUDUQp5Dk2yJUgF36YsZ81cOyxT0QxmXD2EQpapAouzWVA=="}}, "npm:express": {"type": "npm", "name": "npm:express", "data": {"version": "5.1.0", "packageName": "express", "hash": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA=="}}, "npm:express-rate-limit": {"type": "npm", "name": "npm:express-rate-limit", "data": {"version": "7.5.0", "packageName": "express-rate-limit", "hash": "sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg=="}}, "npm:extract-zip": {"type": "npm", "name": "npm:extract-zip", "data": {"version": "2.0.1", "packageName": "extract-zip", "hash": "sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg=="}}, "npm:fast-deep-equal": {"type": "npm", "name": "npm:fast-deep-equal", "data": {"version": "3.1.3", "packageName": "fast-deep-equal", "hash": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}}, "npm:fast-fifo": {"type": "npm", "name": "npm:fast-fifo", "data": {"version": "1.3.2", "packageName": "fast-fifo", "hash": "sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ=="}}, "npm:fast-uri": {"type": "npm", "name": "npm:fast-uri", "data": {"version": "3.0.6", "packageName": "fast-uri", "hash": "sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw=="}}, "npm:fd-slicer": {"type": "npm", "name": "npm:fd-slicer", "data": {"version": "1.1.0", "packageName": "fd-slicer", "hash": "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g=="}}, "npm:fetch-blob": {"type": "npm", "name": "npm:fetch-blob", "data": {"version": "3.2.0", "packageName": "fetch-blob", "hash": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ=="}}, "npm:figures": {"type": "npm", "name": "npm:figures", "data": {"version": "3.2.0", "packageName": "figures", "hash": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg=="}}, "npm:finalhandler": {"type": "npm", "name": "npm:finalhandler", "data": {"version": "2.1.0", "packageName": "finalhandler", "hash": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q=="}}, "npm:flat": {"type": "npm", "name": "npm:flat", "data": {"version": "5.0.2", "packageName": "flat", "hash": "sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ=="}}, "npm:follow-redirects": {"type": "npm", "name": "npm:follow-redirects", "data": {"version": "1.15.9", "packageName": "follow-redirects", "hash": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ=="}}, "npm:foreground-child": {"type": "npm", "name": "npm:foreground-child", "data": {"version": "3.3.1", "packageName": "foreground-child", "hash": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw=="}}, "npm:form-data": {"type": "npm", "name": "npm:form-data", "data": {"version": "4.0.2", "packageName": "form-data", "hash": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w=="}}, "npm:mime-db@1.52.0": {"type": "npm", "name": "npm:mime-db@1.52.0", "data": {"version": "1.52.0", "packageName": "mime-db", "hash": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}}, "npm:mime-db": {"type": "npm", "name": "npm:mime-db", "data": {"version": "1.54.0", "packageName": "mime-db", "hash": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ=="}}, "npm:mime-types@2.1.35": {"type": "npm", "name": "npm:mime-types@2.1.35", "data": {"version": "2.1.35", "packageName": "mime-types", "hash": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="}}, "npm:mime-types": {"type": "npm", "name": "npm:mime-types", "data": {"version": "3.0.1", "packageName": "mime-types", "hash": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA=="}}, "npm:formdata-polyfill": {"type": "npm", "name": "npm:formdata-polyfill", "data": {"version": "4.0.10", "packageName": "formdata-polyfill", "hash": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g=="}}, "npm:forwarded": {"type": "npm", "name": "npm:forwarded", "data": {"version": "0.2.0", "packageName": "forwarded", "hash": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="}}, "npm:fresh": {"type": "npm", "name": "npm:fresh", "data": {"version": "2.0.0", "packageName": "fresh", "hash": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A=="}}, "npm:front-matter": {"type": "npm", "name": "npm:front-matter", "data": {"version": "4.0.2", "packageName": "front-matter", "hash": "sha512-I8ZuJ/qG92NWX8i5x1Y8qyj3vizhXS31OxjKDu3LKP+7/qBgfIKValiZIEwoVoJKUHlhWtYrktkxV1XsX+pPlg=="}}, "npm:fs-constants": {"type": "npm", "name": "npm:fs-constants", "data": {"version": "1.0.0", "packageName": "fs-constants", "hash": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="}}, "npm:function-bind": {"type": "npm", "name": "npm:function-bind", "data": {"version": "1.1.2", "packageName": "function-bind", "hash": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}}, "npm:get-caller-file": {"type": "npm", "name": "npm:get-caller-file", "data": {"version": "2.0.5", "packageName": "get-caller-file", "hash": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="}}, "npm:get-intrinsic": {"type": "npm", "name": "npm:get-intrinsic", "data": {"version": "1.3.0", "packageName": "get-intrinsic", "hash": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ=="}}, "npm:get-proto": {"type": "npm", "name": "npm:get-proto", "data": {"version": "1.0.1", "packageName": "get-proto", "hash": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g=="}}, "npm:get-stream": {"type": "npm", "name": "npm:get-stream", "data": {"version": "5.2.0", "packageName": "get-stream", "hash": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA=="}}, "npm:get-uri": {"type": "npm", "name": "npm:get-uri", "data": {"version": "6.0.4", "packageName": "get-uri", "hash": "sha512-E1b1lFFLvLgak2whF2xDBcOy6NLVGZBqqjJjsIhvopKfWWEi64pLVTWWehV8KlLerZkfNTA95sTe2OdJKm1OzQ=="}}, "npm:glob": {"type": "npm", "name": "npm:glob", "data": {"version": "10.4.5", "packageName": "glob", "hash": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg=="}}, "npm:minimatch@9.0.5": {"type": "npm", "name": "npm:minimatch@9.0.5", "data": {"version": "9.0.5", "packageName": "minimatch", "hash": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="}}, "npm:minimatch": {"type": "npm", "name": "npm:minimatch", "data": {"version": "10.0.1", "packageName": "minimatch", "hash": "sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ=="}}, "npm:minimatch@9.0.3": {"type": "npm", "name": "npm:minimatch@9.0.3", "data": {"version": "9.0.3", "packageName": "minimatch", "hash": "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg=="}}, "npm:gopd": {"type": "npm", "name": "npm:gopd", "data": {"version": "1.2.0", "packageName": "gopd", "hash": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}}, "npm:has-flag": {"type": "npm", "name": "npm:has-flag", "data": {"version": "4.0.0", "packageName": "has-flag", "hash": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="}}, "npm:has-symbols": {"type": "npm", "name": "npm:has-symbols", "data": {"version": "1.1.0", "packageName": "has-symbols", "hash": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="}}, "npm:has-tostringtag": {"type": "npm", "name": "npm:has-tostringtag", "data": {"version": "1.0.2", "packageName": "has-tostringtag", "hash": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="}}, "npm:hasown": {"type": "npm", "name": "npm:hasown", "data": {"version": "2.0.2", "packageName": "hasown", "hash": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="}}, "npm:http-errors": {"type": "npm", "name": "npm:http-errors", "data": {"version": "2.0.0", "packageName": "http-errors", "hash": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="}}, "npm:http-proxy-agent": {"type": "npm", "name": "npm:http-proxy-agent", "data": {"version": "7.0.2", "packageName": "http-proxy-agent", "hash": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig=="}}, "npm:https-proxy-agent": {"type": "npm", "name": "npm:https-proxy-agent", "data": {"version": "7.0.6", "packageName": "https-proxy-agent", "hash": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw=="}}, "npm:iconv-lite": {"type": "npm", "name": "npm:iconv-lite", "data": {"version": "0.6.3", "packageName": "iconv-lite", "hash": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="}}, "npm:ieee754": {"type": "npm", "name": "npm:ieee754", "data": {"version": "1.2.1", "packageName": "ieee754", "hash": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="}}, "npm:ignore": {"type": "npm", "name": "npm:ignore", "data": {"version": "5.3.2", "packageName": "ignore", "hash": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="}}, "npm:import-fresh": {"type": "npm", "name": "npm:import-fresh", "data": {"version": "3.3.1", "packageName": "import-fresh", "hash": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ=="}}, "npm:inherits": {"type": "npm", "name": "npm:inherits", "data": {"version": "2.0.4", "packageName": "inherits", "hash": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}}, "npm:ip-address": {"type": "npm", "name": "npm:ip-address", "data": {"version": "9.0.5", "packageName": "ip-address", "hash": "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g=="}}, "npm:ipaddr.js": {"type": "npm", "name": "npm:ipaddr.js", "data": {"version": "1.9.1", "packageName": "ipaddr.js", "hash": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="}}, "npm:is-arrayish": {"type": "npm", "name": "npm:is-arrayish", "data": {"version": "0.2.1", "packageName": "is-arrayish", "hash": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg=="}}, "npm:is-docker": {"type": "npm", "name": "npm:is-docker", "data": {"version": "3.0.0", "packageName": "is-docker", "hash": "sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ=="}}, "npm:is-docker@2.2.1": {"type": "npm", "name": "npm:is-docker@2.2.1", "data": {"version": "2.2.1", "packageName": "is-docker", "hash": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ=="}}, "npm:is-fullwidth-code-point": {"type": "npm", "name": "npm:is-fullwidth-code-point", "data": {"version": "3.0.0", "packageName": "is-fullwidth-code-point", "hash": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}}, "npm:is-inside-container": {"type": "npm", "name": "npm:is-inside-container", "data": {"version": "1.0.0", "packageName": "is-inside-container", "hash": "sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA=="}}, "npm:is-interactive": {"type": "npm", "name": "npm:is-interactive", "data": {"version": "1.0.0", "packageName": "is-interactive", "hash": "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w=="}}, "npm:is-promise": {"type": "npm", "name": "npm:is-promise", "data": {"version": "4.0.0", "packageName": "is-promise", "hash": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ=="}}, "npm:is-unicode-supported": {"type": "npm", "name": "npm:is-unicode-supported", "data": {"version": "0.1.0", "packageName": "is-unicode-supported", "hash": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw=="}}, "npm:is-wsl": {"type": "npm", "name": "npm:is-wsl", "data": {"version": "3.1.0", "packageName": "is-wsl", "hash": "sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw=="}}, "npm:is-wsl@2.2.0": {"type": "npm", "name": "npm:is-wsl@2.2.0", "data": {"version": "2.2.0", "packageName": "is-wsl", "hash": "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww=="}}, "npm:isexe": {"type": "npm", "name": "npm:isexe", "data": {"version": "2.0.0", "packageName": "isexe", "hash": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="}}, "npm:jackspeak": {"type": "npm", "name": "npm:jackspeak", "data": {"version": "3.4.3", "packageName": "jackspeak", "hash": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw=="}}, "npm:jest-diff": {"type": "npm", "name": "npm:jest-diff", "data": {"version": "29.7.0", "packageName": "jest-diff", "hash": "sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw=="}}, "npm:jest-get-type": {"type": "npm", "name": "npm:jest-get-type", "data": {"version": "29.6.3", "packageName": "jest-get-type", "hash": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw=="}}, "npm:js-tokens": {"type": "npm", "name": "npm:js-tokens", "data": {"version": "4.0.0", "packageName": "js-tokens", "hash": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}}, "npm:jsbn": {"type": "npm", "name": "npm:jsbn", "data": {"version": "1.1.0", "packageName": "jsbn", "hash": "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A=="}}, "npm:json-parse-even-better-errors": {"type": "npm", "name": "npm:json-parse-even-better-errors", "data": {"version": "2.3.1", "packageName": "json-parse-even-better-errors", "hash": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="}}, "npm:json-schema-traverse": {"type": "npm", "name": "npm:json-schema-traverse", "data": {"version": "1.0.0", "packageName": "json-schema-traverse", "hash": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="}}, "npm:json5": {"type": "npm", "name": "npm:json5", "data": {"version": "2.2.3", "packageName": "json5", "hash": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="}}, "npm:jsonc-parser": {"type": "npm", "name": "npm:jsonc-parser", "data": {"version": "3.2.0", "packageName": "jsonc-parser", "hash": "sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w=="}}, "npm:jsonwebtoken": {"type": "npm", "name": "npm:j<PERSON><PERSON><PERSON><PERSON>", "data": {"version": "9.0.2", "packageName": "jsonwebtoken", "hash": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ=="}}, "npm:jwa": {"type": "npm", "name": "npm:jwa", "data": {"version": "1.4.2", "packageName": "jwa", "hash": "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw=="}}, "npm:jws": {"type": "npm", "name": "npm:jws", "data": {"version": "3.2.2", "packageName": "jws", "hash": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA=="}}, "npm:lines-and-columns": {"type": "npm", "name": "npm:lines-and-columns", "data": {"version": "1.2.4", "packageName": "lines-and-columns", "hash": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="}}, "npm:lines-and-columns@2.0.3": {"type": "npm", "name": "npm:lines-and-columns@2.0.3", "data": {"version": "2.0.3", "packageName": "lines-and-columns", "hash": "sha512-cNOjgCnLB+FnvWWtyRTzmB3POJ+cXxTA81LoW7u8JdmhfXzriropYwpjShnz1QLLWsQwY7nIxoDmcPTwphDK9w=="}}, "npm:lodash.includes": {"type": "npm", "name": "npm:lodash.includes", "data": {"version": "4.3.0", "packageName": "lodash.includes", "hash": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="}}, "npm:lodash.isboolean": {"type": "npm", "name": "npm:lodash.isboolean", "data": {"version": "3.0.3", "packageName": "lodash.isboolean", "hash": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="}}, "npm:lodash.isinteger": {"type": "npm", "name": "npm:lodash.isint<PERSON>r", "data": {"version": "4.0.4", "packageName": "lodash.isinteger", "hash": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="}}, "npm:lodash.isnumber": {"type": "npm", "name": "npm:lodash.isnumber", "data": {"version": "3.0.3", "packageName": "lodash.isnumber", "hash": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="}}, "npm:lodash.isplainobject": {"type": "npm", "name": "npm:lodash.isplainobject", "data": {"version": "4.0.6", "packageName": "lodash.isplainobject", "hash": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="}}, "npm:lodash.isstring": {"type": "npm", "name": "npm:lodash.isstring", "data": {"version": "4.0.1", "packageName": "lodash.isstring", "hash": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="}}, "npm:lodash.once": {"type": "npm", "name": "npm:lodash.once", "data": {"version": "4.1.1", "packageName": "lodash.once", "hash": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="}}, "npm:log-symbols": {"type": "npm", "name": "npm:log-symbols", "data": {"version": "4.1.0", "packageName": "log-symbols", "hash": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg=="}}, "npm:lru-cache": {"type": "npm", "name": "npm:lru-cache", "data": {"version": "7.18.3", "packageName": "lru-cache", "hash": "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA=="}}, "npm:lru-cache@10.4.3": {"type": "npm", "name": "npm:lru-cache@10.4.3", "data": {"version": "10.4.3", "packageName": "lru-cache", "hash": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="}}, "npm:math-intrinsics": {"type": "npm", "name": "npm:math-intrinsics", "data": {"version": "1.1.0", "packageName": "math-intrinsics", "hash": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="}}, "npm:media-typer": {"type": "npm", "name": "npm:media-typer", "data": {"version": "1.1.0", "packageName": "media-typer", "hash": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw=="}}, "npm:merge-descriptors": {"type": "npm", "name": "npm:merge-descriptors", "data": {"version": "2.0.0", "packageName": "merge-descriptors", "hash": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g=="}}, "npm:mimic-fn": {"type": "npm", "name": "npm:mimic-fn", "data": {"version": "2.1.0", "packageName": "mimic-fn", "hash": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg=="}}, "npm:minimist": {"type": "npm", "name": "npm:minimist", "data": {"version": "1.2.8", "packageName": "minimist", "hash": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="}}, "npm:minipass": {"type": "npm", "name": "npm:minipass", "data": {"version": "7.1.2", "packageName": "minipass", "hash": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="}}, "npm:mitt": {"type": "npm", "name": "npm:mitt", "data": {"version": "3.0.1", "packageName": "mitt", "hash": "sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw=="}}, "npm:ms": {"type": "npm", "name": "npm:ms", "data": {"version": "2.1.3", "packageName": "ms", "hash": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}, "npm:negotiator": {"type": "npm", "name": "npm:negotiator", "data": {"version": "1.0.0", "packageName": "negotiator", "hash": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg=="}}, "npm:netmask": {"type": "npm", "name": "npm:netmask", "data": {"version": "2.0.2", "packageName": "netmask", "hash": "sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg=="}}, "npm:node-domexception": {"type": "npm", "name": "npm:node-domexception", "data": {"version": "1.0.0", "packageName": "node-domexception", "hash": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ=="}}, "npm:node-fetch": {"type": "npm", "name": "npm:node-fetch", "data": {"version": "3.3.2", "packageName": "node-fetch", "hash": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA=="}}, "npm:node-machine-id": {"type": "npm", "name": "npm:node-machine-id", "data": {"version": "1.1.12", "packageName": "node-machine-id", "hash": "sha512-QNABxbrPa3qEIfrE6GOJ7BYIuignnJw7iQ2YPbc3Nla1HzRJjXzZOiikfF8m7eAMfichLt3M4VgLOetqgDmgGQ=="}}, "npm:npm-run-path": {"type": "npm", "name": "npm:npm-run-path", "data": {"version": "4.0.1", "packageName": "npm-run-path", "hash": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw=="}}, "npm:nx": {"type": "npm", "name": "npm:nx", "data": {"version": "21.0.4", "packageName": "nx", "hash": "sha512-ppk6WpwQ1aX2LCYOA0uHdMB3ncpZULoQreJZ/ebCTpZK8BGP6TPOJH6wrTLW+FRS9Y38MZzOwiPqz8VxDQnP/Q=="}}, "npm:open@8.4.2": {"type": "npm", "name": "npm:open@8.4.2", "data": {"version": "8.4.2", "packageName": "open", "hash": "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ=="}}, "npm:open": {"type": "npm", "name": "npm:open", "data": {"version": "10.1.2", "packageName": "open", "hash": "sha512-cxN6aIDPz6rm8hbebcP7vrQNhvRcveZoJU72Y7vskh4oIm+BZwBECnx5nTmrlres1Qapvx27Qo1Auukpf8PKXw=="}}, "npm:tar-stream@2.2.0": {"type": "npm", "name": "npm:tar-stream@2.2.0", "data": {"version": "2.2.0", "packageName": "tar-stream", "hash": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ=="}}, "npm:tar-stream": {"type": "npm", "name": "npm:tar-stream", "data": {"version": "3.1.7", "packageName": "tar-stream", "hash": "sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ=="}}, "npm:object-assign": {"type": "npm", "name": "npm:object-assign", "data": {"version": "4.1.1", "packageName": "object-assign", "hash": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="}}, "npm:object-inspect": {"type": "npm", "name": "npm:object-inspect", "data": {"version": "1.13.4", "packageName": "object-inspect", "hash": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew=="}}, "npm:on-finished": {"type": "npm", "name": "npm:on-finished", "data": {"version": "2.4.1", "packageName": "on-finished", "hash": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="}}, "npm:once": {"type": "npm", "name": "npm:once", "data": {"version": "1.4.0", "packageName": "once", "hash": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="}}, "npm:onetime": {"type": "npm", "name": "npm:onetime", "data": {"version": "5.1.2", "packageName": "onetime", "hash": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg=="}}, "npm:ora": {"type": "npm", "name": "npm:ora", "data": {"version": "5.3.0", "packageName": "ora", "hash": "sha512-zAKMgGXUim0Jyd6CXK9lraBnD3H5yPGBPPOkC23a2BG6hsm4Zu6OQSjQuEtV0BHDf4aKHcUFvJiGRrFuW3MG8g=="}}, "npm:pac-proxy-agent": {"type": "npm", "name": "npm:pac-proxy-agent", "data": {"version": "7.2.0", "packageName": "pac-proxy-agent", "hash": "sha512-TEB8ESquiLMc0lV8vcd5Ql/JAKAoyzHFXaStwjkzpOpC5Yv+pIzLfHvjTSdf3vpa2bMiUQrg9i6276yn8666aA=="}}, "npm:pac-resolver": {"type": "npm", "name": "npm:pac-resolver", "data": {"version": "7.0.1", "packageName": "pac-resolver", "hash": "sha512-5NPgf87AT2STgwa2ntRMr45jTKrYBGkVU36yT0ig/n/GMAa3oPqhZfIQ2kMEimReg0+t9kZViDVZ83qfVUlckg=="}}, "npm:package-json-from-dist": {"type": "npm", "name": "npm:package-json-from-dist", "data": {"version": "1.0.1", "packageName": "package-json-from-dist", "hash": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw=="}}, "npm:parent-module": {"type": "npm", "name": "npm:parent-module", "data": {"version": "1.0.1", "packageName": "parent-module", "hash": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="}}, "npm:parse-json": {"type": "npm", "name": "npm:parse-json", "data": {"version": "5.2.0", "packageName": "parse-json", "hash": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="}}, "npm:parseurl": {"type": "npm", "name": "npm:parseurl", "data": {"version": "1.3.3", "packageName": "parseurl", "hash": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="}}, "npm:path-key": {"type": "npm", "name": "npm:path-key", "data": {"version": "3.1.1", "packageName": "path-key", "hash": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="}}, "npm:path-scurry": {"type": "npm", "name": "npm:path-scurry", "data": {"version": "1.11.1", "packageName": "path-scurry", "hash": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA=="}}, "npm:path-to-regexp": {"type": "npm", "name": "npm:path-to-regexp", "data": {"version": "8.2.0", "packageName": "path-to-regexp", "hash": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ=="}}, "npm:pend": {"type": "npm", "name": "npm:pend", "data": {"version": "1.2.0", "packageName": "pend", "hash": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg=="}}, "npm:picocolors": {"type": "npm", "name": "npm:picocolors", "data": {"version": "1.1.1", "packageName": "picocolors", "hash": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="}}, "npm:pkce-challenge": {"type": "npm", "name": "npm:pkce-challenge", "data": {"version": "5.0.0", "packageName": "pkce-challenge", "hash": "sha512-ueGLflrrnvwB3xuo/uGob5pd5FN7l0MsLf0Z87o/UQmRtwjvfylfc9MurIxRAWywCYTgrvpXBcqjV4OfCYGCIQ=="}}, "npm:pretty-format": {"type": "npm", "name": "npm:pretty-format", "data": {"version": "29.7.0", "packageName": "pretty-format", "hash": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ=="}}, "npm:progress": {"type": "npm", "name": "npm:progress", "data": {"version": "2.0.3", "packageName": "progress", "hash": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="}}, "npm:proxy-addr": {"type": "npm", "name": "npm:proxy-addr", "data": {"version": "2.0.7", "packageName": "proxy-addr", "hash": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="}}, "npm:proxy-agent": {"type": "npm", "name": "npm:proxy-agent", "data": {"version": "6.5.0", "packageName": "proxy-agent", "hash": "sha512-TmatMXdr2KlRiA2CyDu8GqR8EjahTG3aY3nXjdzFyoZbmB8hrBsTyMezhULIXKnC0jpfjlmiZ3+EaCzoInSu/A=="}}, "npm:proxy-from-env": {"type": "npm", "name": "npm:proxy-from-env", "data": {"version": "1.1.0", "packageName": "proxy-from-env", "hash": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}}, "npm:pump": {"type": "npm", "name": "npm:pump", "data": {"version": "3.0.2", "packageName": "pump", "hash": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw=="}}, "npm:puppeteer": {"type": "npm", "name": "npm:puppeteer", "data": {"version": "23.11.1", "packageName": "puppeteer", "hash": "sha512-53uIX3KR5en8l7Vd8n5DUv90Ae9QDQsyIthaUFVzwV6yU750RjqRznEtNMBT20VthqAdemnJN+hxVdmMHKt7Zw=="}}, "npm:puppeteer-core": {"type": "npm", "name": "npm:puppeteer-core", "data": {"version": "23.11.1", "packageName": "puppeteer-core", "hash": "sha512-3HZ2/7hdDKZvZQ7dhhITOUg4/wOrDRjyK2ZBllRB0ZCOi9u0cwq1ACHDjBB+nX+7+kltHjQvBRdeY7+W0T+7Gg=="}}, "npm:qs": {"type": "npm", "name": "npm:qs", "data": {"version": "6.14.0", "packageName": "qs", "hash": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w=="}}, "npm:range-parser": {"type": "npm", "name": "npm:range-parser", "data": {"version": "1.2.1", "packageName": "range-parser", "hash": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="}}, "npm:raw-body": {"type": "npm", "name": "npm:raw-body", "data": {"version": "3.0.0", "packageName": "raw-body", "hash": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g=="}}, "npm:react-is": {"type": "npm", "name": "npm:react-is", "data": {"version": "18.3.1", "packageName": "react-is", "hash": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="}}, "npm:readable-stream": {"type": "npm", "name": "npm:readable-stream", "data": {"version": "3.6.2", "packageName": "readable-stream", "hash": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="}}, "npm:require-directory": {"type": "npm", "name": "npm:require-directory", "data": {"version": "2.1.1", "packageName": "require-directory", "hash": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="}}, "npm:require-from-string": {"type": "npm", "name": "npm:require-from-string", "data": {"version": "2.0.2", "packageName": "require-from-string", "hash": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="}}, "npm:resolve-from": {"type": "npm", "name": "npm:resolve-from", "data": {"version": "4.0.0", "packageName": "resolve-from", "hash": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="}}, "npm:resolve.exports": {"type": "npm", "name": "npm:resolve.exports", "data": {"version": "2.0.3", "packageName": "resolve.exports", "hash": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A=="}}, "npm:restore-cursor": {"type": "npm", "name": "npm:restore-cursor", "data": {"version": "3.1.0", "packageName": "restore-cursor", "hash": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA=="}}, "npm:signal-exit@3.0.7": {"type": "npm", "name": "npm:signal-exit@3.0.7", "data": {"version": "3.0.7", "packageName": "signal-exit", "hash": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="}}, "npm:signal-exit": {"type": "npm", "name": "npm:signal-exit", "data": {"version": "4.1.0", "packageName": "signal-exit", "hash": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw=="}}, "npm:router": {"type": "npm", "name": "npm:router", "data": {"version": "2.2.0", "packageName": "router", "hash": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ=="}}, "npm:run-applescript": {"type": "npm", "name": "npm:run-applescript", "data": {"version": "7.0.0", "packageName": "run-applescript", "hash": "sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A=="}}, "npm:safe-buffer": {"type": "npm", "name": "npm:safe-buffer", "data": {"version": "5.2.1", "packageName": "safe-buffer", "hash": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="}}, "npm:safer-buffer": {"type": "npm", "name": "npm:safer-buffer", "data": {"version": "2.1.2", "packageName": "safer-buffer", "hash": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}}, "npm:semver": {"type": "npm", "name": "npm:semver", "data": {"version": "7.7.2", "packageName": "semver", "hash": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="}}, "npm:send": {"type": "npm", "name": "npm:send", "data": {"version": "1.2.0", "packageName": "send", "hash": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw=="}}, "npm:serve-static": {"type": "npm", "name": "npm:serve-static", "data": {"version": "2.2.0", "packageName": "serve-static", "hash": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ=="}}, "npm:setprototypeof": {"type": "npm", "name": "npm:set<PERSON><PERSON><PERSON><PERSON>", "data": {"version": "1.2.0", "packageName": "setprot<PERSON>of", "hash": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}}, "npm:shebang-command": {"type": "npm", "name": "npm:shebang-command", "data": {"version": "2.0.0", "packageName": "shebang-command", "hash": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="}}, "npm:shebang-regex": {"type": "npm", "name": "npm:shebang-regex", "data": {"version": "3.0.0", "packageName": "shebang-regex", "hash": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="}}, "npm:side-channel": {"type": "npm", "name": "npm:side-channel", "data": {"version": "1.1.0", "packageName": "side-channel", "hash": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw=="}}, "npm:side-channel-list": {"type": "npm", "name": "npm:side-channel-list", "data": {"version": "1.0.0", "packageName": "side-channel-list", "hash": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA=="}}, "npm:side-channel-map": {"type": "npm", "name": "npm:side-channel-map", "data": {"version": "1.0.1", "packageName": "side-channel-map", "hash": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA=="}}, "npm:side-channel-weakmap": {"type": "npm", "name": "npm:side-channel-weakmap", "data": {"version": "1.0.2", "packageName": "side-channel-weakmap", "hash": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A=="}}, "npm:smart-buffer": {"type": "npm", "name": "npm:smart-buffer", "data": {"version": "4.2.0", "packageName": "smart-buffer", "hash": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg=="}}, "npm:socks": {"type": "npm", "name": "npm:socks", "data": {"version": "2.8.4", "packageName": "socks", "hash": "sha512-D3YaD0aRxR3mEcqnidIs7ReYJFVzWdd6fXJYUM8ixcQcJRGTka/b3saV0KflYhyVJXKhb947GndU35SxYNResQ=="}}, "npm:socks-proxy-agent": {"type": "npm", "name": "npm:socks-proxy-agent", "data": {"version": "8.0.5", "packageName": "socks-proxy-agent", "hash": "sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw=="}}, "npm:source-map": {"type": "npm", "name": "npm:source-map", "data": {"version": "0.6.1", "packageName": "source-map", "hash": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}}, "npm:statuses": {"type": "npm", "name": "npm:statuses", "data": {"version": "2.0.1", "packageName": "statuses", "hash": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="}}, "npm:streamx": {"type": "npm", "name": "npm:streamx", "data": {"version": "2.22.0", "packageName": "streamx", "hash": "sha512-sLh1evHOzBy/iWRiR6d1zRcLao4gGZr3C1kzNz4fopCOKJb6xD9ub8Mpi9Mr1R6id5o43S+d93fI48UC5uM9aw=="}}, "npm:string_decoder": {"type": "npm", "name": "npm:string_decoder", "data": {"version": "1.3.0", "packageName": "string_decoder", "hash": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="}}, "npm:string-width-cjs": {"type": "npm", "name": "npm:string-width-cjs", "data": {"version": "npm:string-width@4.2.3", "packageName": "string-width-cjs", "hash": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="}}, "npm:strip-ansi-cjs": {"type": "npm", "name": "npm:strip-ansi-cjs", "data": {"version": "npm:strip-ansi@6.0.1", "packageName": "strip-ansi-cjs", "hash": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="}}, "npm:strip-bom": {"type": "npm", "name": "npm:strip-bom", "data": {"version": "3.0.0", "packageName": "strip-bom", "hash": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA=="}}, "npm:supports-color": {"type": "npm", "name": "npm:supports-color", "data": {"version": "7.2.0", "packageName": "supports-color", "hash": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="}}, "npm:tar-fs": {"type": "npm", "name": "npm:tar-fs", "data": {"version": "3.0.8", "packageName": "tar-fs", "hash": "sha512-ZoROL70jptorGAlgAYiLoBLItEKw/fUxg9BSYK/dF/GAGYFJOJJJMvjPAKDJraCXFwadD456FCuvLWgfhMsPwg=="}}, "npm:text-decoder": {"type": "npm", "name": "npm:text-decoder", "data": {"version": "1.2.3", "packageName": "text-decoder", "hash": "sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA=="}}, "npm:through": {"type": "npm", "name": "npm:through", "data": {"version": "2.3.8", "packageName": "through", "hash": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="}}, "npm:tmp": {"type": "npm", "name": "npm:tmp", "data": {"version": "0.2.3", "packageName": "tmp", "hash": "sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w=="}}, "npm:toidentifier": {"type": "npm", "name": "npm:toidentifier", "data": {"version": "1.0.1", "packageName": "toidentifier", "hash": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="}}, "npm:tree-kill": {"type": "npm", "name": "npm:tree-kill", "data": {"version": "1.2.2", "packageName": "tree-kill", "hash": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A=="}}, "npm:tsconfig-paths": {"type": "npm", "name": "npm:tsconfig-paths", "data": {"version": "4.2.0", "packageName": "tsconfig-paths", "hash": "sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg=="}}, "npm:tslib": {"type": "npm", "name": "npm:tslib", "data": {"version": "2.8.1", "packageName": "tslib", "hash": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}}, "npm:tunnel": {"type": "npm", "name": "npm:tunnel", "data": {"version": "0.0.6", "packageName": "tunnel", "hash": "sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg=="}}, "npm:type-is": {"type": "npm", "name": "npm:type-is", "data": {"version": "2.0.1", "packageName": "type-is", "hash": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw=="}}, "npm:typed-query-selector": {"type": "npm", "name": "npm:typed-query-selector", "data": {"version": "2.12.0", "packageName": "typed-query-selector", "hash": "sha512-SbklCd1F0EiZOyPiW192rrHZzZ5sBijB6xM+cpmrwDqObvdtunOHHIk9fCGsoK5JVIYXoyEp4iEdE3upFH3PAg=="}}, "npm:typed-rest-client": {"type": "npm", "name": "npm:typed-rest-client", "data": {"version": "1.8.11", "packageName": "typed-rest-client", "hash": "sha512-5UvfMpd1oelmUPRbbaVnq+rHP7ng2cE4qoQkQeAqxRL6PklkxsM0g32/HL0yfvruK6ojQ5x8EE+HF4YV6DtuCA=="}}, "npm:unbzip2-stream": {"type": "npm", "name": "npm:unbzip2-stream", "data": {"version": "1.4.3", "packageName": "unbzip2-stream", "hash": "sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg=="}}, "npm:underscore": {"type": "npm", "name": "npm:underscore", "data": {"version": "1.13.7", "packageName": "underscore", "hash": "sha512-GMXzWtsc57XAtguZgaQViUOzs0KTkk8ojr3/xAxXLITqf/3EMwxC0inyETfDFjH/Krbhuep0HNbbjI9i/q3F3g=="}}, "npm:undici-types": {"type": "npm", "name": "npm:undici-types", "data": {"version": "6.21.0", "packageName": "undici-types", "hash": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ=="}}, "npm:universal-user-agent": {"type": "npm", "name": "npm:universal-user-agent", "data": {"version": "7.0.3", "packageName": "universal-user-agent", "hash": "sha512-TmnEAEAsBJVZM/AADELsK76llnwcf9vMKuPz8JflO1frO8Lchitr0fNaN9d+Ap0BjKtqWqd/J17qeDnXh8CL2A=="}}, "npm:unpipe": {"type": "npm", "name": "npm:unpipe", "data": {"version": "1.0.0", "packageName": "unpipe", "hash": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="}}, "npm:util-deprecate": {"type": "npm", "name": "npm:util-deprecate", "data": {"version": "1.0.2", "packageName": "util-deprecate", "hash": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}}, "npm:uuid": {"type": "npm", "name": "npm:uuid", "data": {"version": "8.3.2", "packageName": "uuid", "hash": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="}}, "npm:vary": {"type": "npm", "name": "npm:vary", "data": {"version": "1.1.2", "packageName": "vary", "hash": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="}}, "npm:wcwidth": {"type": "npm", "name": "npm:wcwidth", "data": {"version": "1.0.1", "packageName": "wcwidth", "hash": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg=="}}, "npm:web-streams-polyfill": {"type": "npm", "name": "npm:web-streams-polyfill", "data": {"version": "3.3.3", "packageName": "web-streams-polyfill", "hash": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw=="}}, "npm:which": {"type": "npm", "name": "npm:which", "data": {"version": "2.0.2", "packageName": "which", "hash": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="}}, "npm:wrap-ansi-cjs": {"type": "npm", "name": "npm:wrap-ansi-cjs", "data": {"version": "npm:wrap-ansi@7.0.0", "packageName": "wrap-ansi-cjs", "hash": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q=="}}, "npm:wrappy": {"type": "npm", "name": "npm:wrappy", "data": {"version": "1.0.2", "packageName": "wrappy", "hash": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}}, "npm:ws": {"type": "npm", "name": "npm:ws", "data": {"version": "8.18.2", "packageName": "ws", "hash": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ=="}}, "npm:y18n": {"type": "npm", "name": "npm:y18n", "data": {"version": "5.0.8", "packageName": "y18n", "hash": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA=="}}, "npm:yaml": {"type": "npm", "name": "npm:yaml", "data": {"version": "2.8.0", "packageName": "yaml", "hash": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ=="}}, "npm:yargs": {"type": "npm", "name": "npm:yargs", "data": {"version": "17.7.2", "packageName": "yargs", "hash": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w=="}}, "npm:yargs-parser": {"type": "npm", "name": "npm:yargs-parser", "data": {"version": "21.1.1", "packageName": "yargs-parser", "hash": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw=="}}, "npm:yauzl": {"type": "npm", "name": "npm:yauzl", "data": {"version": "2.10.0", "packageName": "yauzl", "hash": "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g=="}}, "npm:zod-to-json-schema": {"type": "npm", "name": "npm:zod-to-json-schema", "data": {"version": "3.24.5", "packageName": "zod-to-json-schema", "hash": "sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g=="}}}, "dependencies": {"npm:@azure/abort-controller": [{"source": "npm:@azure/abort-controller", "target": "npm:tslib", "type": "static"}], "npm:@azure/core-auth": [{"source": "npm:@azure/core-auth", "target": "npm:@azure/abort-controller", "type": "static"}, {"source": "npm:@azure/core-auth", "target": "npm:@azure/core-util", "type": "static"}, {"source": "npm:@azure/core-auth", "target": "npm:tslib", "type": "static"}], "npm:@azure/core-client": [{"source": "npm:@azure/core-client", "target": "npm:@azure/abort-controller", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:@azure/core-auth", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:@azure/core-rest-pipeline", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:@azure/core-tracing", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:@azure/core-util", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:@azure/logger", "type": "static"}, {"source": "npm:@azure/core-client", "target": "npm:tslib", "type": "static"}], "npm:@azure/core-rest-pipeline": [{"source": "npm:@azure/core-rest-pipeline", "target": "npm:@azure/abort-controller", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:@azure/core-auth", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:@azure/core-tracing", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:@azure/core-util", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:@azure/logger", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:@typespec/ts-http-runtime", "type": "static"}, {"source": "npm:@azure/core-rest-pipeline", "target": "npm:tslib", "type": "static"}], "npm:@azure/core-tracing": [{"source": "npm:@azure/core-tracing", "target": "npm:tslib", "type": "static"}], "npm:@azure/core-util": [{"source": "npm:@azure/core-util", "target": "npm:@azure/abort-controller", "type": "static"}, {"source": "npm:@azure/core-util", "target": "npm:@typespec/ts-http-runtime", "type": "static"}, {"source": "npm:@azure/core-util", "target": "npm:tslib", "type": "static"}], "npm:@azure/identity": [{"source": "npm:@azure/identity", "target": "npm:@azure/abort-controller", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/core-auth", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/core-client", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/core-rest-pipeline", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/core-tracing", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/core-util", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/logger", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/msal-browser", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:@azure/msal-node", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:open", "type": "static"}, {"source": "npm:@azure/identity", "target": "npm:tslib", "type": "static"}], "npm:@azure/logger": [{"source": "npm:@azure/logger", "target": "npm:@typespec/ts-http-runtime", "type": "static"}, {"source": "npm:@azure/logger", "target": "npm:tslib", "type": "static"}], "npm:@azure/msal-browser": [{"source": "npm:@azure/msal-browser", "target": "npm:@azure/msal-common", "type": "static"}], "npm:@azure/msal-node": [{"source": "npm:@azure/msal-node", "target": "npm:@azure/msal-common", "type": "static"}, {"source": "npm:@azure/msal-node", "target": "npm:j<PERSON><PERSON><PERSON><PERSON>", "type": "static"}, {"source": "npm:@azure/msal-node", "target": "npm:uuid", "type": "static"}], "npm:@babel/code-frame": [{"source": "npm:@babel/code-frame", "target": "npm:@babel/helper-validator-identifier", "type": "static"}, {"source": "npm:@babel/code-frame", "target": "npm:js-tokens", "type": "static"}, {"source": "npm:@babel/code-frame", "target": "npm:picocolors", "type": "static"}], "npm:@emnapi/core": [{"source": "npm:@emnapi/core", "target": "npm:@emnapi/wasi-threads", "type": "static"}, {"source": "npm:@emnapi/core", "target": "npm:tslib", "type": "static"}], "npm:@emnapi/runtime": [{"source": "npm:@emnapi/runtime", "target": "npm:tslib", "type": "static"}], "npm:@emnapi/wasi-threads": [{"source": "npm:@emnapi/wasi-threads", "target": "npm:tslib", "type": "static"}], "npm:@isaacs/cliui": [{"source": "npm:@isaacs/cliui", "target": "npm:string-width@5.1.2", "type": "static"}, {"source": "npm:@isaacs/cliui", "target": "npm:string-width-cjs", "type": "static"}, {"source": "npm:@isaacs/cliui", "target": "npm:strip-ansi@7.1.0", "type": "static"}, {"source": "npm:@isaacs/cliui", "target": "npm:strip-ansi-cjs", "type": "static"}, {"source": "npm:@isaacs/cliui", "target": "npm:wrap-ansi@8.1.0", "type": "static"}, {"source": "npm:@isaacs/cliui", "target": "npm:wrap-ansi-cjs", "type": "static"}], "npm:string-width@5.1.2": [{"source": "npm:string-width@5.1.2", "target": "npm:eastasianwidth", "type": "static"}, {"source": "npm:string-width@5.1.2", "target": "npm:emoji-regex@9.2.2", "type": "static"}, {"source": "npm:string-width@5.1.2", "target": "npm:strip-ansi@7.1.0", "type": "static"}], "npm:strip-ansi@7.1.0": [{"source": "npm:strip-ansi@7.1.0", "target": "npm:ansi-regex@6.1.0", "type": "static"}], "npm:wrap-ansi@8.1.0": [{"source": "npm:wrap-ansi@8.1.0", "target": "npm:ansi-styles@6.2.1", "type": "static"}, {"source": "npm:wrap-ansi@8.1.0", "target": "npm:string-width@5.1.2", "type": "static"}, {"source": "npm:wrap-ansi@8.1.0", "target": "npm:strip-ansi@7.1.0", "type": "static"}], "npm:@jest/schemas": [{"source": "npm:@jest/schemas", "target": "npm:@sinclair/typebox", "type": "static"}], "npm:@modelcontextprotocol/sdk": [{"source": "npm:@modelcontextprotocol/sdk", "target": "npm:content-type", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk", "target": "npm:raw-body", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk", "target": "npm:zod", "type": "static"}], "npm:@modelcontextprotocol/server-filesystem": [{"source": "npm:@modelcontextprotocol/server-filesystem", "target": "npm:@modelcontextprotocol/sdk@0.5.0", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-filesystem", "target": "npm:diff", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-filesystem", "target": "npm:glob", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-filesystem", "target": "npm:minimatch", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-filesystem", "target": "npm:zod-to-json-schema", "type": "static"}], "npm:@modelcontextprotocol/sdk@0.5.0": [{"source": "npm:@modelcontextprotocol/sdk@0.5.0", "target": "npm:content-type", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@0.5.0", "target": "npm:raw-body", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@0.5.0", "target": "npm:zod", "type": "static"}], "npm:@modelcontextprotocol/server-github": [{"source": "npm:@modelcontextprotocol/server-github", "target": "npm:@modelcontextprotocol/sdk", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:@types/node", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:@types/node-fetch", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:node-fetch", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:universal-user-agent", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:zod", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-github", "target": "npm:zod-to-json-schema", "type": "static"}], "npm:@modelcontextprotocol/server-memory": [{"source": "npm:@modelcontextprotocol/server-memory", "target": "npm:@modelcontextprotocol/sdk", "type": "static"}], "npm:@modelcontextprotocol/server-puppeteer": [{"source": "npm:@modelcontextprotocol/server-puppeteer", "target": "npm:@modelcontextprotocol/sdk", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-puppeteer", "target": "npm:puppeteer", "type": "static"}], "npm:@modelcontextprotocol/server-sequential-thinking": [{"source": "npm:@modelcontextprotocol/server-sequential-thinking", "target": "npm:@modelcontextprotocol/sdk@0.5.0", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-sequential-thinking", "target": "npm:chalk", "type": "static"}, {"source": "npm:@modelcontextprotocol/server-sequential-thinking", "target": "npm:yargs", "type": "static"}], "npm:@napi-rs/wasm-runtime": [{"source": "npm:@napi-rs/wasm-runtime", "target": "npm:@emnapi/core", "type": "static"}, {"source": "npm:@napi-rs/wasm-runtime", "target": "npm:@emnapi/runtime", "type": "static"}, {"source": "npm:@napi-rs/wasm-runtime", "target": "npm:@tybys/wasm-util", "type": "static"}], "npm:@puppeteer/browsers": [{"source": "npm:@puppeteer/browsers", "target": "npm:debug", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:extract-zip", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:progress", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:proxy-agent", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:semver", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:tar-fs", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:unbzip2-stream", "type": "static"}, {"source": "npm:@puppeteer/browsers", "target": "npm:yargs", "type": "static"}], "npm:@tiberriver256/mcp-server-azure-devops": [{"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:@azure/identity", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:@modelcontextprotocol/sdk@1.11.4", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:axios", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:azure-devops-node-api", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:dotenv", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:minimatch", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:zod", "type": "static"}, {"source": "npm:@tiberriver256/mcp-server-azure-devops", "target": "npm:zod-to-json-schema", "type": "static"}], "npm:@modelcontextprotocol/sdk@1.11.4": [{"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:ajv", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:content-type", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:cors", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:cross-spawn", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:eventsource", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:express", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:express-rate-limit", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:pkce-challenge", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:raw-body", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:zod", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.4", "target": "npm:zod-to-json-schema", "type": "static"}], "npm:@tybys/wasm-util": [{"source": "npm:@tybys/wasm-util", "target": "npm:tslib", "type": "static"}], "npm:@types/node": [{"source": "npm:@types/node", "target": "npm:undici-types", "type": "static"}], "npm:@types/node-fetch": [{"source": "npm:@types/node-fetch", "target": "npm:@types/node", "type": "static"}, {"source": "npm:@types/node-fetch", "target": "npm:form-data", "type": "static"}], "npm:@types/yauzl": [{"source": "npm:@types/yauzl", "target": "npm:@types/node", "type": "static"}], "npm:@typespec/ts-http-runtime": [{"source": "npm:@typespec/ts-http-runtime", "target": "npm:http-proxy-agent", "type": "static"}, {"source": "npm:@typespec/ts-http-runtime", "target": "npm:https-proxy-agent", "type": "static"}, {"source": "npm:@typespec/ts-http-runtime", "target": "npm:tslib", "type": "static"}], "npm:@upstash/context7-mcp": [{"source": "npm:@upstash/context7-mcp", "target": "npm:@modelcontextprotocol/sdk@1.11.2", "type": "static"}, {"source": "npm:@upstash/context7-mcp", "target": "npm:dotenv", "type": "static"}, {"source": "npm:@upstash/context7-mcp", "target": "npm:zod", "type": "static"}], "npm:@modelcontextprotocol/sdk@1.11.2": [{"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:content-type", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:cors", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:cross-spawn", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:eventsource", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:express", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:express-rate-limit", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:pkce-challenge", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:raw-body", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:zod", "type": "static"}, {"source": "npm:@modelcontextprotocol/sdk@1.11.2", "target": "npm:zod-to-json-schema", "type": "static"}], "npm:@yarnpkg/parsers": [{"source": "npm:@yarnpkg/parsers", "target": "npm:js-yaml@3.14.1", "type": "static"}, {"source": "npm:@yarnpkg/parsers", "target": "npm:tslib", "type": "static"}], "npm:argparse@1.0.10": [{"source": "npm:argparse@1.0.10", "target": "npm:sprintf-js@1.0.3", "type": "static"}], "npm:js-yaml@3.14.1": [{"source": "npm:js-yaml@3.14.1", "target": "npm:argparse@1.0.10", "type": "static"}, {"source": "npm:js-yaml@3.14.1", "target": "npm:esprima", "type": "static"}], "npm:@zkochan/js-yaml": [{"source": "npm:@zkochan/js-yaml", "target": "npm:a<PERSON><PERSON><PERSON>", "type": "static"}], "npm:accepts": [{"source": "npm:accepts", "target": "npm:mime-types", "type": "static"}, {"source": "npm:accepts", "target": "npm:negotiator", "type": "static"}], "npm:ajv": [{"source": "npm:ajv", "target": "npm:fast-deep-equal", "type": "static"}, {"source": "npm:ajv", "target": "npm:fast-uri", "type": "static"}, {"source": "npm:ajv", "target": "npm:json-schema-traverse", "type": "static"}, {"source": "npm:ajv", "target": "npm:require-from-string", "type": "static"}], "npm:ansi-styles": [{"source": "npm:ansi-styles", "target": "npm:color-convert", "type": "static"}], "npm:ast-types": [{"source": "npm:ast-types", "target": "npm:tslib", "type": "static"}], "npm:axios": [{"source": "npm:axios", "target": "npm:follow-redirects", "type": "static"}, {"source": "npm:axios", "target": "npm:form-data", "type": "static"}, {"source": "npm:axios", "target": "npm:proxy-from-env", "type": "static"}], "npm:azure-devops-node-api": [{"source": "npm:azure-devops-node-api", "target": "npm:tunnel", "type": "static"}, {"source": "npm:azure-devops-node-api", "target": "npm:typed-rest-client", "type": "static"}], "npm:bare-fs": [{"source": "npm:bare-fs", "target": "npm:bare-events", "type": "static"}, {"source": "npm:bare-fs", "target": "npm:bare-path", "type": "static"}, {"source": "npm:bare-fs", "target": "npm:bare-stream", "type": "static"}], "npm:bare-path": [{"source": "npm:bare-path", "target": "npm:bare-os", "type": "static"}], "npm:bare-stream": [{"source": "npm:bare-stream", "target": "npm:bare-events", "type": "static"}, {"source": "npm:bare-stream", "target": "npm:streamx", "type": "static"}], "npm:bl": [{"source": "npm:bl", "target": "npm:buffer", "type": "static"}, {"source": "npm:bl", "target": "npm:inherits", "type": "static"}, {"source": "npm:bl", "target": "npm:readable-stream", "type": "static"}], "npm:body-parser": [{"source": "npm:body-parser", "target": "npm:bytes", "type": "static"}, {"source": "npm:body-parser", "target": "npm:content-type", "type": "static"}, {"source": "npm:body-parser", "target": "npm:debug", "type": "static"}, {"source": "npm:body-parser", "target": "npm:http-errors", "type": "static"}, {"source": "npm:body-parser", "target": "npm:iconv-lite", "type": "static"}, {"source": "npm:body-parser", "target": "npm:on-finished", "type": "static"}, {"source": "npm:body-parser", "target": "npm:qs", "type": "static"}, {"source": "npm:body-parser", "target": "npm:raw-body", "type": "static"}, {"source": "npm:body-parser", "target": "npm:type-is", "type": "static"}], "npm:brace-expansion": [{"source": "npm:brace-expansion", "target": "npm:balanced-match", "type": "static"}], "npm:buffer": [{"source": "npm:buffer", "target": "npm:base64-js", "type": "static"}, {"source": "npm:buffer", "target": "npm:ieee754", "type": "static"}], "npm:bundle-name": [{"source": "npm:bundle-name", "target": "npm:run-applescript", "type": "static"}], "npm:call-bind-apply-helpers": [{"source": "npm:call-bind-apply-helpers", "target": "npm:es-errors", "type": "static"}, {"source": "npm:call-bind-apply-helpers", "target": "npm:function-bind", "type": "static"}], "npm:call-bound": [{"source": "npm:call-bound", "target": "npm:call-bind-apply-helpers", "type": "static"}, {"source": "npm:call-bound", "target": "npm:get-intrinsic", "type": "static"}], "npm:chromium-bidi": [{"source": "npm:chromium-bidi", "target": "npm:devtools-protocol", "type": "static"}, {"source": "npm:chromium-bidi", "target": "npm:mitt", "type": "static"}, {"source": "npm:chromium-bidi", "target": "npm:zod@3.23.8", "type": "static"}], "npm:cli-cursor": [{"source": "npm:cli-cursor", "target": "npm:restore-cursor", "type": "static"}], "npm:cliui": [{"source": "npm:cliui", "target": "npm:string-width", "type": "static"}, {"source": "npm:cliui", "target": "npm:strip-ansi", "type": "static"}, {"source": "npm:cliui", "target": "npm:wrap-ansi", "type": "static"}], "npm:color-convert": [{"source": "npm:color-convert", "target": "npm:color-name", "type": "static"}], "npm:combined-stream": [{"source": "npm:combined-stream", "target": "npm:delayed-stream", "type": "static"}], "npm:content-disposition": [{"source": "npm:content-disposition", "target": "npm:safe-buffer", "type": "static"}], "npm:cors": [{"source": "npm:cors", "target": "npm:object-assign", "type": "static"}, {"source": "npm:cors", "target": "npm:vary", "type": "static"}], "npm:cosmiconfig": [{"source": "npm:cosmiconfig", "target": "npm:env-paths", "type": "static"}, {"source": "npm:cosmiconfig", "target": "npm:import-fresh", "type": "static"}, {"source": "npm:cosmiconfig", "target": "npm:js-yaml", "type": "static"}, {"source": "npm:cosmiconfig", "target": "npm:parse-json", "type": "static"}], "npm:cross-spawn": [{"source": "npm:cross-spawn", "target": "npm:path-key", "type": "static"}, {"source": "npm:cross-spawn", "target": "npm:shebang-command", "type": "static"}, {"source": "npm:cross-spawn", "target": "npm:which", "type": "static"}], "npm:debug": [{"source": "npm:debug", "target": "npm:ms", "type": "static"}], "npm:default-browser": [{"source": "npm:default-browser", "target": "npm:bundle-name", "type": "static"}, {"source": "npm:default-browser", "target": "npm:default-browser-id", "type": "static"}], "npm:defaults": [{"source": "npm:defaults", "target": "npm:clone", "type": "static"}], "npm:degenerator": [{"source": "npm:degenerator", "target": "npm:ast-types", "type": "static"}, {"source": "npm:degenerator", "target": "npm:escodegen", "type": "static"}, {"source": "npm:degenerator", "target": "npm:esprima", "type": "static"}], "npm:dotenv-expand": [{"source": "npm:dotenv-expand", "target": "npm:dotenv", "type": "static"}], "npm:dunder-proto": [{"source": "npm:dunder-proto", "target": "npm:call-bind-apply-helpers", "type": "static"}, {"source": "npm:dunder-proto", "target": "npm:es-errors", "type": "static"}, {"source": "npm:dunder-proto", "target": "npm:gopd", "type": "static"}], "npm:ecdsa-sig-formatter": [{"source": "npm:ecdsa-sig-formatter", "target": "npm:safe-buffer", "type": "static"}], "npm:end-of-stream": [{"source": "npm:end-of-stream", "target": "npm:once", "type": "static"}], "npm:enquirer": [{"source": "npm:enquirer", "target": "npm:ansi-colors", "type": "static"}], "npm:error-ex": [{"source": "npm:error-ex", "target": "npm:is-arrayish", "type": "static"}], "npm:es-object-atoms": [{"source": "npm:es-object-atoms", "target": "npm:es-errors", "type": "static"}], "npm:es-set-tostringtag": [{"source": "npm:es-set-tostringtag", "target": "npm:es-errors", "type": "static"}, {"source": "npm:es-set-tostringtag", "target": "npm:get-intrinsic", "type": "static"}, {"source": "npm:es-set-tostringtag", "target": "npm:has-tostringtag", "type": "static"}, {"source": "npm:es-set-tostringtag", "target": "npm:hasown", "type": "static"}], "npm:escodegen": [{"source": "npm:escodegen", "target": "npm:esprima", "type": "static"}, {"source": "npm:escodegen", "target": "npm:estraverse", "type": "static"}, {"source": "npm:escodegen", "target": "npm:esutils", "type": "static"}, {"source": "npm:escodegen", "target": "npm:source-map", "type": "static"}], "npm:eventsource": [{"source": "npm:eventsource", "target": "npm:eventsource-parser", "type": "static"}], "npm:express": [{"source": "npm:express", "target": "npm:accepts", "type": "static"}, {"source": "npm:express", "target": "npm:body-parser", "type": "static"}, {"source": "npm:express", "target": "npm:content-disposition", "type": "static"}, {"source": "npm:express", "target": "npm:content-type", "type": "static"}, {"source": "npm:express", "target": "npm:cookie", "type": "static"}, {"source": "npm:express", "target": "npm:cookie-signature", "type": "static"}, {"source": "npm:express", "target": "npm:debug", "type": "static"}, {"source": "npm:express", "target": "npm:encodeurl", "type": "static"}, {"source": "npm:express", "target": "npm:escape-html", "type": "static"}, {"source": "npm:express", "target": "npm:etag", "type": "static"}, {"source": "npm:express", "target": "npm:finalhandler", "type": "static"}, {"source": "npm:express", "target": "npm:fresh", "type": "static"}, {"source": "npm:express", "target": "npm:http-errors", "type": "static"}, {"source": "npm:express", "target": "npm:merge-descriptors", "type": "static"}, {"source": "npm:express", "target": "npm:mime-types", "type": "static"}, {"source": "npm:express", "target": "npm:on-finished", "type": "static"}, {"source": "npm:express", "target": "npm:once", "type": "static"}, {"source": "npm:express", "target": "npm:parseurl", "type": "static"}, {"source": "npm:express", "target": "npm:proxy-addr", "type": "static"}, {"source": "npm:express", "target": "npm:qs", "type": "static"}, {"source": "npm:express", "target": "npm:range-parser", "type": "static"}, {"source": "npm:express", "target": "npm:router", "type": "static"}, {"source": "npm:express", "target": "npm:send", "type": "static"}, {"source": "npm:express", "target": "npm:serve-static", "type": "static"}, {"source": "npm:express", "target": "npm:statuses", "type": "static"}, {"source": "npm:express", "target": "npm:type-is", "type": "static"}, {"source": "npm:express", "target": "npm:vary", "type": "static"}], "npm:express-rate-limit": [{"source": "npm:express-rate-limit", "target": "npm:express", "type": "static"}], "npm:extract-zip": [{"source": "npm:extract-zip", "target": "npm:debug", "type": "static"}, {"source": "npm:extract-zip", "target": "npm:get-stream", "type": "static"}, {"source": "npm:extract-zip", "target": "npm:yauzl", "type": "static"}, {"source": "npm:extract-zip", "target": "npm:@types/yauzl", "type": "static"}], "npm:fd-slicer": [{"source": "npm:fd-slicer", "target": "npm:pend", "type": "static"}], "npm:fetch-blob": [{"source": "npm:fetch-blob", "target": "npm:node-domexception", "type": "static"}, {"source": "npm:fetch-blob", "target": "npm:web-streams-polyfill", "type": "static"}], "npm:figures": [{"source": "npm:figures", "target": "npm:escape-string-regexp", "type": "static"}], "npm:finalhandler": [{"source": "npm:finalhandler", "target": "npm:debug", "type": "static"}, {"source": "npm:finalhandler", "target": "npm:encodeurl", "type": "static"}, {"source": "npm:finalhandler", "target": "npm:escape-html", "type": "static"}, {"source": "npm:finalhandler", "target": "npm:on-finished", "type": "static"}, {"source": "npm:finalhandler", "target": "npm:parseurl", "type": "static"}, {"source": "npm:finalhandler", "target": "npm:statuses", "type": "static"}], "npm:foreground-child": [{"source": "npm:foreground-child", "target": "npm:cross-spawn", "type": "static"}, {"source": "npm:foreground-child", "target": "npm:signal-exit", "type": "static"}], "npm:form-data": [{"source": "npm:form-data", "target": "npm:asynckit", "type": "static"}, {"source": "npm:form-data", "target": "npm:combined-stream", "type": "static"}, {"source": "npm:form-data", "target": "npm:es-set-tostringtag", "type": "static"}, {"source": "npm:form-data", "target": "npm:mime-types@2.1.35", "type": "static"}], "npm:mime-types@2.1.35": [{"source": "npm:mime-types@2.1.35", "target": "npm:mime-db@1.52.0", "type": "static"}], "npm:formdata-polyfill": [{"source": "npm:formdata-polyfill", "target": "npm:fetch-blob", "type": "static"}], "npm:front-matter": [{"source": "npm:front-matter", "target": "npm:js-yaml@3.14.1", "type": "static"}], "npm:get-intrinsic": [{"source": "npm:get-intrinsic", "target": "npm:call-bind-apply-helpers", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:es-define-property", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:es-errors", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:es-object-atoms", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:function-bind", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:get-proto", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:gopd", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:has-symbols", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:hasown", "type": "static"}, {"source": "npm:get-intrinsic", "target": "npm:math-intrinsics", "type": "static"}], "npm:get-proto": [{"source": "npm:get-proto", "target": "npm:dunder-proto", "type": "static"}, {"source": "npm:get-proto", "target": "npm:es-object-atoms", "type": "static"}], "npm:get-stream": [{"source": "npm:get-stream", "target": "npm:pump", "type": "static"}], "npm:get-uri": [{"source": "npm:get-uri", "target": "npm:basic-ftp", "type": "static"}, {"source": "npm:get-uri", "target": "npm:data-uri-to-buffer", "type": "static"}, {"source": "npm:get-uri", "target": "npm:debug", "type": "static"}], "npm:glob": [{"source": "npm:glob", "target": "npm:foreground-child", "type": "static"}, {"source": "npm:glob", "target": "npm:jackspeak", "type": "static"}, {"source": "npm:glob", "target": "npm:minimatch@9.0.5", "type": "static"}, {"source": "npm:glob", "target": "npm:minipass", "type": "static"}, {"source": "npm:glob", "target": "npm:package-json-from-dist", "type": "static"}, {"source": "npm:glob", "target": "npm:path-scurry", "type": "static"}], "npm:minimatch@9.0.5": [{"source": "npm:minimatch@9.0.5", "target": "npm:brace-expansion", "type": "static"}], "npm:has-tostringtag": [{"source": "npm:has-tostringtag", "target": "npm:has-symbols", "type": "static"}], "npm:hasown": [{"source": "npm:hasown", "target": "npm:function-bind", "type": "static"}], "npm:http-errors": [{"source": "npm:http-errors", "target": "npm:depd", "type": "static"}, {"source": "npm:http-errors", "target": "npm:inherits", "type": "static"}, {"source": "npm:http-errors", "target": "npm:set<PERSON><PERSON><PERSON><PERSON>", "type": "static"}, {"source": "npm:http-errors", "target": "npm:statuses", "type": "static"}, {"source": "npm:http-errors", "target": "npm:toidentifier", "type": "static"}], "npm:http-proxy-agent": [{"source": "npm:http-proxy-agent", "target": "npm:agent-base", "type": "static"}, {"source": "npm:http-proxy-agent", "target": "npm:debug", "type": "static"}], "npm:https-proxy-agent": [{"source": "npm:https-proxy-agent", "target": "npm:agent-base", "type": "static"}, {"source": "npm:https-proxy-agent", "target": "npm:debug", "type": "static"}], "npm:iconv-lite": [{"source": "npm:iconv-lite", "target": "npm:safer-buffer", "type": "static"}], "npm:import-fresh": [{"source": "npm:import-fresh", "target": "npm:parent-module", "type": "static"}, {"source": "npm:import-fresh", "target": "npm:resolve-from", "type": "static"}], "npm:ip-address": [{"source": "npm:ip-address", "target": "npm:jsbn", "type": "static"}, {"source": "npm:ip-address", "target": "npm:sprintf-js", "type": "static"}], "npm:is-inside-container": [{"source": "npm:is-inside-container", "target": "npm:is-docker", "type": "static"}], "npm:is-wsl": [{"source": "npm:is-wsl", "target": "npm:is-inside-container", "type": "static"}], "npm:jackspeak": [{"source": "npm:jackspeak", "target": "npm:@isaacs/cliui", "type": "static"}, {"source": "npm:jackspeak", "target": "npm:@pkgjs/parseargs", "type": "static"}], "npm:jest-diff": [{"source": "npm:jest-diff", "target": "npm:chalk@4.1.2", "type": "static"}, {"source": "npm:jest-diff", "target": "npm:diff-sequences", "type": "static"}, {"source": "npm:jest-diff", "target": "npm:jest-get-type", "type": "static"}, {"source": "npm:jest-diff", "target": "npm:pretty-format", "type": "static"}], "npm:chalk@4.1.2": [{"source": "npm:chalk@4.1.2", "target": "npm:ansi-styles", "type": "static"}, {"source": "npm:chalk@4.1.2", "target": "npm:supports-color", "type": "static"}], "npm:js-yaml": [{"source": "npm:js-yaml", "target": "npm:a<PERSON><PERSON><PERSON>", "type": "static"}], "npm:jsonwebtoken": [{"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:jws", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.includes", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.isboolean", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.isint<PERSON>r", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.isnumber", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.isplainobject", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.isstring", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:lodash.once", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:ms", "type": "static"}, {"source": "npm:j<PERSON><PERSON><PERSON><PERSON>", "target": "npm:semver", "type": "static"}], "npm:jwa": [{"source": "npm:jwa", "target": "npm:buffer-equal-constant-time", "type": "static"}, {"source": "npm:jwa", "target": "npm:ecdsa-sig-formatter", "type": "static"}, {"source": "npm:jwa", "target": "npm:safe-buffer", "type": "static"}], "npm:jws": [{"source": "npm:jws", "target": "npm:jwa", "type": "static"}, {"source": "npm:jws", "target": "npm:safe-buffer", "type": "static"}], "npm:log-symbols": [{"source": "npm:log-symbols", "target": "npm:chalk@4.1.2", "type": "static"}, {"source": "npm:log-symbols", "target": "npm:is-unicode-supported", "type": "static"}], "npm:mime-types": [{"source": "npm:mime-types", "target": "npm:mime-db", "type": "static"}], "npm:minimatch": [{"source": "npm:minimatch", "target": "npm:brace-expansion", "type": "static"}], "npm:node-fetch": [{"source": "npm:node-fetch", "target": "npm:data-uri-to-buffer@4.0.1", "type": "static"}, {"source": "npm:node-fetch", "target": "npm:fetch-blob", "type": "static"}, {"source": "npm:node-fetch", "target": "npm:formdata-polyfill", "type": "static"}], "npm:npm-run-path": [{"source": "npm:npm-run-path", "target": "npm:path-key", "type": "static"}], "npm:nx": [{"source": "npm:nx", "target": "npm:@napi-rs/wasm-runtime", "type": "static"}, {"source": "npm:nx", "target": "npm:@yarnpkg/lockfile", "type": "static"}, {"source": "npm:nx", "target": "npm:@yarnpkg/parsers", "type": "static"}, {"source": "npm:nx", "target": "npm:@zkochan/js-yaml", "type": "static"}, {"source": "npm:nx", "target": "npm:axios", "type": "static"}, {"source": "npm:nx", "target": "npm:chalk@4.1.2", "type": "static"}, {"source": "npm:nx", "target": "npm:cli-cursor", "type": "static"}, {"source": "npm:nx", "target": "npm:cli-spinners", "type": "static"}, {"source": "npm:nx", "target": "npm:cliui", "type": "static"}, {"source": "npm:nx", "target": "npm:dotenv@16.4.7", "type": "static"}, {"source": "npm:nx", "target": "npm:dotenv-expand", "type": "static"}, {"source": "npm:nx", "target": "npm:enquirer", "type": "static"}, {"source": "npm:nx", "target": "npm:figures", "type": "static"}, {"source": "npm:nx", "target": "npm:flat", "type": "static"}, {"source": "npm:nx", "target": "npm:front-matter", "type": "static"}, {"source": "npm:nx", "target": "npm:ignore", "type": "static"}, {"source": "npm:nx", "target": "npm:jest-diff", "type": "static"}, {"source": "npm:nx", "target": "npm:jsonc-parser", "type": "static"}, {"source": "npm:nx", "target": "npm:lines-and-columns@2.0.3", "type": "static"}, {"source": "npm:nx", "target": "npm:minimatch@9.0.3", "type": "static"}, {"source": "npm:nx", "target": "npm:node-machine-id", "type": "static"}, {"source": "npm:nx", "target": "npm:npm-run-path", "type": "static"}, {"source": "npm:nx", "target": "npm:open@8.4.2", "type": "static"}, {"source": "npm:nx", "target": "npm:ora", "type": "static"}, {"source": "npm:nx", "target": "npm:resolve.exports", "type": "static"}, {"source": "npm:nx", "target": "npm:semver", "type": "static"}, {"source": "npm:nx", "target": "npm:string-width", "type": "static"}, {"source": "npm:nx", "target": "npm:tar-stream@2.2.0", "type": "static"}, {"source": "npm:nx", "target": "npm:tmp", "type": "static"}, {"source": "npm:nx", "target": "npm:tree-kill", "type": "static"}, {"source": "npm:nx", "target": "npm:tsconfig-paths", "type": "static"}, {"source": "npm:nx", "target": "npm:tslib", "type": "static"}, {"source": "npm:nx", "target": "npm:yaml", "type": "static"}, {"source": "npm:nx", "target": "npm:yargs", "type": "static"}, {"source": "npm:nx", "target": "npm:yargs-parser", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-darwin-arm64", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-darwin-x64", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-freebsd-x64", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-linux-arm-gnueabihf", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-linux-arm64-gnu", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-linux-arm64-musl", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-linux-x64-gnu", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-linux-x64-musl", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-win32-arm64-msvc", "type": "static"}, {"source": "npm:nx", "target": "npm:@nx/nx-win32-x64-msvc", "type": "static"}], "npm:is-wsl@2.2.0": [{"source": "npm:is-wsl@2.2.0", "target": "npm:is-docker@2.2.1", "type": "static"}], "npm:minimatch@9.0.3": [{"source": "npm:minimatch@9.0.3", "target": "npm:brace-expansion", "type": "static"}], "npm:open@8.4.2": [{"source": "npm:open@8.4.2", "target": "npm:define-lazy-prop@2.0.0", "type": "static"}, {"source": "npm:open@8.4.2", "target": "npm:is-docker@2.2.1", "type": "static"}, {"source": "npm:open@8.4.2", "target": "npm:is-wsl@2.2.0", "type": "static"}], "npm:tar-stream@2.2.0": [{"source": "npm:tar-stream@2.2.0", "target": "npm:bl", "type": "static"}, {"source": "npm:tar-stream@2.2.0", "target": "npm:end-of-stream", "type": "static"}, {"source": "npm:tar-stream@2.2.0", "target": "npm:fs-constants", "type": "static"}, {"source": "npm:tar-stream@2.2.0", "target": "npm:inherits", "type": "static"}, {"source": "npm:tar-stream@2.2.0", "target": "npm:readable-stream", "type": "static"}], "npm:on-finished": [{"source": "npm:on-finished", "target": "npm:ee-first", "type": "static"}], "npm:once": [{"source": "npm:once", "target": "npm:wrappy", "type": "static"}], "npm:onetime": [{"source": "npm:onetime", "target": "npm:mimic-fn", "type": "static"}], "npm:open": [{"source": "npm:open", "target": "npm:default-browser", "type": "static"}, {"source": "npm:open", "target": "npm:define-lazy-prop", "type": "static"}, {"source": "npm:open", "target": "npm:is-inside-container", "type": "static"}, {"source": "npm:open", "target": "npm:is-wsl", "type": "static"}], "npm:ora": [{"source": "npm:ora", "target": "npm:bl", "type": "static"}, {"source": "npm:ora", "target": "npm:chalk@4.1.2", "type": "static"}, {"source": "npm:ora", "target": "npm:cli-cursor", "type": "static"}, {"source": "npm:ora", "target": "npm:cli-spinners", "type": "static"}, {"source": "npm:ora", "target": "npm:is-interactive", "type": "static"}, {"source": "npm:ora", "target": "npm:log-symbols", "type": "static"}, {"source": "npm:ora", "target": "npm:strip-ansi", "type": "static"}, {"source": "npm:ora", "target": "npm:wcwidth", "type": "static"}], "npm:pac-proxy-agent": [{"source": "npm:pac-proxy-agent", "target": "npm:@tootallnate/quickjs-emscripten", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:agent-base", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:debug", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:get-uri", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:http-proxy-agent", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:https-proxy-agent", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:pac-resolver", "type": "static"}, {"source": "npm:pac-proxy-agent", "target": "npm:socks-proxy-agent", "type": "static"}], "npm:pac-resolver": [{"source": "npm:pac-resolver", "target": "npm:degenerator", "type": "static"}, {"source": "npm:pac-resolver", "target": "npm:netmask", "type": "static"}], "npm:parent-module": [{"source": "npm:parent-module", "target": "npm:callsites", "type": "static"}], "npm:parse-json": [{"source": "npm:parse-json", "target": "npm:@babel/code-frame", "type": "static"}, {"source": "npm:parse-json", "target": "npm:error-ex", "type": "static"}, {"source": "npm:parse-json", "target": "npm:json-parse-even-better-errors", "type": "static"}, {"source": "npm:parse-json", "target": "npm:lines-and-columns", "type": "static"}], "npm:path-scurry": [{"source": "npm:path-scurry", "target": "npm:lru-cache@10.4.3", "type": "static"}, {"source": "npm:path-scurry", "target": "npm:minipass", "type": "static"}], "npm:pretty-format": [{"source": "npm:pretty-format", "target": "npm:@jest/schemas", "type": "static"}, {"source": "npm:pretty-format", "target": "npm:ansi-styles@5.2.0", "type": "static"}, {"source": "npm:pretty-format", "target": "npm:react-is", "type": "static"}], "npm:proxy-addr": [{"source": "npm:proxy-addr", "target": "npm:forwarded", "type": "static"}, {"source": "npm:proxy-addr", "target": "npm:ipaddr.js", "type": "static"}], "npm:proxy-agent": [{"source": "npm:proxy-agent", "target": "npm:agent-base", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:debug", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:http-proxy-agent", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:https-proxy-agent", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:lru-cache", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:pac-proxy-agent", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:proxy-from-env", "type": "static"}, {"source": "npm:proxy-agent", "target": "npm:socks-proxy-agent", "type": "static"}], "npm:pump": [{"source": "npm:pump", "target": "npm:end-of-stream", "type": "static"}, {"source": "npm:pump", "target": "npm:once", "type": "static"}], "npm:puppeteer": [{"source": "npm:puppeteer", "target": "npm:@puppeteer/browsers", "type": "static"}, {"source": "npm:puppeteer", "target": "npm:chromium-bidi", "type": "static"}, {"source": "npm:puppeteer", "target": "npm:cosmiconfig", "type": "static"}, {"source": "npm:puppeteer", "target": "npm:devtools-protocol", "type": "static"}, {"source": "npm:puppeteer", "target": "npm:puppeteer-core", "type": "static"}, {"source": "npm:puppeteer", "target": "npm:typed-query-selector", "type": "static"}], "npm:puppeteer-core": [{"source": "npm:puppeteer-core", "target": "npm:@puppeteer/browsers", "type": "static"}, {"source": "npm:puppeteer-core", "target": "npm:chromium-bidi", "type": "static"}, {"source": "npm:puppeteer-core", "target": "npm:debug", "type": "static"}, {"source": "npm:puppeteer-core", "target": "npm:devtools-protocol", "type": "static"}, {"source": "npm:puppeteer-core", "target": "npm:typed-query-selector", "type": "static"}, {"source": "npm:puppeteer-core", "target": "npm:ws", "type": "static"}], "npm:qs": [{"source": "npm:qs", "target": "npm:side-channel", "type": "static"}], "npm:raw-body": [{"source": "npm:raw-body", "target": "npm:bytes", "type": "static"}, {"source": "npm:raw-body", "target": "npm:http-errors", "type": "static"}, {"source": "npm:raw-body", "target": "npm:iconv-lite", "type": "static"}, {"source": "npm:raw-body", "target": "npm:unpipe", "type": "static"}], "npm:readable-stream": [{"source": "npm:readable-stream", "target": "npm:inherits", "type": "static"}, {"source": "npm:readable-stream", "target": "npm:string_decoder", "type": "static"}, {"source": "npm:readable-stream", "target": "npm:util-deprecate", "type": "static"}], "npm:restore-cursor": [{"source": "npm:restore-cursor", "target": "npm:onetime", "type": "static"}, {"source": "npm:restore-cursor", "target": "npm:signal-exit@3.0.7", "type": "static"}], "npm:router": [{"source": "npm:router", "target": "npm:debug", "type": "static"}, {"source": "npm:router", "target": "npm:depd", "type": "static"}, {"source": "npm:router", "target": "npm:is-promise", "type": "static"}, {"source": "npm:router", "target": "npm:parseurl", "type": "static"}, {"source": "npm:router", "target": "npm:path-to-regexp", "type": "static"}], "npm:send": [{"source": "npm:send", "target": "npm:debug", "type": "static"}, {"source": "npm:send", "target": "npm:encodeurl", "type": "static"}, {"source": "npm:send", "target": "npm:escape-html", "type": "static"}, {"source": "npm:send", "target": "npm:etag", "type": "static"}, {"source": "npm:send", "target": "npm:fresh", "type": "static"}, {"source": "npm:send", "target": "npm:http-errors", "type": "static"}, {"source": "npm:send", "target": "npm:mime-types", "type": "static"}, {"source": "npm:send", "target": "npm:ms", "type": "static"}, {"source": "npm:send", "target": "npm:on-finished", "type": "static"}, {"source": "npm:send", "target": "npm:range-parser", "type": "static"}, {"source": "npm:send", "target": "npm:statuses", "type": "static"}], "npm:serve-static": [{"source": "npm:serve-static", "target": "npm:encodeurl", "type": "static"}, {"source": "npm:serve-static", "target": "npm:escape-html", "type": "static"}, {"source": "npm:serve-static", "target": "npm:parseurl", "type": "static"}, {"source": "npm:serve-static", "target": "npm:send", "type": "static"}], "npm:shebang-command": [{"source": "npm:shebang-command", "target": "npm:shebang-regex", "type": "static"}], "npm:side-channel": [{"source": "npm:side-channel", "target": "npm:es-errors", "type": "static"}, {"source": "npm:side-channel", "target": "npm:object-inspect", "type": "static"}, {"source": "npm:side-channel", "target": "npm:side-channel-list", "type": "static"}, {"source": "npm:side-channel", "target": "npm:side-channel-map", "type": "static"}, {"source": "npm:side-channel", "target": "npm:side-channel-weakmap", "type": "static"}], "npm:side-channel-list": [{"source": "npm:side-channel-list", "target": "npm:es-errors", "type": "static"}, {"source": "npm:side-channel-list", "target": "npm:object-inspect", "type": "static"}], "npm:side-channel-map": [{"source": "npm:side-channel-map", "target": "npm:call-bound", "type": "static"}, {"source": "npm:side-channel-map", "target": "npm:es-errors", "type": "static"}, {"source": "npm:side-channel-map", "target": "npm:get-intrinsic", "type": "static"}, {"source": "npm:side-channel-map", "target": "npm:object-inspect", "type": "static"}], "npm:side-channel-weakmap": [{"source": "npm:side-channel-weakmap", "target": "npm:call-bound", "type": "static"}, {"source": "npm:side-channel-weakmap", "target": "npm:es-errors", "type": "static"}, {"source": "npm:side-channel-weakmap", "target": "npm:get-intrinsic", "type": "static"}, {"source": "npm:side-channel-weakmap", "target": "npm:object-inspect", "type": "static"}, {"source": "npm:side-channel-weakmap", "target": "npm:side-channel-map", "type": "static"}], "npm:socks": [{"source": "npm:socks", "target": "npm:ip-address", "type": "static"}, {"source": "npm:socks", "target": "npm:smart-buffer", "type": "static"}], "npm:socks-proxy-agent": [{"source": "npm:socks-proxy-agent", "target": "npm:agent-base", "type": "static"}, {"source": "npm:socks-proxy-agent", "target": "npm:debug", "type": "static"}, {"source": "npm:socks-proxy-agent", "target": "npm:socks", "type": "static"}], "npm:streamx": [{"source": "npm:streamx", "target": "npm:fast-fifo", "type": "static"}, {"source": "npm:streamx", "target": "npm:text-decoder", "type": "static"}, {"source": "npm:streamx", "target": "npm:bare-events", "type": "static"}], "npm:string_decoder": [{"source": "npm:string_decoder", "target": "npm:safe-buffer", "type": "static"}], "npm:string-width": [{"source": "npm:string-width", "target": "npm:emoji-regex", "type": "static"}, {"source": "npm:string-width", "target": "npm:is-fullwidth-code-point", "type": "static"}, {"source": "npm:string-width", "target": "npm:strip-ansi", "type": "static"}], "npm:string-width-cjs": [{"source": "npm:string-width-cjs", "target": "npm:emoji-regex", "type": "static"}, {"source": "npm:string-width-cjs", "target": "npm:is-fullwidth-code-point", "type": "static"}, {"source": "npm:string-width-cjs", "target": "npm:strip-ansi", "type": "static"}], "npm:strip-ansi": [{"source": "npm:strip-ansi", "target": "npm:ansi-regex", "type": "static"}], "npm:strip-ansi-cjs": [{"source": "npm:strip-ansi-cjs", "target": "npm:ansi-regex", "type": "static"}], "npm:supports-color": [{"source": "npm:supports-color", "target": "npm:has-flag", "type": "static"}], "npm:tar-fs": [{"source": "npm:tar-fs", "target": "npm:pump", "type": "static"}, {"source": "npm:tar-fs", "target": "npm:tar-stream", "type": "static"}, {"source": "npm:tar-fs", "target": "npm:bare-fs", "type": "static"}, {"source": "npm:tar-fs", "target": "npm:bare-path", "type": "static"}], "npm:tar-stream": [{"source": "npm:tar-stream", "target": "npm:b4a", "type": "static"}, {"source": "npm:tar-stream", "target": "npm:fast-fifo", "type": "static"}, {"source": "npm:tar-stream", "target": "npm:streamx", "type": "static"}], "npm:text-decoder": [{"source": "npm:text-decoder", "target": "npm:b4a", "type": "static"}], "npm:tsconfig-paths": [{"source": "npm:tsconfig-paths", "target": "npm:json5", "type": "static"}, {"source": "npm:tsconfig-paths", "target": "npm:minimist", "type": "static"}, {"source": "npm:tsconfig-paths", "target": "npm:strip-bom", "type": "static"}], "npm:type-is": [{"source": "npm:type-is", "target": "npm:content-type", "type": "static"}, {"source": "npm:type-is", "target": "npm:media-typer", "type": "static"}, {"source": "npm:type-is", "target": "npm:mime-types", "type": "static"}], "npm:typed-rest-client": [{"source": "npm:typed-rest-client", "target": "npm:qs", "type": "static"}, {"source": "npm:typed-rest-client", "target": "npm:tunnel", "type": "static"}, {"source": "npm:typed-rest-client", "target": "npm:underscore", "type": "static"}], "npm:unbzip2-stream": [{"source": "npm:unbzip2-stream", "target": "npm:buffer", "type": "static"}, {"source": "npm:unbzip2-stream", "target": "npm:through", "type": "static"}], "npm:wcwidth": [{"source": "npm:wcwidth", "target": "npm:defaults", "type": "static"}], "npm:which": [{"source": "npm:which", "target": "npm:isexe", "type": "static"}], "npm:wrap-ansi": [{"source": "npm:wrap-ansi", "target": "npm:ansi-styles", "type": "static"}, {"source": "npm:wrap-ansi", "target": "npm:string-width", "type": "static"}, {"source": "npm:wrap-ansi", "target": "npm:strip-ansi", "type": "static"}], "npm:wrap-ansi-cjs": [{"source": "npm:wrap-ansi-cjs", "target": "npm:ansi-styles", "type": "static"}, {"source": "npm:wrap-ansi-cjs", "target": "npm:string-width", "type": "static"}, {"source": "npm:wrap-ansi-cjs", "target": "npm:strip-ansi", "type": "static"}], "npm:yargs": [{"source": "npm:yargs", "target": "npm:cliui", "type": "static"}, {"source": "npm:yargs", "target": "npm:escalade", "type": "static"}, {"source": "npm:yargs", "target": "npm:get-caller-file", "type": "static"}, {"source": "npm:yargs", "target": "npm:require-directory", "type": "static"}, {"source": "npm:yargs", "target": "npm:string-width", "type": "static"}, {"source": "npm:yargs", "target": "npm:y18n", "type": "static"}, {"source": "npm:yargs", "target": "npm:yargs-parser", "type": "static"}], "npm:yauzl": [{"source": "npm:yauzl", "target": "npm:buffer-crc32", "type": "static"}, {"source": "npm:yauzl", "target": "npm:fd-slicer", "type": "static"}], "npm:zod-to-json-schema": [{"source": "npm:zod-to-json-schema", "target": "npm:zod", "type": "static"}]}, "version": "6.0", "errors": [], "computedAt": 1748442527521}