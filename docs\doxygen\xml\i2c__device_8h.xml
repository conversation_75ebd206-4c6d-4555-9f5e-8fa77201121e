<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="i2c__device_8h" kind="file" language="C++">
    <compoundname>i2c_device.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="kmdf__i2c_8h" local="yes">../bus/kmdf_i2c.h</includes>
    <incdepgraph>
      <node id="6">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/devices/i2c_device.h</label>
        <link refid="i2c__device_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <innerclass refid="struct__I2C__STATISTICS" prot="public">_I2C_STATISTICS</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="i2c__device_8h_1a64738b9f6c1ba2f1f919ef7a61ad1e35" prot="public" static="no">
        <name>I2C_ADDRESS_10BIT</name>
        <initializer>1</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="21" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="21" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1ab65b9830c26b77346e0f420c643d63b9" prot="public" static="no">
        <name>I2C_ADDRESS_7BIT</name>
        <initializer>0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="20" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="20" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1ad6922f686b3fc13f8365467975aba1d7" prot="public" static="no">
        <name>I2C_TRANSFER_READ</name>
        <initializer>0x00000001</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="18" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="18" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1a23164ebc8fc22800438176c588caa941" prot="public" static="no">
        <name>IOCTL_I2C_GET_STATISTICS</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="43" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="43" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1a69ebe19cf050058019d84f005052cc00" prot="public" static="no">
        <name>IOCTL_I2C_RESET</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="44" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="44" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1a9c3881592ae1c10fbd5ba1b9ae7e85ec" prot="public" static="no">
        <name>IOCTL_I2C_SET_BUS_SPEED</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="45" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="45" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1a15204e5c2582622fc3ef40f01fe93322" prot="public" static="no">
        <name>IOCTL_I2C_TRANSFER</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="41" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="41" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="i2c__device_8h_1a478905e3b3f7f700dd9e0975f42fe1a3" prot="public" static="no">
        <name>IOCTL_I2C_TRANSFER_SEQUENCE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="42" column="9" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="42" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="i2c__device_8h_1a7d8f35e89fbf4832545b3dfab3bd1ae1" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__STATISTICS" kindref="compound">_I2C_STATISTICS</ref></type>
        <definition>typedef struct _I2C_STATISTICS I2C_STATISTICS</definition>
        <argsstring></argsstring>
        <name>I2C_STATISTICS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="38" column="16"/>
      </memberdef>
      <memberdef kind="typedef" id="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__STATISTICS" kindref="compound">_I2C_STATISTICS</ref> *</type>
        <definition>typedef struct _I2C_STATISTICS * PI2C_STATISTICS</definition>
        <argsstring></argsstring>
        <name>PI2C_STATISTICS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="38" column="33"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="i2c__device_8h_1a40f62c35b73ac8d898f982eba3295ed5" prot="public" static="no" mutable="no">
        <type>PVOID</type>
        <definition>PVOID Data</definition>
        <argsstring></argsstring>
        <name>Data</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="26" column="11" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="26" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1a2c5c92bbcc636db6bd909df12a1802da" prot="public" static="no" mutable="no">
        <type>UCHAR</type>
        <definition>UCHAR DataAddress</definition>
        <argsstring></argsstring>
        <name>DataAddress</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="25" column="11" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="25" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1a306a7e4c1d020919c3002e3b8bd37c27" prot="public" static="no" mutable="no">
        <type>ULONG</type>
        <definition>ULONG DelayInMicroseconds</definition>
        <argsstring></argsstring>
        <name>DelayInMicroseconds</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="28" column="11" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="28" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1a862821561008426245a34e458d02a093" prot="public" static="no" mutable="no">
        <type>UCHAR</type>
        <definition>UCHAR DeviceAddress</definition>
        <argsstring></argsstring>
        <name>DeviceAddress</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="24" column="11" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="24" bodyend="-1"/>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="i2c__device_8c_1ae957556f2bac1175f6f4b37cd8f268f9">LogInfo</referencedby>
        <referencedby refid="i2c__device_8c_1a6e8f3cbefed6c462cd6392131f5f0a29">LogInfo</referencedby>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1a385668d045c2844f0f13d8613b3e0459" prot="public" static="no" mutable="no">
        <type>ULONG</type>
        <definition>ULONG Flags</definition>
        <argsstring></argsstring>
        <name>Flags</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="27" column="11" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="27" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1af8486d1fd9d3904ca8eb4408ec81d9c4" prot="public" static="no" mutable="no">
        <type></type>
        <definition>I2C_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>I2C_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="29" column="2" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="29" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8h_1a95996125e1f73ecc8e0ecf222dd14372" prot="public" static="no" mutable="no">
        <type>*</type>
        <definition>* PI2C_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>PI2C_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="29" column="22" bodyfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" bodystart="29" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="i2c__device_8h_1a5da67a960d3cf99caa6874438a84629b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID I2cDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>I2cDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceCleanup - 娓呯悊I2C璁惧璧勬簮</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="71" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="157" bodyend="186" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="71" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8h_1a709aca0009ccfb39adebbdd9ce97e252" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceGetStatistics</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics)</argsstring>
        <name>I2cDeviceGetStatistics</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ <ref refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kindref="member">PI2C_STATISTICS</ref></type>
          <declname>Statistics</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceGetStatistics - 鑾峰彇I2C璁惧缁熻淇℃伅</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Statistics</parametername>
</parameternamelist>
<parameterdescription>
<para>缁熻淇℃伅缁撴瀯</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="138" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="362" bodyend="372" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="138" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8h_1ab0c3b778b5a363d418c3d768cdb1e2d4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</argsstring>
        <name>I2cDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref></type>
          <declname>I2cConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceInitialize - 鍒濆鍖朓2C璁惧</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">I2cConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C閰嶇疆</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="60" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="39" bodyend="55" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="60" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <references refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" compoundref="gpio__core_8c" startline="169">interruptConfig</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8h_1a6576f1e3485d12c22c444244044c1d30" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceRead</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</argsstring>
        <name>I2cDeviceRead</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>DeviceAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_writes_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesRead</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceRead - 浠嶪2C璁惧璇诲彇鏁版嵁</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">DeviceAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C璁惧鍦板潃 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>瀵勫瓨鍣ㄥ湴鍧€ </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>鏁版嵁缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>缂撳啿鍖洪暱搴? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesRead</parametername>
</parameternamelist>
<parameterdescription>
<para>瀹為檯璇诲彇鐨勫瓧鑺傛暟</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="86" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="192" bodyend="208" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="86" declcolumn="1"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8h_1ad84f26684684313ff193803d1d9c7c32" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceTransfer</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount)</argsstring>
        <name>I2cDeviceTransfer</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_reads_(TransferCount) <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>Transfers</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TransferCount</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceTransfer - 鎵ц澶嶆潅I2C浼犺緭</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Transfers</parametername>
</parameternamelist>
<parameterdescription>
<para>浼犺緭鏁版嵁鍖呮暟缁? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TransferCount</parametername>
</parameternamelist>
<parameterdescription>
<para>浼犺緭鏁版嵁鍖呮暟閲? * </para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="123" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="314" bodyend="326" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="123" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8h_1a580f2434082501937a3d8bc4d5591866" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceWrite</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</argsstring>
        <name>I2cDeviceWrite</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>DeviceAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_reads_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesWritten</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceWrite - 鍚慖2C璁惧鍐欏叆鏁版嵁</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">DeviceAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C璁惧鍦板潃 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>瀵勫瓨鍣ㄥ湴鍧€ </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>鏁版嵁缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>缂撳啿鍖洪暱搴? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesWritten</parametername>
</parameternamelist>
<parameterdescription>
<para>瀹為檯鍐欏叆鐨勫瓧鑺傛暟</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h" line="106" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="253" bodyend="269" declfile="C:/KMDF Driver1/include/hal/devices/i2c_device.h" declline="106" declcolumn="1"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>i2c_device.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>I2C璁惧椹卞姩鎺ュ彛澶存枃浠?<sp/>*<sp/>鎻愪緵I2C璁惧鎿嶄綔鐨勭粺涓€鎺ュ彛</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>I2C_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>I2C_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__i2c_8h" kindref="compound">../bus/kmdf_i2c.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//<sp/>甯搁噺鍜岀粨鏋勫畾涔?//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C浼犺緭鏍囧織</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="i2c__device_8h_1ad6922f686b3fc13f8365467975aba1d7" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>I2C_TRANSFER_READ<sp/><sp/><sp/><sp/><sp/><sp/>0x00000001<sp/><sp/></highlight><highlight class="comment">//<sp/>璇讳紶杈?#define<sp/>I2C_TRANSFER_WRITE<sp/><sp/><sp/><sp/><sp/>0x00000002<sp/><sp/>//<sp/>鍐欎紶杈?#define<sp/>I2C_TRANSFER_FAST_MODE<sp/>0x00000004<sp/><sp/>//<sp/>蹇€熸ā寮?#define<sp/>I2C_TRANSFER_NO_DELAY<sp/><sp/>0x00000008<sp/><sp/>//<sp/>鏃犲欢杩?#define<sp/>I2C_TRANSFER_NO_STOP<sp/><sp/><sp/>0x00000010<sp/><sp/>//<sp/>鏃犲仠姝㈡潯浠?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C鍦板潃闀垮害</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="i2c__device_8h_1ab65b9830c26b77346e0f420c643d63b9" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>I2C_ADDRESS_7BIT<sp/><sp/><sp/><sp/><sp/><sp/><sp/>0<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>7浣嶅湴鍧€</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="i2c__device_8h_1a64738b9f6c1ba2f1f919ef7a61ad1e35" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>I2C_ADDRESS_10BIT<sp/><sp/><sp/><sp/><sp/><sp/>1<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>10浣嶅湴鍧€</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C浼犺緭鏁版嵁鍖呯粨鏋?typedef<sp/>struct<sp/>_I2C_TRANSFER_PACKET<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="i2c__device_8h_1a862821561008426245a34e458d02a093" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>璁惧鍦板潃</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="i2c__device_8h_1a2c5c92bbcc636db6bd909df12a1802da" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/><ref refid="i2c__device_8c_1a63268b1e9e5ee12309a44d8d6c9fc652" kindref="member">DataAddress</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>瀵勫瓨鍣?鏁版嵁鍦板潃</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="i2c__device_8h_1a40f62c35b73ac8d898f982eba3295ed5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="i2c__device_8c_1a7cd334185ddc76afeacf2cd57615dc81" kindref="member">Data</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鏁版嵁缂撳啿鍖?<sp/><sp/><sp/><sp/>ULONG<sp/>DataLength;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>鏁版嵁闀垮害</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="i2c__device_8h_1a385668d045c2844f0f13d8613b3e0459" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="i2c__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" kindref="member">Flags</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>浼犺緭鏍囧織</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="i2c__device_8h_1a306a7e4c1d020919c3002e3b8bd37c27" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="i2c__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" kindref="member">DelayInMicroseconds</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>浼犺緭鍚庣殑寤惰繜锛堝井绉掞級</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="i2c__device_8h_1af8486d1fd9d3904ca8eb4408ec81d9c4" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref>,<sp/>*<ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref>;</highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C缁熻淇℃伅缁撴瀯</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="struct__I2C__STATISTICS" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__I2C__STATISTICS" kindref="compound">_I2C_STATISTICS</ref><sp/>{</highlight></codeline>
<codeline lineno="33" refid="struct__I2C__STATISTICS_1add2f1e1c89d79eee2641ef44c12f29d3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__STATISTICS_1add2f1e1c89d79eee2641ef44c12f29d3" kindref="member">TransactionCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>浼犺緭鎬绘暟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="struct__I2C__STATISTICS_1a717ee36112176580f0ec5152cd12e0a2" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__STATISTICS_1a717ee36112176580f0ec5152cd12e0a2" kindref="member">ErrorCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閿欒鎬绘暟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="struct__I2C__STATISTICS_1a1ac16411f8ad21fe7d60868251438978" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__I2C__STATISTICS_1a1ac16411f8ad21fe7d60868251438978" kindref="member">DeviceInitialized</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>璁惧鏄惁宸插垵濮嬪寲</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__I2C__STATISTICS_1a0d581d279416ac434f26896560418188" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__STATISTICS_1a0d581d279416ac434f26896560418188" kindref="member">BusNumber</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鎬荤嚎缂栧彿</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__I2C__STATISTICS_1ac6637ce7aa131fb27cf2b60ce31f35db" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__STATISTICS_1ac6637ce7aa131fb27cf2b60ce31f35db" kindref="member">BusClockFrequency</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鎬荤嚎鏃堕挓棰戠巼</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="i2c__device_8h_1a7d8f35e89fbf4832545b3dfab3bd1ae1" refkind="member"><highlight class="normal">}<sp/><ref refid="i2c__device_8h_1a7d8f35e89fbf4832545b3dfab3bd1ae1" kindref="member">I2C_STATISTICS</ref>,<sp/>*<ref refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kindref="member">PI2C_STATISTICS</ref>;</highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C<sp/>IOCTL<sp/>鎺у埗鐮?#define<sp/>IOCTL_I2C_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0x8100</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="i2c__device_8h_1a15204e5c2582622fc3ef40f01fe93322" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_I2C_TRANSFER<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_I2C_BASE<sp/>+<sp/>0,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="i2c__device_8h_1a478905e3b3f7f700dd9e0975f42fe1a3" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_I2C_TRANSFER_SEQUENCE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_I2C_BASE<sp/>+<sp/>1,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="i2c__device_8h_1a23164ebc8fc22800438176c588caa941" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_I2C_GET_STATISTICS<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_I2C_BASE<sp/>+<sp/>2,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="i2c__device_8h_1a69ebe19cf050058019d84f005052cc00" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_I2C_RESET<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_I2C_BASE<sp/>+<sp/>3,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="i2c__device_8h_1a9c3881592ae1c10fbd5ba1b9ae7e85ec" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_I2C_SET_BUS_SPEED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>IOCTL_I2C_BASE<sp/>+<sp/>4,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C璁惧鎺ュ彛鍑芥暟澹版槑</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"></highlight><highlight class="comment">//===============================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50"><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><ref refid="i2c__device_8h_1ab0c3b778b5a363d418c3d768cdb1e2d4" kindref="member">I2cDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref><sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></highlight></codeline>
<codeline lineno="63"><highlight class="normal">);</highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight></codeline>
<codeline lineno="70"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><ref refid="i2c__device_8h_1a5da67a960d3cf99caa6874438a84629b" kindref="member">I2cDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="73"><highlight class="normal">);</highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight></codeline>
<codeline lineno="85"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><ref refid="i2c__device_8h_1a6576f1e3485d12c22c444244044c1d30" kindref="member">I2cDeviceRead</ref>(</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesRead</highlight></codeline>
<codeline lineno="93"><highlight class="normal">);</highlight></codeline>
<codeline lineno="94"><highlight class="normal"></highlight></codeline>
<codeline lineno="105"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><ref refid="i2c__device_8h_1a580f2434082501937a3d8bc4d5591866" kindref="member">I2cDeviceWrite</ref>(</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesWritten</highlight></codeline>
<codeline lineno="113"><highlight class="normal">);</highlight></codeline>
<codeline lineno="114"><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><ref refid="i2c__device_8h_1ad84f26684684313ff193803d1d9c7c32" kindref="member">I2cDeviceTransfer</ref>(</highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_(TransferCount)<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>Transfers,</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TransferCount</highlight></codeline>
<codeline lineno="127"><highlight class="normal">);</highlight></codeline>
<codeline lineno="128"><highlight class="normal"></highlight></codeline>
<codeline lineno="137"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><ref refid="i2c__device_8h_1a709aca0009ccfb39adebbdd9ce97e252" kindref="member">I2cDeviceGetStatistics</ref>(</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kindref="member">PI2C_STATISTICS</ref><sp/>Statistics</highlight></codeline>
<codeline lineno="141"><highlight class="normal">);</highlight></codeline>
<codeline lineno="142"><highlight class="normal"></highlight></codeline>
<codeline lineno="143"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>I2C_DEVICE_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/devices/i2c_device.h"/>
  </compounddef>
</doxygen>
