{"name": "Mistral Large", "provider": "mistral", "api_key": "tsJOAMaKB2dZN8jitd0QM7n2BCg12kdM", "base_url": "https://api.mistral.ai/v1/chat/completions", "api_base": "https://api.mistral.ai/v1/chat/completions", "model": "mistral-large-latest", "alternate_models": ["mistral-medium-latest", "open-mistral-7b"], "max_tokens": 8192, "temperature": 0.4, "top_p": 0.9, "timeout": 120, "retry_count": 3, "retry_delay": 5, "randomness": "low", "safe_mode": false, "system_prompt": "你是Mistral Large模型，一个精通系统编程和驱动开发的AI助手。请利用你的专业知识，对Windows驱动程序代码进行全面分析。\n\n请关注下列特定方面：\n1. KMDF/WDM驱动框架的使用方式\n2. 事件处理和同步机制\n3. 设备堆栈和电源管理\n4. 文件操作和缓冲区策略\n5. PnP和电源管理是否正确\n\n请提供全面、深入的中文代码注释和解释，遵循Windows驱动开发的最佳实践和设计模式。请指出代码中的潜在风险和优化机会，并分析可能的改进空间。", "security_focus": true}