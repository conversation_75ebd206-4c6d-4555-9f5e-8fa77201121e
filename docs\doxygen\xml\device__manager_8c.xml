<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="device__manager_8c" kind="file" language="C++">
    <compoundname>device_manager.c</compoundname>
    <includes refid="precomp_8h" local="yes">../../precomp.h</includes>
    <includes refid="device__manager_8h" local="yes">../../../include/core/device/device_manager.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="ioctl_8h" local="yes">../../../include/common/ioctl.h</includes>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes local="no">usbspec.h</includes>
    <includes local="no">hidpddi.h</includes>
    <includes local="no">hidpi.h</includes>
    <includes local="no">hidclass.h</includes>
    <includes local="no">wdfusb.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="20" relation="include">
        </childnode>
        <childnode refid="21" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="21">
        <label>hidclass.h</label>
      </node>
      <node id="19">
        <label>hidpddi.h</label>
      </node>
      <node id="20">
        <label>hidpi.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="device__manager_8c_1af11aade3f3741fb554915d10d3f514eb" prot="public" static="no">
        <name>INITGUID</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="1" column="9" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="1" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="device__manager_8c_1a7346c529a00f42617b55dfed2c4a8c6b" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_DISABLE</type>
        <definition>EVT_WDF_INTERRUPT_DISABLE DeviceInterruptDisable</definition>
        <argsstring></argsstring>
        <name>DeviceInterruptDisable</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="55" column="27" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="55" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1a27966856080fa6cd1a9b7692140774b6" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_DPC</type>
        <definition>EVT_WDF_INTERRUPT_DPC DeviceInterruptDpc</definition>
        <argsstring></argsstring>
        <name>DeviceInterruptDpc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="53" column="23" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="53" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1a7c6d2ffd6c40693fcc51198997b8220c" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_ENABLE</type>
        <definition>EVT_WDF_INTERRUPT_ENABLE DeviceInterruptEnable</definition>
        <argsstring></argsstring>
        <name>DeviceInterruptEnable</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="54" column="26" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="54" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1a66bb4353dba21c361d66c286dc941155" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_ISR</type>
        <definition>EVT_WDF_INTERRUPT_ISR DeviceInterruptIsr</definition>
        <argsstring></argsstring>
        <name>DeviceInterruptIsr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="52" column="23" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="52" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1aa3daed4e70ed14cc04cbd390a34a3e90" prot="public" static="no" mutable="no">
        <type>EVT_WDF_IO_QUEUE_IO_DEVICE_CONTROL</type>
        <definition>EVT_WDF_IO_QUEUE_IO_DEVICE_CONTROL DeviceIoControl</definition>
        <argsstring></argsstring>
        <name>DeviceIoControl</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="58" column="36" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="58" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1adbd8ce862fd891104b08816d03110316" prot="public" static="no" mutable="no">
        <type>EVT_WDF_IO_QUEUE_IO_READ</type>
        <definition>EVT_WDF_IO_QUEUE_IO_READ DeviceIoRead</definition>
        <argsstring></argsstring>
        <name>DeviceIoRead</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="59" column="26" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="59" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1aafc7ca77e7f4cc11bf35ad04cff9a631" prot="public" static="no" mutable="no">
        <type>EVT_WDF_IO_QUEUE_IO_WRITE</type>
        <definition>EVT_WDF_IO_QUEUE_IO_WRITE DeviceIoWrite</definition>
        <argsstring></argsstring>
        <name>DeviceIoWrite</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="60" column="27" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="60" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="device__manager_8c_1ab5c458c6825a16167b4f7baef381f352" prot="public" static="no" mutable="no">
        <type>EVT_WDF_TIMER</type>
        <definition>EVT_WDF_TIMER DeviceTimerFunc</definition>
        <argsstring></argsstring>
        <name>DeviceTimerFunc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="63" column="15" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="63" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceConfigureIoQueue</definition>
        <argsstring>(WDFDEVICE Device)</argsstring>
        <name>DeviceConfigureIoQueue</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="316" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="316" bodyend="354"/>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceCreate</definition>
        <argsstring>(WDFDRIVER Driver, PDEVICE_INIT_CONFIG InitConfig, WDFDEVICE *Device)</argsstring>
        <name>DeviceCreate</name>
        <param>
          <type><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>Driver</declname>
        </param>
        <param>
          <type><ref refid="device__manager_8h_1a57aff078ae347e96d32d9f9f76b00fb2" kindref="member">PDEVICE_INIT_CONFIG</ref></type>
          <declname>InitConfig</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref> *</type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="1007" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="1007" bodyend="1100"/>
        <references refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" compoundref="device__manager_8c" startline="316" endline="354">DeviceConfigureIoQueue</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="device__manager_8h_1a51a0fa7bdbb22680e79efaa2cacbb729" compoundref="device__manager_8h" startline="127">DeviceD0Entry</references>
        <references refid="device__manager_8h_1a1625e40d469a21ece1cad244736fbe81" compoundref="device__manager_8h" startline="128">DeviceD0Exit</references>
        <references refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" compoundref="device__manager_8h" startline="92">_DEVICE_INIT_CONFIG::DeviceInterfaceGuid</references>
        <references refid="device__manager_8h_1a4fda7ae13ae040932bf046e74f5bb8a6" compoundref="device__manager_8h" startline="125">DevicePrepareHardware</references>
        <references refid="device__manager_8h_1a20074db76287e7b7a18bc2f2c83c2c61" compoundref="device__manager_8h" startline="126">DeviceReleaseHardware</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1aca71f0560489dd375ac4baa0e0b35dd7" compoundref="device__manager_8h" startline="93">_DEVICE_INIT_CONFIG::SymbolicLinkName</references>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceD0Entry</definition>
        <argsstring>(WDFDEVICE Device, WDF_POWER_DEVICE_STATE PreviousState)</argsstring>
        <name>DeviceD0Entry</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDF_POWER_DEVICE_STATE</type>
          <declname>PreviousState</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="957" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="957" bodyend="976"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceD0Exit</definition>
        <argsstring>(WDFDEVICE Device, WDF_POWER_DEVICE_STATE TargetState)</argsstring>
        <name>DeviceD0Exit</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDF_POWER_DEVICE_STATE</type>
          <declname>TargetState</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="982" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="982" bodyend="1001"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceInitContext</definition>
        <argsstring>(WDFDEVICE Device, PDEVICE_INIT_CONFIG InitConfig)</argsstring>
        <name>DeviceInitContext</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type><ref refid="device__manager_8h_1a57aff078ae347e96d32d9f9f76b00fb2" kindref="member">PDEVICE_INIT_CONFIG</ref></type>
          <declname>InitConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="278" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="278" bodyend="310"/>
        <references refid="struct__DEVICE__INIT__CONFIG_1a55239223743c68f1900157add76e756b" compoundref="device__manager_8h" startline="91">_DEVICE_INIT_CONFIG::BusType</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a7ebe4b67b3f9303e10ddace98e7f69ff" compoundref="device__manager_8h" startline="89">_DEVICE_INIT_CONFIG::DeviceId</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" compoundref="device__manager_8h" startline="92">_DEVICE_INIT_CONFIG::DeviceInterfaceGuid</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__DEVICE__INIT__CONFIG_1accbb4ad53561dc015a600f3684e552e6" compoundref="device__manager_8h" startline="88">_DEVICE_INIT_CONFIG::VendorId</references>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceInterruptDisable</definition>
        <argsstring>(WDFINTERRUPT Interrupt, WDFDEVICE AssociatedDevice)</argsstring>
        <name>DeviceInterruptDisable</name>
        <param>
          <type>WDFINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>AssociatedDevice</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="258" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="258" bodyend="272"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a7a7512003b2efe8ec1d3412af1b7c0b3" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID DeviceInterruptDpc</definition>
        <argsstring>(WDFINTERRUPT Interrupt, WDFOBJECT AssociatedObject)</argsstring>
        <name>DeviceInterruptDpc</name>
        <param>
          <type>WDFINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a3921100f8f21c9e6503c02e51eb84644" kindref="member">WDFOBJECT</ref></type>
          <declname>AssociatedObject</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="215" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="215" bodyend="231"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceInterruptEnable</definition>
        <argsstring>(WDFINTERRUPT Interrupt, WDFDEVICE AssociatedDevice)</argsstring>
        <name>DeviceInterruptEnable</name>
        <param>
          <type>WDFINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>AssociatedDevice</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="237" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="237" bodyend="252"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a27d4fa34ed290d1ce4e0cba9b619de2b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>BOOLEAN</type>
        <definition>BOOLEAN DeviceInterruptIsr</definition>
        <argsstring>(WDFINTERRUPT Interrupt, ULONG MessageID)</argsstring>
        <name>DeviceInterruptIsr</name>
        <param>
          <type>WDFINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type>ULONG</type>
          <declname>MessageID</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="186" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="186" bodyend="209"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID DeviceIoControl</definition>
        <argsstring>(WDFQUEUE Queue, WDFREQUEST Request, size_t OutputBufferLength, size_t InputBufferLength, ULONG IoControlCode)</argsstring>
        <name>DeviceIoControl</name>
        <param>
          <type><ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref></type>
          <declname>Queue</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref></type>
          <declname>Request</declname>
        </param>
        <param>
          <type>size_t</type>
          <declname>OutputBufferLength</declname>
        </param>
        <param>
          <type>size_t</type>
          <declname>InputBufferLength</declname>
        </param>
        <param>
          <type>ULONG</type>
          <declname>IoControlCode</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="369" column="6" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="369" bodyend="465"/>
        <references refid="struct__DEVICE__INFO_1aecb40da598eb07be27b2fa762a56cb7c" compoundref="device__manager_8h" startline="103">_DEVICE_INFO::BusType</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="struct__DEVICE__INFO_1ad1b5079ed73fc6009607d35543e275ff" compoundref="device__manager_8h" startline="102">_DEVICE_INFO::DeviceId</references>
        <references refid="struct__DEVICE__INFO_1a7a411dffe28d739e918801b1a3a60517" compoundref="device__manager_8h" startline="105">_DEVICE_INFO::FirmwareVersion</references>
        <references refid="device__manager_8h_1a5d922ac9f0d09258cd06cb7d8d7160af" compoundref="device__manager_8h" startline="111">IOCTL_GET_DEVICE_INFO</references>
        <references refid="ioctl_8h_1ad40a8f5f93a2d0fdabdc3b13510850b6" compoundref="ioctl_8h" startline="19">IOCTL_TOUCH_GET_DATA</references>
        <references refid="struct__TOUCH__POINT_1a80ff6717c5b2724381d8aa3d6be00695" compoundref="device__manager_8h" startline="23">_TOUCH_POINT::IsValid</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__DEVICE__INFO_1affc12ad434fe559e681e77ee32b1e3da" compoundref="device__manager_8h" startline="104">_DEVICE_INFO::PowerState</references>
        <references refid="struct__TOUCH__POINT_1a2e7c2f66ac7d84eae585c1bc409a79b8" compoundref="device__manager_8h" startline="27">_TOUCH_POINT::Pressure</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__DEVICE__INFO_1a7f74be1556d3a954c0540a166ca6221f" compoundref="device__manager_8h" startline="101">_DEVICE_INFO::VendorId</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
        <references refid="struct__TOUCH__POINT_1aacdb49dfac683510c0c3eeae45f5f8f8" compoundref="device__manager_8h" startline="25">_TOUCH_POINT::X</references>
        <references refid="struct__TOUCH__POINT_1a82bf07c3929eea9e0235dfcd631669d8" compoundref="device__manager_8h" startline="26">_TOUCH_POINT::Y</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1af9753d89f71d81c5fc2038491eb88932" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID DeviceIoRead</definition>
        <argsstring>(WDFQUEUE Queue, WDFREQUEST Request, size_t Length)</argsstring>
        <name>DeviceIoRead</name>
        <param>
          <type><ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref></type>
          <declname>Queue</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref></type>
          <declname>Request</declname>
        </param>
        <param>
          <type>size_t</type>
          <declname>Length</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="357" column="6" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="357" bodyend="361"/>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a47b96d5bfdb1f42b07cc85978325d77a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID DeviceIoWrite</definition>
        <argsstring>(WDFQUEUE Queue, WDFREQUEST Request, size_t Length)</argsstring>
        <name>DeviceIoWrite</name>
        <param>
          <type><ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref></type>
          <declname>Queue</declname>
        </param>
        <param>
          <type><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref></type>
          <declname>Request</declname>
        </param>
        <param>
          <type>size_t</type>
          <declname>Length</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="363" column="6" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="363" bodyend="367"/>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceManager_EvtDeviceAdd</definition>
        <argsstring>(WDFDRIVER Driver, PWDFDEVICE_INIT DeviceInit)</argsstring>
        <name>DeviceManager_EvtDeviceAdd</name>
        <param>
          <type><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>Driver</declname>
        </param>
        <param>
          <type>PWDFDEVICE_INIT</type>
          <declname>DeviceInit</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="1113" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="1113" bodyend="1145"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1ae47038053b18948b8fc9579f5a785cf4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>void</type>
        <definition>void DeviceManager_EvtDeviceContextCleanup</definition>
        <argsstring>(WDFDEVICE Device)</argsstring>
        <name>DeviceManager_EvtDeviceContextCleanup</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="1102" column="6" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="1102" bodyend="1110"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1afa743e52d2410ed36cf2f06a30c07fcd" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceMapResources</definition>
        <argsstring>(WDFDEVICE Device, WDFCMRESLIST Resources)</argsstring>
        <name>DeviceMapResources</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDFCMRESLIST</type>
          <declname>Resources</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="146" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="146" bodyend="154"/>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DevicePrepareHardware</definition>
        <argsstring>(WDFDEVICE Device, WDFCMRESLIST ResourcesRaw, WDFCMRESLIST ResourcesTranslated)</argsstring>
        <name>DevicePrepareHardware</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDFCMRESLIST</type>
          <declname>ResourcesRaw</declname>
        </param>
        <param>
          <type>WDFCMRESLIST</type>
          <declname>ResourcesTranslated</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="519" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="519" bodyend="870"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="device__manager_8h_1a07afcd8a4d15d856ac651241642ebcd8" compoundref="device__manager_8h" startline="131">EvtUsbInterruptPipeReadComplete</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="core__types_8h_1aa5171e0d8c548a75e9b42333beb6390e" compoundref="core__types_8h" startline="27">WDF_NO_HANDLE</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceReleaseHardware</definition>
        <argsstring>(WDFDEVICE Device, WDFCMRESLIST ResourcesTranslated)</argsstring>
        <name>DeviceReleaseHardware</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDFCMRESLIST</type>
          <declname>ResourcesTranslated</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="876" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="876" bodyend="951"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceResetHardware</definition>
        <argsstring>(WDFDEVICE Device)</argsstring>
        <name>DeviceResetHardware</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="471" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="471" bodyend="513"/>
        <references refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" compoundref="kmdf__bus__common_8h" startline="17">BusTypeI2C</references>
        <references refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" compoundref="kmdf__bus__common_8h" startline="18">BusTypeSPI</references>
        <references refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" compoundref="kmdf__bus__common_8h" startline="19">BusTypeUSB</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1ac506ca5136446bf4cc6f68c0747f56e0" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceSetupInterrupt</definition>
        <argsstring>(WDFDEVICE Device, WDFCMRESLIST Resources)</argsstring>
        <name>DeviceSetupInterrupt</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>WDFCMRESLIST</type>
          <declname>Resources</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="172" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="172" bodyend="180"/>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a55323ce5aacec6667669f95f8abf7e22" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DeviceUnmapResources</definition>
        <argsstring>(WDFDEVICE Device)</argsstring>
        <name>DeviceUnmapResources</name>
        <param>
          <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="160" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="160" bodyend="166"/>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID EvtUsbInterruptPipeReadComplete</definition>
        <argsstring>(WDFUSBPIPE Pipe, WDFMEMORY Buffer, size_t NumBytesTransferred, WDFCONTEXT Context)</argsstring>
        <name>EvtUsbInterruptPipeReadComplete</name>
        <param>
          <type>WDFUSBPIPE</type>
          <declname>Pipe</declname>
        </param>
        <param>
          <type>WDFMEMORY</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>size_t</type>
          <declname>NumBytesTransferred</declname>
        </param>
        <param>
          <type>WDFCONTEXT</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/device/device_manager.c" line="69" column="1" bodyfile="C:/KMDF Driver1/src/core/device/device_manager.c" bodystart="69" bodyend="140"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1acfe39a25e08737b535dc881071ebf149" compoundref="src_2core_2log_2driver__log_8h" startline="134">LOG_DEBUG</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1" refid="device__manager_8c_1af11aade3f3741fb554915d10d3f514eb" refkind="member"><highlight class="preprocessor">#define<sp/>INITGUID</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">../../precomp.h</ref>&quot;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>precomp.h<sp/>已包含<sp/>ntddk.h<sp/>和<sp/>wdf.h</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>2025-05-12<sp/>23:36:44</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*<sp/>目的：设备管理模块实现，提供统一的设备创建、初始化和管理功能</highlight></codeline>
<codeline lineno="7"><highlight class="comment"><sp/>*<sp/>思路：</highlight></codeline>
<codeline lineno="8"><highlight class="comment"><sp/>*<sp/>1.<sp/>通过KMDF框架实现设备资源管理，包括内存映射、中断处理等</highlight></codeline>
<codeline lineno="9"><highlight class="comment"><sp/>*<sp/>2.<sp/>实现统一的设备创建、配置和初始化流程，简化驱动开发</highlight></codeline>
<codeline lineno="10"><highlight class="comment"><sp/>*<sp/>3.<sp/>提供标准化的IO请求处理机制，支持设备控制、读写操作</highlight></codeline>
<codeline lineno="11"><highlight class="comment"><sp/>*<sp/>4.<sp/>集成电源管理和PnP支持，确保设备正常工作和资源释放</highlight></codeline>
<codeline lineno="12"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="13"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+---------------------+</highlight></codeline>
<codeline lineno="14"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/>设备管理器模块<sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="15"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+----------+----------+</highlight></codeline>
<codeline lineno="16"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="17"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+----------v----------+</highlight></codeline>
<codeline lineno="18"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/>资源映射与中断处理<sp/><sp/>|</highlight></codeline>
<codeline lineno="19"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+----------+----------+</highlight></codeline>
<codeline lineno="20"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="21"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+----------v----------+</highlight></codeline>
<codeline lineno="22"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/>IO请求处理与转发<sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="23"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+---------------------+</highlight></codeline>
<codeline lineno="24"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight><highlight class="comment">//<sp/>#include<sp/>&lt;ntddk.h&gt;<sp/>//<sp/>已由<sp/>precomp.h<sp/>包含</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>#include<sp/>&lt;wdf.h&gt;<sp/><sp/><sp/>//<sp/>已由<sp/>precomp.h<sp/>包含</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="device__manager_8h" kindref="compound">../../../include/core/device/device_manager.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="ioctl_8h" kindref="compound">../../../include/common/ioctl.h</ref>&quot;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>Our<sp/>IOCTL<sp/>definitions</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Include<sp/>essential<sp/>Windows<sp/>Driver<sp/>Kit<sp/>headers<sp/>and<sp/>system<sp/>headers</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>Included<sp/>in<sp/>precomp.h</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Included<sp/>in<sp/>precomp.h</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;usbspec.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>USB<sp/>specification<sp/>constants</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;hidpddi.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>HID<sp/>support<sp/>in<sp/>drivers<sp/>(e.g.,<sp/>for<sp/>IOCTLs,<sp/>structures)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;hidpi.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>HID<sp/>Parser<sp/>Interface<sp/>(HidP_GetCaps,<sp/>etc.)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;hidclass.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>HID<sp/>Class<sp/>Driver<sp/>constants<sp/>(e.g.,<sp/>GUID_DEVINTERFACE_HID)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdfusb.h&gt;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>WDF<sp/>USB<sp/>support</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight></codeline>
<codeline lineno="42"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>message(&quot;INCLUDE:<sp/>&quot;<sp/>__FILE__<sp/>&quot;<sp/>(driver_log.h<sp/>included)&quot;)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43"><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="45"><highlight class="comment"><sp/>*<sp/>driver_log.h</highlight></codeline>
<codeline lineno="46"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="47"><highlight class="comment"><sp/>*<sp/>驱动程序日志记录模块头文件</highlight></codeline>
<codeline lineno="48"><highlight class="comment"><sp/>*<sp/>提供统一的日志记录和调试跟踪功能</highlight></codeline>
<codeline lineno="49"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50"><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal"></highlight><highlight class="comment">//<sp/>中断处理回调函数声明</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52" refid="device__manager_8c_1a66bb4353dba21c361d66c286dc941155" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_ISR<sp/><ref refid="device__manager_8c_1a66bb4353dba21c361d66c286dc941155" kindref="member">DeviceInterruptIsr</ref>;</highlight></codeline>
<codeline lineno="53" refid="device__manager_8c_1a27966856080fa6cd1a9b7692140774b6" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_DPC<sp/><ref refid="device__manager_8c_1a27966856080fa6cd1a9b7692140774b6" kindref="member">DeviceInterruptDpc</ref>;</highlight></codeline>
<codeline lineno="54" refid="device__manager_8c_1a7c6d2ffd6c40693fcc51198997b8220c" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_ENABLE<sp/><ref refid="device__manager_8c_1a7c6d2ffd6c40693fcc51198997b8220c" kindref="member">DeviceInterruptEnable</ref>;</highlight></codeline>
<codeline lineno="55" refid="device__manager_8c_1a7346c529a00f42617b55dfed2c4a8c6b" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_DISABLE<sp/><ref refid="device__manager_8c_1a7346c529a00f42617b55dfed2c4a8c6b" kindref="member">DeviceInterruptDisable</ref>;</highlight></codeline>
<codeline lineno="56"><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I/O队列回调函数声明</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58" refid="device__manager_8c_1aa3daed4e70ed14cc04cbd390a34a3e90" refkind="member"><highlight class="normal">EVT_WDF_IO_QUEUE_IO_DEVICE_CONTROL<sp/><ref refid="device__manager_8c_1aa3daed4e70ed14cc04cbd390a34a3e90" kindref="member">DeviceIoControl</ref>;</highlight></codeline>
<codeline lineno="59" refid="device__manager_8c_1adbd8ce862fd891104b08816d03110316" refkind="member"><highlight class="normal">EVT_WDF_IO_QUEUE_IO_READ<sp/><ref refid="device__manager_8c_1adbd8ce862fd891104b08816d03110316" kindref="member">DeviceIoRead</ref>;</highlight></codeline>
<codeline lineno="60" refid="device__manager_8c_1aafc7ca77e7f4cc11bf35ad04cff9a631" refkind="member"><highlight class="normal">EVT_WDF_IO_QUEUE_IO_WRITE<sp/><ref refid="device__manager_8c_1aafc7ca77e7f4cc11bf35ad04cff9a631" kindref="member">DeviceIoWrite</ref>;</highlight></codeline>
<codeline lineno="61"><highlight class="normal"></highlight></codeline>
<codeline lineno="62"><highlight class="normal"></highlight><highlight class="comment">//<sp/>定时器回调函数声明</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="63" refid="device__manager_8c_1ab5c458c6825a16167b4f7baef381f352" refkind="member"><highlight class="normal">EVT_WDF_TIMER<sp/><ref refid="device__manager_8c_1ab5c458c6825a16167b4f7baef381f352" kindref="member">DeviceTimerFunc</ref>;</highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight></codeline>
<codeline lineno="65"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"></highlight><highlight class="comment">//<sp/>USB<sp/>Interrupt<sp/>Pipe<sp/>Read<sp/>Completion<sp/>Routine</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="67"><highlight class="normal"></highlight><highlight class="comment">//</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="68"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="69" refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" refkind="member"><highlight class="normal"><ref refid="device__manager_8h_1a07afcd8a4d15d856ac651241642ebcd8" kindref="member">EvtUsbInterruptPipeReadComplete</ref>(</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFUSBPIPE<sp/>Pipe,</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFMEMORY<sp/>Buffer,</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>NumBytesTransferred,</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCONTEXT<sp/>Context</highlight></codeline>
<codeline lineno="74"><highlight class="normal">)</highlight></codeline>
<codeline lineno="75"><highlight class="normal">{</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Pipe);<sp/></highlight><highlight class="comment">//<sp/>Pipe<sp/>is<sp/>implicitly<sp/>known<sp/>via<sp/>deviceContext<sp/>or<sp/>not<sp/>directly<sp/>used<sp/>here</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Context);</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/>PUCHAR<sp/>reportBuffer<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>reportBufferSize<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="80"><highlight class="normal"></highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>LOG_ENTER_FUNCTION_MSG<sp/>is<sp/>good,<sp/>but<sp/>let&apos;s<sp/>use<sp/>LOG_INFO<sp/>for<sp/>specific<sp/>entry<sp/>with<sp/>params</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>WDFMEMORY<sp/>object<sp/>is<sp/>&apos;Buffer&apos;.<sp/>We<sp/>need<sp/>to<sp/>get<sp/>the<sp/>actual<sp/>system<sp/>buffer<sp/>pointer<sp/>from<sp/>it.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>reportBuffer<sp/>=<sp/>(PUCHAR)WdfMemoryGetBuffer(Buffer,<sp/>&amp;reportBufferSize);</highlight></codeline>
<codeline lineno="84"><highlight class="normal"></highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete<sp/>called.<sp/>BytesTransferred:<sp/>%zu,<sp/>BufferSize:<sp/>%zu,<sp/>BufferPtr:<sp/>%p&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NumBytesTransferred,<sp/>reportBufferSize,<sp/>reportBuffer);</highlight></codeline>
<codeline lineno="87"><highlight class="normal"></highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NumBytesTransferred<sp/>==<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>0<sp/>bytes<sp/>transferred.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>No<sp/>LOG_EXIT_FUNCTION<sp/>here<sp/>as<sp/>it&apos;s<sp/>a<sp/>common<sp/>path</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="93"><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(reportBuffer<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>WdfMemoryGetBuffer<sp/>returned<sp/>NULL.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="98"><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Log<sp/>the<sp/>first<sp/>few<sp/>bytes<sp/>of<sp/>the<sp/>report<sp/>for<sp/>debugging</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>bytesToLog<sp/>=<sp/>min((ULONG)NumBytesTransferred,<sp/>32);<sp/></highlight><highlight class="comment">//<sp/>Log<sp/>up<sp/>to<sp/>32<sp/>bytes</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(bytesToLog<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">char</highlight><highlight class="normal"><sp/>hexBuffer[3<sp/>*<sp/>32<sp/>+<sp/>1];<sp/></highlight><highlight class="comment">//<sp/>Max<sp/>3<sp/>chars<sp/>per<sp/>byte<sp/>(XX<sp/>)<sp/>+<sp/>null<sp/>terminator</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>hexBuffer[0]<sp/>=<sp/></highlight><highlight class="charliteral">&apos;\0&apos;</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NTSTATUS<sp/>statusFmt<sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(ULONG<sp/>i<sp/>=<sp/>0;<sp/>i<sp/>&lt;<sp/>bytesToLog;<sp/>i++)<sp/>{</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">char</highlight><highlight class="normal"><sp/>byteStr[4];</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>statusFmt<sp/>=<sp/>RtlStringCchPrintfA(byteStr,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(byteStr),<sp/></highlight><highlight class="stringliteral">&quot;%02X<sp/>&quot;</highlight><highlight class="normal">,<sp/>reportBuffer[i]);</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(statusFmt))<sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>statusFmt<sp/>=<sp/>RtlStringCchCatA(hexBuffer,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(hexBuffer),<sp/>byteStr);</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(statusFmt))<sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(statusFmt))<sp/>{</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>Received<sp/>%zu<sp/>bytes:<sp/>%s&quot;</highlight><highlight class="normal">,<sp/>NumBytesTransferred,<sp/>hexBuffer);</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>Received<sp/>%zu<sp/>bytes.<sp/>Error<sp/>formatting<sp/>hex<sp/>string.&quot;</highlight><highlight class="normal">,<sp/>NumBytesTransferred);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>Received<sp/>%zu<sp/>bytes<sp/>(not<sp/>logging<sp/>content<sp/>as<sp/>length<sp/>is<sp/>0).&quot;</highlight><highlight class="normal">,<sp/>NumBytesTransferred);</highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="120"><highlight class="normal"></highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Implement<sp/>full<sp/>HID<sp/>report<sp/>parsing<sp/>using<sp/>HidP_GetUsages,<sp/>HidP_GetUsageValue<sp/>etc.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>requires<sp/>deviceContext-&gt;HidPreparsedData,<sp/>which<sp/>is<sp/>not<sp/>yet<sp/>available.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we<sp/>will<sp/>just<sp/>mark<sp/>that<sp/>data<sp/>was<sp/>received<sp/>and<sp/>update<sp/>the<sp/>timestamp.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="124"><highlight class="normal"></highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfSpinLockAcquire(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock);</highlight></codeline>
<codeline lineno="126"><highlight class="normal"></highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.IsValid<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Placeholders,<sp/>as<sp/>we<sp/>are<sp/>not<sp/>parsing<sp/>specific<sp/>values<sp/>yet</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;CurrentTouchPoint.X<sp/>=<sp/>0;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;CurrentTouchPoint.Y<sp/>=<sp/>0;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;CurrentTouchPoint.Pressure<sp/>=<sp/>0;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;CurrentTouchPoint.TouchId<sp/>=<sp/>0;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="133"><highlight class="normal"></highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/>KeQuerySystemTimePrecise(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.Timestamp);</highlight></codeline>
<codeline lineno="135"><highlight class="normal"></highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock);</highlight></codeline>
<codeline lineno="137"><highlight class="normal"></highlight></codeline>
<codeline lineno="138"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1acfe39a25e08737b535dc881071ebf149" kindref="member">LOG_DEBUG</ref>(</highlight><highlight class="stringliteral">&quot;EvtUsbInterruptPipeReadComplete:<sp/>Updated<sp/>CurrentTouchPoint.IsValid<sp/>and<sp/>Timestamp.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>LOG_EXIT_FUNCTION();<sp/>//<sp/>Matched<sp/>with<sp/>LOG_ENTER_FUNCTION<sp/>if<sp/>used</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="140"><highlight class="normal">}</highlight></codeline>
<codeline lineno="141"><highlight class="normal"></highlight></codeline>
<codeline lineno="142"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="143"><highlight class="comment"><sp/>*<sp/>映射设备内存资源</highlight></codeline>
<codeline lineno="144"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="145"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="146" refid="device__manager_8c_1afa743e52d2410ed36cf2f06a30c07fcd" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1afa743e52d2410ed36cf2f06a30c07fcd" kindref="member">DeviceMapResources</ref>(</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCMRESLIST<sp/>Resources</highlight></codeline>
<codeline lineno="149"><highlight class="normal">)</highlight></codeline>
<codeline lineno="150"><highlight class="normal">{</highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);</highlight></codeline>
<codeline lineno="152"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Resources);</highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="154"><highlight class="normal">}</highlight></codeline>
<codeline lineno="155"><highlight class="normal"></highlight></codeline>
<codeline lineno="156"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="157"><highlight class="comment"><sp/>*<sp/>取消设备内存资源映射</highlight></codeline>
<codeline lineno="158"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="159"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="160" refid="device__manager_8c_1a55323ce5aacec6667669f95f8abf7e22" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a55323ce5aacec6667669f95f8abf7e22" kindref="member">DeviceUnmapResources</ref>(</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="162"><highlight class="normal">)</highlight></codeline>
<codeline lineno="163"><highlight class="normal">{</highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="166"><highlight class="normal">}</highlight></codeline>
<codeline lineno="167"><highlight class="normal"></highlight></codeline>
<codeline lineno="168"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="169"><highlight class="comment"><sp/>*<sp/>设置设备中断</highlight></codeline>
<codeline lineno="170"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="171"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="172" refid="device__manager_8c_1ac506ca5136446bf4cc6f68c0747f56e0" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1ac506ca5136446bf4cc6f68c0747f56e0" kindref="member">DeviceSetupInterrupt</ref>(</highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="174"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCMRESLIST<sp/>Resources</highlight></codeline>
<codeline lineno="175"><highlight class="normal">)</highlight></codeline>
<codeline lineno="176"><highlight class="normal">{</highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Resources);</highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="180"><highlight class="normal">}</highlight></codeline>
<codeline lineno="181"><highlight class="normal"></highlight></codeline>
<codeline lineno="182"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="183"><highlight class="comment"><sp/>*<sp/>中断服务例程：处理中断并调度DPC</highlight></codeline>
<codeline lineno="184"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="185"><highlight class="normal">BOOLEAN</highlight></codeline>
<codeline lineno="186" refid="device__manager_8c_1a27d4fa34ed290d1ce4e0cba9b619de2b" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a66bb4353dba21c361d66c286dc941155" kindref="member">DeviceInterruptIsr</ref>(</highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="188"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>MessageID</highlight></codeline>
<codeline lineno="189"><highlight class="normal">)</highlight></codeline>
<codeline lineno="190"><highlight class="normal">{</highlight></codeline>
<codeline lineno="191"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(MessageID);</highlight></codeline>
<codeline lineno="192"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>claimed<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device;</highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="195"><highlight class="normal"></highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/>device<sp/>=<sp/>WdfInterruptGetDevice(Interrupt);</highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(device);</highlight></codeline>
<codeline lineno="198"><highlight class="normal"></highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现中断服务例程</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="200"><highlight class="normal"></highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>如果确认是由我们的设备产生的，则处理中断并调度DPC</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TRUE)<sp/>{</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>claimed<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="204"><highlight class="normal"></highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfInterruptQueueDpcForIsr(Interrupt);</highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="207"><highlight class="normal"></highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>claimed;</highlight></codeline>
<codeline lineno="209"><highlight class="normal">}</highlight></codeline>
<codeline lineno="210"><highlight class="normal"></highlight></codeline>
<codeline lineno="211"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="212"><highlight class="comment"><sp/>*<sp/>中断DPC处理函数：完成中断后的延迟处理</highlight></codeline>
<codeline lineno="213"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="214"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="215" refid="device__manager_8c_1a7a7512003b2efe8ec1d3412af1b7c0b3" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a27966856080fa6cd1a9b7692140774b6" kindref="member">DeviceInterruptDpc</ref>(</highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="217"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a3921100f8f21c9e6503c02e51eb84644" kindref="member">WDFOBJECT</ref><sp/>AssociatedObject</highlight></codeline>
<codeline lineno="218"><highlight class="normal">)</highlight></codeline>
<codeline lineno="219"><highlight class="normal">{</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device;</highlight></codeline>
<codeline lineno="221"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="222"><highlight class="normal"></highlight></codeline>
<codeline lineno="223"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(AssociatedObject);</highlight></codeline>
<codeline lineno="224"><highlight class="normal"></highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/>device<sp/>=<sp/>WdfInterruptGetDevice(Interrupt);</highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(device);</highlight></codeline>
<codeline lineno="227"><highlight class="normal"></highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现中断DPC处理函数</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="229"><highlight class="normal"></highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Interrupt<sp/>DPC<sp/>processed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="231"><highlight class="normal">}</highlight></codeline>
<codeline lineno="232"><highlight class="normal"></highlight></codeline>
<codeline lineno="233"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="234"><highlight class="comment"><sp/>*<sp/>启用设备中断</highlight></codeline>
<codeline lineno="235"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="236"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="237" refid="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a7c6d2ffd6c40693fcc51198997b8220c" kindref="member">DeviceInterruptEnable</ref>(</highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>AssociatedDevice</highlight></codeline>
<codeline lineno="240"><highlight class="normal">)</highlight></codeline>
<codeline lineno="241"><highlight class="normal">{</highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="243"><highlight class="normal"></highlight></codeline>
<codeline lineno="244"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Interrupt);</highlight></codeline>
<codeline lineno="245"><highlight class="normal"></highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(AssociatedDevice);</highlight></codeline>
<codeline lineno="247"><highlight class="normal"></highlight></codeline>
<codeline lineno="248"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现中断启用逻辑</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="249"><highlight class="normal"></highlight></codeline>
<codeline lineno="250"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>interrupt<sp/>enabled&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="251"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="252"><highlight class="normal">}</highlight></codeline>
<codeline lineno="253"><highlight class="normal"></highlight></codeline>
<codeline lineno="254"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="255"><highlight class="comment"><sp/>*<sp/>禁用设备中断</highlight></codeline>
<codeline lineno="256"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="257"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="258" refid="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a7346c529a00f42617b55dfed2c4a8c6b" kindref="member">DeviceInterruptDisable</ref>(</highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>AssociatedDevice</highlight></codeline>
<codeline lineno="261"><highlight class="normal">)</highlight></codeline>
<codeline lineno="262"><highlight class="normal">{</highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="264"><highlight class="normal"></highlight></codeline>
<codeline lineno="265"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Interrupt);</highlight></codeline>
<codeline lineno="266"><highlight class="normal"></highlight></codeline>
<codeline lineno="267"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(AssociatedDevice);</highlight></codeline>
<codeline lineno="268"><highlight class="normal"></highlight></codeline>
<codeline lineno="269"><highlight class="normal"></highlight></codeline>
<codeline lineno="270"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>interrupt<sp/>disabled&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="272"><highlight class="normal">}</highlight></codeline>
<codeline lineno="273"><highlight class="normal"></highlight></codeline>
<codeline lineno="274"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="275"><highlight class="comment"><sp/>*<sp/>初始化设备上下文，包括ID、总线类型、电源状态等</highlight></codeline>
<codeline lineno="276"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="277"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="278" refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" kindref="member">DeviceInitContext</ref>(</highlight></codeline>
<codeline lineno="279"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="280"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a57aff078ae347e96d32d9f9f76b00fb2" kindref="member">PDEVICE_INIT_CONFIG</ref><sp/>InitConfig</highlight></codeline>
<codeline lineno="281"><highlight class="normal">)</highlight></codeline>
<codeline lineno="282"><highlight class="normal">{</highlight></codeline>
<codeline lineno="283"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="284"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);<sp/></highlight><highlight class="comment">//<sp/>尝试解决<sp/>C4100</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="285"><highlight class="normal"></highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NULL<sp/>==<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)<sp/>{</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>);</highlight></codeline>
<codeline lineno="288"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="289"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="290"><highlight class="normal"></highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a5fe592117cc2296f7e78acab9055f526" kindref="member">DEVICE_CONTEXT</ref>));</highlight></codeline>
<codeline lineno="292"><highlight class="normal"></highlight></codeline>
<codeline lineno="293"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceId<sp/>=<sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a7ebe4b67b3f9303e10ddace98e7f69ff" kindref="member">DeviceId</ref>;</highlight></codeline>
<codeline lineno="294"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;VendorId<sp/>=<sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1accbb4ad53561dc015a600f3684e552e6" kindref="member">VendorId</ref>;</highlight></codeline>
<codeline lineno="295"><highlight class="normal"></highlight></codeline>
<codeline lineno="296"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;BusType<sp/>=<sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a55239223743c68f1900157add76e756b" kindref="member">BusType</ref>;</highlight></codeline>
<codeline lineno="297"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;BusContext<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="298"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceStarted<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="299"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DevicePowered<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="300"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;PowerState<sp/>=<sp/>PowerDeviceD3;</highlight></codeline>
<codeline lineno="301"><highlight class="normal"></highlight></codeline>
<codeline lineno="302"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kindref="member">DeviceInterfaceGuid</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="303"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInterfaceGuid,<sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kindref="member">DeviceInterfaceGuid</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(GUID));</highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="305"><highlight class="normal"></highlight></codeline>
<codeline lineno="306"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>context<sp/>initialized,<sp/>DeviceId=0x%08X,<sp/>VendorId=0x%08X&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceId,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;VendorId);</highlight></codeline>
<codeline lineno="308"><highlight class="normal"></highlight></codeline>
<codeline lineno="309"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="310"><highlight class="normal">}</highlight></codeline>
<codeline lineno="311"><highlight class="normal"></highlight></codeline>
<codeline lineno="312"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="313"><highlight class="comment"><sp/>*<sp/>配置设备I/O队列，设置请求处理回调</highlight></codeline>
<codeline lineno="314"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="315"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="316" refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" kindref="member">DeviceConfigureIoQueue</ref>(</highlight></codeline>
<codeline lineno="317"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="318"><highlight class="normal">)</highlight></codeline>
<codeline lineno="319"><highlight class="normal">{</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>NTSTATUS<sp/>status;<sp/>//<sp/>Commented<sp/>out</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="321"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF_IO_QUEUE_CONFIG<sp/>queueConfig;<sp/>//<sp/>Commented<sp/>out</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDFQUEUE<sp/>queue;<sp/>//<sp/>Commented<sp/>out</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="323"><highlight class="normal"></highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);<sp/></highlight><highlight class="comment">//<sp/>Added<sp/>to<sp/>handle<sp/>C4100<sp/>if<sp/>Device<sp/>becomes<sp/>unused</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="325"><highlight class="normal"></highlight></codeline>
<codeline lineno="326"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Configuring<sp/>device<sp/>I/O<sp/>queues&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="327"><highlight class="normal"></highlight></codeline>
<codeline lineno="328"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="329"><highlight class="comment"><sp/><sp/><sp/><sp/>WDF_IO_QUEUE_CONFIG_INIT_DEFAULT_QUEUE(&amp;queueConfig,<sp/>WdfIoQueueDispatchParallel);</highlight></codeline>
<codeline lineno="330"><highlight class="comment"></highlight></codeline>
<codeline lineno="331"><highlight class="comment"><sp/><sp/><sp/><sp/>//<sp/>Set<sp/>request<sp/>handler<sp/>callback<sp/>functions<sp/>(Original<sp/>comment<sp/>was<sp/>mojibake)</highlight></codeline>
<codeline lineno="332"><highlight class="comment"><sp/><sp/><sp/><sp/>queueConfig.EvtIoDeviceControl<sp/>=<sp/>DeviceIoControl;</highlight></codeline>
<codeline lineno="333"><highlight class="comment"><sp/><sp/><sp/><sp/>queueConfig.EvtIoRead<sp/>=<sp/>DeviceIoRead;</highlight></codeline>
<codeline lineno="334"><highlight class="comment"><sp/><sp/><sp/><sp/>queueConfig.EvtIoWrite<sp/>=<sp/>DeviceIoWrite;</highlight></codeline>
<codeline lineno="335"><highlight class="comment"></highlight></codeline>
<codeline lineno="336"><highlight class="comment"><sp/><sp/><sp/><sp/>//<sp/>Create<sp/>default<sp/>queue<sp/>(Original<sp/>comment<sp/>was<sp/>mojibake)</highlight></codeline>
<codeline lineno="337"><highlight class="comment"><sp/><sp/><sp/><sp/>status<sp/>=<sp/>WdfIoQueueCreate(</highlight></codeline>
<codeline lineno="338"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Device,</highlight></codeline>
<codeline lineno="339"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;queueConfig,</highlight></codeline>
<codeline lineno="340"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_NO_OBJECT_ATTRIBUTES,</highlight></codeline>
<codeline lineno="341"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;queue</highlight></codeline>
<codeline lineno="342"><highlight class="comment"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="343"><highlight class="comment"><sp/><sp/><sp/><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="344"><highlight class="normal"></highlight></codeline>
<codeline lineno="345"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">/*<sp/>//<sp/>Commented<sp/>out<sp/>status<sp/>check</highlight></codeline>
<codeline lineno="346"><highlight class="comment"><sp/><sp/><sp/><sp/>if<sp/>(!NT_SUCCESS(status))<sp/>{</highlight></codeline>
<codeline lineno="347"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LOG_ERROR(&quot;WdfIoQueueCreate<sp/>failed<sp/>for<sp/>default<sp/>queue,<sp/>status:<sp/>0x%X&quot;,<sp/>status);</highlight></codeline>
<codeline lineno="348"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>return<sp/>status;</highlight></codeline>
<codeline lineno="349"><highlight class="comment"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="350"><highlight class="comment"><sp/><sp/><sp/><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="351"><highlight class="normal"></highlight></codeline>
<codeline lineno="352"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>I/O<sp/>queues<sp/>configured<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="353"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;<sp/></highlight><highlight class="comment">//<sp/>Always<sp/>return<sp/>success<sp/>for<sp/>now</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="354"><highlight class="normal">}</highlight></codeline>
<codeline lineno="355"><highlight class="normal"></highlight></codeline>
<codeline lineno="356"><highlight class="normal"></highlight><highlight class="comment">//<sp/>=====================<sp/>极简唯一标准实现<sp/>=====================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="357" refid="device__manager_8c_1af9753d89f71d81c5fc2038491eb88932" refkind="member"><highlight class="normal">VOID<sp/><ref refid="device__manager_8c_1adbd8ce862fd891104b08816d03110316" kindref="member">DeviceIoRead</ref>(<ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref><sp/>Queue,<sp/><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref><sp/>Request,<sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>Length)<sp/>{</highlight></codeline>
<codeline lineno="358"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Queue);</highlight></codeline>
<codeline lineno="359"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Request);</highlight></codeline>
<codeline lineno="360"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Length);</highlight></codeline>
<codeline lineno="361"><highlight class="normal">}</highlight></codeline>
<codeline lineno="362"><highlight class="normal"></highlight></codeline>
<codeline lineno="363" refid="device__manager_8c_1a47b96d5bfdb1f42b07cc85978325d77a" refkind="member"><highlight class="normal">VOID<sp/><ref refid="device__manager_8c_1aafc7ca77e7f4cc11bf35ad04cff9a631" kindref="member">DeviceIoWrite</ref>(<ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref><sp/>Queue,<sp/><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref><sp/>Request,<sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>Length)<sp/>{</highlight></codeline>
<codeline lineno="364"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Queue);</highlight></codeline>
<codeline lineno="365"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Request);</highlight></codeline>
<codeline lineno="366"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Length);</highlight></codeline>
<codeline lineno="367"><highlight class="normal">}</highlight></codeline>
<codeline lineno="368"><highlight class="normal"></highlight></codeline>
<codeline lineno="369" refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" refkind="member"><highlight class="normal">VOID<sp/><ref refid="device__manager_8c_1aa3daed4e70ed14cc04cbd390a34a3e90" kindref="member">DeviceIoControl</ref>(</highlight></codeline>
<codeline lineno="370"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kindref="member">WDFQUEUE</ref><sp/>Queue,</highlight></codeline>
<codeline lineno="371"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref><sp/>Request,</highlight></codeline>
<codeline lineno="372"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>OutputBufferLength,</highlight></codeline>
<codeline lineno="373"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>InputBufferLength,</highlight></codeline>
<codeline lineno="374"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>IoControlCode</highlight></codeline>
<codeline lineno="375"><highlight class="normal">)</highlight></codeline>
<codeline lineno="376"><highlight class="normal">{</highlight></codeline>
<codeline lineno="377"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_INVALID_DEVICE_REQUEST;<sp/></highlight><highlight class="comment">//<sp/>Default<sp/>status<sp/>for<sp/>unhandled<sp/>IOCTLs</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="378"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>bytesReturned<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="379"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(WdfIoQueueGetDevice(Queue));</highlight></codeline>
<codeline lineno="380"><highlight class="normal"></highlight></codeline>
<codeline lineno="381"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(InputBufferLength);<sp/></highlight><highlight class="comment">//<sp/>Will<sp/>be<sp/>used<sp/>if<sp/>IOCTLs<sp/>require<sp/>input</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="382"><highlight class="normal"></highlight></codeline>
<codeline lineno="383"><highlight class="normal"><sp/><sp/><sp/><sp/>LOG_ENTER_FUNCTION_MSG(</highlight><highlight class="stringliteral">&quot;IOCTL:<sp/>0x%X,<sp/>OutputBufferLength:<sp/>%zu,<sp/>InputBufferLength:<sp/>%zu&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="384"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>IoControlCode,<sp/>OutputBufferLength,<sp/>InputBufferLength);</highlight></codeline>
<codeline lineno="385"><highlight class="normal"></highlight></codeline>
<codeline lineno="386"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(IoControlCode)<sp/>{</highlight></codeline>
<codeline lineno="387"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="device__manager_8h_1a5d922ac9f0d09258cd06cb7d8d7160af" kindref="member">IOCTL_GET_DEVICE_INFO</ref>:<sp/></highlight><highlight class="comment">//<sp/>Example<sp/>existing<sp/>IOCTL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="388"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>{</highlight></codeline>
<codeline lineno="389"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Processing<sp/>IOCTL_GET_DEVICE_INFO&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="390"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(OutputBufferLength<sp/>&lt;<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kindref="member">DEVICE_INFO</ref>))<sp/>{</highlight></codeline>
<codeline lineno="391"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_BUFFER_TOO_SMALL;</highlight></codeline>
<codeline lineno="392"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>bytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kindref="member">DEVICE_INFO</ref>);</highlight></codeline>
<codeline lineno="393"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Output<sp/>buffer<sp/>too<sp/>small<sp/>for<sp/>IOCTL_GET_DEVICE_INFO.<sp/>Required:<sp/>%d,<sp/>Available:<sp/>%zu&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="394"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kindref="member">DEVICE_INFO</ref>),<sp/>OutputBufferLength);</highlight></codeline>
<codeline lineno="395"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="396"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="397"><highlight class="normal"></highlight></codeline>
<codeline lineno="398"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ac8affe61bb901b8aa1b863ada6ac87bc" kindref="member">PDEVICE_INFO</ref><sp/>deviceInfo<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="399"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfRequestRetrieveOutputBuffer(Request,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kindref="member">DEVICE_INFO</ref>),<sp/>(PVOID*)&amp;deviceInfo,<sp/>NULL);</highlight></codeline>
<codeline lineno="400"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="401"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfRequestRetrieveOutputBuffer<sp/>failed<sp/>for<sp/>IOCTL_GET_DEVICE_INFO,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="402"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="403"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="404"><highlight class="normal"></highlight></codeline>
<codeline lineno="405"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Populate<sp/>deviceInfo<sp/>(example<sp/>values)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="406"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInfo-&gt;<ref refid="struct__DEVICE__INFO_1a7f74be1556d3a954c0540a166ca6221f" kindref="member">VendorId</ref><sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;VendorId;</highlight></codeline>
<codeline lineno="407"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInfo-&gt;<ref refid="struct__DEVICE__INFO_1ad1b5079ed73fc6009607d35543e275ff" kindref="member">DeviceId</ref><sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceId;</highlight></codeline>
<codeline lineno="408"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInfo-&gt;<ref refid="struct__DEVICE__INFO_1aecb40da598eb07be27b2fa762a56cb7c" kindref="member">BusType</ref><sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;BusType;</highlight></codeline>
<codeline lineno="409"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInfo-&gt;<ref refid="struct__DEVICE__INFO_1affc12ad434fe559e681e77ee32b1e3da" kindref="member">PowerState</ref><sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;PowerState;</highlight></codeline>
<codeline lineno="410"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInfo-&gt;<ref refid="struct__DEVICE__INFO_1a7a411dffe28d739e918801b1a3a60517" kindref="member">FirmwareVersion</ref><sp/>=<sp/>0x0100;<sp/></highlight><highlight class="comment">//<sp/>Example<sp/>firmware<sp/>version</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="411"><highlight class="normal"></highlight></codeline>
<codeline lineno="412"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>bytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kindref="member">DEVICE_INFO</ref>);</highlight></codeline>
<codeline lineno="413"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="414"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Successfully<sp/>processed<sp/>IOCTL_GET_DEVICE_INFO.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="415"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="416"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="417"><highlight class="normal"></highlight></codeline>
<codeline lineno="418"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="ioctl_8h_1ad40a8f5f93a2d0fdabdc3b13510850b6" kindref="member">IOCTL_TOUCH_GET_DATA</ref>:</highlight></codeline>
<codeline lineno="419"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>{</highlight></codeline>
<codeline lineno="420"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Processing<sp/>IOCTL_TOUCH_GET_DATA&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="421"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(OutputBufferLength<sp/>&lt;<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>))<sp/>{</highlight></codeline>
<codeline lineno="422"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_BUFFER_TOO_SMALL;</highlight></codeline>
<codeline lineno="423"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>bytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>);<sp/></highlight><highlight class="comment">//<sp/>Indicate<sp/>required<sp/>size</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="424"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Output<sp/>buffer<sp/>too<sp/>small<sp/>for<sp/>IOCTL_TOUCH_GET_DATA.<sp/>Required:<sp/>%d,<sp/>Available:<sp/>%zu&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="425"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>),<sp/>OutputBufferLength);</highlight></codeline>
<codeline lineno="426"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="427"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="428"><highlight class="normal"></highlight></codeline>
<codeline lineno="429"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a28f8698ae45fe8be0e44c195bea1e3ee" kindref="member">PTOUCH_POINT</ref><sp/>outputBuffer<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="430"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfRequestRetrieveOutputBuffer(Request,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>),<sp/>(PVOID*)&amp;outputBuffer,<sp/>NULL);</highlight></codeline>
<codeline lineno="431"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="432"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfRequestRetrieveOutputBuffer<sp/>failed<sp/>for<sp/>IOCTL_TOUCH_GET_DATA,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="433"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="434"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="435"><highlight class="normal"></highlight></codeline>
<codeline lineno="436"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="437"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;TouchDataLock<sp/>is<sp/>NULL<sp/>in<sp/>IOCTL_TOUCH_GET_DATA.<sp/>DevicePrepareHardware<sp/>might<sp/>have<sp/>failed<sp/>to<sp/>create<sp/>it.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="438"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_DEVICE_CONFIGURATION_ERROR;</highlight></codeline>
<codeline lineno="439"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="440"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="441"><highlight class="normal"></highlight></codeline>
<codeline lineno="442"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfSpinLockAcquire(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock);</highlight></codeline>
<codeline lineno="443"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(outputBuffer,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>));</highlight></codeline>
<codeline lineno="444"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Optionally,<sp/>invalidate<sp/>CurrentTouchPoint<sp/>after<sp/>read<sp/>if<sp/>desired<sp/>by<sp/>application<sp/>logic:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="445"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;CurrentTouchPoint.IsValid<sp/>=<sp/>FALSE;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="446"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock);</highlight></codeline>
<codeline lineno="447"><highlight class="normal"></highlight></codeline>
<codeline lineno="448"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>bytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kindref="member">TOUCH_POINT</ref>);</highlight></codeline>
<codeline lineno="449"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="450"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Successfully<sp/>processed<sp/>IOCTL_TOUCH_GET_DATA.<sp/>X:<sp/>%d,<sp/>Y:<sp/>%d,<sp/>Pressure:<sp/>%d,<sp/>Valid:<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="451"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>outputBuffer-&gt;<ref refid="struct__TOUCH__POINT_1aacdb49dfac683510c0c3eeae45f5f8f8" kindref="member">X</ref>,<sp/>outputBuffer-&gt;<ref refid="struct__TOUCH__POINT_1a82bf07c3929eea9e0235dfcd631669d8" kindref="member">Y</ref>,<sp/>outputBuffer-&gt;<ref refid="struct__TOUCH__POINT_1a2e7c2f66ac7d84eae585c1bc409a79b8" kindref="member">Pressure</ref>,<sp/>outputBuffer-&gt;<ref refid="struct__TOUCH__POINT_1a80ff6717c5b2724381d8aa3d6be00695" kindref="member">IsValid</ref>);</highlight></codeline>
<codeline lineno="452"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="453"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="454"><highlight class="normal"></highlight></codeline>
<codeline lineno="455"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Add<sp/>cases<sp/>for<sp/>other<sp/>IOCTLs<sp/>like<sp/>IOCTL_DEVICE_SPECIFIC_COMMAND,<sp/>IOCTL_RESET_DEVICE<sp/>if<sp/>needed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="456"><highlight class="normal"></highlight></codeline>
<codeline lineno="457"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="458"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;Received<sp/>unknown<sp/>or<sp/>unsupported<sp/>IOCTL:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/>IoControlCode);</highlight></codeline>
<codeline lineno="459"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>status<sp/>remains<sp/>STATUS_INVALID_DEVICE_REQUEST</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="460"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="461"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="462"><highlight class="normal"></highlight></codeline>
<codeline lineno="463"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfRequestCompleteWithInformation(Request,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>bytesReturned);</highlight></codeline>
<codeline lineno="464"><highlight class="normal"><sp/><sp/><sp/><sp/>LOG_EXIT_FUNCTION_MSG(</highlight><highlight class="stringliteral">&quot;Completed<sp/>with<sp/>status:<sp/>0x%X,<sp/>BytesReturned:<sp/>%zu&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>bytesReturned);</highlight></codeline>
<codeline lineno="465"><highlight class="normal">}</highlight></codeline>
<codeline lineno="466"><highlight class="normal"></highlight></codeline>
<codeline lineno="467"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="468"><highlight class="comment"><sp/>*<sp/>复位设备硬件，根据总线类型分别处理</highlight></codeline>
<codeline lineno="469"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="470"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="471" refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" kindref="member">DeviceResetHardware</ref>(</highlight></codeline>
<codeline lineno="472"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="473"><highlight class="normal">)</highlight></codeline>
<codeline lineno="474"><highlight class="normal">{</highlight></codeline>
<codeline lineno="475"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="476"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="477"><highlight class="normal"></highlight></codeline>
<codeline lineno="478"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Resetting<sp/>device<sp/>hardware&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="479"><highlight class="normal"></highlight></codeline>
<codeline lineno="480"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="481"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="482"><highlight class="normal"></highlight></codeline>
<codeline lineno="483"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Reset<sp/>device<sp/>hardware</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="484"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;BusType)<sp/>{</highlight></codeline>
<codeline lineno="485"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" kindref="member">BusTypeI2C</ref>:</highlight></codeline>
<codeline lineno="486"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Reset<sp/>I2C<sp/>device<sp/>hardware</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="487"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="488"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="489"><highlight class="normal"></highlight></codeline>
<codeline lineno="490"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" kindref="member">BusTypeSPI</ref>:</highlight></codeline>
<codeline lineno="491"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Reset<sp/>SPI<sp/>device<sp/>hardware</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="492"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="493"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="494"><highlight class="normal"></highlight></codeline>
<codeline lineno="495"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" kindref="member">BusTypeUSB</ref>:</highlight></codeline>
<codeline lineno="496"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Reset<sp/>USB<sp/>device<sp/>hardware</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="497"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="498"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="499"><highlight class="normal"></highlight></codeline>
<codeline lineno="500"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="501"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;Unsupported<sp/>bus<sp/>type<sp/>for<sp/>reset<sp/>operation:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;BusType);</highlight></codeline>
<codeline lineno="502"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_NOT_SUPPORTED;</highlight></codeline>
<codeline lineno="503"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="504"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="505"><highlight class="normal"></highlight></codeline>
<codeline lineno="506"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="507"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>hardware<sp/>reset<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="508"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="509"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>reset<sp/>device<sp/>hardware,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="510"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="511"><highlight class="normal"></highlight></codeline>
<codeline lineno="512"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="513"><highlight class="normal">}</highlight></codeline>
<codeline lineno="514"><highlight class="normal"></highlight></codeline>
<codeline lineno="515"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="516"><highlight class="comment"><sp/>*<sp/>设备硬件资源准备，遍历并初始化各类资源</highlight></codeline>
<codeline lineno="517"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="518"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="519" refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" refkind="member"><highlight class="normal"><ref refid="device__manager_8h_1a4fda7ae13ae040932bf046e74f5bb8a6" kindref="member">DevicePrepareHardware</ref>(</highlight></codeline>
<codeline lineno="520"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="521"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCMRESLIST<sp/>ResourcesRaw,</highlight></codeline>
<codeline lineno="522"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCMRESLIST<sp/>ResourcesTranslated</highlight></codeline>
<codeline lineno="523"><highlight class="normal">)</highlight></codeline>
<codeline lineno="524"><highlight class="normal">{</highlight></codeline>
<codeline lineno="525"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(ResourcesRaw);</highlight></codeline>
<codeline lineno="526"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="527"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="528"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>resourceCount;</highlight></codeline>
<codeline lineno="529"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>i;</highlight></codeline>
<codeline lineno="530"><highlight class="normal"><sp/><sp/><sp/><sp/>PCM_PARTIAL_RESOURCE_DESCRIPTOR<sp/>descriptor;</highlight></codeline>
<codeline lineno="531"><highlight class="normal"></highlight></codeline>
<codeline lineno="532"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Preparing<sp/>hardware<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="533"><highlight class="normal"></highlight></codeline>
<codeline lineno="534"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="535"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="536"><highlight class="normal"></highlight></codeline>
<codeline lineno="537"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>resource<sp/>count</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="538"><highlight class="normal"><sp/><sp/><sp/><sp/>resourceCount<sp/>=<sp/>WdfCmResourceListGetCount(ResourcesTranslated);</highlight></codeline>
<codeline lineno="539"><highlight class="normal"></highlight></codeline>
<codeline lineno="540"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Iterate<sp/>through<sp/>resources</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="541"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(i<sp/>=<sp/>0;<sp/>i<sp/>&lt;<sp/>resourceCount;<sp/>i++)<sp/>{</highlight></codeline>
<codeline lineno="542"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>descriptor<sp/>=<sp/>WdfCmResourceListGetDescriptor(ResourcesTranslated,<sp/>i);</highlight></codeline>
<codeline lineno="543"><highlight class="normal"></highlight></codeline>
<codeline lineno="544"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Check<sp/>if<sp/>descriptor<sp/>is<sp/>valid</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="545"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!descriptor)<sp/>{</highlight></codeline>
<codeline lineno="546"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfCmResourceListGetDescriptor<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>);</highlight></codeline>
<codeline lineno="547"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="548"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="549"><highlight class="normal"></highlight></codeline>
<codeline lineno="550"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Process<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="551"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(descriptor-&gt;Type)<sp/>{</highlight></codeline>
<codeline lineno="552"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/>CmResourceTypePort:</highlight></codeline>
<codeline lineno="553"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Process<sp/>port<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="554"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>I/O<sp/>port<sp/>resource:<sp/>0x%llx,<sp/>length:<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="555"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>descriptor-&gt;u.Port.Start.QuadPart,<sp/>descriptor-&gt;u.Port.Length);</highlight></codeline>
<codeline lineno="556"><highlight class="normal"></highlight></codeline>
<codeline lineno="557"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>port<sp/>base<sp/>address<sp/>and<sp/>length</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="558"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBasePA<sp/>=<sp/>descriptor-&gt;u.Port.Start;</highlight></codeline>
<codeline lineno="559"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength<sp/>=<sp/>descriptor-&gt;u.Port.Length;</highlight></codeline>
<codeline lineno="560"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="561"><highlight class="normal"></highlight></codeline>
<codeline lineno="562"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/>CmResourceTypeInterrupt:</highlight></codeline>
<codeline lineno="563"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Process<sp/>interrupt<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="564"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>interrupt<sp/>resource,<sp/>level:<sp/>%d,<sp/>vector:<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="565"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>descriptor-&gt;Flags<sp/>&amp;<sp/>CM_RESOURCE_INTERRUPT_LEVEL_SENSITIVE,</highlight></codeline>
<codeline lineno="566"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>descriptor-&gt;u.Interrupt.Vector);</highlight></codeline>
<codeline lineno="567"><highlight class="normal"></highlight></codeline>
<codeline lineno="568"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>interrupt<sp/>vector<sp/>and<sp/>mode</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="569"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptVector<sp/>=<sp/>descriptor-&gt;u.Interrupt.Vector;</highlight></codeline>
<codeline lineno="570"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptMode<sp/>=<sp/>(descriptor-&gt;Flags<sp/>&amp;<sp/>CM_RESOURCE_INTERRUPT_LEVEL_SENSITIVE)<sp/>?</highlight></codeline>
<codeline lineno="571"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LevelSensitive<sp/>:<sp/>Latched;</highlight></codeline>
<codeline lineno="572"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="573"><highlight class="normal"></highlight></codeline>
<codeline lineno="574"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/>CmResourceTypeMemory:</highlight></codeline>
<codeline lineno="575"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Process<sp/>memory<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="576"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>memory<sp/>resource:<sp/>0x%llx,<sp/>length:<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="577"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>descriptor-&gt;u.Memory.Start.QuadPart,<sp/>descriptor-&gt;u.Memory.Length);</highlight></codeline>
<codeline lineno="578"><highlight class="normal"></highlight></codeline>
<codeline lineno="579"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>memory<sp/>base<sp/>address<sp/>and<sp/>length</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="580"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBasePA<sp/>=<sp/>descriptor-&gt;u.Memory.Start;</highlight></codeline>
<codeline lineno="581"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength<sp/>=<sp/>descriptor-&gt;u.Memory.Length;</highlight></codeline>
<codeline lineno="582"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="583"><highlight class="normal"></highlight></codeline>
<codeline lineno="584"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="585"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Ignore<sp/>other<sp/>resource<sp/>types</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="586"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>resource<sp/>of<sp/>type<sp/>%d<sp/>(skipping)&quot;</highlight><highlight class="normal">,<sp/>descriptor-&gt;Type);</highlight></codeline>
<codeline lineno="587"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="588"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="589"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="590"><highlight class="normal"></highlight></codeline>
<codeline lineno="591"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>---<sp/>BEGIN<sp/>USB<sp/>HID<sp/>Touch<sp/>Initialization<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="592"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>TouchDataLock</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="593"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>lockAttributes;</highlight></codeline>
<codeline lineno="594"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;lockAttributes);</highlight></codeline>
<codeline lineno="595"><highlight class="normal"><sp/><sp/><sp/><sp/>lockAttributes.ParentObject<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice;<sp/></highlight><highlight class="comment">//<sp/>Associate<sp/>with<sp/>device<sp/>object<sp/>for<sp/>lifetime<sp/>management</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="596"><highlight class="normal"></highlight></codeline>
<codeline lineno="597"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfSpinLockCreate(&amp;lockAttributes,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TouchDataLock);</highlight></codeline>
<codeline lineno="598"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="599"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfSpinLockCreate<sp/>failed<sp/>for<sp/>TouchDataLock,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="600"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>is<sp/>a<sp/>critical<sp/>failure<sp/>for<sp/>touch<sp/>functionality</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="601"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Depending<sp/>on<sp/>requirements,<sp/>you<sp/>might<sp/>choose<sp/>to<sp/>return<sp/>status<sp/>or<sp/>allow<sp/>the<sp/>device<sp/>to<sp/>load<sp/>without<sp/>touch</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="602"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we&apos;ll<sp/>log<sp/>and<sp/>continue,<sp/>but<sp/>touch<sp/>IOCTL<sp/>will<sp/>likely<sp/>fail<sp/>or<sp/>misbehave.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="603"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="604"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;TouchDataLock<sp/>created<sp/>successfully.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="605"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="606"><highlight class="normal"></highlight></codeline>
<codeline lineno="607"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>CurrentTouchPoint</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="608"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.IsValid<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="609"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.X<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="610"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.Y<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="611"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.Pressure<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="612"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;CurrentTouchPoint.TouchId<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="613"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>KeQuerySystemTimePrecise(&amp;deviceContext-&gt;CurrentTouchPoint.Timestamp);<sp/>//<sp/>Initialize<sp/>timestamp<sp/>if<sp/>needed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="614"><highlight class="normal"></highlight></codeline>
<codeline lineno="615"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_DEVICE_CREATE_CONFIG<sp/>createParams;</highlight></codeline>
<codeline lineno="616"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_DEVICE_SELECT_CONFIG_PARAMS<sp/>configParams;</highlight></codeline>
<codeline lineno="617"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>interfaceNum;</highlight></codeline>
<codeline lineno="618"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFUSBINTERFACE<sp/>usbInterface<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="619"><highlight class="normal"><sp/><sp/><sp/><sp/>USB_INTERFACE_DESCRIPTOR<sp/>interfaceDescriptor;</highlight></codeline>
<codeline lineno="620"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>pipeIndex;</highlight></codeline>
<codeline lineno="621"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_PIPE_INFORMATION<sp/>pipeInfo;</highlight></codeline>
<codeline lineno="622"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>hidInterfaceFound<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="623"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>interruptInPipeFound<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="624"><highlight class="normal"></highlight></codeline>
<codeline lineno="625"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Starting<sp/>USB<sp/>device<sp/>configuration<sp/>for<sp/>HID<sp/>touch.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="626"><highlight class="normal"></highlight></codeline>
<codeline lineno="627"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>1.<sp/>Create<sp/>a<sp/>WDFUSBDEVICE<sp/>object</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="628"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_DEVICE_CREATE_CONFIG_INIT(&amp;createParams,<sp/>USBD_CLIENT_CONTRACT_VERSION_602);</highlight></codeline>
<codeline lineno="629"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfUsbTargetDeviceCreateWithParameters(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice,<sp/>&amp;createParams,<sp/>WDF_NO_OBJECT_ATTRIBUTES,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbDevice);</highlight></codeline>
<codeline lineno="630"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="631"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbTargetDeviceCreateWithParameters<sp/>failed:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="632"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>No<sp/>need<sp/>to<sp/>return<sp/>here<sp/>if<sp/>other<sp/>hardware<sp/>resources<sp/>were<sp/>prepared,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="633"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>but<sp/>touch<sp/>functionality<sp/>will<sp/>be<sp/>unavailable.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="634"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndUsbConfig;</highlight></codeline>
<codeline lineno="635"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="636"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;WDFUSBDEVICE<sp/>created<sp/>successfully.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="637"><highlight class="normal"></highlight></codeline>
<codeline lineno="638"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>2.<sp/>Select<sp/>USB<sp/>Configuration<sp/>(usually<sp/>the<sp/>first<sp/>one)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="639"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_DEVICE_SELECT_CONFIG_PARAMS_INIT_SINGLE_INTERFACE(&amp;configParams);</highlight></codeline>
<codeline lineno="640"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfUsbTargetDeviceSelectConfig(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbDevice,<sp/>WDF_NO_OBJECT_ATTRIBUTES,<sp/>&amp;configParams);</highlight></codeline>
<codeline lineno="641"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="642"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>can<sp/>happen<sp/>if<sp/>the<sp/>device<sp/>has<sp/>multiple<sp/>interfaces<sp/>and<sp/>INIT_SINGLE_INTERFACE<sp/>is<sp/>not<sp/>appropriate.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="643"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Or<sp/>if<sp/>there&apos;s<sp/>no<sp/>suitable<sp/>configuration.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="644"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbTargetDeviceSelectConfig<sp/>(single<sp/>interface)<sp/>failed:<sp/>0x%X.<sp/>Trying<sp/>to<sp/>iterate<sp/>interfaces.&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="645"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>If<sp/>single<sp/>interface<sp/>selection<sp/>fails,<sp/>try<sp/>to<sp/>get<sp/>the<sp/>first<sp/>interface<sp/>if<sp/>that&apos;s<sp/>the<sp/>expectation.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="646"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>a<sp/>HID<sp/>device,<sp/>it&apos;s<sp/>common<sp/>to<sp/>have<sp/>one<sp/>HID<sp/>interface.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="647"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>We<sp/>will<sp/>iterate<sp/>to<sp/>find<sp/>the<sp/>HID<sp/>interface<sp/>explicitly<sp/>below.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="648"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we<sp/>assume<sp/>the<sp/>config<sp/>was<sp/>selected<sp/>if<sp/>it<sp/>has<sp/>interfaces.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="649"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>A<sp/>more<sp/>robust<sp/>approach<sp/>might<sp/>involve<sp/>WdfUsbTargetDeviceGetNumInterfaces<sp/>to<sp/>check<sp/>if<sp/>a<sp/>config<sp/>is<sp/>active.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="650"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="651"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Even<sp/>if<sp/>the<sp/>above<sp/>WdfUsbTargetDeviceSelectConfig<sp/>fails<sp/>with<sp/>single<sp/>interface,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="652"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>the<sp/>device<sp/>might<sp/>still<sp/>be<sp/>configured.<sp/>We<sp/>proceed<sp/>to<sp/>find<sp/>the<sp/>HID<sp/>interface.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="653"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Attempted<sp/>to<sp/>select<sp/>USB<sp/>configuration.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="654"><highlight class="normal"></highlight></codeline>
<codeline lineno="655"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>3.<sp/>Find<sp/>the<sp/>HID<sp/>Interface<sp/>and<sp/>Select<sp/>Setting</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="656"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/>A<sp/>typical<sp/>HID<sp/>device<sp/>has<sp/>bInterfaceClass<sp/>=<sp/>0x03<sp/>(HID_DEVICE_CLASS_CODE)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="657"><highlight class="normal"><sp/><sp/><sp/><sp/>BYTE<sp/>numInterfaces<sp/>=<sp/>WdfUsbTargetDeviceGetNumInterfaces(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbDevice);</highlight></codeline>
<codeline lineno="658"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>has<sp/>%d<sp/>USB<sp/>interface(s).&quot;</highlight><highlight class="normal">,<sp/>numInterfaces);</highlight></codeline>
<codeline lineno="659"><highlight class="normal"></highlight></codeline>
<codeline lineno="660"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(interfaceNum<sp/>=<sp/>0;<sp/>interfaceNum<sp/>&lt;<sp/>numInterfaces;<sp/>interfaceNum++)<sp/>{</highlight></codeline>
<codeline lineno="661"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>usbInterface<sp/>=<sp/>WdfUsbTargetDeviceGetInterface(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbDevice,<sp/>interfaceNum);</highlight></codeline>
<codeline lineno="662"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(usbInterface<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="663"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbTargetDeviceGetInterface<sp/>failed<sp/>for<sp/>interface<sp/>%d.&quot;</highlight><highlight class="normal">,<sp/>interfaceNum);</highlight></codeline>
<codeline lineno="664"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">continue</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="665"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="666"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfUsbInterfaceGetDescriptor(usbInterface,<sp/>&amp;interfaceDescriptor);</highlight></codeline>
<codeline lineno="667"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Interface<sp/>%d:<sp/>Class=0x%X,<sp/>SubClass=0x%X,<sp/>Protocol=0x%X&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="668"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>interfaceNum,<sp/>interfaceDescriptor.bInterfaceClass,</highlight></codeline>
<codeline lineno="669"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>interfaceDescriptor.bInterfaceSubClass,<sp/>interfaceDescriptor.bInterfaceProtocol);</highlight></codeline>
<codeline lineno="670"><highlight class="normal"></highlight></codeline>
<codeline lineno="671"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(interfaceDescriptor.bInterfaceClass<sp/>==<sp/>HID_DEVICE_CLASS_CODE)<sp/>{</highlight></codeline>
<codeline lineno="672"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;HID<sp/>Interface<sp/>found<sp/>at<sp/>index<sp/>%d.&quot;</highlight><highlight class="normal">,<sp/>interfaceNum);</highlight></codeline>
<codeline lineno="673"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface<sp/>=<sp/>usbInterface;</highlight></codeline>
<codeline lineno="674"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>hidInterfaceFound<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="675"><highlight class="normal"></highlight></codeline>
<codeline lineno="676"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Select<sp/>the<sp/>first<sp/>setting<sp/>(usually<sp/>AlternateSetting<sp/>0<sp/>for<sp/>HID)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="677"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfUsbInterfaceSelectSetting(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface,<sp/>WDF_NO_OBJECT_ATTRIBUTES,<sp/>NULL);<sp/></highlight><highlight class="comment">//<sp/>NULL<sp/>for<sp/>setting<sp/>0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="678"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="679"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbInterfaceSelectSetting<sp/>failed<sp/>for<sp/>HID<sp/>interface<sp/>%d:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/>interfaceNum,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="680"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface<sp/>=<sp/>NULL;<sp/></highlight><highlight class="comment">//<sp/>Clear<sp/>if<sp/>selection<sp/>failed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="681"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>hidInterfaceFound<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="682"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">continue</highlight><highlight class="normal">;<sp/></highlight><highlight class="comment">//<sp/>Try<sp/>next<sp/>interface<sp/>if<sp/>selection<sp/>failed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="683"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="684"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Selected<sp/>setting<sp/>0<sp/>for<sp/>HID<sp/>interface<sp/>%d.&quot;</highlight><highlight class="normal">,<sp/>interfaceNum);</highlight></codeline>
<codeline lineno="685"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;<sp/></highlight><highlight class="comment">//<sp/>Found<sp/>and<sp/>selected<sp/>HID<sp/>interface</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="686"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="687"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="688"><highlight class="normal"></highlight></codeline>
<codeline lineno="689"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!hidInterfaceFound)<sp/>{</highlight></codeline>
<codeline lineno="690"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;No<sp/>HID<sp/>interface<sp/>found<sp/>on<sp/>the<sp/>device.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="691"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_DEVICE_CONFIGURATION_ERROR;<sp/></highlight><highlight class="comment">//<sp/>Set<sp/>appropriate<sp/>error</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="692"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndUsbConfig;</highlight></codeline>
<codeline lineno="693"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="694"><highlight class="normal"></highlight></codeline>
<codeline lineno="695"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>4.<sp/>Find<sp/>the<sp/>Interrupt<sp/>IN<sp/>Pipe<sp/>for<sp/>the<sp/>HID<sp/>Interface</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="696"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>numPipes<sp/>=<sp/>WdfUsbInterfaceGetNumConfiguredPipes(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface);</highlight></codeline>
<codeline lineno="697"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;HID<sp/>Interface<sp/>has<sp/>%d<sp/>pipe(s).&quot;</highlight><highlight class="normal">,<sp/>numPipes);</highlight></codeline>
<codeline lineno="698"><highlight class="normal"></highlight></codeline>
<codeline lineno="699"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(pipeIndex<sp/>=<sp/>0;<sp/>pipeIndex<sp/>&lt;<sp/>numPipes;<sp/>pipeIndex++)<sp/>{</highlight></codeline>
<codeline lineno="700"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDFUSBPIPE<sp/>usbPipe<sp/>=<sp/>WdfUsbInterfaceGetConfiguredPipe(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface,<sp/>pipeIndex,<sp/>NULL);</highlight></codeline>
<codeline lineno="701"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(usbPipe<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="702"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbInterfaceGetConfiguredPipe<sp/>failed<sp/>for<sp/>pipe<sp/>index<sp/>%d.&quot;</highlight><highlight class="normal">,<sp/>pipeIndex);</highlight></codeline>
<codeline lineno="703"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">continue</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="704"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="705"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfUsbTargetPipeGetInformation(usbPipe,<sp/>&amp;pipeInfo);</highlight></codeline>
<codeline lineno="706"><highlight class="normal"></highlight></codeline>
<codeline lineno="707"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(pipeInfo.PipeType<sp/>==<sp/>WdfUsbPipeTypeInterrupt<sp/>&amp;&amp;<sp/>WdfUsbTargetPipeIsInEndpoint(usbPipe))<sp/>{</highlight></codeline>
<codeline lineno="708"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Interrupt<sp/>IN<sp/>pipe<sp/>found.<sp/>PipeType=0x%X,<sp/>EndpointAddress=0x%X,<sp/>MaxPacketSize=%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="709"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>pipeInfo.PipeType,<sp/>pipeInfo.EndpointAddress,<sp/>pipeInfo.MaximumPacketSize);</highlight></codeline>
<codeline lineno="710"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe<sp/>=<sp/>usbPipe;</highlight></codeline>
<codeline lineno="711"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipeMaxPacketSize<sp/>=<sp/>pipeInfo.MaximumPacketSize;</highlight></codeline>
<codeline lineno="712"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>interruptInPipeFound<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="713"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;<sp/></highlight><highlight class="comment">//<sp/>Found<sp/>the<sp/>interrupt<sp/>IN<sp/>pipe</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="714"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="715"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="716"><highlight class="normal"></highlight></codeline>
<codeline lineno="717"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!interruptInPipeFound)<sp/>{</highlight></codeline>
<codeline lineno="718"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;No<sp/>Interrupt<sp/>IN<sp/>pipe<sp/>found<sp/>on<sp/>the<sp/>HID<sp/>interface.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="719"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_DEVICE_CONFIGURATION_ERROR;<sp/></highlight><highlight class="comment">//<sp/>Set<sp/>appropriate<sp/>error</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="720"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndUsbConfig;</highlight></codeline>
<codeline lineno="721"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="722"><highlight class="normal"></highlight></codeline>
<codeline lineno="723"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;USB<sp/>device<sp/>and<sp/>pipe<sp/>configuration<sp/>for<sp/>HID<sp/>touch<sp/>completed<sp/>successfully.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="724"><highlight class="normal"></highlight></codeline>
<codeline lineno="725"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>6.<sp/>Retrieve<sp/>HID<sp/>Report<sp/>Descriptor</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="726"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_MEMORY_DESCRIPTOR<sp/>memoryDescriptor;</highlight></codeline>
<codeline lineno="727"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFMEMORY<sp/>WdfMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="728"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>First,<sp/>get<sp/>the<sp/>HID<sp/>descriptor<sp/>to<sp/>find<sp/>the<sp/>length<sp/>of<sp/>the<sp/>report<sp/>descriptor</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="729"><highlight class="normal"><sp/><sp/><sp/><sp/>USB_HID_DESCRIPTOR<sp/>hidDescriptor;</highlight></codeline>
<codeline lineno="730"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>hidDescriptorLength<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(USB_HID_DESCRIPTOR);</highlight></codeline>
<codeline lineno="731"><highlight class="normal"></highlight></codeline>
<codeline lineno="732"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>It&apos;s<sp/>more<sp/>reliable<sp/>to<sp/>get<sp/>the<sp/>HID<sp/>descriptor<sp/>for<sp/>the<sp/>interface<sp/>first.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="733"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>HID<sp/>descriptor<sp/>contains<sp/>the<sp/>length<sp/>of<sp/>the<sp/>report<sp/>descriptor.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="734"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WdfUsbInterfaceGetDescriptor<sp/>can<sp/>get<sp/>standard<sp/>descriptors,<sp/>but<sp/>HID<sp/>descriptor<sp/>is<sp/>class-specific.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="735"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>We<sp/>might<sp/>need<sp/>a<sp/>control<sp/>transfer<sp/>for<sp/>GET_DESCRIPTOR<sp/>(HID_DESCRIPTOR_TYPE)<sp/>on<sp/>the<sp/>interface.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="736"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>However,<sp/>often<sp/>the<sp/>report<sp/>descriptor<sp/>length<sp/>is<sp/>known<sp/>or<sp/>can<sp/>be<sp/>queried.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="737"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>simplicity,<sp/>let&apos;s<sp/>assume<sp/>we<sp/>try<sp/>to<sp/>get<sp/>a<sp/>reasonably<sp/>sized<sp/>buffer<sp/>first.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="738"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>A<sp/>more<sp/>robust<sp/>way<sp/>is<sp/>to<sp/>get<sp/>the<sp/>HID<sp/>descriptor,<sp/>then<sp/>the<sp/>report<sp/>descriptor<sp/>with<sp/>correct<sp/>length.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="739"><highlight class="normal"></highlight></codeline>
<codeline lineno="740"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Let&apos;s<sp/>try<sp/>to<sp/>get<sp/>the<sp/>HID<sp/>descriptor<sp/>for<sp/>the<sp/>interface<sp/>first<sp/>to<sp/>find<sp/>bDescriptorLength<sp/>for<sp/>the<sp/>report<sp/>descriptor.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="741"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>is<sp/>complex<sp/>as<sp/>WDF<sp/>doesn&apos;t<sp/>have<sp/>a<sp/>direct<sp/>function<sp/>for<sp/>class-specific<sp/>interface<sp/>descriptors.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="742"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>A<sp/>common<sp/>approach<sp/>is<sp/>to<sp/>iterate<sp/>all<sp/>descriptors<sp/>for<sp/>the<sp/>selected<sp/>interface<sp/>setting.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="743"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we&apos;ll<sp/>make<sp/>an<sp/>educated<sp/>guess<sp/>for<sp/>the<sp/>report<sp/>descriptor<sp/>length<sp/>or<sp/>use<sp/>a<sp/>fixed<sp/>size<sp/>buffer.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="744"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>A<sp/>typical<sp/>HID<sp/>report<sp/>descriptor<sp/>is<sp/>less<sp/>than<sp/>256<sp/>bytes.<sp/>Let&apos;s<sp/>try<sp/>that.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="745"><highlight class="normal"></highlight></codeline>
<codeline lineno="746"><highlight class="normal"><sp/><sp/><sp/><sp/>USHORT<sp/>reportDescLength<sp/>=<sp/>256;<sp/></highlight><highlight class="comment">//<sp/>Initial<sp/>guess<sp/>or<sp/>a<sp/>known<sp/>max<sp/>size</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="747"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>memoryAttributes;</highlight></codeline>
<codeline lineno="748"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;memoryAttributes);</highlight></codeline>
<codeline lineno="749"><highlight class="normal"><sp/><sp/><sp/><sp/>memoryAttributes.ParentObject<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice;<sp/></highlight><highlight class="comment">//<sp/>So<sp/>it&apos;s<sp/>auto-cleaned</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="750"><highlight class="normal"></highlight></codeline>
<codeline lineno="751"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfMemoryCreate(&amp;memoryAttributes,<sp/>NonPagedPoolNx,<sp/>POOL_TAG_HID,<sp/>reportDescLength,<sp/>&amp;WdfMemory,<sp/>(PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor);</highlight></codeline>
<codeline lineno="752"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="753"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfMemoryCreate<sp/>for<sp/>HID<sp/>Report<sp/>Descriptor<sp/>failed:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="754"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="755"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndHidConfig;<sp/></highlight><highlight class="comment">//<sp/>Skip<sp/>further<sp/>HID<sp/>config<sp/>if<sp/>memory<sp/>allocation<sp/>fails</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="756"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="757"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_MEMORY_DESCRIPTOR_INIT_HANDLE(&amp;memoryDescriptor,<sp/>WdfMemory,<sp/>NULL);</highlight></codeline>
<codeline lineno="758"><highlight class="normal"></highlight></codeline>
<codeline lineno="759"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_USB_CONTROL_SETUP_PACKET<sp/>setupPacket;</highlight></codeline>
<codeline lineno="760"><highlight class="normal"><sp/><sp/><sp/><sp/>NT_ASSERT(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface<sp/>!=<sp/>NULL);<sp/></highlight><highlight class="comment">//<sp/>Should<sp/>have<sp/>been<sp/>found<sp/>earlier</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="761"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>hidInterfaceIndex<sp/>=<sp/>WdfUsbInterfaceGetInterfaceNumber(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbInterface);</highlight></codeline>
<codeline lineno="762"><highlight class="normal"></highlight></codeline>
<codeline lineno="763"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(&amp;setupPacket,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(WDF_USB_CONTROL_SETUP_PACKET));</highlight></codeline>
<codeline lineno="764"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.bm.Request.Dir<sp/>=<sp/>BMREQUEST_DEVICE_TO_HOST;</highlight></codeline>
<codeline lineno="765"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.bm.Request.Type<sp/>=<sp/>BMREQUEST_STANDARD;</highlight></codeline>
<codeline lineno="766"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.bm.Request.Recipient<sp/>=<sp/>BMREQUEST_TO_INTERFACE;</highlight></codeline>
<codeline lineno="767"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.bRequest<sp/>=<sp/>USB_REQUEST_GET_DESCRIPTOR;</highlight></codeline>
<codeline lineno="768"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.wValue.Bytes.HiByte<sp/>=<sp/>HID_REPORT_DESCRIPTOR_TYPE;<sp/></highlight><highlight class="comment">//<sp/>Descriptor<sp/>Type</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="769"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.wValue.Bytes.LoByte<sp/>=<sp/>0;<sp/></highlight><highlight class="comment">//<sp/>Descriptor<sp/>Index<sp/>(usually<sp/>0<sp/>for<sp/>report<sp/>descriptor)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="770"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.wIndex.Value<sp/>=<sp/>hidInterfaceIndex;<sp/></highlight><highlight class="comment">//<sp/>Interface<sp/>number</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="771"><highlight class="normal"><sp/><sp/><sp/><sp/>setupPacket.Packet.wLength<sp/>=<sp/>reportDescLength;</highlight></codeline>
<codeline lineno="772"><highlight class="normal"></highlight></codeline>
<codeline lineno="773"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Attempting<sp/>to<sp/>get<sp/>HID<sp/>Report<sp/>Descriptor<sp/>(Interface:<sp/>%d,<sp/>MaxLength:<sp/>%d)&quot;</highlight><highlight class="normal">,<sp/>hidInterfaceIndex,<sp/>reportDescLength);</highlight></codeline>
<codeline lineno="774"><highlight class="normal"></highlight></codeline>
<codeline lineno="775"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>bytesRead;</highlight></codeline>
<codeline lineno="776"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfUsbTargetDeviceSendControlTransferSynchronously(</highlight></codeline>
<codeline lineno="777"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;UsbDevice,</highlight></codeline>
<codeline lineno="778"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1aa5171e0d8c548a75e9b42333beb6390e" kindref="member">WDF_NO_HANDLE</ref>,<sp/></highlight><highlight class="comment">//<sp/>Optional<sp/>WDFREQUEST</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="779"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Optional<sp/>SendOptions</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="780"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;setupPacket,<sp/><sp/></highlight><highlight class="comment">//<sp/>SetupPacket</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="781"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;memoryDescriptor,<sp/></highlight><highlight class="comment">//<sp/>MemoryDescriptor<sp/>for<sp/>data<sp/>buffer</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="782"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;bytesRead<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>BytesRead</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="783"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="784"><highlight class="normal"></highlight></codeline>
<codeline lineno="785"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="786"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbTargetDeviceSendControlTransferSynchronously<sp/>for<sp/>HID<sp/>Report<sp/>Descriptor<sp/>failed:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="787"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>deviceContext-&gt;HidReportDescriptor<sp/>will<sp/>be<sp/>an<sp/>empty<sp/>WDFMEMORY,<sp/>which<sp/>is<sp/>fine.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="788"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Or<sp/>we<sp/>can<sp/>WdfObjectDelete(WdfMemory)<sp/>here<sp/>if<sp/>we<sp/>don&apos;t<sp/>want<sp/>to<sp/>keep<sp/>an<sp/>empty<sp/>buffer.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="789"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(WdfMemory);<sp/></highlight><highlight class="comment">//<sp/>Free<sp/>the<sp/>allocated<sp/>memory</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="790"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="791"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="792"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndHidConfig;</highlight></codeline>
<codeline lineno="793"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="794"><highlight class="normal"></highlight></codeline>
<codeline lineno="795"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(bytesRead<sp/>==<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="796"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;HID<sp/>Report<sp/>Descriptor<sp/>read<sp/>0<sp/>bytes.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="797"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(WdfMemory);</highlight></codeline>
<codeline lineno="798"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="799"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="800"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_UNSUCCESSFUL;<sp/></highlight><highlight class="comment">//<sp/>Indicate<sp/>failure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="801"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>EndHidConfig;</highlight></codeline>
<codeline lineno="802"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="803"><highlight class="normal"></highlight></codeline>
<codeline lineno="804"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptorLength<sp/>=<sp/>(USHORT)bytesRead;</highlight></codeline>
<codeline lineno="805"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>actual<sp/>descriptor<sp/>is<sp/>now<sp/>in<sp/>deviceContext-&gt;HidReportDescriptor<sp/>(buffer<sp/>obtained<sp/>from<sp/>WdfMemory)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="806"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Successfully<sp/>read<sp/>%d<sp/>bytes<sp/>of<sp/>HID<sp/>Report<sp/>Descriptor.&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptorLength);</highlight></codeline>
<codeline lineno="807"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Note:<sp/>deviceContext-&gt;HidReportDescriptor<sp/>points<sp/>to<sp/>the<sp/>buffer<sp/>within<sp/>WdfMemory.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="808"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WdfMemory<sp/>will<sp/>be<sp/>released<sp/>when<sp/>deviceContext-&gt;WdfDevice<sp/>is<sp/>destroyed<sp/>due<sp/>to<sp/>ParentObject<sp/>setting.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="809"><highlight class="normal"></highlight></codeline>
<codeline lineno="810"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Parse<sp/>HID<sp/>Report<sp/>Descriptor<sp/>using<sp/>HidP_GetCaps</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="811"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>requires<sp/>PHIDP_PREPARSED_DATA.<sp/>How<sp/>to<sp/>get/create<sp/>this<sp/>from<sp/>raw<sp/>descriptor<sp/>in<sp/>KMDF<sp/>USB<sp/>driver?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="812"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Option<sp/>1:<sp/>If<sp/>this<sp/>driver<sp/>were<sp/>a<sp/>HID<sp/>minidriver,<sp/>HidRegisterMinidriver<sp/>helps.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="813"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Option<sp/>2:<sp/>Send<sp/>IOCTL_HID_GET_PREPARSED_DATA<sp/>to<sp/>a<sp/>HID<sp/>collection<sp/>FileObject<sp/>(if<sp/>we<sp/>had<sp/>one).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="814"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Option<sp/>3:<sp/>Manually<sp/>parse<sp/>or<sp/>use<sp/>a<sp/>simpler<sp/>parsing<sp/>logic<sp/>if<sp/>format<sp/>is<sp/>known<sp/>and<sp/>simple.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="815"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we<sp/>have<sp/>the<sp/>raw<sp/>descriptor.<sp/>We&apos;ll<sp/>defer<sp/>parsing<sp/>to<sp/>HidP_GetCaps.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="816"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;HID<sp/>Report<sp/>Descriptor<sp/>parsing<sp/>(HidP_GetCaps)<sp/>not<sp/>yet<sp/>implemented.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="817"><highlight class="normal"></highlight></codeline>
<codeline lineno="818"><highlight class="normal">EndHidConfig:</highlight></codeline>
<codeline lineno="819"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>label<sp/>is<sp/>for<sp/>cleanup<sp/>if<sp/>HID<sp/>specific<sp/>configuration<sp/>fails<sp/>mid-way</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="820"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>&amp;&amp;<sp/>WdfMemory<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="821"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Ensure<sp/>WdfMemory<sp/>is<sp/>cleaned<sp/>up<sp/>if<sp/>HidReportDescriptor<sp/>is<sp/>not<sp/>validly<sp/>set</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="822"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(WdfMemory);</highlight></codeline>
<codeline lineno="823"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="824"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptorLength<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="825"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="826"><highlight class="normal"></highlight></codeline>
<codeline lineno="827"><highlight class="normal"></highlight></codeline>
<codeline lineno="828"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>8.<sp/>Configure<sp/>continuous<sp/>reader<sp/>for<sp/>the<sp/>Interrupt<sp/>IN<sp/>pipe</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="829"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>&amp;&amp;<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="830"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_USB_CONTINUOUS_READER_CONFIG<sp/>readerConfig;</highlight></codeline>
<codeline lineno="831"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_USB_CONTINUOUS_READER_CONFIG_INIT(&amp;readerConfig,</highlight></codeline>
<codeline lineno="832"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a07afcd8a4d15d856ac651241642ebcd8" kindref="member">EvtUsbInterruptPipeReadComplete</ref>,</highlight></codeline>
<codeline lineno="833"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>,<sp/></highlight><highlight class="comment">//<sp/>Context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="834"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipeMaxPacketSize);<sp/></highlight><highlight class="comment">//<sp/>TransferLength</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="835"><highlight class="normal"></highlight></codeline>
<codeline lineno="836"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Optional:<sp/>Specify<sp/>a<sp/>callback<sp/>for<sp/>when<sp/>the<sp/>reader<sp/>fails</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="837"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>readerConfig.EvtUsbTargetPipeReadersFailed<sp/>=<sp/>EvtUsbReadersFailed;<sp/>//<sp/>TODO:<sp/>Implement<sp/>EvtUsbReadersFailed<sp/>if<sp/>needed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="838"><highlight class="normal"></highlight></codeline>
<codeline lineno="839"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Configuring<sp/>continuous<sp/>reader<sp/>for<sp/>Interrupt<sp/>IN<sp/>pipe<sp/>with<sp/>TransferLength:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipeMaxPacketSize);</highlight></codeline>
<codeline lineno="840"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfUsbTargetPipeConfigContinuousReader(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe,<sp/>&amp;readerConfig);</highlight></codeline>
<codeline lineno="841"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="842"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfUsbTargetPipeConfigContinuousReader<sp/>failed:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="843"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Failure<sp/>to<sp/>configure<sp/>continuous<sp/>reader<sp/>means<sp/>no<sp/>touch<sp/>input</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="844"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="845"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Continuous<sp/>reader<sp/>configured<sp/>successfully.<sp/>Starting<sp/>reader.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="846"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>reader<sp/>is<sp/>typically<sp/>started<sp/>implicitly<sp/>by<sp/>WdfIoTargetStart,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="847"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>or<sp/>explicitly<sp/>if<sp/>WdfIoTargetStop<sp/>was<sp/>called.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="848"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>If<sp/>the<sp/>I/O<sp/>target<sp/>for<sp/>the<sp/>pipe<sp/>(which<sp/>is<sp/>the<sp/>USB<sp/>device<sp/>I/O<sp/>target)<sp/>is<sp/>already<sp/>started,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="849"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>the<sp/>reader<sp/>should<sp/>start.<sp/>We<sp/>can<sp/>explicitly<sp/>start<sp/>it<sp/>to<sp/>be<sp/>sure<sp/>if<sp/>needed<sp/>after<sp/>device<sp/>is<sp/>started.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="850"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>configuration<sp/>is<sp/>enough.<sp/>It<sp/>will<sp/>start<sp/>when<sp/>the<sp/>I/O<sp/>target<sp/>starts.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="851"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="852"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="853"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="854"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Cannot<sp/>configure<sp/>continuous<sp/>reader<sp/>because<sp/>InterruptInPipe<sp/>is<sp/>NULL.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="855"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_UNSUCCESSFUL;<sp/></highlight><highlight class="comment">//<sp/>Ensure<sp/>status<sp/>reflects<sp/>this<sp/>failure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="856"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="857"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="858"><highlight class="normal"></highlight></codeline>
<codeline lineno="859"><highlight class="normal">EndUsbConfig:</highlight></codeline>
<codeline lineno="860"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="861"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;USB<sp/>HID<sp/>Touch<sp/>initialization<sp/>failed<sp/>with<sp/>status<sp/>0x%X.<sp/>Touch<sp/>input<sp/>will<sp/>not<sp/>be<sp/>available.&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="862"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Cleanup<sp/>partially<sp/>initialized<sp/>resources<sp/>if<sp/>necessary,<sp/>though<sp/>WDF<sp/>handles<sp/>most<sp/>of<sp/>this.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="863"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>example,<sp/>if<sp/>UsbDevice<sp/>was<sp/>created<sp/>but<sp/>subsequent<sp/>steps<sp/>failed,<sp/>it<sp/>will<sp/>be<sp/>cleaned<sp/>up<sp/>when<sp/>WdfDevice<sp/>is<sp/>removed.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="864"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="865"><highlight class="normal"></highlight></codeline>
<codeline lineno="866"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>---<sp/>END<sp/>USB<sp/>HID<sp/>Touch<sp/>Initialization<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="867"><highlight class="normal"></highlight></codeline>
<codeline lineno="868"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Hardware<sp/>resources<sp/>prepared<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="869"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="870"><highlight class="normal">}</highlight></codeline>
<codeline lineno="871"><highlight class="normal"></highlight></codeline>
<codeline lineno="872"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="873"><highlight class="comment"><sp/>*<sp/>释放设备硬件资源，释放端口、中断、内存等</highlight></codeline>
<codeline lineno="874"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="875"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="876" refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" refkind="member"><highlight class="normal"><ref refid="device__manager_8h_1a20074db76287e7b7a18bc2f2c83c2c61" kindref="member">DeviceReleaseHardware</ref>(</highlight></codeline>
<codeline lineno="877"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="878"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFCMRESLIST<sp/>ResourcesTranslated</highlight></codeline>
<codeline lineno="879"><highlight class="normal">)</highlight></codeline>
<codeline lineno="880"><highlight class="normal">{</highlight></codeline>
<codeline lineno="881"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="882"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="883"><highlight class="normal"></highlight></codeline>
<codeline lineno="884"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Releasing<sp/>hardware<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="885"><highlight class="normal"></highlight></codeline>
<codeline lineno="886"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="887"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="888"><highlight class="normal"></highlight></codeline>
<codeline lineno="889"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Release<sp/>port<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="890"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="891"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>MmUnmapIoSpace(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength);</highlight></codeline>
<codeline lineno="892"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="893"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="894"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Port<sp/>resource<sp/>released&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="895"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="896"><highlight class="normal"></highlight></codeline>
<codeline lineno="897"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Release<sp/>interrupt<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="898"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="899"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfInterruptDisable(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="900"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfInterruptDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="901"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="902"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Interrupt<sp/>resource<sp/>released&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="903"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="904"><highlight class="normal"></highlight></codeline>
<codeline lineno="905"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Release<sp/>memory<sp/>resource</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="906"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="907"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>MmUnmapIoSpace(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA,<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength);</highlight></codeline>
<codeline lineno="908"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryBaseVA<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="909"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;MemoryLength<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="910"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Memory<sp/>resource<sp/>released&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="911"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="912"><highlight class="normal"></highlight></codeline>
<codeline lineno="913"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>---<sp/>BEGIN<sp/>USB<sp/>HID<sp/>Touch<sp/>Deinitialization<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="914"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Stop<sp/>continuous<sp/>reader<sp/>if<sp/>it<sp/>was<sp/>started<sp/>(e.g.,<sp/>WdfIoTargetStop<sp/>for<sp/>the<sp/>pipe&apos;s<sp/>I/O<sp/>target).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="915"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/><sp/><sp/><sp/>This<sp/>should<sp/>be<sp/>done<sp/>before<sp/>the<sp/>WDFUSBPIPE<sp/>object<sp/>is<sp/>destroyed.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="916"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>Free<sp/>HID<sp/>resources<sp/>if<sp/>manually<sp/>allocated:</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="917"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/><sp/><sp/><sp/>-<sp/>deviceContext-&gt;HidReportDescriptor<sp/>(e.g.,<sp/>ExFreePoolWithTag<sp/>if<sp/>allocated<sp/>with<sp/>ExAllocatePoolWithTag)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="918"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/><sp/><sp/><sp/>-<sp/>deviceContext-&gt;HidPreparsedData<sp/>(if<sp/>HidD_GetPreparsedData<sp/>was<sp/>somehow<sp/>used<sp/>and<sp/>needs<sp/>HidD_FreePreparsedData).</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="919"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Stop<sp/>the<sp/>continuous<sp/>reader<sp/>for<sp/>the<sp/>interrupt<sp/>pipe</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="920"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="921"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Stopping<sp/>continuous<sp/>reader<sp/>for<sp/>Interrupt<sp/>IN<sp/>pipe.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="922"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>It&apos;s<sp/>important<sp/>to<sp/>stop<sp/>the<sp/>I/O<sp/>target<sp/>associated<sp/>with<sp/>the<sp/>pipe<sp/>to<sp/>halt<sp/>the<sp/>continuous<sp/>reader.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="923"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfIoTargetStop(WdfUsbTargetPipeGetIoTarget(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;InterruptInPipe),<sp/>WdfIoTargetCancelSentIo);</highlight></codeline>
<codeline lineno="924"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Continuous<sp/>reader<sp/>for<sp/>Interrupt<sp/>IN<sp/>pipe<sp/>stopped.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="925"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDFUSBPIPE<sp/>objects<sp/>are<sp/>children<sp/>of<sp/>WDFUSBINTERFACE<sp/>or<sp/>WDFUSBDEVICE<sp/>and<sp/>are<sp/>deleted<sp/>automatically.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="926"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>No<sp/>need<sp/>to<sp/>explicitly<sp/>delete<sp/>deviceContext-&gt;InterruptInPipe<sp/>here.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="927"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="928"><highlight class="normal"></highlight></codeline>
<codeline lineno="929"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>HID<sp/>Report<sp/>Descriptor<sp/>buffer<sp/>was<sp/>allocated<sp/>via<sp/>WdfMemoryCreate<sp/>with<sp/>WdfDevice<sp/>as<sp/>ParentObject.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="930"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF<sp/>will<sp/>automatically<sp/>free<sp/>this<sp/>WDFMEMORY<sp/>object<sp/>and<sp/>its<sp/>associated<sp/>buffer<sp/>when<sp/>the<sp/>parent<sp/>is<sp/>destroyed.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="931"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>We<sp/>should<sp/>not<sp/>call<sp/>ExFreePoolWithTag<sp/>on<sp/>deviceContext-&gt;HidReportDescriptor<sp/>here.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="932"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>We<sp/>can,<sp/>however,<sp/>clear<sp/>our<sp/>context&apos;s<sp/>pointer<sp/>and<sp/>length.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="933"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="934"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Clearing<sp/>HID<sp/>Report<sp/>Descriptor<sp/>context<sp/>pointers<sp/>(memory<sp/>managed<sp/>by<sp/>WDF).&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="935"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptor<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="936"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HidReportDescriptorLength<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="937"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;HID<sp/>Report<sp/>Descriptor<sp/>context<sp/>pointers<sp/>cleared.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="938"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="939"><highlight class="normal"></highlight></codeline>
<codeline lineno="940"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>If<sp/>HidPreparsedData<sp/>was<sp/>ever<sp/>populated,<sp/>it<sp/>would<sp/>need<sp/>to<sp/>be<sp/>freed<sp/>here.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="941"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Example:<sp/>if<sp/>(deviceContext-&gt;HidPreparsedData)<sp/>{<sp/>HidD_FreePreparsedData(deviceContext-&gt;HidPreparsedData);<sp/>deviceContext-&gt;HidPreparsedData<sp/>=<sp/>NULL;<sp/>}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="942"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Currently,<sp/>HidPreparsedData<sp/>is<sp/>not<sp/>being<sp/>populated,<sp/>so<sp/>no<sp/>action<sp/>needed<sp/>for<sp/>it<sp/>yet.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="943"><highlight class="normal"></highlight></codeline>
<codeline lineno="944"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDFUSBDEVICE,<sp/>WDFUSBINTERFACE,<sp/>and<sp/>WDFSPINLOCK<sp/>(if<sp/>parented<sp/>to<sp/>WDFDEVICE)<sp/>are<sp/>auto-cleaned<sp/>by<sp/>WDF.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="945"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>The<sp/>WDFUSBPIPE<sp/>(deviceContext-&gt;InterruptInPipe)<sp/>is<sp/>also<sp/>managed<sp/>by<sp/>WDF<sp/>as<sp/>it&apos;s<sp/>a<sp/>child<sp/>of<sp/>the<sp/>WDFUSBINTERFACE.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="946"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;USB<sp/>HID<sp/>deinitialization<sp/>for<sp/>touch<sp/>completed.&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="947"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>---<sp/>END<sp/>USB<sp/>HID<sp/>Touch<sp/>Deinitialization<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="948"><highlight class="normal"></highlight></codeline>
<codeline lineno="949"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Hardware<sp/>resources<sp/>released<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="950"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="951"><highlight class="normal">}</highlight></codeline>
<codeline lineno="952"><highlight class="normal"></highlight></codeline>
<codeline lineno="953"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="954"><highlight class="comment"><sp/>*<sp/>设备进入D0电源状态，更新电源标志</highlight></codeline>
<codeline lineno="955"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="956"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="957" refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" refkind="member"><highlight class="normal"><ref refid="device__manager_8h_1a51a0fa7bdbb22680e79efaa2cacbb729" kindref="member">DeviceD0Entry</ref>(</highlight></codeline>
<codeline lineno="958"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="959"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_POWER_DEVICE_STATE<sp/>PreviousState</highlight></codeline>
<codeline lineno="960"><highlight class="normal">)</highlight></codeline>
<codeline lineno="961"><highlight class="normal">{</highlight></codeline>
<codeline lineno="962"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="963"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="964"><highlight class="normal"></highlight></codeline>
<codeline lineno="965"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>entering<sp/>D0<sp/>power<sp/>state<sp/>from<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>PreviousState);</highlight></codeline>
<codeline lineno="966"><highlight class="normal"></highlight></codeline>
<codeline lineno="967"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="968"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="969"><highlight class="normal"></highlight></codeline>
<codeline lineno="970"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Enable<sp/>device</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="971"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DevicePowered<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="972"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;PowerState<sp/>=<sp/>PowerDeviceD0;</highlight></codeline>
<codeline lineno="973"><highlight class="normal"></highlight></codeline>
<codeline lineno="974"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>entered<sp/>D0<sp/>power<sp/>state<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="975"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="976"><highlight class="normal">}</highlight></codeline>
<codeline lineno="977"><highlight class="normal"></highlight></codeline>
<codeline lineno="978"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="979"><highlight class="comment"><sp/>*<sp/>设备退出D0电源状态，恢复目标电源状态</highlight></codeline>
<codeline lineno="980"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="981"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="982" refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" refkind="member"><highlight class="normal"><ref refid="device__manager_8h_1a1625e40d469a21ece1cad244736fbe81" kindref="member">DeviceD0Exit</ref>(</highlight></codeline>
<codeline lineno="983"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="984"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_POWER_DEVICE_STATE<sp/>TargetState</highlight></codeline>
<codeline lineno="985"><highlight class="normal">)</highlight></codeline>
<codeline lineno="986"><highlight class="normal">{</highlight></codeline>
<codeline lineno="987"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="988"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="989"><highlight class="normal"></highlight></codeline>
<codeline lineno="990"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>exiting<sp/>D0<sp/>power<sp/>state<sp/>to<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>TargetState);</highlight></codeline>
<codeline lineno="991"><highlight class="normal"></highlight></codeline>
<codeline lineno="992"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Get<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="993"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="994"><highlight class="normal"></highlight></codeline>
<codeline lineno="995"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Disable<sp/>device</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="996"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DevicePowered<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="997"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;PowerState<sp/>=<sp/>TargetState;</highlight></codeline>
<codeline lineno="998"><highlight class="normal"></highlight></codeline>
<codeline lineno="999"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>exited<sp/>D0<sp/>power<sp/>state<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="1000"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1001"><highlight class="normal">}</highlight></codeline>
<codeline lineno="1002"><highlight class="normal"></highlight></codeline>
<codeline lineno="1003"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="1004"><highlight class="comment"><sp/>*<sp/>创建设备对象，初始化上下文、接口、符号链接等</highlight></codeline>
<codeline lineno="1005"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1006"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="1007" refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" kindref="member">DeviceCreate</ref>(</highlight></codeline>
<codeline lineno="1008"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver,</highlight></codeline>
<codeline lineno="1009"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a57aff078ae347e96d32d9f9f76b00fb2" kindref="member">PDEVICE_INIT_CONFIG</ref><sp/>InitConfig,</highlight></codeline>
<codeline lineno="1010"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref>*<sp/>Device</highlight></codeline>
<codeline lineno="1011"><highlight class="normal">)</highlight></codeline>
<codeline lineno="1012"><highlight class="normal">{</highlight></codeline>
<codeline lineno="1013"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1014"><highlight class="normal"><sp/><sp/><sp/><sp/>PWDFDEVICE_INIT<sp/>deviceInit<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="1015"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="1016"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>deviceAttributes;</highlight></codeline>
<codeline lineno="1017"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_PNPPOWER_EVENT_CALLBACKS<sp/>pnpPowerCallbacks;</highlight></codeline>
<codeline lineno="1018"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="1019"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>);</highlight></codeline>
<codeline lineno="1020"><highlight class="normal"></highlight></codeline>
<codeline lineno="1021"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Creating<sp/>device<sp/>object&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="1022"><highlight class="normal"></highlight></codeline>
<codeline lineno="1023"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>device<sp/>init<sp/>structure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1024"><highlight class="normal"><sp/><sp/><sp/><sp/>deviceInit<sp/>=<sp/>WdfDeviceInitAllocate(Driver);</highlight></codeline>
<codeline lineno="1025"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(deviceInit<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="1026"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfDeviceInitAllocate<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/>STATUS_INSUFFICIENT_RESOURCES);</highlight></codeline>
<codeline lineno="1027"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INSUFFICIENT_RESOURCES;</highlight></codeline>
<codeline lineno="1028"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1029"><highlight class="normal"></highlight></codeline>
<codeline lineno="1030"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>PnP<sp/>power<sp/>event<sp/>callbacks</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1031"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_PNPPOWER_EVENT_CALLBACKS_INIT(&amp;pnpPowerCallbacks);</highlight></codeline>
<codeline lineno="1032"><highlight class="normal"><sp/><sp/><sp/><sp/>pnpPowerCallbacks.EvtDevicePrepareHardware<sp/>=<sp/><ref refid="device__manager_8h_1a4fda7ae13ae040932bf046e74f5bb8a6" kindref="member">DevicePrepareHardware</ref>;</highlight></codeline>
<codeline lineno="1033"><highlight class="normal"><sp/><sp/><sp/><sp/>pnpPowerCallbacks.EvtDeviceReleaseHardware<sp/>=<sp/><ref refid="device__manager_8h_1a20074db76287e7b7a18bc2f2c83c2c61" kindref="member">DeviceReleaseHardware</ref>;</highlight></codeline>
<codeline lineno="1034"><highlight class="normal"><sp/><sp/><sp/><sp/>pnpPowerCallbacks.EvtDeviceD0Entry<sp/>=<sp/><ref refid="device__manager_8h_1a51a0fa7bdbb22680e79efaa2cacbb729" kindref="member">DeviceD0Entry</ref>;</highlight></codeline>
<codeline lineno="1035"><highlight class="normal"><sp/><sp/><sp/><sp/>pnpPowerCallbacks.EvtDeviceD0Exit<sp/>=<sp/><ref refid="device__manager_8h_1a1625e40d469a21ece1cad244736fbe81" kindref="member">DeviceD0Exit</ref>;</highlight></codeline>
<codeline lineno="1036"><highlight class="normal"></highlight></codeline>
<codeline lineno="1037"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Set<sp/>PnP<sp/>power<sp/>event<sp/>callbacks</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1038"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfDeviceInitSetPnpPowerEventCallbacks(deviceInit,<sp/>&amp;pnpPowerCallbacks);</highlight></codeline>
<codeline lineno="1039"><highlight class="normal"></highlight></codeline>
<codeline lineno="1040"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>device<sp/>attributes</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1041"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&amp;deviceAttributes,<sp/><ref refid="device__manager_8h_1a5fe592117cc2296f7e78acab9055f526" kindref="member">DEVICE_CONTEXT</ref>);</highlight></codeline>
<codeline lineno="1042"><highlight class="normal"></highlight></codeline>
<codeline lineno="1043"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Create<sp/>device<sp/>object</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1044"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfDeviceCreate(</highlight></codeline>
<codeline lineno="1045"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>deviceInit,</highlight></codeline>
<codeline lineno="1046"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;deviceAttributes,</highlight></codeline>
<codeline lineno="1047"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;device</highlight></codeline>
<codeline lineno="1048"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="1049"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1050"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfDeviceCreate<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="1051"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfDeviceInitFree(deviceInit);</highlight></codeline>
<codeline lineno="1052"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1053"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1054"><highlight class="normal"></highlight></codeline>
<codeline lineno="1055"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initialize<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1056"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" kindref="member">DeviceInitContext</ref>(device,<sp/>InitConfig);</highlight></codeline>
<codeline lineno="1057"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1058"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;DeviceInitContext<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="1059"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1060"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1061"><highlight class="normal"></highlight></codeline>
<codeline lineno="1062"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Create<sp/>device<sp/>interface</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1063"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kindref="member">DeviceInterfaceGuid</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="1064"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfDeviceCreateDeviceInterface(</highlight></codeline>
<codeline lineno="1065"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>device,</highlight></codeline>
<codeline lineno="1066"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kindref="member">DeviceInterfaceGuid</ref>,</highlight></codeline>
<codeline lineno="1067"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL</highlight></codeline>
<codeline lineno="1068"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="1069"><highlight class="normal"></highlight></codeline>
<codeline lineno="1070"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1071"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfDeviceCreateDeviceInterface<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="1072"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1073"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1074"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1075"><highlight class="normal"></highlight></codeline>
<codeline lineno="1076"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Create<sp/>symbolic<sp/>link</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1077"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1aca71f0560489dd375ac4baa0e0b35dd7" kindref="member">SymbolicLinkName</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="1078"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfDeviceCreateSymbolicLink(</highlight></codeline>
<codeline lineno="1079"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>device,</highlight></codeline>
<codeline lineno="1080"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InitConfig-&gt;<ref refid="struct__DEVICE__INIT__CONFIG_1aca71f0560489dd375ac4baa0e0b35dd7" kindref="member">SymbolicLinkName</ref></highlight></codeline>
<codeline lineno="1081"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="1082"><highlight class="normal"></highlight></codeline>
<codeline lineno="1083"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1084"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;WdfDeviceCreateSymbolicLink<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="1085"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1086"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1087"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1088"><highlight class="normal"></highlight></codeline>
<codeline lineno="1089"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Configure<sp/>I/O<sp/>queue</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1090"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" kindref="member">DeviceConfigureIoQueue</ref>(device);</highlight></codeline>
<codeline lineno="1091"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1092"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;DeviceConfigureIoQueue<sp/>failed,<sp/>status:<sp/>0x%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="1093"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1094"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1095"><highlight class="normal"></highlight></codeline>
<codeline lineno="1096"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Return<sp/>device<sp/>object</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1097"><highlight class="normal"><sp/><sp/><sp/><sp/>*Device<sp/>=<sp/>device;</highlight></codeline>
<codeline lineno="1098"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Device<sp/>object<sp/>created<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="1099"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="1100"><highlight class="normal">}</highlight></codeline>
<codeline lineno="1101"><highlight class="normal"></highlight></codeline>
<codeline lineno="1102" refid="device__manager_8c_1ae47038053b18948b8fc9579f5a785cf4" refkind="member"><highlight class="normal"></highlight><highlight class="keywordtype">void</highlight><highlight class="normal"><sp/><ref refid="device__manager_8c_1ae47038053b18948b8fc9579f5a785cf4" kindref="member">DeviceManager_EvtDeviceContextCleanup</ref>(</highlight></codeline>
<codeline lineno="1103"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="1104"><highlight class="normal">)<sp/>{</highlight></codeline>
<codeline lineno="1105"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(Device);</highlight></codeline>
<codeline lineno="1106"><highlight class="normal"></highlight></codeline>
<codeline lineno="1107"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Example<sp/>of<sp/>WDF<sp/>function<sp/>usage</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1108"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WdfInterruptDelete(deviceContext-&gt;Interrupt);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1109"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WdfDeviceInitAllocate(Driver);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1110"><highlight class="normal">}</highlight></codeline>
<codeline lineno="1111"><highlight class="normal"></highlight></codeline>
<codeline lineno="1112"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="1113" refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" refkind="member"><highlight class="normal"><ref refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" kindref="member">DeviceManager_EvtDeviceAdd</ref>(</highlight></codeline>
<codeline lineno="1114"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver,</highlight></codeline>
<codeline lineno="1115"><highlight class="normal"><sp/><sp/><sp/><sp/>PWDFDEVICE_INIT<sp/>DeviceInit</highlight></codeline>
<codeline lineno="1116"><highlight class="normal">)<sp/>{</highlight></codeline>
<codeline lineno="1117"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1118"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device;</highlight></codeline>
<codeline lineno="1119"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kindref="member">PDEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>;</highlight></codeline>
<codeline lineno="1120"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>deviceAttributes;<sp/></highlight><highlight class="comment">//<sp/>声明<sp/>WDF_OBJECT_ATTRIBUTES<sp/>变量</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1121"><highlight class="normal"></highlight></codeline>
<codeline lineno="1122"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Set<sp/>device<sp/>attributes</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1123"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(</highlight></codeline>
<codeline lineno="1124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;deviceAttributes,</highlight></codeline>
<codeline lineno="1125"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="device__manager_8h_1a5fe592117cc2296f7e78acab9055f526" kindref="member">DEVICE_CONTEXT</ref></highlight></codeline>
<codeline lineno="1126"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="1127"><highlight class="normal"></highlight></codeline>
<codeline lineno="1128"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Create<sp/>the<sp/>device</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1129"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfDeviceCreate(</highlight></codeline>
<codeline lineno="1130"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceInit,</highlight></codeline>
<codeline lineno="1131"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;deviceAttributes,</highlight></codeline>
<codeline lineno="1132"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;device</highlight></codeline>
<codeline lineno="1133"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="1134"><highlight class="normal"></highlight></codeline>
<codeline lineno="1135"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="1136"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1137"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="1138"><highlight class="normal"></highlight></codeline>
<codeline lineno="1139"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Store<sp/>the<sp/>device<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1140"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetDeviceContext(device);</highlight></codeline>
<codeline lineno="1141"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>device;</highlight></codeline>
<codeline lineno="1142"><highlight class="normal"></highlight></codeline>
<codeline lineno="1143"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Return<sp/>success</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="1144"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="1145"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/core/device/device_manager.c"/>
  </compounddef>
</doxygen>
