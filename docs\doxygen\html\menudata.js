/*
 @licstart  The following is the entire license notice for the JavaScript code in this file.

 The MIT License (MIT)

 Copyright (C) 1997-2020 by <PERSON>

 Permission is hereby granted, free of charge, to any person obtaining a copy of this software
 and associated documentation files (the "Software"), to deal in the Software without restriction,
 including without limitation the rights to use, copy, modify, merge, publish, distribute,
 sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all copies or
 substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
 DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

 @licend  The above is the entire license notice for the JavaScript code in this file
*/
var menudata={children:[
{text:"Main Page",url:"index.html"},
{text:"Classes",url:"annotated.html",children:[
{text:"Class List",url:"annotated.html"},
{text:"Class Index",url:"classes.html"},
{text:"Class Members",url:"functions.html",children:[
{text:"All",url:"functions.html",children:[
{text:"a",url:"functions.html#index_a"},
{text:"b",url:"functions_b.html#index_b"},
{text:"c",url:"functions_c.html#index_c"},
{text:"d",url:"functions_d.html#index_d"},
{text:"e",url:"functions_e.html#index_e"},
{text:"f",url:"functions_f.html#index_f"},
{text:"g",url:"functions_g.html#index_g"},
{text:"h",url:"functions_h.html#index_h"},
{text:"i",url:"functions_i.html#index_i"},
{text:"l",url:"functions_l.html#index_l"},
{text:"m",url:"functions_m.html#index_m"},
{text:"o",url:"functions_o.html#index_o"},
{text:"p",url:"functions_p.html#index_p"},
{text:"r",url:"functions_r.html#index_r"},
{text:"s",url:"functions_s.html#index_s"},
{text:"t",url:"functions_t.html#index_t"},
{text:"u",url:"functions_u.html#index_u"},
{text:"v",url:"functions_v.html#index_v"},
{text:"w",url:"functions_w.html#index_w"},
{text:"x",url:"functions_x.html#index_x"},
{text:"y",url:"functions_y.html#index_y"}]},
{text:"Functions",url:"functions_func.html"},
{text:"Variables",url:"functions_vars.html",children:[
{text:"a",url:"functions_vars.html#index_a"},
{text:"b",url:"functions_vars_b.html#index_b"},
{text:"c",url:"functions_vars_c.html#index_c"},
{text:"d",url:"functions_vars_d.html#index_d"},
{text:"e",url:"functions_vars_e.html#index_e"},
{text:"f",url:"functions_vars_f.html#index_f"},
{text:"g",url:"functions_vars_g.html#index_g"},
{text:"h",url:"functions_vars_h.html#index_h"},
{text:"i",url:"functions_vars_i.html#index_i"},
{text:"l",url:"functions_vars_l.html#index_l"},
{text:"m",url:"functions_vars_m.html#index_m"},
{text:"o",url:"functions_vars_o.html#index_o"},
{text:"p",url:"functions_vars_p.html#index_p"},
{text:"r",url:"functions_vars_r.html#index_r"},
{text:"s",url:"functions_vars_s.html#index_s"},
{text:"t",url:"functions_vars_t.html#index_t"},
{text:"u",url:"functions_vars_u.html#index_u"},
{text:"v",url:"functions_vars_v.html#index_v"},
{text:"w",url:"functions_vars_w.html#index_w"},
{text:"x",url:"functions_vars_x.html#index_x"},
{text:"y",url:"functions_vars_y.html#index_y"}]}]}]},
{text:"Files",url:"files.html",children:[
{text:"File List",url:"files.html"},
{text:"File Members",url:"globals.html",children:[
{text:"All",url:"globals.html",children:[
{text:"_",url:"globals.html#index__5F"},
{text:"a",url:"globals_a.html#index_a"},
{text:"b",url:"globals_b.html#index_b"},
{text:"c",url:"globals_c.html#index_c"},
{text:"d",url:"globals_d.html#index_d"},
{text:"e",url:"globals_e.html#index_e"},
{text:"f",url:"globals_f.html#index_f"},
{text:"g",url:"globals_g.html#index_g"},
{text:"h",url:"globals_h.html#index_h"},
{text:"i",url:"globals_i.html#index_i"},
{text:"k",url:"globals_k.html#index_k"},
{text:"l",url:"globals_l.html#index_l"},
{text:"m",url:"globals_m.html#index_m"},
{text:"n",url:"globals_n.html#index_n"},
{text:"o",url:"globals_o.html#index_o"},
{text:"p",url:"globals_p.html#index_p"},
{text:"r",url:"globals_r.html#index_r"},
{text:"s",url:"globals_s.html#index_s"},
{text:"t",url:"globals_t.html#index_t"},
{text:"w",url:"globals_w.html#index_w"}]},
{text:"Functions",url:"globals_func.html",children:[
{text:"b",url:"globals_func.html#index_b"},
{text:"c",url:"globals_func_c.html#index_c"},
{text:"d",url:"globals_func_d.html#index_d"},
{text:"e",url:"globals_func_e.html#index_e"},
{text:"f",url:"globals_func_f.html#index_f"},
{text:"g",url:"globals_func_g.html#index_g"},
{text:"h",url:"globals_func_h.html#index_h"},
{text:"i",url:"globals_func_i.html#index_i"},
{text:"l",url:"globals_func_l.html#index_l"},
{text:"p",url:"globals_func_p.html#index_p"},
{text:"r",url:"globals_func_r.html#index_r"},
{text:"s",url:"globals_func_s.html#index_s"},
{text:"w",url:"globals_func_w.html#index_w"}]},
{text:"Variables",url:"globals_vars.html",children:[
{text:"_",url:"globals_vars.html#index__5F"},
{text:"a",url:"globals_vars.html#index_a"},
{text:"c",url:"globals_vars.html#index_c"},
{text:"d",url:"globals_vars.html#index_d"},
{text:"e",url:"globals_vars.html#index_e"},
{text:"f",url:"globals_vars.html#index_f"},
{text:"g",url:"globals_vars.html#index_g"},
{text:"h",url:"globals_vars.html#index_h"},
{text:"i",url:"globals_vars.html#index_i"},
{text:"l",url:"globals_vars.html#index_l"},
{text:"o",url:"globals_vars.html#index_o"},
{text:"p",url:"globals_vars.html#index_p"},
{text:"r",url:"globals_vars.html#index_r"},
{text:"s",url:"globals_vars.html#index_s"},
{text:"t",url:"globals_vars.html#index_t"},
{text:"w",url:"globals_vars.html#index_w"}]},
{text:"Typedefs",url:"globals_type.html",children:[
{text:"b",url:"globals_type.html#index_b"},
{text:"c",url:"globals_type.html#index_c"},
{text:"d",url:"globals_type.html#index_d"},
{text:"e",url:"globals_type.html#index_e"},
{text:"g",url:"globals_type.html#index_g"},
{text:"h",url:"globals_type.html#index_h"},
{text:"i",url:"globals_type.html#index_i"},
{text:"k",url:"globals_type.html#index_k"},
{text:"l",url:"globals_type.html#index_l"},
{text:"p",url:"globals_type.html#index_p"},
{text:"s",url:"globals_type.html#index_s"},
{text:"t",url:"globals_type.html#index_t"},
{text:"w",url:"globals_type.html#index_w"}]},
{text:"Enumerations",url:"globals_enum.html"},
{text:"Enumerator",url:"globals_eval.html",children:[
{text:"b",url:"globals_eval.html#index_b"},
{text:"d",url:"globals_eval.html#index_d"},
{text:"g",url:"globals_eval.html#index_g"},
{text:"h",url:"globals_eval.html#index_h"},
{text:"i",url:"globals_eval.html#index_i"},
{text:"l",url:"globals_eval.html#index_l"},
{text:"s",url:"globals_eval.html#index_s"},
{text:"w",url:"globals_eval.html#index_w"}]},
{text:"Macros",url:"globals_defs.html",children:[
{text:"_",url:"globals_defs.html#index__5F"},
{text:"a",url:"globals_defs.html#index_a"},
{text:"d",url:"globals_defs.html#index_d"},
{text:"e",url:"globals_defs.html#index_e"},
{text:"f",url:"globals_defs.html#index_f"},
{text:"g",url:"globals_defs.html#index_g"},
{text:"h",url:"globals_defs.html#index_h"},
{text:"i",url:"globals_defs.html#index_i"},
{text:"k",url:"globals_defs.html#index_k"},
{text:"l",url:"globals_defs.html#index_l"},
{text:"m",url:"globals_defs.html#index_m"},
{text:"n",url:"globals_defs.html#index_n"},
{text:"o",url:"globals_defs.html#index_o"},
{text:"r",url:"globals_defs.html#index_r"},
{text:"s",url:"globals_defs.html#index_s"},
{text:"w",url:"globals_defs.html#index_w"}]}]}]}]}
