<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/precomp.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('precomp_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">precomp.h</div></div>
</div><!--header-->
<div class="contents">
<a href="precomp_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">// Precompiled header for KMDFDriver1</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span> </div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="preprocessor">#ifndef _PRECOMP_H_</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="preprocessor">#define _PRECOMP_H_</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span> </div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="preprocessor">#pragma once</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span> </div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment">// 确保正确的 Windows 目标版本</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#ifndef WINVER</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno"><a class="line" href="precomp_8h.html#a966cd377b9f3fdeb1432460c33352af1">   10</a></span><span class="preprocessor">#define WINVER 0x0A00           </span><span class="comment">// Windows 10</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span> </div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="preprocessor">#ifndef _WIN32_WINNT</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno"><a class="line" href="precomp_8h.html#ac50762666aa00bd3a4308158510f1748">   14</a></span><span class="preprocessor">#define _WIN32_WINNT 0x0A00     </span><span class="comment">// Windows 10</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment">// 标准 Windows 头文件</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span>              <span class="comment">// Windows 驱动程序开发工具包核心头文件</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="preprocessor">#include &lt;wdf.h&gt;</span>                <span class="comment">// Windows 驱动程序框架头文件</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span> </div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment">// 标准库头文件</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="preprocessor">#include &lt;ntstrsafe.h&gt;</span>          <span class="comment">// 安全字符串函数</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#include &lt;wdfusb.h&gt;</span>             <span class="comment">// WDF USB 支持</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &lt;usbspec.h&gt;</span>            <span class="comment">// USB 规范定义</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &lt;usb.h&gt;</span>                <span class="comment">// USB 核心功能</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span> </div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="comment">// KMDF 头文件</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &lt;wdfldr.h&gt;</span>             <span class="comment">// WDF 加载器</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &lt;wdfinstaller.h&gt;</span>        <span class="comment">// WDF 安装程序</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span> </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="comment">// 项目头文件</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#include &quot;<a class="code" href="driver__core_8h.html">../include/core/driver/driver_core.h</a>&quot;</span>    <span class="comment">// 驱动核心功能</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="preprocessor">#include &quot;<a class="code" href="device__manager_8h.html">../include/core/device/device_manager.h</a>&quot;</span>  <span class="comment">// 设备管理</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#include &quot;<a class="code" href="include_2core_2log_2driver__log_8h.html">../include/core/log/driver_log.h</a>&quot;</span>        <span class="comment">// 日志功能</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#include &quot;<a class="code" href="ioctl_8h.html">../include/common/ioctl.h</a>&quot;</span>               <span class="comment">// IOCTL 定义</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span> </div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="comment">// 调试支持</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#if DBG</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#define DEBUG_PRINT(Level, Fmt, ...) \</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">    DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_ERROR_LEVEL, &quot;[%s] &quot; Fmt &quot;\n&quot;, \</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="preprocessor">               __FUNCTION__, ##__VA_ARGS__)</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="precomp_8h.html#a7ba5cb5a943f312e7b50de9cd8ffaf37">   43</a></span><span class="preprocessor">#define DEBUG_PRINT(Level, Fmt, ...) do { } while (0)</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span> </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="comment">// 常用宏</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="precomp_8h.html#abf0924766241e8f46e68e2dcbca9ac5b">   47</a></span><span class="preprocessor">#define ARRAY_SIZE(Array) (sizeof(Array) / sizeof((Array)[0]))</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="precomp_8h.html#a3acffbd305ee72dcd4593c0d8af64a4f">   48</a></span><span class="preprocessor">#define MIN(a, b) (((a) &lt; (b)) ? (a) : (b))</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="precomp_8h.html#afa99ec4acc4ecb2dc3c2d05da15d0e3f">   49</a></span><span class="preprocessor">#define MAX(a, b) (((a) &gt; (b)) ? (a) : (b))</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span> </div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span><span class="comment">// 函数属性</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">   52</a></span><span class="preprocessor">#define IN</span></div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno"><a class="line" href="precomp_8h.html#aec78e7a9e90a406a56f859ee456e8eae">   53</a></span><span class="preprocessor">#define OUT</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno"><a class="line" href="precomp_8h.html#afdff552467cc2d2d5815831e9656cffc">   54</a></span><span class="preprocessor">#define OPTIONAL</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span> </div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span><span class="comment">// 返回值检查宏</span></div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">   57</a></span><span class="preprocessor">#define NT_SUCCESS(Status) ((NTSTATUS)(Status) &gt;= 0)</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno"><a class="line" href="precomp_8h.html#abc91c0b65b29dc8fb94e770e4066e07c">   58</a></span><span class="preprocessor">#define NT_ERROR(Status) ((NTSTATUS)(Status) &lt; 0)</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span> </div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="preprocessor">#endif </span><span class="comment">// _PRECOMP_H_</span></div>
<div class="ttc" id="adevice__manager_8h_html"><div class="ttname"><a href="device__manager_8h.html">device_manager.h</a></div><div class="ttdoc">Brief description.</div></div>
<div class="ttc" id="adriver__core_8h_html"><div class="ttname"><a href="driver__core_8h.html">driver_core.h</a></div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html">driver_log.h</a></div></div>
<div class="ttc" id="aioctl_8h_html"><div class="ttname"><a href="ioctl_8h.html">ioctl.h</a></div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="precomp_8h.html">precomp.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
