<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/precomp.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('precomp_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">precomp.h</div></div>
</div><!--header-->
<div class="contents">
<a href="precomp_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">// Precompiled header for KMDFDriver1</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span> </div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="preprocessor">#ifndef PRECOMP_H</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="preprocessor">#define PRECOMP_H</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span> </div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment">// 首先定义内核模式宏</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno"><a class="line" href="precomp_8h.html#a0c797d6741e18c9edcef19d6049b8254">    7</a></span><span class="preprocessor">#define _KERNEL_MODE_</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno"><a class="line" href="precomp_8h.html#aecf841e6d30b2565cffcb9e35531c7d4">    8</a></span><span class="preprocessor">#define _AMD64_</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno"><a class="line" href="precomp_8h.html#ad1cbc0b24485be6857de50cce7e86e5d">    9</a></span><span class="preprocessor">#define _WIN64</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno"><a class="line" href="precomp_8h.html#a691c3e0b7c53581adffe6c2c00c2d3cf">   10</a></span><span class="preprocessor">#define _X86_</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno"><a class="line" href="precomp_8h.html#ac7bef5d85e3dcd73eef56ad39ffc84a9">   11</a></span><span class="preprocessor">#define WIN32_LEAN_AND_MEAN</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno"><a class="line" href="precomp_8h.html#ac655ab401ed399c26e30292b40ca5dea">   12</a></span><span class="preprocessor">#define WIN32_NO_STATUS</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="preprocessor">#include &lt;windows.h&gt;</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="preprocessor">#undef WIN32_NO_STATUS</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span> </div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment">// 包含 WDK 头文件</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="comment">// 包含 KMDF 头文件</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment">// 包含其他必要的 WDK 头文件</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &lt;ntstrsafe.h&gt;</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &lt;ntintsafe.h&gt;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span> </div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="comment">// 包含项目头文件</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &quot;<a class="code" href="src_2core_2log_2driver__log_8h.html">core/log/driver_log.h</a>&quot;</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &quot;<a class="code" href="error__handling_8h.html">core/error/error_handling.h</a>&quot;</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#include &quot;<a class="code" href="driver__core_8h.html">core/driver/driver_core.h</a>&quot;</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#include &quot;<a class="code" href="device__manager_8h.html">core/device/device_manager.h</a>&quot;</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span> </div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="comment">// 包含 HAL 层头文件</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#include &quot;hal/bus/gpio_core.h&quot;</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#include &quot;hal/bus/i2c_core.h&quot;</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#include &quot;hal/bus/spi_core.h&quot;</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="preprocessor">#include &quot;<a class="code" href="gpio__device_8h.html">hal/devices/gpio_device.h</a>&quot;</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#include &quot;<a class="code" href="i2c__device_8h.html">hal/devices/i2c_device.h</a>&quot;</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#include &quot;<a class="code" href="spi__device_8h.html">hal/devices/spi_device.h</a>&quot;</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="comment">// 最后包含 USB 相关头文件（如果需要）</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><span class="preprocessor">#ifdef _USBDDI_</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="preprocessor">#include &lt;usb.h&gt;</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="preprocessor">#include &lt;usbspec.h&gt;</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="preprocessor">#include &lt;wdfusb.h&gt;</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span> </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="preprocessor">#endif </span><span class="comment">// PRECOMP_H</span></div>
<div class="ttc" id="adevice__manager_8h_html"><div class="ttname"><a href="device__manager_8h.html">device_manager.h</a></div><div class="ttdoc">Brief description.</div></div>
<div class="ttc" id="adriver__core_8h_html"><div class="ttname"><a href="driver__core_8h.html">driver_core.h</a></div></div>
<div class="ttc" id="aerror__handling_8h_html"><div class="ttname"><a href="error__handling_8h.html">error_handling.h</a></div><div class="ttdoc">驱动程序错误处理和断言宏定义</div></div>
<div class="ttc" id="agpio__device_8h_html"><div class="ttname"><a href="gpio__device_8h.html">gpio_device.h</a></div></div>
<div class="ttc" id="ai2c__device_8h_html"><div class="ttname"><a href="i2c__device_8h.html">i2c_device.h</a></div></div>
<div class="ttc" id="aspi__device_8h_html"><div class="ttname"><a href="spi__device_8h.html">spi_device.h</a></div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html">driver_log.h</a></div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="precomp_8h.html">precomp.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
