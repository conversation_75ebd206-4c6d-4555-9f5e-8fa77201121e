{"version": "6.0", "nxVersion": "21.0.4", "pathMappings": {}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "tmp_structure/tools/utils/reorganize_files.py", "hash": "8799389103087372924"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "tests/src/driver/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "tests/unit/device_power.c", "hash": "12389752397870169629"}, {"file": "tests/unit/kmdf_core.c", "hash": "11085129429806464239"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/CompilerIdC.exe", "hash": "1365352937048091686"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "config/templates/basic.txt", "hash": "16933140311407412703"}, {"file": "tmp_structure/tests/unit/spi_interface.c", "hash": "13749364630813119982"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverEntry.md", "hash": "16472842237000653964"}, {"file": "tests/unit/driverlog.c", "hash": "13535373348672446805"}, {"file": "tmp_structure/tests/unit/touchdebug.h", "hash": "7306178602982181008"}, {"file": "build/CMakeFiles/3.31.3/CMakeCXXCompiler.cmake", "hash": "17140870414517345131"}, {"file": "tmp_structure/tests/unit/device_state.c", "hash": "232531793412726154"}, {"file": "tmp_structure/tests/unit/spi_controller.c", "hash": "15313722384938567487"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryCompression.md", "hash": "449121492967900735"}, {"file": "tests/src/utils/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "docs/KMDF Driver1/tmp_structure/include/log/trace.md", "hash": "17217117922386663488"}, {"file": "tests/src/io/IOManager.c", "hash": "6435585653620536882"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_manager.md", "hash": "17123311457972373377"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model_ensemble.md", "hash": "8343451949614003054"}, {"file": "test_volcengine_complete.py", "hash": "17935958284127479142"}, {"file": "tests/unit/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "tests/src/utils/logging/logger_test.c", "hash": "15334138061466012986"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceMonitor.md", "hash": "2312054369463986103"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/tests/src/utils/module_manager.md", "hash": "5061824811813677188"}, {"file": "docs/KMDF Driver1/tests/src/security/error_prevention.md", "hash": "1875309196829316540"}, {"file": "docs/KMDF Driver1/tests/src/driver/Driver.md", "hash": "8867171595501583707"}, {"file": "docs/KMDF Driver1/backup/include/driver/driver_types.md", "hash": "6648445345879484221"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "tmp_structure/tests/unit/auto_tuning.h", "hash": "5228767904912538066"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFLogger.md", "hash": "13534544793886613631"}, {"file": "tmp_structure/tools/analysis/run_enhanced_analysis.ps1", "hash": "12205270835548525724"}, {"file": "find_duplicates.py", "hash": "2445160768485977829"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "tests/src/security/ErrorCore.c", "hash": "8426091125501211666"}, {"file": "docs/KMDF Driver1/backup/src/core/error_handling.md", "hash": "8736094554619853824"}, {"file": "docs/硬件方案设计终极方案.txt", "hash": "15157568784909259289"}, {"file": "tests/unit/hardware.c", "hash": "2307175637427553353"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "321518494769210079"}, {"file": "tests/src/performance/PerformanceData.c", "hash": "10364043397620603240"}, {"file": "tmp_structure/tests/unit/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "tests/performance/performance_benchmark.c", "hash": "2916244813509275032"}, {"file": "test_knowledge_agent.py", "hash": "2899840828343350953"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_resource.md", "hash": "11019102042198787538"}, {"file": "tests/src/utils/device/device_timer.c", "hash": "3835497012896294941"}, {"file": "tests/unit/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "version.h.in", "hash": "13397157829776257934"}, {"file": ".vscode/settings.json", "hash": "1781777465057523696"}, {"file": "tmp_structure/tests/unit/modulemanager.c", "hash": "12705701726995479740"}, {"file": "tests/src/utils/event_buffer.c", "hash": "7571709781436123223"}, {"file": "generatecomments.bat", "hash": "7046739641919960466"}, {"file": "src/hal/devices/spi_device.c.bak", "hash": "6866354232947168482"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/CustomBuild.read.1.tlog", "hash": "11253596291149010805"}, {"file": ".vscode/dev_environment.json", "hash": "12134811363302867853"}, {"file": "tests/src/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "tmp_structure/tests/unit/memorypool.c", "hash": "8309557649383373614"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "tmp_structure/tests/unit/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "tests/src/utils/notification_manager.c", "hash": "16862955048666143712"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "tests/unit/securitymanager.c", "hash": "2211632392311551414"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/types/BaseTypes.md", "hash": "14629461928739236646"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_core.md", "hash": "587477157269630570"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_test.md", "hash": "4783658079732715387"}, {"file": ".vscode/sdv-default.xml", "hash": "10931019705986488358"}, {"file": "docs/KMDF Driver1/tests/src/security/SecurityManager.md", "hash": "6164524214767333503"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "tmp_structure/tools/analysis/report_generator.py", "hash": "7014199262691885841"}, {"file": "docs/KMDF Driver1/tests/src/device/HardwareManager.md", "hash": "4112210358845696142"}, {"file": "tests/unit/memorymanager.h", "hash": "15989478217969481753"}, {"file": "tests/unit/advanced_models.c", "hash": "10802020780109055119"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorRecovery.md", "hash": "677055025807912378"}, {"file": "tests/src/core/GlobalPerformanceManager.c", "hash": "13783737954691746004"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "tests/unit/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAManager.md", "hash": "9127980178123817019"}, {"file": "build/unit_tests.vcxproj.filters", "hash": "10893447708098652453"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_dma.md", "hash": "15203853900872757579"}, {"file": "tests/src/device/GyroHwSim.c", "hash": "15003407868114686644"}, {"file": "tests/unit/touch.c", "hash": "5834024937245580957"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/notification_manager.md", "hash": "15727167061045092053"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_test.md", "hash": "8777324941864887741"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/auto_optimizer.md", "hash": "3883996352235318562"}, {"file": "tests/src/security/error_prevention.c", "hash": "6292741100613388313"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "tests/unit/kmdferror.c", "hash": "3859077427525291268"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "docs/project_md/cursor_machine_id.py.teaching.md", "hash": "7810754778805062073"}, {"file": "README.md", "hash": "11356701142608283317"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/scheduler_optimizer.md", "hash": "3965136730573772049"}, {"file": "tests/src/utils/.gitkeep", "hash": "3244421341483603138"}, {"file": "scripts/quick_check.ps1", "hash": "9636392711934536876"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ReliabilityManager.md", "hash": "8634971326101470578"}, {"file": "tests/unit/gyrohwsim.c", "hash": "9939547639384961965"}, {"file": "tools/scripts/enhanced_code_analyzer.py", "hash": "5763503898948417376"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/GamepadCore.md", "hash": "17415216908876162054"}, {"file": "tmp_structure/tests/performance/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchProcessor.md", "hash": "4070778693111906027"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_ops.md", "hash": "6617533335334583642"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": "docs/KMDF Driver1/tests/src/driver/kmdf_core.md", "hash": "6419799093797329322"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/i2_c_interface.md", "hash": "9147246998176705712"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.command.1.tlog", "hash": "15817397181947717326"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "tests/src/security/ErrorManager.h", "hash": "1005541988791518561"}, {"file": "tmp_structure/tests/unit/simd_ops.c", "hash": "4755770406672099314"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_io.md", "hash": "16194404026375741960"}, {"file": "tmp_structure/tests/unit/monitor.h", "hash": "7267562670448795700"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFI2C.md", "hash": "2401467074656005273"}, {"file": "tmp_structure/tests/unit/simd_optimization.c", "hash": "1016262082760471380"}, {"file": ".vscode/documentation_config.json", "hash": "16120575310560030239"}, {"file": "tmp_structure/tools/build/build_manager.py", "hash": "15492535410479556845"}, {"file": "tmp_structure/tools/utils/core/adaptive_output.py", "hash": "1928394092660785968"}, {"file": "tests/src/device/TouchPerformance.h", "hash": "3643163510723135612"}, {"file": "docs/api/火山方舟API.txt", "hash": "12834483488995527018"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CompilerIdC.lastbuildstate", "hash": "12489794117589609048"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver_core.md", "hash": "15214766627783842292"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.write.1.tlog", "hash": "14117961221003328239"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/trace_core.md", "hash": "14024586508680330554"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/queue_test.md", "hash": "1004855502735791525"}, {"file": "build/CMakeFiles/TargetDirectories.txt", "hash": "10199785005722450232"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "tmp_structure/tools/build/build_driver.ps1", "hash": "17906769147179237377"}, {"file": "tmp_structure/tests/unit/device_queue.c", "hash": "16684379454006678062"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_history.md", "hash": "10843695273737027041"}, {"file": "tmp_structure/tests/unit/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_performance.md", "hash": "3905147999090521122"}, {"file": "tmp_structure/tools/utils/restructure_project.py", "hash": "10519243202187776696"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/event_trace.md", "hash": "14921740565764576536"}, {"file": "docs/KMDF Driver1/backup/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "include/hal/bus/kmdf_bus_common.h", "hash": "4327436346351321104"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/queue.md", "hash": "17411770077844460848"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/event_trace.md", "hash": "12299964648108506000"}, {"file": "tests/unit/gyro.c", "hash": "12598367591223412709"}, {"file": "docs/KMDF Driver1/include/core/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/tests/src/device/GyroHwSim.md", "hash": "16847761403613423154"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery_kmdf.md", "hash": "7623256312282370166"}, {"file": "tests/unit/power_manager.c", "hash": "16887739356335473604"}, {"file": "tests/unit/registry_manager.c", "hash": "7212363665723535550"}, {"file": "tmp_structure/tests/unit/sample_test.c", "hash": "6872651473851085099"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-MinSizeRel-58bf87a980b8a045b764.json", "hash": "16673725507908079927"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_core.md", "hash": "2996160899354651317"}, {"file": "tests/unit/registry_cache.c", "hash": "18036733393600373308"}, {"file": "tmp_structure/tests/unit/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_callback.md", "hash": "1464403380017115903"}, {"file": "tests/unit/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "tests/src/utils/ml/optimizer.cpp", "hash": "8927165647459203052"}, {"file": "include/device/base/device.h", "hash": "11087151227771504575"}, {"file": "tests/unit/hardwaremanager.h", "hash": "1603174420775838200"}, {"file": "touch.c", "hash": "6578032005835634426"}, {"file": "tmp_structure/include/driver/driver_core.h", "hash": "7766847313701610631"}, {"file": "tmp_structure/tests/unit/device.c", "hash": "6161212960231513500"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CMakeCXXCompilerId.cpp", "hash": "18364213766541449008"}, {"file": "tmp_structure/tools/fix/fix_header_guards_improved.py", "hash": "2118443273864971945"}, {"file": "tests/unit/errormanager.c", "hash": "14480009171928608282"}, {"file": "tmp_structure/tools/utils/analyze_project.ps1", "hash": "3670972854219527193"}, {"file": "src/hal/bus/spi_core.c.bak", "hash": "2402126896693520852"}, {"file": "tmp_structure/tools/utils/core/__pycache__/mace_config.cpython-313.pyc", "hash": "4929717637443591073"}, {"file": "tests/src/utils/device/device_state.c", "hash": "232531793412726154"}, {"file": "tests/src/core/resource_management_test.c", "hash": "81080999296386809"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchAI.md", "hash": "939606902265643493"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/telemetry_processor.md", "hash": "8356681802505862867"}, {"file": "tests/unit/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_strategies.md", "hash": "6375213808090771433"}, {"file": "renametosnakecase.bat", "hash": "13623342575885702600"}, {"file": "include/core/log/driver_log.h", "hash": "11882093310528751752"}, {"file": "tmp_structure/output/分析结果.json", "hash": "3415510036615062894"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/PerfMonitor.md", "hash": "7405414634036513776"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_state.md", "hash": "4327613149066213288"}, {"file": "docs/KMDF Driver1/include/device/power/device_power.md", "hash": "2872841184312516318"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchManager.md", "hash": "67499987134114106"}, {"file": "tests/unit/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": "build/version.h", "hash": "15174072068962538387"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_performance.md", "hash": "6815440093307604725"}, {"file": "tests/src/utils/ml/auto_optimizer.cpp", "hash": "8101721435210232709"}, {"file": "build/.cmake/api/v1/reply/target-run-tests-Release-76f6c5e7868abb6d8741.json", "hash": "10486406065451624556"}, {"file": "docs/KMDF Driver1/backup/backup/src/error/error_handler.md", "hash": "130901189025584605"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/CoreManager.md", "hash": "3302314418596781527"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "tmp_structure/tools/utils/dev_toolkit.py", "hash": "8660747273767930454"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_database.md", "hash": "3118559368204033654"}, {"file": "project_cleanup.py", "hash": "13183627259473291690"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/log_manager.md", "hash": "14895641069151458651"}, {"file": "tmp_structure/tools/test/test_e2b.py", "hash": "12009727001778827673"}, {"file": "tmp_structure/tools/test/safe_driver_test.ps1", "hash": "17834237941870637588"}, {"file": "tests/unit/notification_manager.c", "hash": "16862955048666143712"}, {"file": "tests/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/advanced_models.md", "hash": "2138160585059009631"}, {"file": "include/core/error/error_codes.h", "hash": "16962445415915763278"}, {"file": "tests/integration/CMakeLists.txt", "hash": "14784254389476627328"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_handler.md", "hash": "8221316859104640318"}, {"file": "tmp_structure/include/bus/kmdf_spi.h", "hash": "18044188265549547991"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_ops.md", "hash": "13916165421437368796"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery_kmdf.md", "hash": "12638653863570197085"}, {"file": "tests/src/performance/monitor.cpp", "hash": "7364005627501044434"}, {"file": "tests/unit/touchprocessor.c", "hash": "8524256194400325748"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-Debug-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAManager.md", "hash": "9127980178123817019"}, {"file": "tmp_structure/tests/performance/performance_collector.c", "hash": "9157021843784231895"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger.md", "hash": "1309368071159714371"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-RelWithDebInfo-58bf87a980b8a045b764.json", "hash": "16673725507908079927"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_analyzer.md", "hash": "6049406713010296516"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "tmp_structure/tests/unit/touchprocessor.c", "hash": "8524256194400325748"}, {"file": "tools/code_quality_analyzer.py", "hash": "16225249839978784825"}, {"file": "tests/src/security/error_recovery_framework.c", "hash": "15116463532532570605"}, {"file": "test_mace_analysis.py", "hash": "4770203628810889220"}, {"file": "tmp_structure/tools/test/create_test_cert.ps1", "hash": "9282573216776791126"}, {"file": "tests/src/security/ErrorManager.c", "hash": "1036485733612102205"}, {"file": "tmp_structure/tools/fix/fix_build_issues.py", "hash": "12950168642469148032"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "docs/KMDF Driver1/tests/src/core/CoreManager.md", "hash": "5931295104163142810"}, {"file": "tmp_structure/tools/fix/update_includes.py", "hash": "11519157228217203760"}, {"file": "tmp_structure/tools/utils/simple_check.py", "hash": "15682216032295099812"}, {"file": "tests/src/utils/device/device_power.c", "hash": "12389752397870169629"}, {"file": "tests/unit/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "tests/unit/test_framework.c", "hash": "8175968703783348918"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFHAL.md", "hash": "14106586863704401393"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_control.md", "hash": "14823537430827988127"}, {"file": "tmp_structure/tests/unit/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "generatenotes.bat", "hash": "8638447732626674053"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Hardware.md", "hash": "12499629258003032487"}, {"file": "tests/unit/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformancePredictor.md", "hash": "15411689379061262882"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_test.md", "hash": "8777324941864887741"}, {"file": "tmp_structure/tools/test/test_api.py", "hash": "726597173146823267"}, {"file": "config/models/openrouter.json", "hash": "11233378633730705709"}, {"file": ".github/workflows/code-style-check.yml", "hash": "9198763704460426098"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorPrevention.md", "hash": "731783132979183964"}, {"file": "tmp_structure/tools/utils/core/__pycache__/model_adapters.cpython-313.pyc", "hash": "3926203778600644025"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_queue.md", "hash": "10025303424683255249"}, {"file": "tmp_structure/tests/unit/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "src/core/device/device_manager.c", "hash": "10379298258398101428"}, {"file": "tests/src/device/Hardware.h", "hash": "10373285935336127041"}, {"file": "tmp_structure/tests/unit/simd_implementation.h", "hash": "14180874393517690822"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model.md", "hash": "5760546782410447305"}, {"file": "tests/unit/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFUSB.md", "hash": "2444171969419869765"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceLogger.md", "hash": "14724788428389008979"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFHAL.md", "hash": "8895607898594294427"}, {"file": "config/models/mistral.json", "hash": "9872388936552245613"}, {"file": "include/device/io/device_queue.h", "hash": "7815653821389369623"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/spi_interface.md", "hash": "13233617362743644576"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_init_test.md", "hash": "17333699458054262275"}, {"file": "build/CMakeFiles/generate.stamp", "hash": "9089816209918881806"}, {"file": "tmp_structure/tools/utils/verify_dev_env.ps1", "hash": "10167228439969573549"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_analyzer.md", "hash": "6049406713010296516"}, {"file": "docs/KMDF Driver1/tests/src/utils/health_checker.md", "hash": "1198728672636693643"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/device/DeviceTypes.md", "hash": "10281915747584305109"}, {"file": "docs/structured_docs/tutorials/kmdf_入门指南.md", "hash": "11948446666877054333"}, {"file": "tmp_structure/tools/utils/check_line_endings.py", "hash": "12193072142883303704"}, {"file": "docs/KMDF Driver1/backup/include/driver/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "tests/unit/errorcore.c", "hash": "8426091125501211666"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt.md", "hash": "7122344570168608003"}, {"file": "tests/test_quality_controller.py", "hash": "13643594330830895234"}, {"file": "build.bat", "hash": "12097253425592496067"}, {"file": "tmp_structure/tools/utils/core/__pycache__/code_parser.cpython-313.pyc", "hash": "6448863451993605535"}, {"file": "tests/unit/log_manager.c", "hash": "5709293517199487694"}, {"file": "convert_encoding.ps1", "hash": "16993032560780310748"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_queue_1.md", "hash": "14570869599811644805"}, {"file": "docs/KMDF Driver1/tests/src/performance/monitor.md", "hash": "10197168810983286962"}, {"file": ".vscode/alert_rules.json", "hash": "3146142592217095474"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_reporter.md", "hash": "1776717960419419935"}, {"file": "tests/src/utils/device/device_performance_monitor.c", "hash": "9414896522403079228"}, {"file": "tmp_structure/tools/fix/fix_tabs.py", "hash": "16509530680484815031"}, {"file": "docs/修改步骤.md", "hash": "10364683472406199775"}, {"file": "tmp_structure/tests/unit/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "tmp_structure/tools/utils/format.ps1", "hash": "12071962958350408212"}, {"file": "analyze_folder.py", "hash": "7672536776940708803"}, {"file": "tmp_structure/tools/utils/smart_annotator.py", "hash": "18279375395162153177"}, {"file": "tmp_structure/tests/unit/hardware.c", "hash": "2307175637427553353"}, {"file": "tests/src/performance/PerformanceMetricsImpl.cpp", "hash": "1970069259736763262"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "tmp_structure/tools/utils/tidy.ps1", "hash": "15876974776598375532"}, {"file": "tmp_structure/tools/utils/verify_env.py", "hash": "15386892121415965824"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/advanced_models.md", "hash": "12496496022757077985"}, {"file": "tmp_structure/tests/unit/globalvariables.c", "hash": "703525128182974641"}, {"file": "docs/KMDF Driver1/backup/include/core/types/kmdf_types.md", "hash": "11820292990252072646"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Touch.md", "hash": "6473562417806726533"}, {"file": "docs/KMDF Driver1/tests/src/utils/recovery_impl.md", "hash": "2569326196912403502"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/GlobalPerformanceManager.md", "hash": "8455061991712322545"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/PerfMonitor.md", "hash": "7405414634036513776"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/monitor.md", "hash": "12333284298038535705"}, {"file": "tests/src/device/TouchPerformance.c", "hash": "12536186045815100314"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "tmp_structure/tests/unit/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/spi_controller.md", "hash": "7615035126808120337"}, {"file": "docs/KMDF Driver1/backup/include/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "tests/src/core/error_handling_test.c", "hash": "15216857325453098660"}, {"file": "tests/src/utils/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "runprojectoptimization.bat", "hash": "2125495458914620176"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/telemetry_processor.md", "hash": "8356681802505862867"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/distributed_training.md", "hash": "5668726731270982163"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/manager.md", "hash": "12857642143589749316"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_scheduler.md", "hash": "12584940170140586153"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath/x64/Debug/VCTargetsPath.recipe", "hash": "14189227093951361660"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/hal.md", "hash": "6548767525705845893"}, {"file": "tests/__pycache__/test_output_formatter.cpython-314.pyc", "hash": "12340273229223992362"}, {"file": "tests/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "tests/src/utils/MaintenanceManager.c", "hash": "13897789596428706927"}, {"file": "tmp_structure/tools/utils/agents/security_agent.py", "hash": "16177457410736494698"}, {"file": "tmp_structure/tests/unit/device_context.c", "hash": "3876629123896102954"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_prevention.md", "hash": "14119463069258361082"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceAnalyzer.md", "hash": "17860791912881724719"}, {"file": "tmp_structure/output/维护文档.md", "hash": "16577422483792727038"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_framework.md", "hash": "15481924532410518968"}, {"file": "tmp_structure/tests/unit/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "certs/kmdf_driver_dev_cert.cer", "hash": "7785696195850755360"}, {"file": "tmp_structure/tests/unit/dmamanager.c", "hash": "16085011483513293531"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_history.md", "hash": "10843695273737027041"}, {"file": "tmp_structure/tests/unit/kmdferror.c", "hash": "3859077427525291268"}, {"file": "docs/KMDF Driver1/backup/include/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_context.md", "hash": "5783058402535230175"}, {"file": "tests/performance/perfmonitor.c", "hash": "11571150342783768226"}, {"file": "tmp_structure/tools/utils/optimize_project.py", "hash": "13813192560649161115"}, {"file": "tmp_structure/tests/unit/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "tests/src/utils/resource_manager.c", "hash": "157428320305306828"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "tmp_structure/tools/utils/update_includes.py", "hash": "7148404675258928119"}, {"file": "docs/KMDF Driver1/tests/src/security/error_module.md", "hash": "16613471808559261902"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/memory_pool.md", "hash": "1168139887190025561"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_context.md", "hash": "14526931168947945277"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorRecovery.md", "hash": "677055025807912378"}, {"file": "tmp_structure/tools/utils/__pycache__/api_adapter.cpython-313.pyc", "hash": "2956084882020527952"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_state_manager.md", "hash": "1635534176611093163"}, {"file": "configureclean.bat", "hash": "18310326671993069378"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CompilerIdCXX.vcxproj", "hash": "11403924021700003081"}, {"file": "tests/unit/iooptimizer.c", "hash": "11289350191801585883"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/spi_controller.md", "hash": "7615035126808120337"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/power_optimizer.md", "hash": "1361782774390628471"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger_core.md", "hash": "3068037791562044206"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/hardware/KMDFHAL.md", "hash": "18089637407439543636"}, {"file": "docs/project_md/project_structure_full.txt", "hash": "6391899482456897709"}, {"file": "tmp_structure/include/hardware/kmdf_hal.h", "hash": "2499818489678582216"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/resource_optimizer_1.md", "hash": "5677242174374142631"}, {"file": "tmp_structure/tools/utils/ai_deployment_setup.py", "hash": "17399500009219247070"}, {"file": "docs/project_md/fix_header_guards.py.teaching.md", "hash": "7986253460171213892"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_manager.md", "hash": "6803732204210283668"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "docs/project_md/requirements.txt", "hash": "1345609730279446603"}, {"file": "tests/src/utils/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "docs/KMDF Driver1/backup/include/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "tmp_structure/tests/unit/trace_core.c", "hash": "10541209614786999240"}, {"file": "build/test_core.vcxproj.filters", "hash": "1253591125304974559"}, {"file": "tmp_structure/tests/unit/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "tests/unit/power_test.c", "hash": "9102832245856542684"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/GlobalPerformanceManager.md", "hash": "8455061991712322545"}, {"file": ".vs/KMDF Driver1/v17/DocumentLayout.json", "hash": "8378132551731795648"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger_core.md", "hash": "17698932983924238851"}, {"file": "docs/KMDF Driver1/backup/tests/src/io/IOOptimizer.md", "hash": "2425979290851834818"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/Queue.md", "hash": "10162557302537918983"}, {"file": "tmp_structure/tests/unit/device_queue1.c", "hash": "330133164890642531"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAHandler.md", "hash": "3316050523215663600"}, {"file": "docs/KMDF Driver1/backup/include/driver/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAHandler.md", "hash": "3316050523215663600"}, {"file": "tmp_structure/tests/performance/performanceexport.c", "hash": "7332497143218165792"}, {"file": "tmp_structure/tests/performance/simd_performance.c", "hash": "9288036268856686374"}, {"file": "tmp_structure/tools/analysis/html_report_generator.py", "hash": "16765524786776403210"}, {"file": "signwithexistingcert.bat", "hash": "14803017271940057798"}, {"file": "tests/src/utils/device/spi_interface.c", "hash": "13749364630813119982"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "tests/src/memory/DMAOptimizer.c", "hash": "12436293271881128025"}, {"file": "tmp_structure/include/device/device_state_types.h", "hash": "3888403413223082294"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "tmp_structure/tools/analysis/run_analysis.ps1", "hash": "5967366306196400324"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery.md", "hash": "11567773110083975900"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "build/.cmake/api/v1/reply/directory-.-Debug-d0094a50bb2071803777.json", "hash": "2254957343676018481"}, {"file": "docs/project_md/test_annotator.md", "hash": "11268776145223411133"}, {"file": "templates/expert_prompt.json", "hash": "15278861292494340274"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/driver_types.md", "hash": "7096294206536811380"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_i2c.md", "hash": "7356725021734401791"}, {"file": "docs/api/api2_key.txt", "hash": "299278678968192926"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device.md", "hash": "7894195249420829021"}, {"file": "tests/src/utils/optimization/auto_tuning.h", "hash": "1159516589502636342"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/queue.md", "hash": "11907408096764236543"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/MaintenanceManager.md", "hash": "5703461401317863092"}, {"file": "build/format-code.vcxproj.filters", "hash": "12227384943922075735"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceMetricsImpl.md", "hash": "6718369574091188526"}, {"file": "tests/unit/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.exe.recipe", "hash": "10075652962856881769"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFError.md", "hash": "2086467841528888470"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "tmp_structure/tests/unit/touchdebug.c", "hash": "3448456818563192507"}, {"file": "cmake/checks.cmake", "hash": "2668118663152144359"}, {"file": "tests/unit/driver_includes.h", "hash": "17360877510984532985"}, {"file": "build/CMakeFiles/3.31.3/CMakeDetermineCompilerABI_CXX.bin", "hash": "12343371425551065701"}, {"file": "docs/KMDF Driver1/tmp_structure/src/error/error_handling.md", "hash": "12651407806898763387"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/SecurityManager.md", "hash": "3884452379323183165"}, {"file": "build/run_tests.vcxproj", "hash": "17049647273496337604"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_core.md", "hash": "4962745022155368642"}, {"file": "build/.cmake/api/v1/reply/cmakeFiles-v1-383dc5c8757e6360a1da.json", "hash": "10814953175081002875"}, {"file": "include/core/types/CommonTypes.h", "hash": "11814470043667503803"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath.txt", "hash": "11217971592272304290"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CMakeCXXCompilerId.obj", "hash": "2046843756746274496"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "docs/KMDF Driver1/backup/include/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "tmp_structure/src/driver/driverlog.c", "hash": "16668896646093452722"}, {"file": "tests/src/utils/interrupt/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/CoreManager.md", "hash": "3302314418596781527"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_manager_1.md", "hash": "5492128948262595846"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "tmp_structure/tests/unit/kmdfhal.c", "hash": "13443276317294174339"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/trace.md", "hash": "12676781890278582523"}, {"file": "tmp_structure/tools/test/test_load_driver.ps1", "hash": "4841543805499648601"}, {"file": "docs/KMDF Driver1/tests/src/utils/Predictor.md", "hash": "16900082115977006923"}, {"file": "docs/project_md/search_results.txt", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/include/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_executor.md", "hash": "14361540514063420667"}, {"file": "tmp_structure/src/error/error_handling.c", "hash": "11901117793078601406"}, {"file": "docs/api/wolfarmAPI.txt", "hash": "6531473657264747639"}, {"file": "tests/unit/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "tests/unit/trace.h", "hash": "7325021250740089223"}, {"file": "build/KMDFDriver1.vcxproj.filters", "hash": "17770543467951650106"}, {"file": "docs/KMDF Driver1/backup/include/driver/Driver.md", "hash": "3073102130935575849"}, {"file": "include/hal/bus/kmdf_i2c.h", "hash": "9544153363692164388"}, {"file": "tests/src/utils/simd/simd_ops.c", "hash": "4755770406672099314"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_scheduler.md", "hash": "4149850547689728390"}, {"file": "docs/KMDF Driver1/tests/src/utils/GamepadCore.md", "hash": "8501127116693419912"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/scheduler_optimizer.md", "hash": "3965136730573772049"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/cache_optimizer.md", "hash": "4292989671843767456"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/io/IOManager.md", "hash": "12962392107439165305"}, {"file": "src/core/driver/driver_core.c", "hash": "9838208723867973961"}, {"file": "build/CMakeFiles/3.31.3/CMakeRCCompiler.cmake", "hash": "7478422761629706063"}, {"file": "tests/unit/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_collector.md", "hash": "14684244266011149021"}, {"file": "tmp_structure/tests/unit/kmdfusb.c", "hash": "3804768512383554498"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/GlobalVariables.md", "hash": "3478961572820031987"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/auto_optimizer.md", "hash": "11758342447071272246"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_analyzer.md", "hash": "12003155831058412718"}, {"file": "include/device/io/device_io.h", "hash": "10250326044456562543"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-RelWithDebInfo-64ed55b4407a5d569bc0.json", "hash": "5329372131228871393"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_analyzer.md", "hash": "3484574314302268365"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_monitor.md", "hash": "16894954371874134391"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_core.md", "hash": "1723612259800373307"}, {"file": "tests/src/utils/device/spi_controller.c", "hash": "9569065947463762991"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFError.md", "hash": "9994384953078924681"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "tmp_structure/tests/unit/prediction_context.c", "hash": "8771136116938717181"}, {"file": "tmp_structure/tools/utils/driver_manager.ps1", "hash": "14443047261930491096"}, {"file": ".github/workflows/ci_tests.yml", "hash": "6228437175284659019"}, {"file": "build/CMakeFiles/3.31.3/CMakeSystem.cmake", "hash": "7909334668730673541"}, {"file": "tests/src/security/error_recovery.c", "hash": "3264091201689155259"}, {"file": "tests/src/utils/ml/prediction_core.c", "hash": "1857614276287916371"}, {"file": "tmp_structure/tests/unit/log_manager.c", "hash": "5709293517199487694"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_database.md", "hash": "3898663799363477466"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handler.md", "hash": "12359971319317369078"}, {"file": "tmp_structure/tools/utils/sign_driver_ps.ps1", "hash": "3768153872774026849"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "tmp_structure/src/driver/driver.c", "hash": "4929818876129192855"}, {"file": "tmp_structure/tests/unit/dmaoptimizer.c", "hash": "12446589818635954550"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "docs/KMDF Driver1/tests/src/test_main.md", "hash": "5037830315100233995"}, {"file": "analyze_dependencies.py", "hash": "8993602809188413502"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFError.md", "hash": "2086467841528888470"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_analyzer.md", "hash": "12003155831058412718"}, {"file": "tests/unit/maintenancemanager.c", "hash": "13897789596428706927"}, {"file": "tmp_structure/tests/unit/optimization_database.c", "hash": "1382797123011915962"}, {"file": "tmp_structure/tests/unit/error_recovery.c", "hash": "6818450905492757794"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_usb.md", "hash": "1408341058858284475"}, {"file": "docs/project_md/driver_testing_guide.md", "hash": "14028189855036050473"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.read.1.tlog", "hash": "18152747188165060222"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/resource_optimizer.md", "hash": "13332246610626941843"}, {"file": "tests/src/device/TouchController.c", "hash": "8555681626782575179"}, {"file": "tmp_structure/tests/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": ".vscode/performance_test_config.json", "hash": "4503311343993568100"}, {"file": "docs/硬件方案设计.txt", "hash": "5039807137852333625"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/driver_core.md", "hash": "2981211088971537176"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_i2c.md", "hash": "4279440534981425318"}, {"file": "tests/src/performance/performance_analyzer.h", "hash": "2328266082764694390"}, {"file": "docs/project_md/ai_deployment_setup.py.teaching.md", "hash": "17306184925241801519"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_executor.md", "hash": "8670951633232470291"}, {"file": "tests/src/security/ErrorRecovery.c", "hash": "7122396928859668049"}, {"file": ".vscode/ci_config.json", "hash": "11090209362633637534"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/memory_manager.md", "hash": "10511834971114926523"}, {"file": "fix_unit_tests_filenames.bat", "hash": "3664211143947467761"}, {"file": "docs/KMDF Driver1/backup/include/driver/queue.md", "hash": "17411770077844460848"}, {"file": "docs/KMDF Driver1/backup/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "tmp_structure/tests/unit/touchai.c", "hash": "6072030623807491314"}, {"file": "tests/unit/logger_core.c", "hash": "5596205493550234397"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/interactive_agent.cpython-313.pyc", "hash": "13970068067423626862"}, {"file": "docs/KMDF Driver1/include/device/io/device_queue.md", "hash": "10025303424683255249"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_io.md", "hash": "15280929294455826365"}, {"file": "tmp_structure/tools/utils/enhanced_analyzer.py", "hash": "1750511501545068280"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "tmp_structure/tests/unit/device_dma.c", "hash": "7826498135188615291"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/cache_optimizer.md", "hash": "16401685227458206285"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ConfigManager.md", "hash": "14798362301014302758"}, {"file": "docs/KMDF Driver1/backup/include/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "tmp_structure/tests/unit/test_reporter.c", "hash": "15868353065156172044"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_manager.md", "hash": "6803732204210283668"}, {"file": "build/run_tests.vcxproj.filters", "hash": "15389693572654091318"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "tests/src/device/Gyro.c", "hash": "11748498765099230555"}, {"file": "tmp_structure/tools/utils/header_repair.py", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Gyro.md", "hash": "11459815508399627952"}, {"file": "docs/KMDF Driver1/tests/src/utils/config_group.md", "hash": "10471112854900039136"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_context.md", "hash": "14526931168947945277"}, {"file": "tests/src/utils/Queue.c", "hash": "4795497370184670599"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/GyroHwSim.md", "hash": "1957359040119195426"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_monitor.md", "hash": "439363998595416534"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_performance_monitor.md", "hash": "5561470136532144482"}, {"file": "tests/src/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "tmp_structure/tools/fix/fix_code_style.py", "hash": "9125106425754353024"}, {"file": "tests/unit/device_manager.c", "hash": "1823201296405534462"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_i2c.md", "hash": "12150332390307086755"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/kmdf_core.md", "hash": "14537529512716125567"}, {"file": ".vs/KMDF Driver1/v17/DocumentLayout.backup.json", "hash": "8378132551731795648"}, {"file": "configure.bat", "hash": "696193800616722376"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_executor.md", "hash": "8670951633232470291"}, {"file": "tmp_structure/test_driver_code.py", "hash": "8892117878506912793"}, {"file": "docs/project_md/generate_driver_comments.py.teaching.md", "hash": "1396230862035816761"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFUSB.md", "hash": "3480578702525265059"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_resource.md", "hash": "474047599558141070"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/Predictor.md", "hash": "10386180171614306631"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.recipe", "hash": "367578897923677545"}, {"file": "tests/src/utils/power/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "include/driver/log/DriverLog.h", "hash": "10436857440106258735"}, {"file": "tests/src/driver/DriverLog.c", "hash": "13535373348672446805"}, {"file": "tests/__pycache__/test_config_loader.cpython-314.pyc", "hash": "17131360598512678191"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "build/Testing/20250522-0740/Test.xml", "hash": "11748138237215281965"}, {"file": "tmp_structure/tests/unit/queue_test.c", "hash": "665738556786491367"}, {"file": "src/core/driver/driver_entry.c", "hash": "17997000647826761057"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/trace.md", "hash": "938798632008336272"}, {"file": "docs/api/gemini.txt", "hash": "14621487865774973161"}, {"file": "tmp_structure/tools/utils/ai_collaboration_process.py", "hash": "12273776964799610497"}, {"file": "docs/KMDF Driver1/tests/src/security/error_core.md", "hash": "12071042476686428773"}, {"file": "docs/project_md/project_ideas.md", "hash": "1186718690124445127"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handling.md", "hash": "8736094554619853824"}, {"file": "tests/src/utils/module_manager.c", "hash": "6226471683647205323"}, {"file": "tests/unit/model.c", "hash": "13330488231876145168"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchPerformance.md", "hash": "3331360388054379050"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_framework.md", "hash": "4745469584451480626"}, {"file": "tests/src/memory/DMAManager.h", "hash": "18121240782498049197"}, {"file": "docs/MCP使用方法/sequential_thinking_mcp使用指南.md", "hash": "14143681468153017152"}, {"file": "tests/src/utils/ml/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "tmp_structure/tools/fix/fix_file_format.ps1", "hash": "6779604317763307623"}, {"file": "start_mcp_servers.bat", "hash": "10276851506467346063"}, {"file": "tests/src/utils/trace/log_manager.h", "hash": "15729901125985167955"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_manager.md", "hash": "14138160186724125070"}, {"file": "tests/unit/queueoperations.c", "hash": "8016806957506324853"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_callback.md", "hash": "817082525321811673"}, {"file": "tests/unit/test_main.c", "hash": "17856805622170871630"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFSPI.md", "hash": "18131184567748303267"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorCore.md", "hash": "1340757887149976797"}, {"file": "scripts/fix_tabs.ps1", "hash": "7234465746255906538"}, {"file": "test_volcengine_sdk.py", "hash": "4563110209721761775"}, {"file": "tests/performance/performance_monitor_manager.c", "hash": "9008144578910281466"}, {"file": ".editorconfig", "hash": "16188607900808665775"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_control.md", "hash": "14823537430827988127"}, {"file": "tmp_structure/tests/unit/notification_manager.c", "hash": "16862955048666143712"}, {"file": "tests/unit/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "tests/src/utils/device/device_test.c", "hash": "4283402855535051437"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "docs/KMDF Driver1/include/device/base/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "tests/src/utils/test/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "tmp_structure/tests/performance/device_performance_monitor.c", "hash": "11916941423559648567"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_manager.md", "hash": "17231002221847300177"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "docs/project_md/ai_optimization_strategies.py.teaching.md", "hash": "12681679724514468251"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "config/models/wolframalpha.json", "hash": "754122350504641271"}, {"file": "certs/.gitkeep", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_model.md", "hash": "17020077241309144761"}, {"file": ".vs/KMDF Driver1/FileContentIndex/81110e3d-8f3e-43f4-959a-2c0399269fac.vsidx", "hash": "10995894794753696794"}, {"file": "tests/unit/device_queue.c", "hash": "16684379454006678062"}, {"file": "tests/unit/touchpredictor.c", "hash": "15043340538585148217"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverCore.md", "hash": "11754590676982575203"}, {"file": ".vscode/test_config.json", "hash": "10963637313950704576"}, {"file": "docs/KMDF Driver1/tests/src/utils/PerfMonitor.md", "hash": "1661169831681127515"}, {"file": "tests/src/utils/device/device_workitem.c", "hash": "15964808878924499639"}, {"file": "tmp_structure/tests/unit/kmdflogger.c", "hash": "3048538446081918844"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/KMDFDriver1.lastbuildstate", "hash": "528630556704688606"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorHandler.md", "hash": "15475208645081206135"}, {"file": "tmp_structure/tools/fix/fix_encoding.ps1", "hash": "5147143283489740337"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_evaluator.md", "hash": "8871547997162145722"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "config/models/huoshan.json", "hash": "8084680881529474041"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_data_collector.md", "hash": "6566743570803561025"}, {"file": "tmp_structure/tests/unit/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "build/.cmake/api/v1/reply/directory-.-Release-d0094a50bb2071803777.json", "hash": "2254957343676018481"}, {"file": "tmp_structure/tests/unit/prediction_core.c", "hash": "1857614276287916371"}, {"file": "tests/unit/error_manager.c", "hash": "17523935371877631884"}, {"file": "fix_hal_encoding.ps1", "hash": "10991348314127528048"}, {"file": "tests/unit/memorycompression.c", "hash": "16305212441378240323"}, {"file": "tmp_structure/tests/unit/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/spi_interface.md", "hash": "13233617362743644576"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "tmp_structure/tests/unit/log_manager.h", "hash": "9031062045211023480"}, {"file": "tmp_structure/tests/unit/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device.md", "hash": "7894195249420829021"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFSPI.md", "hash": "13937455001840529656"}, {"file": "docs/MACE-S初始实施计划.md", "hash": "4428332049883244925"}, {"file": "tests/src/performance/PerformancePredictor.c", "hash": "16358558668992702664"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/optimizer.md", "hash": "4900046108333349195"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/Touch.md", "hash": "1942753579508101543"}, {"file": "tmp_structure/tools/utils/core/mace_config.py", "hash": "6801869608993043489"}, {"file": "scripts/fix_encoding.ps1", "hash": "9576252314598886296"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-7aoy4m/cmTC_01de6.dir/Debug/cmTC_01de6.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": ".github/workflows/build.yml", "hash": "17535677776325500891"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_interrupt.md", "hash": "8991698687127595156"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_manager.md", "hash": "12926653278278343738"}, {"file": "docs/project_md/secureboot.txt", "hash": "16256824369678776978"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_analyzer.md", "hash": "12681780716891350526"}, {"file": "tests/src/device/TouchProcessor.c", "hash": "783968660014355332"}, {"file": "docs/api/jinaAPI.txt", "hash": "6331602777864225042"}, {"file": "config/models/siliconflow.json", "hash": "9961775674181545909"}, {"file": "tests/src/utils/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": "tmp_structure/tools/build/analyze_build_log.py", "hash": "15023787929842826386"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_workitem.md", "hash": "17613561711832481936"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "tests/unit/trace.c", "hash": "7049877013033903552"}, {"file": "docs/Intelligent code comment system features and usage guide.md", "hash": "16576773019115353639"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.write.1.tlog", "hash": "15389382939781618152"}, {"file": "tests/src/driver/KMDFError.c", "hash": "4864198464511138425"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/Common.md", "hash": "14593471796666110241"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/ZERO_CHECK.lastbuildstate", "hash": "528630556704688606"}, {"file": "docs/KMDF Driver1/tmp_structure/include/core/common.md", "hash": "16647291213906929623"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/health_checker.md", "hash": "9850915435701639911"}, {"file": "tmp_structure/tools/utils/collaboration_process.py", "hash": "1343857773564930929"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.command.1.tlog", "hash": "14995931337178451584"}, {"file": "tmp_structure/tools/fix/fix_newlines.py", "hash": "94768762135738281"}, {"file": "tmp_structure/tests/unit/config_group.c", "hash": "13073677126841465732"}, {"file": "build/.cmake/api/v1/query/client-vscode/query.json", "hash": "16428861418797765294"}, {"file": "tmp_structure/tests/unit/device_io.c", "hash": "9774509186909211676"}, {"file": "include/device/base/device_context.h", "hash": "17688454042905726515"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_manager.md", "hash": "3365062664957336262"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/device.md", "hash": "15950813879397350842"}, {"file": "tests/unit/configmanager.c", "hash": "12407172891497760359"}, {"file": "simple_knowledge_test.py", "hash": "2074031030093214766"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "docs/KMDF Driver1/backup/src/utils/logging/Logger.md", "hash": "4387232194484138377"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/resource_optimizer.md", "hash": "3478979109090558593"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_internal.md", "hash": "17346287682889573781"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/HardwareManager.md", "hash": "6930293510445000701"}, {"file": "tools/dependency_visualizer.py", "hash": "6834548461517481430"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": "tmp_structure/tests/unit/registry_cache.c", "hash": "18036733393600373308"}, {"file": "tests/unit/commoninternal.c", "hash": "12692290799640821342"}, {"file": "docs/KMDF Driver1/tests/src/utils/ProcessFunctions.md", "hash": "6572274267278185400"}, {"file": "tests/src/device/TouchAI.c", "hash": "1972091963095643935"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "build/.cmake/api/v1/reply/target-format-code-Release-86279ed80debd9c7b8b0.json", "hash": "10186950633527459120"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handling_test.md", "hash": "7236286416758687736"}, {"file": "tmp_structure/tools/utils/batch_annotate.py", "hash": "16564175115918697176"}, {"file": "tests/performance/device_performance_monitor.c", "hash": "9414896522403079228"}, {"file": "fix_headers.py", "hash": "15475551727779115310"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/memory_pool.md", "hash": "1168139887190025561"}, {"file": "docs/整改计划.md", "hash": "11438517125829045543"}, {"file": "tests/src/driver/KMDFHAL.c", "hash": "13443276317294174339"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "docs/KMDF Driver1/backup/backup/src/utils/logging/Logger.md", "hash": "4387232194484138377"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/simd_optimization.md", "hash": "3740213908017695348"}, {"file": "tests/unit/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_spi.md", "hash": "6188852679481726868"}, {"file": "tests/unit/device_context.c", "hash": "3876629123896102954"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_policy.md", "hash": "9008996255866575673"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_types.md", "hash": "15916424518037135132"}, {"file": "tmp_structure/tests/unit/error_core.c", "hash": "13244377760009713122"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "tmp_structure/tools/fix/fix_sources.ps1", "hash": "15787374859056701385"}, {"file": "tmp_structure/tests/unit/drivercore.c", "hash": "2661836926917990059"}, {"file": "tests/unit/simd_operations.c", "hash": "2970634337399079429"}, {"file": "tests/unit/device_resource.c", "hash": "2198688305377271422"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/CustomBuild.read.1.tlog", "hash": "8756765976692345942"}, {"file": "tmp_structure/tools/utils/core/integration_engine.py", "hash": "28873787710426007"}, {"file": "tmp_structure/tests/unit/hardware.h", "hash": "3559458026889427556"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_error_monitor.md", "hash": "2218156172550209497"}, {"file": "tests/src/utils/QueueOperations.c", "hash": "8016806957506324853"}, {"file": "tests/unit/predictor.c", "hash": "15488274478411287487"}, {"file": "tests/src/utils/test/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger_trace.md", "hash": "1185709471855129467"}, {"file": "tests/unit/prediction_context.c", "hash": "8771136116938717181"}, {"file": "tmp_structure/tests/unit/configmanager.c", "hash": "12407172891497760359"}, {"file": "tests/src/utils/ml/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "tests/src/utils/optimization/adaptive_scheduler.c", "hash": "10385198795992790880"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchAI.md", "hash": "12773210007988829165"}, {"file": "tmp_structure/tests/unit/logger_trace.c", "hash": "9545559538338278246"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_test.md", "hash": "7510514405337728791"}, {"file": "test_huoshan_new.py", "hash": "4151729223194610259"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CommonInternal.md", "hash": "7520992645958513174"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver_entry.md", "hash": "17773269773045267719"}, {"file": "tests/src/security/ErrorHandler.c", "hash": "638403771659111855"}, {"file": "tests/src/security/error_handler.c", "hash": "2913023791582526489"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/event_dispatcher.md", "hash": "6014464624155038316"}, {"file": "build/unit_tests.vcxproj", "hash": "11639480655848654369"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFLogger.md", "hash": "13534544793886613631"}, {"file": "tests/src/driver/KMDFI2C.c", "hash": "16086037633168955850"}, {"file": "tmp_structure/include/log/driver_log.h", "hash": "10680616300119219702"}, {"file": "tests/unit/compressionpredictor.c", "hash": "11817161568988905271"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_power.md", "hash": "2872841184312516318"}, {"file": "config/models/cloudflare.json", "hash": "1927853033215223763"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/agent_manager.cpython-313.pyc", "hash": "3893011543862468908"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "src/core/error/error_handling.c", "hash": "15557826950637572065"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/scheduler_optimizer.md", "hash": "7056918987226729867"}, {"file": "tests/unit/distributed_training.c", "hash": "13748589215294334048"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_core.md", "hash": "14131705015021059174"}, {"file": "tests/src/utils/trace/logger_core.c", "hash": "5596205493550234397"}, {"file": "tests/unit/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "docs/KMDF Driver1/backup/include/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/telemetry_manager.md", "hash": "247681583355927358"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_queue.md", "hash": "7516366818546283918"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/simd_prediction.md", "hash": "7095774571489160819"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFHAL.md", "hash": "14106586863704401393"}, {"file": "tests/src/utils/device/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handling.md", "hash": "8736094554619853824"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_core.md", "hash": "16004942062479818338"}, {"file": "tmp_structure/tests/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_io.md", "hash": "16633121732728631638"}, {"file": "build/generated/version.h", "hash": "15174072068962538387"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_core.md", "hash": "12158579485392829125"}, {"file": "tests/unit/touchmanager.c", "hash": "2612989593074257093"}, {"file": "fix_final_headers.py", "hash": "18202133227987375845"}, {"file": "build/test_driver.vcxproj.filters", "hash": "4154311487132175209"}, {"file": "tests/src/io/IOOptimizer.c", "hash": "9949378264387320340"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/feature_engineering.md", "hash": "15700191621865806204"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "tmp_structure/tests/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "tests/performance/performance_analyzer.h", "hash": "244392829769829688"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/simd_optimizer.md", "hash": "16564215083805726102"}, {"file": "project_structure_full.txt", "hash": "10506296151893659615"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": "tests/src/memory/DMAOptimizer.h", "hash": "17577588181507726687"}, {"file": "build/.cmake/api/v1/reply/target-run-tests-MinSizeRel-76f6c5e7868abb6d8741.json", "hash": "10486406065451624556"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "336743115064200357"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "tests/src/utils/trace/trace.h", "hash": "4336778835874238702"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/sample_test.md", "hash": "7667131459515975553"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_context.md", "hash": "9967185622851685113"}, {"file": "include/device/power/power_manager.h", "hash": "5873311443046064208"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/best_practice_agent.cpython-313.pyc", "hash": "18373879793777027551"}, {"file": "docs/project_md/test_e2b.md", "hash": "9062257711975633675"}, {"file": "tests/src/driver/driver_includes.h", "hash": "12262707748624835517"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/project_md/ai_roles.py.teaching.md", "hash": "17146544006075112183"}, {"file": "tmp_structure/tests/unit/iooptimizer.c", "hash": "11289350191801585883"}, {"file": "cmake/code_quality.cmake", "hash": "1182718018541730159"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_optimizer.md", "hash": "473303732562322893"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/analyzer.md", "hash": "8824479516875383820"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFI2C.md", "hash": "6535570456787467291"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-pu6pcu/cmTC_03648.dir/Debug/cmTC_03648.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "gitignore", "hash": "2047371395103394036"}, {"file": "tests/src/utils/registry_cache.c", "hash": "18036733393600373308"}, {"file": "tests/src/utils/CommonInternal.c", "hash": "12692290799640821342"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "tests/unit/touchmanager.h", "hash": "18031675363219744266"}, {"file": "tests/src/utils/trace/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_test.md", "hash": "5528223060007826182"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver.md", "hash": "12889633037907936946"}, {"file": ".vscode/file_rules.json", "hash": "15179874288697686795"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/statistics_manager.md", "hash": "8721089824310490847"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_internal.md", "hash": "13825641208428294959"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "config/models/deepseek.json", "hash": "14635218583903184388"}, {"file": "tmp_structure/tools/fix/auto_fix_declarations.py", "hash": "2374951955509788451"}, {"file": "include/hal/devices/spi_device.h", "hash": "8018070506967611507"}, {"file": "tests/unit/device_io.c", "hash": "9774509186909211676"}, {"file": "tmp_structure/tests/performance/performancedata.c", "hash": "10364043397620603240"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_health_monitor.md", "hash": "11811529164322313522"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CompressionPredictor.md", "hash": "14348087692511970447"}, {"file": "tmp_structure/tests/unit/touchcontroller.c", "hash": "7422034257177845882"}, {"file": "tests/src/driver/kmdf_core.c", "hash": "11085129429806464239"}, {"file": "tmp_structure/tests/unit/event_trace.c", "hash": "10188444021102792748"}, {"file": "tests/src/utils/optimization/simd_optimization.c", "hash": "1016262082760471380"}, {"file": "tests/performance/performance.c", "hash": "542837662377689141"}, {"file": "test_moonshot.py", "hash": "12439654262898103573"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "tmp_structure/tools/utils/core/__pycache__/model_interface.cpython-313.pyc", "hash": "2786865539013472691"}, {"file": "config/models/jina.json", "hash": "14050760086702475995"}, {"file": "tests/unit/interrupt.c", "hash": "438091713417069452"}, {"file": "check_structure.py", "hash": "12371258786447826844"}, {"file": "tests/src/utils/ml/resource_optimizer_1.c", "hash": "9728211600653272904"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/event_trace.md", "hash": "2482424508797990892"}, {"file": "tmp_structure/tests/unit/errorhandler.c", "hash": "638403771659111855"}, {"file": "tmp_structure/tests/unit/touchpredictor.c", "hash": "15043340538585148217"}, {"file": "tmp_structure/tests/unit/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "tests/src/performance/performance_collector.c", "hash": "13527805926885479396"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/queue.md", "hash": "17411770077844460848"}, {"file": "tests/src/utils/ModuleManager.c", "hash": "12705701726995479740"}, {"file": "build/test_utils.vcxproj.filters", "hash": "8631814011904195954"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "build/Testing/20250519-0442/Test.xml", "hash": "8056147754429930796"}, {"file": "tests/src/utils/ml/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/security_agent.cpython-313.pyc", "hash": "12259403970642078874"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": "tmp_structure/tools/utils/core/__pycache__/__init__.cpython-313.pyc", "hash": "2201897863955216378"}, {"file": "tests/src/utils/ml/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "mace_requirements.txt", "hash": "1191718381784718678"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "tmp_structure/tests/unit/power_test.c", "hash": "9102832245856542684"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "tests/unit/touchprocessor.h", "hash": "12389133180863395364"}, {"file": "docs/project_md/ai_collaboration_roles.py.teaching.md", "hash": "8825015382185079511"}, {"file": "tests/src/utils/interrupt/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "tmp_structure/tests/unit/gyro.c", "hash": "12598367591223412709"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/spi_controller.md", "hash": "14694618006794652787"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "include/device/power/device_power.h", "hash": "7709195725722523957"}, {"file": "analyze_headers.py", "hash": "17648001957220340279"}, {"file": "tmp_structure/tools/fix/fix_coding_style.py", "hash": "15649094057833155149"}, {"file": "docs/MCP使用方法/everything_search_mcp使用指南.md", "hash": "17433933541212280052"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/simd_optimization.md", "hash": "17159784567835091991"}, {"file": "tests/src/utils/optimization/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_framework_test.md", "hash": "10803349116171378917"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/i2_c_interface.md", "hash": "9147246998176705712"}, {"file": "tests/unit/dmahandler.c", "hash": "7693675145006693987"}, {"file": "tests/src/utils/device/usb_interface.c", "hash": "15002876401871970527"}, {"file": "tests/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": "tmp_structure/driver_entry_test.py", "hash": "2253797170165017009"}, {"file": "tmp_structure/tools/fix/fix_header_guards_complete.py", "hash": "15335801640125736637"}, {"file": "build/.cmake/api/v1/reply/codemodel-v2-643ab19882d7cb01bbbc.json", "hash": "11952097986738589038"}, {"file": "tests/src/utils/device/device_control.c", "hash": "1983799697968931608"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/run_tests.rule", "hash": "1819760324823437288"}, {"file": "tests/src/device/TouchDebug.h", "hash": "1920040118285242429"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_test.md", "hash": "15984150053883273172"}, {"file": "tmp_structure/tools/utils/reorganize_project.py", "hash": "70675705915150794"}, {"file": "tmp_structure/tools/utils/agents/structural_agent.py", "hash": "5403844450575887305"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "docs/KMDF Driver1/tests/src/utils/event_dispatcher.md", "hash": "467961671039835308"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_performance.md", "hash": "1048439768488625304"}, {"file": ".vscode/alert_thresholds.json", "hash": "6723932485480696586"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/full-quality-check.rule", "hash": "1819760324823437288"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.write.1.tlog", "hash": "5136852536636850376"}, {"file": "build/ZERO_CHECK.vcxproj", "hash": "13380785407584090960"}, {"file": "tests/src/utils/trace/trace_core.c", "hash": "10541209614786999240"}, {"file": "tmp_structure/tests/unit/advanced_models.c", "hash": "10802020780109055119"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CompressionPredictor.md", "hash": "14348087692511970447"}, {"file": "tmp_structure/tests/unit/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "docs/KMDF Driver1/tests/src/device/Hardware.md", "hash": "2750892896784880619"}, {"file": "tests/unit/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "build/ALL_BUILD.vcxproj", "hash": "10566857757859579651"}, {"file": "tests/src/driver/KMDFSPI.c", "hash": "17213720173027797590"}, {"file": "tests/performance/performanceexport.c", "hash": "7332497143218165792"}, {"file": "docs/KMDF Driver1/backup/include/driver/driver_core.md", "hash": "2981211088971537176"}, {"file": "tests/unit/device.c", "hash": "6161212960231513500"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_scheduler.md", "hash": "4149850547689728390"}, {"file": "tmp_structure/include/error/error_handling.h", "hash": "2498624322782239441"}, {"file": "tests/unit/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "tmp_structure/tests/unit/device_control.c", "hash": "1983799697968931608"}, {"file": "include/hal/devices/gpio_device.h", "hash": "16393459335135799288"}, {"file": "tmp_structure/tests/unit/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/driver_types.md", "hash": "6648445345879484221"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_strategies.md", "hash": "1076962325564799858"}, {"file": "docs/KMDF Driver1/tests/src/utils/ConfigManager.md", "hash": "4345364523641645633"}, {"file": "tests/src/utils/ProcessFunctions.c", "hash": "15471745620740669001"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CompilerIdCXX.exe", "hash": "7621851974530374034"}, {"file": "tmp_structure/tests/unit/registry_manager.c", "hash": "7212363665723535550"}, {"file": "docs/KMDF Driver1/backup/include/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "tmp_structure/tools/utils/ai_roles.py", "hash": "1926999291726298739"}, {"file": "tmp_structure/tools/utils/code_teacher.py", "hash": "1569871746886746141"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_error_monitor.md", "hash": "2218156172550209497"}, {"file": "tests/src/memory/MemoryManager.c", "hash": "2438749304028039406"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/feature_engineering.md", "hash": "15700191621865806204"}, {"file": "tests/src/utils/device/device_manager_1.c", "hash": "9178587879886038827"}, {"file": "tests/unit/device_queue1.c", "hash": "330133164890642531"}, {"file": "tests/unit/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchAI.md", "hash": "939606902265643493"}, {"file": "tests/src/security/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "tests/unit/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_spi.md", "hash": "15156697141188689173"}, {"file": "tests/unit/touchdebug.h", "hash": "5172689095380585051"}, {"file": "tests/unit/simd_ops.c", "hash": "4755770406672099314"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "tests/unit/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.read.1.tlog", "hash": "13129952132537788104"}, {"file": "tmp_structure/tools/fix/fix_header_guards.ps1", "hash": "3509839413937215495"}, {"file": "tests/unit/prediction_model.c", "hash": "10565836354052141209"}, {"file": "tmp_structure/include/error/error_codes.h", "hash": "16517599858835515243"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_callback.md", "hash": "1464403380017115903"}, {"file": "certs/kmdf_driver_test_cert.cer", "hash": "6270925379150306323"}, {"file": "docs/KMDF Driver1/backup/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "config/models/openai.json", "hash": "1357320890085649587"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ProcessFunctions.md", "hash": "5303515720344831466"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery_framework.md", "hash": "9217977908073013080"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/log/LogTypes.md", "hash": "15844539242907667008"}, {"file": "tmp_structure/tests/unit/trace.c", "hash": "7049877013033903552"}, {"file": "tmp_structure/tests/unit/driverlog.c", "hash": "13535373348672446805"}, {"file": "tmp_structure/tests/unit/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "tmp_structure/tests/unit/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "tests/src/utils/ml/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "build/test_core.vcxproj", "hash": "11026630969514173814"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_engine.md", "hash": "1707343726405816748"}, {"file": "docs/module_guidelines.md", "hash": "10696276466022967433"}, {"file": "tests/src/utils/CommonUtils.c", "hash": "11647735861415970558"}, {"file": "tmp_structure/tools/utils/mace_system.py", "hash": "8537759706001371926"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchPerformance.md", "hash": "3331360388054379050"}, {"file": "docs/project_md/code_teacher.md", "hash": "3104141829689213043"}, {"file": "docs/KMDF Driver1/backup/include/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/Common.md", "hash": "14593471796666110241"}, {"file": "src/driver_main.c", "hash": "4797320670848991661"}, {"file": "tmp_structure/tests/unit/driver_manager.c", "hash": "18213335034763467499"}, {"file": "tools/scripts/project_manager.ps1", "hash": "14461698973857256077"}, {"file": "include/hal/bus/kmdf_spi.h", "hash": "4925091507298093682"}, {"file": "docs/KMDF Driver1/backup/20250506_205501/include/core/types/DriverTypes.md", "hash": "5378835974814819684"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "tmp_structure/tests/unit/errorrecovery.c", "hash": "7122396928859668049"}, {"file": "tests/src/utils/trace/logger_trace.c", "hash": "9545559538338278246"}, {"file": "package.json", "hash": "16753833722885029064"}, {"file": "mcp_debug.log", "hash": "3244421341483603138"}, {"file": "tests/unit/trace_core.c", "hash": "10541209614786999240"}, {"file": "tests/unit/sample_test.c", "hash": "6872651473851085099"}, {"file": "tmp_structure/tools/test/test_driver.ps1", "hash": "15765134662692261731"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_control.md", "hash": "16289217216158181524"}, {"file": "tests/src/performance/PerformanceLogger.cpp", "hash": "1714423352534226014"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_optimizer.md", "hash": "473303732562322893"}, {"file": "tests/src/driver/Driver.c", "hash": "5937756760430682963"}, {"file": "tmp_structure/tests/unit/model.c", "hash": "13330488231876145168"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "tests/unit/usb_interface.c", "hash": "15002876401871970527"}, {"file": "tests/src/utils/registry_manager.c", "hash": "7212363665723535550"}, {"file": "tmp_structure/tests/unit/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "tmp_structure/tools/utils/static_analyzer.py", "hash": "3472089084897019467"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/usb_interface.md", "hash": "15608419832055058263"}, {"file": "tests/src/utils/test/test_reporter.c", "hash": "15868353065156172044"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_module.md", "hash": "16648157775560897177"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceData.md", "hash": "17754136278712063044"}, {"file": "tests/unit/monitor.h", "hash": "7267562670448795700"}, {"file": "tmp_structure/tests/unit/logger_core.c", "hash": "5596205493550234397"}, {"file": "tmp_structure/tools/utils/check_dev_env.ps1", "hash": "2785216015131417667"}, {"file": "tests/unit/cache_manager.c", "hash": "868028145507112649"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-Release-f374f9b69aa13624eb70.json", "hash": "8682366638238923366"}, {"file": "docs/refactor_plan_20240101_driver_optimization.md", "hash": "11339095362012304468"}, {"file": "docs/module_dependencies_20240101.md", "hash": "18292169858001390161"}, {"file": "docs/驱动设计方案.txt", "hash": "13131682799839766864"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/analyze-dependencies.rule", "hash": "1819760324823437288"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "tmp_structure/tools/utils/standardize_code_style.py", "hash": "4459166999164536411"}, {"file": "tmp_structure/tools/test/test_code.py", "hash": "8644629831272372251"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "tests/src/utils/device/device_queue.c", "hash": "16684379454006678062"}, {"file": "tests/unit/module_manager.c", "hash": "6226471683647205323"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_io.md", "hash": "16194404026375741960"}, {"file": "tests/src/utils/test/sample_test.c", "hash": "6872651473851085099"}, {"file": "tmp_structure/tests/unit/queue.c", "hash": "4795497370184670599"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_state_types.md", "hash": "7660836948132653389"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger_trace.md", "hash": "10304453869909953023"}, {"file": ".github/workflows/ci.yml", "hash": "12304493898047627145"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "tests/unit/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "tests/src/utils/optimization/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/generate.stamp.rule", "hash": "1819760324823437288"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/Performance.md", "hash": "6708106772424578802"}, {"file": "docs/KMDF Driver1/backup/include/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": "tmp_structure/tests/unit/simd_operations.c", "hash": "2970634337399079429"}, {"file": "docs/KMDF Driver1/tests/src/utils/config_manager.md", "hash": "17618036168927765203"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceExport.md", "hash": "3120014411541002467"}, {"file": "tests/unit/error_recovery_framework.c", "hash": "13699128380064941463"}, {"file": "docs/KMDF Driver1/tmp_structure/include/driver/driver.md", "hash": "9302614752768382714"}, {"file": "tests/unit/memorymanager.c", "hash": "13193390556443584375"}, {"file": "tmp_structure/tests/unit/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "signdrivergui.bat", "hash": "17320788386012970020"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_test.md", "hash": "15477382941510235392"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger_trace.md", "hash": "10304453869909953023"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_internal.md", "hash": "13825641208428294959"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceMonitor.md", "hash": "2312054369463986103"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "docs/KMDF Driver1/backup/include/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "tests/src/security/error_manager.c", "hash": "11703476280763806582"}, {"file": "tmp_structure/tools/fix/fix_format.py", "hash": "14127110787080049849"}, {"file": "docs/design/.gitkeep", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceData.md", "hash": "17754136278712063044"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "tests/unit/error_handler.c", "hash": "2913023791582526489"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_scheduler.md", "hash": "406089374946636554"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "tests/src/performance/performance_monitor_manager.c", "hash": "9008144578910281466"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryCompression.md", "hash": "11093687460873692203"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_engine.md", "hash": "1707343726405816748"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.command.1.tlog", "hash": "2268705047010358660"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "tmp_structure/tools/fix/fix_headers.ps1", "hash": "16935879167314502722"}, {"file": "build/CMakeFiles/generate.stamp.list", "hash": "7355742560215271970"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_spi.md", "hash": "6188852679481726868"}, {"file": "tmp_structure/tests/unit/i2c_interface.c", "hash": "931873009436168421"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_manager_1.md", "hash": "11358954628081867466"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model_evaluation.md", "hash": "11583937441064285777"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "docs/project_md/idear3.txt", "hash": "5720518902774854320"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "tmp_structure/tools/utils/simple_demo.py", "hash": "15158633839973973863"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/config_group.md", "hash": "13037418512325033205"}, {"file": "tmp_structure/tests/unit/driver_includes.h", "hash": "5942675050603689176"}, {"file": "tests/src/memory/MemoryPool.c", "hash": "16759837123446157155"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "execute_cleanup.py", "hash": "12212647398159307009"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ModuleManager.md", "hash": "6642213020461104442"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/run-tests.rule", "hash": "1819760324823437288"}, {"file": "include/core/driver/driver_core.h", "hash": "13407190762478558919"}, {"file": "docs/KMDF Driver1/tests/src/utils/CommonInternal.md", "hash": "4664956977958856282"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CL.command.1.tlog", "hash": "6262474925740181382"}, {"file": "test_agent.py", "hash": "4860432581673247722"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/CMakeCCompilerId.c", "hash": "10232577801309739533"}, {"file": "build/Testing/Temporary/LastTest_20250520-1507.log", "hash": "5739088842043845856"}, {"file": "tmp_structure/tests/performance/performanceanalyzer.c", "hash": "16444703254818628465"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt.md", "hash": "7122344570168608003"}, {"file": "tmp_structure/tools/fix/fix_bom.ps1", "hash": "8939596673589547426"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/MaintenanceManager.md", "hash": "5703461401317863092"}, {"file": "tmp_structure/tests/unit/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceLogger.md", "hash": "10635370005391666013"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Gyro.md", "hash": "11459815508399627952"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/config_group.md", "hash": "13037418512325033205"}, {"file": "tests/__pycache__/test_quality_controller.cpython-314.pyc", "hash": "3992480986374148898"}, {"file": "tests/src/memory/MemoryManager.h", "hash": "16144556611256439050"}, {"file": "docs/KMDF Driver1/tests/src/core/error_handling_test.md", "hash": "14543812925109624658"}, {"file": "tests/src/device/HardwareManager.c", "hash": "5852295366093323504"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_scheduler.md", "hash": "9259475315249995297"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_model.md", "hash": "2200148848594683661"}, {"file": "tmp_structure/tools/utils/agents/interactive_agent.py", "hash": "10286012444766933162"}, {"file": "config/models/glhf.json", "hash": "7514859731510942125"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath/x64/Debug/VCTargetsPath.tlog/VCTargetsPath.lastbuildstate", "hash": "13494505001770249345"}, {"file": "docs/KMDF Driver1/tmp_structure/src/log/driver_log.md", "hash": "17856606067144505067"}, {"file": "tests/unit/error_module.c", "hash": "10513386486768326845"}, {"file": "include/core/driver/driver_entry.h", "hash": "9612320755824265761"}, {"file": "tests/unit/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "tmp_structure/tools/utils/core/model_interface.py", "hash": "10371967563584055373"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "tools/convert_hal_encoding.ps1", "hash": "12457230657918497233"}, {"file": "tests/unit/logger_trace.c", "hash": "9545559538338278246"}, {"file": "tmp_structure/tests/unit/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "tmp_structure/tests/unit/touch.c", "hash": "5834024937245580957"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/event_buffer.md", "hash": "11762678649378503503"}, {"file": "tests/unit/kmdfusb.c", "hash": "3804768512383554498"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": ".vs/slnx.sqlite", "hash": "10372569932437568579"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_performance_monitor.md", "hash": "5433707484237699770"}, {"file": "tmp_structure/tests/unit/device_manager1.c", "hash": "9178587879886038827"}, {"file": "tmp_structure/tests/unit/processfunctions.c", "hash": "15471745620740669001"}, {"file": "tmp_structure/tools/fix/fix_newlines.ps1", "hash": "3484662595788553404"}, {"file": "docs/KMDF Driver1/backup/include/device/device.md", "hash": "15950813879397350842"}, {"file": "tmp_structure/docs/MACE-S_使用指南.md", "hash": "5479686292920693858"}, {"file": "build/full-quality-check.vcxproj", "hash": "6537436389829400725"}, {"file": "tests/src/utils/optimization/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "tmp_structure/tools/utils/resolve_duplicates.py", "hash": "16547623118355958279"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "3921821082679517869"}, {"file": "docs/project_md/idear2怎么把代码注释做好.txt", "hash": "15555374162579802027"}, {"file": "include/core/common/Common.h", "hash": "13124832492036846080"}, {"file": "docs/project_md/generate_driver_comments.md", "hash": "2847487674231699790"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_manager.md", "hash": "17123311457972373377"}, {"file": "scripts/extract_errors.py", "hash": "14839614070312556472"}, {"file": "tmp_structure/tools/fix/fix_header_guards_format.py", "hash": "9777199903109658894"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model.md", "hash": "11576464091276221100"}, {"file": "tmp_structure/tests/unit/trace.h", "hash": "1843899095343991810"}, {"file": "tests/unit/registry_callback.c", "hash": "17205669410881855421"}, {"file": "docs/KMDF Driver1/tests/src/memory/memory_manager.md", "hash": "14583268610010245933"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handling_test.md", "hash": "12833519669823771262"}, {"file": "tmp_structure/tests/unit/commonutils.c", "hash": "11647735861415970558"}, {"file": "tests/unit/touchai.c", "hash": "6072030623807491314"}, {"file": "tests/unit/touchai.h", "hash": "12290558413403050005"}, {"file": "tmp_structure/include/driver/driver.h", "hash": "13846183591385912328"}, {"file": "tmp_structure/tests/unit/distributed_training.c", "hash": "13748589215294334048"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchController.md", "hash": "6274914068193184456"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "example_project/module1/data_processor.py", "hash": "5596420748158219335"}, {"file": "tests/src/utils/simd/simd_core.c", "hash": "12729929013449999851"}, {"file": "tmp_structure/tools/utils/init.py", "hash": "12994821110821436525"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/simd_prediction.md", "hash": "15285699593433826437"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_cache.md", "hash": "10642137261929724889"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_resource_monitor.md", "hash": "9018393555267135129"}, {"file": "tests/src/performance/PerformanceMonitor.c", "hash": "1073978516217722283"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger.md", "hash": "14318949831029739559"}, {"file": "test_volcengine_auth.py", "hash": "17643482513176861101"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_performance_monitor.md", "hash": "767285653915167176"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_spi.md", "hash": "12023608907578026527"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_power.md", "hash": "2872841184312516318"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/ml_optimizer.md", "hash": "8283783923648259421"}, {"file": "tests/unit/logger_test.c", "hash": "15334138061466012986"}, {"file": "tests/unit/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_reporter.md", "hash": "12587998797618101724"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/Driver.md", "hash": "13509455873758057648"}, {"file": "tmp_structure/tests/unit/kmdfspi.c", "hash": "1159729704795014365"}, {"file": "src/hal/devices/i2c_device.c.bak", "hash": "5742460478894627044"}, {"file": "docs/KMDF Driver1/tests/src/utils/AlertManager.md", "hash": "5817230148968624754"}, {"file": "docs/KMDF Driver1/tests/src/utils/telemetry_manager.md", "hash": "16760846509114151494"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_core.md", "hash": "587477157269630570"}, {"file": "tmp_structure/tools/utils/check_env.py", "hash": "15916084536442429794"}, {"file": "tmp_structure/tools/utils/sync_headers_with_teaching.py", "hash": "14152856265668051425"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchController.md", "hash": "5893240157153592323"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/Cl.items.tlog", "hash": "7566281551702455546"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "tests/src/device/TouchAI.h", "hash": "13168883210181320447"}, {"file": "docs/KMDF Driver1/tests/src/utils/ReliabilityManager.md", "hash": "9310313492205542628"}, {"file": "tests/src/device/TouchManager.c", "hash": "2612989593074257093"}, {"file": "tests/src/utils/simd/simd_performance.c", "hash": "9288036268856686374"}, {"file": "tmp_structure/tools/utils/ai_collaboration_roles.py", "hash": "15025797022093181680"}, {"file": "config/templates/basic_prompt.json", "hash": "9401672000993726034"}, {"file": "docs/KMDF Driver1/backup/backup/include/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "tests/unit/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/trace_core.md", "hash": "15790226712616423779"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_workitem.md", "hash": "3984672476542598313"}, {"file": "cleanupprojectfixed.ps1", "hash": "1729661487922461346"}, {"file": "docs/KMDF Driver1/backup/include/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger_core.md", "hash": "9949325019420752238"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_resource_monitor.md", "hash": "10903229905072343484"}, {"file": "tmp_structure/tests/unit/test_main.c", "hash": "17856805622170871630"}, {"file": "tests/unit/processfunctions.c", "hash": "15471745620740669001"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_manager.md", "hash": "8524736742959489422"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/trace_core.md", "hash": "11472634081961337506"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFI2C.md", "hash": "4110153206783316966"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_handler.md", "hash": "12498787032465535448"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformancePredictor.md", "hash": "15411689379061262882"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/memory_manager.md", "hash": "10511834971114926523"}, {"file": "tmp_structure/tests/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "tmp_structure/tests/unit/driverentry.c", "hash": "15432365288492737233"}, {"file": "tests/src/performance/PerformanceAnalyzer.c", "hash": "16444703254818628465"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchPredictor.md", "hash": "12653685510425338727"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "tmp_structure/tests/unit/device_workitem.c", "hash": "15964808878924499639"}, {"file": "docs/KMDF Driver1/tests/src/core/GlobalPerformanceManager.md", "hash": "4214450315185299087"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": ".giti<PERSON>re", "hash": "567407489855212125"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/CompilerIdC.vcxproj", "hash": "2342205852161662893"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "tmp_structure/tests/unit/test_framework.c", "hash": "8175968703783348918"}, {"file": "fix_encoding.ps1", "hash": "15251168971363582810"}, {"file": "tests/unit/optimization_database.c", "hash": "1382797123011915962"}, {"file": ".vscode/project_settings.json", "hash": "18038287660854870704"}, {"file": "tests/unit/touchdebug.c", "hash": "3448456818563192507"}, {"file": "docs/KMDF Driver1/tmp_structure/include/log/driver_log.md", "hash": "12751813489477682259"}, {"file": "tests/src/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_database.md", "hash": "3898663799363477466"}, {"file": "tmp_structure/src/driver/drivercore.c", "hash": "4921296672744000056"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/CL.command.1.tlog", "hash": "6262474925740181382"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_manager.md", "hash": "9371494073877759190"}, {"file": "tmp_structure/tools/utils/check_organization.py", "hash": "5131914251861155780"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "docs/KMDF Driver1/tests/src/utils/MaintenanceManager.md", "hash": "359784250778668666"}, {"file": "tmp_structure/tools/fix/fix_code.ps1", "hash": "2433744338792008947"}, {"file": "docs/project_md/ai_collaboration_process.py.teaching.md", "hash": "7931814780058194392"}, {"file": "tests/src/utils/cache_manager.c", "hash": "868028145507112649"}, {"file": "docs/MCP使用方法/puppeteer_mcp使用指南.md", "hash": "11568379398149184806"}, {"file": "tmp_structure/tests/performance/performance_benchmark.c", "hash": "2916244813509275032"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": "tests/src/utils/optimization/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/__init__.cpython-313.pyc", "hash": "10876873813678925159"}, {"file": "docs/KMDF Driver1/backup/backup/src/core/error_handling.md", "hash": "8736094554619853824"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchManager.md", "hash": "67499987134114106"}, {"file": "tests/src/utils/trace/log_manager.c", "hash": "1851491385887858320"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_manager.md", "hash": "17363996027966417800"}, {"file": "tmp_structure/tests/unit/adaptive_scheduler.c", "hash": "17438257645459382429"}, {"file": "runannotator.bat", "hash": "5714542090824015771"}, {"file": "tests/performance/performanceanalyzer.c", "hash": "16444703254818628465"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_manager.md", "hash": "968320189468067565"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "docs/KMDF Driver1/tests/src/utils/event_buffer.md", "hash": "7850820821631957138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CommonInternal.md", "hash": "7520992645958513174"}, {"file": "tests/src/utils/device/i2_c_interface.c", "hash": "931873009436168421"}, {"file": "tests/src/utils/device/device_manager.c", "hash": "1823201296405534462"}, {"file": "example_project/module2/visualization.py", "hash": "15986574477792699771"}, {"file": "src/hal/devices/gpio_device.c", "hash": "11940977359829578630"}, {"file": "build/.cmake/api/v1/reply/directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "hash": "2254957343676018481"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "docs/KMDF Driver1/tests/src/utils/logging/logger_test.md", "hash": "10138150490318451487"}, {"file": "tests/unit/errorprevention.c", "hash": "10299302956295713904"}, {"file": "tmp_structure/tools/utils/simple_demo_no_emoji.py", "hash": "10037256737053458005"}, {"file": "tmp_structure/tests/unit/maintenancemanager.c", "hash": "13897789596428706927"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-RelWithDebInfo-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceAnalyzer.md", "hash": "17860791912881724719"}, {"file": "tests/unit/driver_stubs.c", "hash": "770506472769498247"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchManager.md", "hash": "8626033256526254123"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "tests/src/utils/optimization/optimization_database.c", "hash": "1382797123011915962"}, {"file": "tmp_structure/tools/utils/check_versions.ps1", "hash": "11390489552012229852"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorCore.md", "hash": "17367002698232849007"}, {"file": "tests/src/utils/trace/event_tracer.c", "hash": "7710005633789833902"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "tmp_structure/include/device/device_stat_types.h", "hash": "12470765283103911762"}, {"file": "tests/unit/gamepadcore.c", "hash": "15479958173847767625"}, {"file": "build/CMakeCache.txt", "hash": "11149142921209073657"}, {"file": ".vscode/project_config.json", "hash": "748546055473273721"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "tmp_structure/tests/unit/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_performance.md", "hash": "1048439768488625304"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_log_test.md", "hash": "5672191744425007329"}, {"file": "tmp_structure/include/core/common.h", "hash": "5175167649420740486"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "build/Testing/Temporary/LastTest_20250522-0740.log", "hash": "14982565904888851673"}, {"file": "tmp_structure/tools/utils/core/code_parser.py", "hash": "12329907362720636894"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceManager.md", "hash": "4322100284458284897"}, {"file": "tmp_structure/tests/unit/event_tracer.c", "hash": "7710005633789833902"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFI2C.md", "hash": "2401467074656005273"}, {"file": "config/config.json", "hash": "6101988485205918148"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "docs/KMDF Driver1/tests/src/performance/Performance.md", "hash": "2031739876215854096"}, {"file": "build/run-tests.vcxproj", "hash": "3287885910138435951"}, {"file": "tests/src/utils/ml/distributed_training.c", "hash": "13748589215294334048"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_manager.md", "hash": "8524736742959489422"}, {"file": "cleanup_documentation.md", "hash": "5781163044308498692"}, {"file": "tmp_structure/tools/fix/fix_chinese_comments.ps1", "hash": "2371586277548138479"}, {"file": "docs/KMDF Driver1/tests/src/utils/manager.md", "hash": "17036075936429722929"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/event_tracer.md", "hash": "4590536025397448341"}, {"file": "tmp_structure/tools/utils/kmdf_controller.py", "hash": "10576471519790572159"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_strategies.md", "hash": "1076962325564799858"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "tmp_structure/tools/utils/dependency_analyzer.py", "hash": "2403107713714667589"}, {"file": "tests/unit/drivercore.c", "hash": "2661836926917990059"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "test_volcengine_api.py", "hash": "4524134310110625423"}, {"file": "docs/KMDF Driver1/build/version.md", "hash": "5643954368802313811"}, {"file": "tmp_structure/mace_system_test.py", "hash": "4332162749339582681"}, {"file": "tmp_structure/tests/performance/globalperformancemanager.c", "hash": "13783737954691746004"}, {"file": "docs/api/api_config_help.md", "hash": "8934429542747036263"}, {"file": "tests/src/utils/CompressionPredictor.c", "hash": "11817161568988905271"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": ".github/config/code_analysis_rules.json", "hash": "15843396151099728677"}, {"file": "docs/project_md/CMakeLists.txt", "hash": "12807661119778426816"}, {"file": "tmp_structure/tools/fix/fix_includes.py", "hash": "18140236329106783058"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/hal.md", "hash": "6548767525705845893"}, {"file": "tests/unit/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/run_unit_tests.lastbuildstate", "hash": "528630556704688606"}, {"file": ".vscode/sdv-default_20250108_140645.xml", "hash": "10931019705986488358"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_context.md", "hash": "4170528201323571228"}, {"file": "build/.cmake/api/v1/reply/target-run_unit_tests-Debug-785a955bdb581871b2c8.json", "hash": "17376154880914891264"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "tmp_structure/tests/unit/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "tmp_structure/src/driver/driverentry.c", "hash": "73705776410683099"}, {"file": "tests/unit/CMakeLists.txt", "hash": "16190250173074905128"}, {"file": "tests/src/utils/ConfigManager.cpp", "hash": "4604724285864392303"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/CustomBuild.write.1.tlog", "hash": "14117961221003328239"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device.md", "hash": "7448308400255503639"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/telemetry_manager.md", "hash": "247681583355927358"}, {"file": "tests/unit/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "tests/unit/health_checker.c", "hash": "13081061987138608445"}, {"file": "docs/KMDF Driver1/tests/src/device/Gyro.md", "hash": "139370696856845314"}, {"file": "docs/KMDF Driver1/backup/backup/include/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "tests/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "tmp_structure/tests/unit/error_manager.c", "hash": "17523935371877631884"}, {"file": "tests/src/utils/test/queue_test.c", "hash": "665738556786491367"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/sample_test.md", "hash": "9338080078639493927"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "docs/KMDF Driver1/tests/src/utils/analyzer.md", "hash": "18403376044835528462"}, {"file": "tmp_structure/tools/fix/fix_tabs.ps1", "hash": "15431393325432432609"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": "tests/unit/prediction_core.c", "hash": "1857614276287916371"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/event_tracer.md", "hash": "11863437582874565376"}, {"file": "tests/src/utils/ml/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "tmp_structure/tests/performance/performancemanager.c", "hash": "12428470120826957350"}, {"file": "tests/src/utils/ml/prediction_model.c", "hash": "10565836354052141209"}, {"file": "docs/doxygen/generate_docs.bat", "hash": "4440528942964720151"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_evaluator.md", "hash": "11857399457039219799"}, {"file": "tests/src/utils/optimization/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "docs/project_md/code_teacher.py.teaching.md", "hash": "4874293727162587059"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "tests/src/security/ErrorPrevention.c", "hash": "10299302956295713904"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "tests/performance/simd_performance.c", "hash": "9288036268856686374"}, {"file": "build/CMakeFiles/3.31.3/CMakeDetermineCompilerABI_C.bin", "hash": "11286436652894209166"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_state.md", "hash": "11490068797830905955"}, {"file": "build/.cmake/api/v1/reply/directory-.-MinSizeRel-d0094a50bb2071803777.json", "hash": "2254957343676018481"}, {"file": "docs/project_md/smart_annotator.md", "hash": "10058532935138847851"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_io.md", "hash": "15280929294455826365"}, {"file": "tmp_structure/tools/utils/print_env.py", "hash": "15462824488675427187"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceData.md", "hash": "3407684398554571116"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/event_dispatcher.md", "hash": "6014464624155038316"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_dma.md", "hash": "8057560216873489968"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/driver_core.md", "hash": "6642007692352427576"}, {"file": "tmp_structure/tests/unit/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "tmp_structure/src/driver/queue.c", "hash": "1510108008238614011"}, {"file": "tests/test_api_manager.py", "hash": "8550478795907993636"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceExport.md", "hash": "17280862790576496424"}, {"file": "tests/unit/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "tests/unit/queue.c", "hash": "4795497370184670599"}, {"file": "tests/performance/performancedata.c", "hash": "10364043397620603240"}, {"file": ".vscode/driver_signing_config.json", "hash": "2007317409999427743"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchPerformance.md", "hash": "6315740490143290014"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorHandler.md", "hash": "15475208645081206135"}, {"file": "tmp_structure/tests/unit/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "tests/unit/modulemanager.c", "hash": "12705701726995479740"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/module_manager.md", "hash": "11840949454089485922"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model_evaluation.md", "hash": "8588449621368013720"}, {"file": "scripts/project_file_auto_reader.py", "hash": "11227123832079663649"}, {"file": "Cmakelists.txt", "hash": "5284639952228040397"}, {"file": "tmp_structure/tools/fix/fix_source_standards.py", "hash": "16802809975601174919"}, {"file": "tmp_structure/tests/unit/simd_core.c", "hash": "12729929013449999851"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/spi_interface.md", "hash": "5118231581729686616"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_usb.md", "hash": "18152046806154247336"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_monitor_manager.md", "hash": "8280728422518682923"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model_ensemble.md", "hash": "2913442869778386948"}, {"file": "tests/unit/device_test.c", "hash": "4283402855535051437"}, {"file": "test_doubao_sdk.py", "hash": "6469760139033215625"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model_ensemble.md", "hash": "2913442869778386948"}, {"file": "tests/unit/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/simd_prediction.md", "hash": "7095774571489160819"}, {"file": "signdriverguidebug.bat", "hash": "6182625504451508622"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/unit_tests.lastbuildstate", "hash": "528630556704688606"}, {"file": "tmp_structure/tests/unit/optimization_history.c", "hash": "11553267181717299345"}, {"file": "tmp_structure/tests/unit/dmaoptimizer.h", "hash": "17813436674171689238"}, {"file": "tests/src/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/queue_test.md", "hash": "9597764611364215491"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_timer.md", "hash": "704186043498800617"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "tmp_structure/tools/utils/agents/agent_discussion.py", "hash": "1336863081078536932"}, {"file": "tmp_structure/tests/unit/event_buffer.c", "hash": "7571709781436123223"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "tests/unit/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAHandler.md", "hash": "4752476239246391491"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "build/.cmake/api/v1/reply/target-analyze-dependencies-Debug-0c879f9bc54410138226.json", "hash": "16101356701135064079"}, {"file": "tmp_structure/tests/unit/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "build/full-quality-check.vcxproj.filters", "hash": "2011835739732795993"}, {"file": "tmp_structure/tests/unit/errormanager.h", "hash": "11956927130609736461"}, {"file": "tmp_structure/tools/utils/__pycache__/mace_system.cpython-313.pyc", "hash": "4916069930443939486"}, {"file": "tmp_structure/tools/fix/fix_line_endings.ps1", "hash": "8795204320686989167"}, {"file": "build/CTestTestfile.cmake", "hash": "5748026853770011401"}, {"file": "include/hal/bus/kmdf_gpio.h", "hash": "5087092588717075623"}, {"file": "simple_knowledge_result.json", "hash": "14876908669090570886"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_core.md", "hash": "1711428132703189616"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-Debug-58bf87a980b8a045b764.json", "hash": "16673725507908079927"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/queue_test.md", "hash": "1004855502735791525"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "docs/project_md/test_annotator.py.teaching.md", "hash": "2315945858860061231"}, {"file": "tmp_structure/driver_analysis.py", "hash": "6874825107015864783"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "304722371227472817"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "tests/unit/simd_core.c", "hash": "12729929013449999851"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_ops.md", "hash": "6617533335334583642"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/simd_optimizer.md", "hash": "1767557284205257898"}, {"file": "build/KMDFDriver1.vcxproj", "hash": "8511934756221250730"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_core.md", "hash": "9042388263742623687"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_state_manager.md", "hash": "13097609162600527176"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/KMDF Driver1/tests/src/core/resource_management_test.md", "hash": "12104931785735659015"}, {"file": "tests/src/utils/interrupt/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_monitor.md", "hash": "8115688258881368457"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/monitor.md", "hash": "12333284298038535705"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/trace_core.md", "hash": "14024586508680330554"}, {"file": "src/hal/devices/gpio_device.c.bak", "hash": "14781073600839470055"}, {"file": "docs/project_md/cursor_machine_id.md", "hash": "12525391712515250278"}, {"file": "tests/src/security/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "tests/src/utils/simd/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorHandler.md", "hash": "17849145562086669299"}, {"file": "build/Testing/Temporary/LastTest_20250518-1434.log", "hash": "13172194162552154085"}, {"file": "nx.json", "hash": "14443875240832808589"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/RUN_TESTS_force.rule", "hash": "1819760324823437288"}, {"file": "project-tree.py", "hash": "13476751054029764548"}, {"file": "docs/KMDF Driver1/tests/src/utils/CompressionPredictor.md", "hash": "1323356466455046451"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handler.md", "hash": "130901189025584605"}, {"file": "tests/test_config_loader.py", "hash": "3011664226933306699"}, {"file": "tests/unit/device_control.c", "hash": "1983799697968931608"}, {"file": "src/hal/devices/spi_device.c", "hash": "15877033809756349196"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_interrupt.md", "hash": "3814950842790577981"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger.md", "hash": "14318949831029739559"}, {"file": "tests/src/performance/performance_benchmark.c", "hash": "11801416405866941523"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ReliabilityManager.md", "hash": "8634971326101470578"}, {"file": "tests/src/memory/MemoryCompression.c", "hash": "10542558592008849227"}, {"file": "tests/unit/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceMetricsImpl.md", "hash": "13584290171069905482"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "cleanupproject.ps1", "hash": "12508153147597765990"}, {"file": "src/core/log/driver_log.c", "hash": "3684527437252715502"}, {"file": "tests/CMakeLists.txt", "hash": "12196540828055306736"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": "docs/project_md/ai_deployment_setup.md", "hash": "12038449922546488687"}, {"file": "tests/src/performance/PerformanceExport.c", "hash": "7332497143218165792"}, {"file": "tmp_structure/tools/fix/fix_header_guard_format.py", "hash": "17645422050763808232"}, {"file": "docs/KMDF Driver1/include/device/io/device_io.md", "hash": "15280929294455826365"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_health_monitor.md", "hash": "11811529164322313522"}, {"file": "tmp_structure/tools/utils/run_wpp.ps1", "hash": "16363139438204069178"}, {"file": "build/KMDFDriver1.sln", "hash": "4091615882908690178"}, {"file": "tests/src/utils/simd/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorRecovery.md", "hash": "17104977433029554526"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/semantic_agent.cpython-313.pyc", "hash": "8694611905034078421"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_operations.md", "hash": "12450057505270295025"}, {"file": "build/.cmake/api/v1/reply/target-run_unit_tests-RelWithDebInfo-dd07e6259d6ecec8ab7d.json", "hash": "6578274857921059366"}, {"file": "tmp_structure/src/log/driver_log.c", "hash": "16668896646093452722"}, {"file": "build/.cmake/api/v1/reply/target-run-tests-RelWithDebInfo-76f6c5e7868abb6d8741.json", "hash": "10486406065451624556"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_manager.md", "hash": "17231002221847300177"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceMetricsImpl.md", "hash": "6718369574091188526"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-Release-58bf87a980b8a045b764.json", "hash": "16673725507908079927"}, {"file": "tests/unit/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "tmp_structure/tests/unit/securitymanager.c", "hash": "2211632392311551414"}, {"file": "src/hal/bus/gpio_core.c", "hash": "11622035365944367488"}, {"file": "tmp_structure/tests/unit/usb_interface.c", "hash": "15002876401871970527"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "tmp_structure/tests/unit/error_handling.c", "hash": "15024041996413401458"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/Cl.items.tlog", "hash": "6087681674888703486"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "tests/unit/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "tests/src/utils/ml/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "tests/src/utils/AlertManager.cpp", "hash": "9142306627596829532"}, {"file": "version.h", "hash": "5153955185604433877"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/QueueOperations.md", "hash": "11607574489902703500"}, {"file": "include/core/device/device_manager.h", "hash": "855725757395601213"}, {"file": "fix_test_filenames.bat", "hash": "14819378410324417697"}, {"file": "tests/unit/error_prevention.c", "hash": "6292741100613388313"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "tmp_structure/tests/unit/errorprevention.c", "hash": "10299302956295713904"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "fix_driver_filenames.bat", "hash": "6743429918490295169"}, {"file": "tmp_structure/tools/utils/debug_driver_load.ps1", "hash": "7995531304533082782"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorPrevention.md", "hash": "17818079868441769260"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/io/IOOptimizer.md", "hash": "2425979290851834818"}, {"file": "tmp_structure/tools/test/standardize_tests.py", "hash": "15877158136264827518"}, {"file": "tmp_structure/tests/unit/touchmanager.h", "hash": "6380166057180092411"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model.md", "hash": "5760546782410447305"}, {"file": "build/Testing/20250518-1434/Test.xml", "hash": "18258110999652138923"}, {"file": "tmp_structure/tests/unit/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "tests/src/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": "tests/src/utils/ml/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "tests/performance/touchperformance.c", "hash": "5007490272852559090"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "tmp_structure/tools/fix/code_fixer.py", "hash": "4486771173918266878"}, {"file": "tests/src/utils/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "fix_hal_files.cmd", "hash": "16562127807537512199"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "tools/doc_generator.py", "hash": "1894546107717736772"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/trace.md", "hash": "938798632008336272"}, {"file": "build/CMakeFiles/3.31.3/CMakeCCompiler.cmake", "hash": "13123115118070135955"}, {"file": "test_ark_sdk.py", "hash": "18356357060012908025"}, {"file": "tmp_structure/tests/unit/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/Predictor.md", "hash": "10386180171614306631"}, {"file": "tests/unit/kmdfspi.c", "hash": "1159729704795014365"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_manager.md", "hash": "15570914612634812124"}, {"file": "fix_remaining_headers.py", "hash": "8144367279178154126"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_performance_monitor.md", "hash": "5561470136532144482"}, {"file": "tmp_structure/tests/unit/interrupt.c", "hash": "438091713417069452"}, {"file": "tests/src/utils/device/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "docs/KMDF Driver1/include/core/types/CommonTypes.md", "hash": "7598532320659218397"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "header_template.h", "hash": "2209264767001714824"}, {"file": "tmp_structure/tools/fix/fix_headers.py", "hash": "10161838224352095660"}, {"file": "tmp_structure/tools/utils/agents/semantic_agent.py", "hash": "14484017070558256533"}, {"file": ".vs/KMDF Driver1/v17/Browse.VC.db", "hash": "3583566759131987680"}, {"file": "tests/src/utils/device.tmh", "hash": "5558032582126480111"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/i2_c_interface.md", "hash": "17772133008981429129"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": ".clang-format", "hash": "17813942105361887437"}, {"file": "tmp_structure/tests/unit/touchai.h", "hash": "7382272859188051659"}, {"file": "tmp_structure/tests/unit/iomanager.c", "hash": "6435585653620536882"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_operations.md", "hash": "12450057505270295025"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFSPI.md", "hash": "5079633368373070158"}, {"file": "tests/src/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "docs/project_md/CMakeCache.txt", "hash": "17177311492269572653"}, {"file": "docs/CMakeLists.txt", "hash": "12467392364659254886"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_core.md", "hash": "2996160899354651317"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "14979245498152404618"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_manager.md", "hash": "17363996027966417800"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_manager.md", "hash": "12781635121050400413"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/cache_optimizer.md", "hash": "4292989671843767456"}, {"file": "docs/KMDF Driver1/backup/include/hardware/kmdf_hal.md", "hash": "7178785057074432366"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_model.md", "hash": "2200148848594683661"}, {"file": "build/.cmake/api/v1/reply/target-format-code-Debug-86279ed80debd9c7b8b0.json", "hash": "10186950633527459120"}, {"file": "tmp_structure/tests/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "tmp_structure/tests/unit/device_test.c", "hash": "4283402855535051437"}, {"file": "src/hal/bus/spi_core.c", "hash": "13313496466091156142"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/Queue.md", "hash": "10162557302537918983"}, {"file": ".vs/KMDF Driver1/FileContentIndex/3af8275c-b9d5-4d99-a938-c606361ca9cf.vsidx", "hash": "12815895691180002701"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_collector.md", "hash": "354623443312585585"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_interrupt.md", "hash": "8991698687127595156"}, {"file": "tmp_structure/tests/unit/memorymanager.h", "hash": "10010922765026236252"}, {"file": "tests/unit/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "tmp_structure/tests/unit/dmahandler.c", "hash": "7693675145006693987"}, {"file": "config/models/xunfei.json", "hash": "6867580776858784280"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Hardware.md", "hash": "12499629258003032487"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_manager_1.md", "hash": "11358954628081867466"}, {"file": "tests/src/device/TouchDebug.c", "hash": "11957451505368650923"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/error/ErrorTypes.md", "hash": "1324368017208072176"}, {"file": "tmp_structure/tests/performance/performance.c", "hash": "542837662377689141"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_collector.md", "hash": "14684244266011149021"}, {"file": "include/hal/hal_interface.h", "hash": "6192118232442890480"}, {"file": "tests/src/driver/driver_stubs.c", "hash": "770506472769498247"}, {"file": "docs/KMDF Driver1/backup/include/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "docs/structured_docs/api_reference/wdf_设备函数.md", "hash": "16787713271245699007"}, {"file": "tmp_structure/tests/unit/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ProcessFunctions.md", "hash": "5303515720344831466"}, {"file": "tests/src/utils/manager.cpp", "hash": "16568954766253776110"}, {"file": "tests/src/utils/ReliabilityManager.c", "hash": "16962898220370878971"}, {"file": "tmp_structure/tools/utils/check_includes.py", "hash": "5368652997572253015"}, {"file": "include/hal/devices/i2c_device.h", "hash": "7372793240612711394"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/analyzer.md", "hash": "8824479516875383820"}, {"file": "tests/unit/simd_optimization.c", "hash": "1016262082760471380"}, {"file": "tests/src/utils/PerfMonitor.c", "hash": "11571150342783768226"}, {"file": "docs/KMDF Driver1/backup/20250506_205501/include/core/types/kmdf_types.md", "hash": "161877053758335927"}, {"file": "tests/unit/auto_tuning.h", "hash": "11138856584000903567"}, {"file": "tmp_structure/tools/utils/agents/best_practice_agent.py", "hash": "10095155569342356554"}, {"file": "tmp_structure/tests/unit/device_monitor.c", "hash": "10802691851731843766"}, {"file": ".vs/CMake Overview", "hash": "3244421341483603138"}, {"file": "build/Testing/TAG", "hash": "10024229132271908305"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_context.md", "hash": "5783058402535230175"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "tmp_structure/tools/utils/format_code.ps1", "hash": "8664614111954803457"}, {"file": "tmp_structure/tools/utils/api_adapter.py", "hash": "5534168103360626276"}, {"file": "tmp_structure/tests/unit/module_manager.c", "hash": "6226471683647205323"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_workitem.md", "hash": "3984672476542598313"}, {"file": "tests/unit/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": "build/cmake_install.cmake", "hash": "13705602105611335771"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/simd_optimizer.md", "hash": "16564215083805726102"}, {"file": ".cursor/rules/nx-rules.mdc", "hash": "14285768348196013884"}, {"file": "tests/unit/hardware.h", "hash": "2185692304651730343"}, {"file": "tests/src/utils/simd/simd_operations.c", "hash": "2970634337399079429"}, {"file": "tests/src/driver/driver_core.c", "hash": "2424469765469195657"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/event_trace.md", "hash": "12299964648108506000"}, {"file": "docs/KMDF Driver1/backup/include/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": ".github/config/deployment_config.json", "hash": "6719983897857056232"}, {"file": "build/.cmake/api/v1/reply/target-format-code-RelWithDebInfo-86279ed80debd9c7b8b0.json", "hash": "10186950633527459120"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath.vcxproj", "hash": "11130639011870723605"}, {"file": "docs/KMDF Driver1/include/core/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "tmp_structure/tests/unit/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "scripts/simple_check.ps1", "hash": "5426566532885727715"}, {"file": "tests/unit/dmaoptimizer.c", "hash": "12446589818635954550"}, {"file": "cmake/driver_tests.cmake", "hash": "18206696834570218411"}, {"file": "tmp_structure/tests/unit/errormanager.c", "hash": "14480009171928608282"}, {"file": "tests/unit/dmamanager.h", "hash": "7624006582029009443"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger_core.md", "hash": "3068037791562044206"}, {"file": "tests/unit/reliabilitymanager.c", "hash": "16962898220370878971"}, {"file": "docs/project_md/multi_ai_collaboration_plan.md", "hash": "4434216606880434162"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "docs/KMDF Driver1/backup/include/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_dma.md", "hash": "15203853900872757579"}, {"file": "tests/unit/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "tests/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "tmp_structure/tools/utils/core/__pycache__/adaptive_output.cpython-313.pyc", "hash": "16586423574870507236"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceLogger.md", "hash": "10635370005391666013"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_performance_monitor.md", "hash": "2458298706816984463"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAManager.md", "hash": "4927005382091664141"}, {"file": "tmp_structure/tools/utils/setup_env.ps1", "hash": "6182253896198528092"}, {"file": "analysis_output/dependency_analysis.json", "hash": "9543739775522133972"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "tmp_structure/tests/unit/logger_test.c", "hash": "15334138061466012986"}, {"file": "tests/src/utils/trace/event_trace.c", "hash": "10188444021102792748"}, {"file": ".vscode/code_analysis_config.json", "hash": "312228725902229289"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/config_manager.md", "hash": "14863063657951608679"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_predictor.md", "hash": "13170289443993434604"}, {"file": "tests/src/utils/interrupt/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_i2c.md", "hash": "4279440534981425318"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFUSB.md", "hash": "2444171969419869765"}, {"file": "tests/src/utils/health_checker.c", "hash": "13081061987138608445"}, {"file": "docs/KMDF Driver1/backup/include/core/Common.md", "hash": "14593471796666110241"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_usb.md", "hash": "18152046806154247336"}, {"file": "build/run-tests.vcxproj.filters", "hash": "8174687166022519349"}, {"file": "tests/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "tests/unit/queue_test.c", "hash": "665738556786491367"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_monitor.md", "hash": "16894954371874134391"}, {"file": "tmp_structure/tools/utils/simple_driver_sign.ps1", "hash": "9714128146809078086"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorCore.md", "hash": "1340757887149976797"}, {"file": "tests/unit/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "tests/src/utils/device/device.c", "hash": "6161212960231513500"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger_trace.md", "hash": "18408039824941962990"}, {"file": "tmp_structure/tests/performance/perfmonitor.c", "hash": "11571150342783768226"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_queue_1.md", "hash": "9135148644199135364"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_queue.md", "hash": "4268410109923421812"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/AlertManager.md", "hash": "6970097577749661159"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "tests/src/utils/optimization/optimization_history.c", "hash": "11553267181717299345"}, {"file": "tmp_structure/tools/utils/agents/__init__.py", "hash": "7742675949575766707"}, {"file": ".windsurf<PERSON><PERSON>", "hash": "4880051458355600887"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "tests/unit/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "tmp_structure/tools/utils/generate_driver_comments.py", "hash": "5763936872795964941"}, {"file": "tmp_structure/tests/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "tests/unit/optimization_history.c", "hash": "11553267181717299345"}, {"file": "tests/unit/device_timer.c", "hash": "3835497012896294941"}, {"file": "tests/src/driver/KMDFDriver1.inf", "hash": "17027072167203267679"}, {"file": "tests/src/driver/DriverEntry.c", "hash": "15432365288492737233"}, {"file": "tmp_structure/tests/unit/driver_core.c", "hash": "2424469765469195657"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "304722371227472817"}, {"file": "tests/src/utils/hal.c", "hash": "13073342420439847883"}, {"file": "tmp_structure/tools/utils/core/__init__.py", "hash": "10839681011534471175"}, {"file": ".vs/KMDF Driver1/FileContentIndex/f57f5149-efaa-499c-b70e-9e6091fa303e.vsidx", "hash": "12643329660832643390"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/feature_engineering.md", "hash": "11114454749909286843"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/distributed_training.md", "hash": "5129859750226130572"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "tests/src/utils/registry_callback.c", "hash": "17205669410881855421"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "env", "hash": "1346634933353421132"}, {"file": "tmp_structure/tools/fix/fix_all_formatting.ps1", "hash": "7231079512419148032"}, {"file": "test_output.txt", "hash": "4331091727924570233"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "tmp_structure/tests/unit/memory_manager.c", "hash": "13099111058581566695"}, {"file": "tests/unit/driver.c", "hash": "5937756760430682963"}, {"file": "tmp_structure/include/device/device_types.h", "hash": "2655726247897297113"}, {"file": "tests/unit/kmdflogger.c", "hash": "3048538446081918844"}, {"file": "tests/unit/spi_controller.c", "hash": "15313722384938567487"}, {"file": "docs/KMDF Driver1/tests/src/utils/cache_manager.md", "hash": "331729552811588733"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/agent_discussion.cpython-313.pyc", "hash": "1448609043699339899"}, {"file": "docs/KMDF Driver1/backup/tests/src/io/IOManager.md", "hash": "12962392107439165305"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_manager.md", "hash": "4482833301212000020"}, {"file": "tmp_structure/output/教学文档.md", "hash": "16979563766180323480"}, {"file": "tests/src/utils/analyzer.cpp", "hash": "13899095304220185495"}, {"file": "runqualitycheck.bat", "hash": "6312967091181481470"}, {"file": "tests/src/utils/trace/trace.c", "hash": "7049877013033903552"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_context.md", "hash": "11117283112430833157"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_context.md", "hash": "11117283112430833157"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/event_tracer.md", "hash": "15011940588707327893"}, {"file": "tests/__pycache__/test_api_manager.cpython-314.pyc", "hash": "17423905573846310036"}, {"file": "tests/src/utils/ml/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/power_optimizer.md", "hash": "1361782774390628471"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "docs/project_md/idear1.txt", "hash": "9456835543446444211"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "docs/KMDF Driver1/include/driver/log/DriverLog.md", "hash": "1410373447449831134"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "docs/doxygen/doc_summary_full.json", "hash": "5020128253340977997"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "tests/unit/config_manager.c", "hash": "18100728998672094806"}, {"file": "tests/unit/coremanager.c", "hash": "9001638691650737173"}, {"file": "tmp_structure/include/bus/kmdf_usb.h", "hash": "11899584837454725746"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/statistics_manager.md", "hash": "8721089824310490847"}, {"file": "build/CMakeFiles/CMakeConfigureLog.yaml", "hash": "16865135216480235896"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "tmp_structure/tests/unit/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-Debug-c02d6f965b8ca5a72d17.json", "hash": "14998815112677804211"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/kmdf_core.md", "hash": "14537529512716125567"}, {"file": "tmp_structure/tests/unit/touchmanager.c", "hash": "2612989593074257093"}, {"file": "scripts/fix_formatting.ps1", "hash": "4421299483655137804"}, {"file": "docs/project_md/ai_optimization_strategies.md", "hash": "14102316258447283852"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "docs/KMDF Driver1/tests/src/io/IOManager.md", "hash": "13313837815783856174"}, {"file": "tmp_structure/tools/test/install_test_driver.ps1", "hash": "685135710408998420"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_operations.md", "hash": "4926486078439259071"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformancePredictor.md", "hash": "13344106447495631249"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "docs/KMDF Driver1/backup/include/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/knowledge_agent.cpython-313.pyc", "hash": "3595420196522840354"}, {"file": "docs/KMDF Driver1/tests/src/utils/statistics_manager.md", "hash": "10687184947354352039"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handling_test.md", "hash": "7236286416758687736"}, {"file": "tests/src/device/TouchManager.h", "hash": "7272426768181106230"}, {"file": "build/.cmake/api/v1/reply/target-analyze-dependencies-Release-0c879f9bc54410138226.json", "hash": "16101356701135064079"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "tmp_structure/tests/unit/gamepadcore.c", "hash": "15479958173847767625"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_test.md", "hash": "5528223060007826182"}, {"file": "tmp_structure/tests/unit/error_handler.c", "hash": "2913023791582526489"}, {"file": "tmp_structure/tools/fix/fix_comments.py", "hash": "10782642882713634312"}, {"file": "everythingsearch.bat", "hash": "3155300558556554196"}, {"file": "build/Testing/Temporary/LastTest_20250519-0442.log", "hash": "4873010348993163695"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/sample_test.md", "hash": "13965977043636224817"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": "tests/performance/touchperformance.h", "hash": "6747028698210755022"}, {"file": "tests/unit/resource_optimizer1.c", "hash": "9728211600653272904"}, {"file": "tmp_structure/tests/unit/memorycompression.c", "hash": "16305212441378240323"}, {"file": "generateteachingnotes.bat", "hash": "13489632455467238609"}, {"file": "config/models/pollinations.json", "hash": "1667566971839229310"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "tmp_structure/tests/unit/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handler.md", "hash": "130901189025584605"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/structural_agent.cpython-313.pyc", "hash": "3684322942785208257"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/SecurityManager.md", "hash": "3884452379323183165"}, {"file": "build/.cmake/api/v1/reply/index-2025-05-22T09-38-44-0363.json", "hash": "12675277458864185341"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "docs/project_structure.md", "hash": "2037958482711414603"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceManager.md", "hash": "4322100284458284897"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "tmp_structure/tests/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_test.md", "hash": "7510514405337728791"}, {"file": "src/hal/bus/i2c_core.c", "hash": "11661495173686531886"}, {"file": "tests/src/utils/device/device_queue_1.c", "hash": "330133164890642531"}, {"file": "docs/KMDF Driver1/tests/src/device/Touch.md", "hash": "5297632455794381816"}, {"file": "tests/performance/performance_collector.c", "hash": "9157021843784231895"}, {"file": "tmp_structure/tests/unit/hardwaremanager.c", "hash": "5852295366093323504"}, {"file": "build/test_driver.vcxproj", "hash": "11958420518030220631"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_benchmark.md", "hash": "4089739322338621121"}, {"file": "docs/project_md/collaboration_process.py.teaching.md", "hash": "2940783448297322589"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_resource.md", "hash": "474047599558141070"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_framework_test.md", "hash": "5313371865062439033"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_test.md", "hash": "17457826267075368302"}, {"file": "templates/teaching_prompt.json", "hash": "6745021026298905574"}, {"file": "build/run_unit_tests.vcxproj", "hash": "3947431855474996163"}, {"file": "tmp_structure/tests/unit/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "build/CMakeFiles/cmake.check_cache", "hash": "8396945883222416317"}, {"file": "tests/src/utils/device/device_dma.c", "hash": "7826498135188615291"}, {"file": "tmp_structure/tests/unit/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": "tests/unit/device_dma.c", "hash": "7826498135188615291"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_benchmark.md", "hash": "9826305723782864030"}, {"file": "tests/src/utils/power/power_manager.c", "hash": "16887739356335473604"}, {"file": "tests/unit/driverentry.c", "hash": "15432365288492737233"}, {"file": "tmp_structure/tests/performance/touchperformance.h", "hash": "12049939923929688356"}, {"file": "tmp_structure/tests/performance/performance_monitor_manager.c", "hash": "6036622391152221299"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "tests/unit/driver_core.c", "hash": "2424469765469195657"}, {"file": "tmp_structure/tests/unit/resource_manager.c", "hash": "157428320305306828"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CommonUtils.md", "hash": "13690485749395410695"}, {"file": "docs/api/glhf.chatuser-settingsapi.txt", "hash": "16963602934096152440"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/core/error_handling.md", "hash": "8226706213229477559"}, {"file": "tmp_structure/tools/utils/analyze_driver.ps1", "hash": "2536145212828588932"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "tests/unit/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchDebug.md", "hash": "1115055897695565800"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_usb.md", "hash": "18093605669182738302"}, {"file": "build/Testing/Temporary/LastTest_20250521-1235.log", "hash": "3933739356164602884"}, {"file": "tmp_structure/tests/unit/error_recovery_framework.c", "hash": "13699128380064941463"}, {"file": "docs/KMDF Driver1/tests/src/security/error_manager.md", "hash": "13578911875909071015"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/Driver.md", "hash": "3073102130935575849"}, {"file": "header_consistency_summary.md", "hash": "6415337201004196467"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/distributed_training.md", "hash": "5668726731270982163"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_error_monitor.md", "hash": "3841573886328726193"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/usb_interface.md", "hash": "1081299217814221501"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery.md", "hash": "11567773110083975900"}, {"file": "tests/performance/performancemanager.c", "hash": "12428470120826957350"}, {"file": "tests/unit/device_state.c", "hash": "232531793412726154"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceManager.md", "hash": "2592179390791234152"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_framework.md", "hash": "14221675578553986939"}, {"file": "scripts/fix_header_guards.ps1", "hash": "12174742135892345499"}, {"file": "tests/src/security/error_module.c", "hash": "10513386486768326845"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_queue.md", "hash": "10025303424683255249"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/cache_manager.md", "hash": "14041659744676161652"}, {"file": ".vscode/deployment_config.json", "hash": "1768465812626934688"}, {"file": "tmp_structure/tools/fix/fix_coding_issues.ps1", "hash": "2051506399048036844"}, {"file": "tmp_structure/tools/utils/annotate.py", "hash": "14018420197385758215"}, {"file": "tests/unit/commonutils.c", "hash": "11647735861415970558"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "tests/src/memory/memory_manager.c", "hash": "13099111058581566695"}, {"file": "tests/src/memory/DMAHandler.c", "hash": "5992585881124374452"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryPool.md", "hash": "15635379478257676982"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_state.md", "hash": "4327613149066213288"}, {"file": "build/.cmake/api/v1/reply/target-run-tests-Debug-76f6c5e7868abb6d8741.json", "hash": "10486406065451624556"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/error/error_handler.md", "hash": "130901189025584605"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_timer.md", "hash": "13275692677651774437"}, {"file": "tests/src/performance/Performance.c", "hash": "542837662377689141"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_manager.md", "hash": "13629910787921453760"}, {"file": "tests/unit/error_handling.c", "hash": "15024041996413401458"}, {"file": "build/CMakeFiles/generate.stamp.depend", "hash": "5649219967778761217"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.read.1.tlog", "hash": "5351354570326925566"}, {"file": "tests/unit/device_workitem.c", "hash": "15964808878924499639"}, {"file": "tests/unit/memory_manager.c", "hash": "13099111058581566695"}, {"file": "tmp_structure/tests/unit/hal.c", "hash": "13073342420439847883"}, {"file": "tests/src/utils/device/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_init_test.md", "hash": "17333699458054262275"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/resource_optimizer_1.md", "hash": "9689921769101350801"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_reporter.md", "hash": "13138863208284489715"}, {"file": "tmp_structure/tests/unit/coremanager.c", "hash": "9001638691650737173"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/device.md", "hash": "15950813879397350842"}, {"file": "test_doubao_standard.py", "hash": "6848855045427525730"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_test.md", "hash": "11010014707977386257"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_monitor_manager.md", "hash": "16650903172073960168"}, {"file": "tests/src/driver/driver_test.c", "hash": "14081732213465014022"}, {"file": "tmp_structure/tests/unit/prediction_model.c", "hash": "10565836354052141209"}, {"file": "tests/src/device/TouchProcessor.h", "hash": "2829154043187812917"}, {"file": "tests/unit/error_core.c", "hash": "13244377760009713122"}, {"file": "tests/src/utils/test/test_framework.c", "hash": "8175968703783348918"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_manager.md", "hash": "13629910787921453760"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchPredictor.md", "hash": "12512413519988847469"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/Performance.md", "hash": "6708106772424578802"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_queue_1.md", "hash": "14570869599811644805"}, {"file": "tmp_structure/tools/analysis/generate_report.py", "hash": "8759885286234283788"}, {"file": "tmp_structure/tests/unit/kmdf_core.c", "hash": "11085129429806464239"}, {"file": ".vs/KMDF Driver1/v17/.wsuo", "hash": "408958271423915178"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "3921821082679517869"}, {"file": "tests/src/performance/PerformanceManager.c", "hash": "12428470120826957350"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_power.md", "hash": "16596401005057937692"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_scheduler.md", "hash": "12584940170140586153"}, {"file": "docs/KMDF Driver1/backup/include/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-MinSizeRel-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CompilerIdCXX.lastbuildstate", "hash": "883149326891923460"}, {"file": "tests/__pycache__/test_prompt_engine.cpython-314.pyc", "hash": "8918093362374298349"}, {"file": "tests/unit/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "tmp_structure/tools/utils/agents/knowledge_agent.py", "hash": "1375480402171389689"}, {"file": "docs/batch_processing.md", "hash": "3156366143293904858"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.command.1.tlog", "hash": "13279721637047200964"}, {"file": "docs/通过自然语言理解代码实施计划.md", "hash": "10895306788647699618"}, {"file": "tmp_structure/tools/utils/agents/agent_manager.py", "hash": "15723084622481599687"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/optimizer.md", "hash": "8333203726053157490"}, {"file": "tmp_structure/tests/unit/resource_management_test.c", "hash": "81080999296386809"}, {"file": "docs/MACE-S具体实施方案.md", "hash": "757424553699069317"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_policy.md", "hash": "9008996255866575673"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchProcessor.md", "hash": "4070778693111906027"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/device/DeviceStatTypes.md", "hash": "8956006212008637204"}, {"file": "docs/KMDF Driver1/include/device/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "tests/unit/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "docs/KMDF Driver1/tests/src/utils/telemetry_processor.md", "hash": "546651824264917243"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CMakeCCompilerId.obj", "hash": "13162870140211519626"}, {"file": "docs/KMDF Driver1/tests/src/utils/hal.md", "hash": "798073682164515556"}, {"file": "tests/unit/simd_implementation.h", "hash": "1488013038368583593"}, {"file": "docs/KMDF Driver1/backup/backup/include/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_manager.md", "hash": "1597906743517107"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_policy.md", "hash": "12279598450356209653"}, {"file": "tests/src/device/Touch.c", "hash": "2902667088611246260"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/queue.md", "hash": "2482812125865225743"}, {"file": "docs/project_md/smart_annotator.py.teaching.md", "hash": "11123789175305425867"}, {"file": "docs/MCP使用方法/memory_mcp使用指南.md", "hash": "12655324536445253388"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/simd_optimization.md", "hash": "3740213908017695348"}, {"file": "header_consistency_report.csv", "hash": "17290809390456052859"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger.md", "hash": "1999416809368717412"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/AlertManager.md", "hash": "6970097577749661159"}, {"file": "tmp_structure/tests/unit/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "12099553868555302793"}, {"file": "tmp_structure/tests/unit/dmamanager.h", "hash": "7624006582029009443"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/QueueOperations.md", "hash": "11607574489902703500"}, {"file": "reorganizeprojectenhanced.py", "hash": "5014953226700913895"}, {"file": "tests/src/driver/DriverCore.c", "hash": "2661836926917990059"}, {"file": "build/analyze-dependencies.vcxproj", "hash": "14179049733356461785"}, {"file": "docs/MACE-S系统改进计划.md", "hash": "2452393628633577333"}, {"file": "docs/KMDF Driver1/tests/src/utils/CommonUtils.md", "hash": "1783789029930977614"}, {"file": "tests/unit/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "tests/src/utils/config_group.c", "hash": "13073677126841465732"}, {"file": "build/ALL_BUILD.vcxproj.filters", "hash": "9995966739536615345"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_data_collector.md", "hash": "6566743570803561025"}, {"file": "tests/unit/device_manager1.c", "hash": "9178587879886038827"}, {"file": "tests/unit/resource_manager.c", "hash": "157428320305306828"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceAnalyzer.md", "hash": "4689131099208303134"}, {"file": "tmp_structure/tools/build/build.ps1", "hash": "12262130116809123457"}, {"file": "tests/unit/i2c_interface.c", "hash": "931873009436168421"}, {"file": "tests/src/device/TouchPredictor.c", "hash": "10637331736903154528"}, {"file": "build/test_utils.vcxproj", "hash": "8360692437292667831"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_core.md", "hash": "1711428132703189616"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model_evaluation.md", "hash": "11583937441064285777"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "tmp_structure/tools/utils/setup_crash_dump.ps1", "hash": "10910026215077032983"}, {"file": "tests/src/security/SecurityManager.c", "hash": "10840736394663120643"}, {"file": "src/hal/bus/i2c_core.c.bak", "hash": "7021082159789302476"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/ml_optimizer.md", "hash": "1811472538079544434"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": "tmp_structure/tests/unit/health_checker.c", "hash": "13081061987138608445"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "tmp_structure/tests/unit/driver_test.c", "hash": "14081732213465014022"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "docs/KMDF Driver1/build/CMakeFiles/3.31.3/CompilerIdC/CMakeCCompilerId.md", "hash": "12087973490887621766"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/kmdf_types.md", "hash": "11820292990252072646"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "14979245498152404618"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/optimizer.md", "hash": "4900046108333349195"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "tmp_structure/tests/unit/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "docs/development_environment.md", "hash": "13754982027817254662"}, {"file": "tests/unit/resource_management_test.c", "hash": "81080999296386809"}, {"file": "scripts/extract_errors.md", "hash": "2406851965891616138"}, {"file": "tmp_structure/tests/unit/config_manager.c", "hash": "18100728998672094806"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.command.1.tlog", "hash": "15703716044304800680"}, {"file": "tmp_structure/tests/unit/driver.c", "hash": "5937756760430682963"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "tests/src/utils/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "package-lock.json", "hash": "11496171963891232631"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "docs/KMDF Driver1/tests/src/utils/ModuleManager.md", "hash": "9355698304463324449"}, {"file": "tmp_structure/tools/utils/teaching_annotator.py", "hash": "3056533638353222275"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/CL.command.1.tlog", "hash": "6262474925740181382"}, {"file": "tmp_structure/tools/utils/ai_optimization_strategies.py", "hash": "7587537925346873322"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_types.md", "hash": "8207845898346842127"}, {"file": "tmp_structure/tests/performance/performance_analyzer.h", "hash": "4806248608216213265"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.secondary.1.tlog", "hash": "11303622200164425732"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_manager.md", "hash": "12926653278278343738"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/advanced_models.md", "hash": "2138160585059009631"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_benchmark.md", "hash": "4089739322338621121"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "build/.cmake/api/v1/reply/target-run_unit_tests-MinSizeRel-7eaf24d9cf98a8ea7168.json", "hash": "10257259622757883222"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_test.md", "hash": "15984150053883273172"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_framework.md", "hash": "15481924532410518968"}, {"file": "tests/unit/log_manager.h", "hash": "11472669487661912463"}, {"file": "source_template.c", "hash": "13440679973508946342"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_stubs.md", "hash": "7773882594705755902"}, {"file": "docs/KMDF Driver1/tests/src/utils/QueueOperations.md", "hash": "14464997083434033348"}, {"file": "tests/unit/kmdfi2c.c", "hash": "13687270665700358473"}, {"file": "tmp_structure/tools/fix/fix_header_guards_improved.ps1", "hash": "7993290607892531405"}, {"file": "config/models/gemini.json", "hash": "5869872821802908452"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/format-code.rule", "hash": "1819760324823437288"}, {"file": "run_mace_analysis.py", "hash": "11960632128360414116"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_core.md", "hash": "1723612259800373307"}, {"file": "tests/src/core/CoreManager.c", "hash": "15365541592656363897"}, {"file": "tests/unit/dmaoptimizer.h", "hash": "17813436674171689238"}, {"file": "tmp_structure/tools/test/test_encoding.py", "hash": "12351899566809374434"}, {"file": "tests/src/utils/ml/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "tests/unit/config_group.c", "hash": "13073677126841465732"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "tests/unit/hal.c", "hash": "13073342420439847883"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/queue_test.md", "hash": "18427168565910843215"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "tests/src/driver/KMDFUSB.c", "hash": "3804768512383554498"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/project_md/teaching_annotator.py.teaching.md", "hash": "5755903323171479734"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryPool.md", "hash": "10702931801727958310"}, {"file": "tests/performance/performancemonitor.c", "hash": "17506105073300868858"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_module.md", "hash": "16648157775560897177"}, {"file": "build/ZERO_CHECK.vcxproj.filters", "hash": "17375854973772690826"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_handling.md", "hash": "8024255047764659432"}, {"file": "tools/scripts/reorganize_project.py", "hash": "12998432177407977966"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_evaluator.md", "hash": "8871547997162145722"}, {"file": "tests/unit/test_reporter.c", "hash": "15868353065156172044"}, {"file": "tests/__pycache__/test_basic.cpython-312-pytest-7.4.4.pyc", "hash": "4085399756435944108"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_reporter.md", "hash": "1776717960419419935"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_timer.md", "hash": "13275692677651774437"}, {"file": "build/.cmake/api/v1/reply/target-run_unit_tests-Release-35979400805dc3ffdb4c.json", "hash": "6242148715137726512"}, {"file": "build/run_unit_tests.vcxproj.filters", "hash": "9080612256799994821"}, {"file": "config/models/e2b.json", "hash": "11946067316528104980"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "tests/unit/event_buffer.c", "hash": "7571709781436123223"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_predictor.md", "hash": "5785307039557457003"}, {"file": "build/.cmake/api/v1/reply/target-analyze-dependencies-RelWithDebInfo-0c879f9bc54410138226.json", "hash": "16101356701135064079"}, {"file": "tmp_structure/src/driver/driver_example.c", "hash": "3805329228591924082"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/resource_optimizer.md", "hash": "13332246610626941843"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/sample_test.md", "hash": "9338080078639493927"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery_kmdf.md", "hash": "12638653863570197085"}, {"file": "tmp_structure/tools/utils/remove_bak_files.ps1", "hash": "14369670428058771939"}, {"file": "tests/unit/buildandruntests.bat", "hash": "9798688693070873269"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/GamepadCore.md", "hash": "17415216908876162054"}, {"file": "tmp_structure/tests/unit/error_module.c", "hash": "10513386486768326845"}, {"file": ".env", "hash": "103273168256038291"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/cache_manager.md", "hash": "14041659744676161652"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/CustomBuild.write.1.tlog", "hash": "14117961221003328239"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.write.1.tlog", "hash": "4971635830808493534"}, {"file": "tmp_structure/tests/unit/gyrohwsim.c", "hash": "9939547639384961965"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFUSB.md", "hash": "3454817418977638669"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "tmp_structure/tools/utils/run_checks.ps1", "hash": "13200448761716466509"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFLogger.md", "hash": "15861020303789370683"}, {"file": "tests/unit/spi_interface.c", "hash": "13749364630813119982"}, {"file": "fix_performance_filenames.bat", "hash": "17577916249826410663"}, {"file": "build/Testing/20250520-1507/Test.xml", "hash": "5150872653836343854"}, {"file": "tmp_structure/tools/fix/fix_encoding.py", "hash": "3311722447143053633"}, {"file": "build/run_unit_tests.dir/Debug/run_unit_tests.tlog/CustomBuild.command.1.tlog", "hash": "13279721637047200964"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.command.1.tlog", "hash": "16509436315269999865"}, {"file": "tests/test_output_formatter.py", "hash": "15495299993053318027"}, {"file": "tests/src/utils/ml/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "tmp_structure/tests/unit/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "tmp_structure/tools/utils/quick_batch.py", "hash": "14073928928729352464"}, {"file": "tests/unit/errorrecovery.c", "hash": "7122396928859668049"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/config_manager.md", "hash": "14863063657951608679"}, {"file": "tests/src/utils/ml/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "tmp_structure/tests/unit/device_power.c", "hash": "12389752397870169629"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/core/device/DeviceStateTypes.md", "hash": "6763864190905748396"}, {"file": "tmp_structure/tools/fix/fix_code_style.ps1", "hash": "5626665489439777687"}, {"file": "tests/src/driver/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "tmp_structure/tests/performance/performancepredictor.c", "hash": "16358558668992702664"}, {"file": "build/.cmake/api/v1/reply/target-analyze-dependencies-MinSizeRel-0c879f9bc54410138226.json", "hash": "16101356701135064079"}, {"file": "tests/src/utils/optimization/optimization_strategies.cpp", "hash": "18062982622965410321"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_monitor.md", "hash": "17046761877009662786"}, {"file": "tmp_structure/output/研究文档.md", "hash": "9640913094441942790"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "tests/src/utils/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "tmp_structure/tools/build/check_build_config.ps1", "hash": "6673720728417110734"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/health_checker.md", "hash": "9850915435701639911"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.read.1.tlog", "hash": "11253596291149010805"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "tests/unit/hardwaremanager.c", "hash": "5852295366093323504"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-Release-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "build/Testing/20250521-1235/Test.xml", "hash": "14536998256272280517"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/HardwareManager.md", "hash": "6930293510445000701"}, {"file": "tests/unit/driver_manager.c", "hash": "18213335034763467499"}, {"file": "tmp_structure/tools/utils/check_critical_issues.py", "hash": "14317396690752310694"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchPredictor.md", "hash": "12512413519988847469"}, {"file": "tests/src/utils/simd/simd_implementation.h", "hash": "5016021608320478502"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "tmp_structure/tools/utils/core/__pycache__/integration_engine.cpython-313.pyc", "hash": "16035286460932738115"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/recovery_impl.md", "hash": "13993772039987120939"}, {"file": "docs/Doxyfile", "hash": "2063202288499522875"}, {"file": "tmp_structure/tests/unit/memorymanager.c", "hash": "13193390556443584375"}, {"file": "tmp_structure/tools/utils/generate_include_graph.py", "hash": "17821567934912060292"}, {"file": "docs/structured_docs/examples/kmdf_队列示例.md", "hash": "15192262027129395961"}, {"file": "docs/project_md/fix_all_headers.py.teaching.md", "hash": "15700785300499607381"}, {"file": "build/format-code.vcxproj", "hash": "16959484065935536609"}, {"file": "tmp_structure/tests/unit/device_manager.c", "hash": "1823201296405534462"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "tmp_structure/tests/unit/power_manager.c", "hash": "16887739356335473604"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_stat_types.md", "hash": "15997768796965679315"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_predictor.md", "hash": "13170289443993434604"}, {"file": "tests/unit/touchcontroller.c", "hash": "7422034257177845882"}, {"file": "docs/project_md/analysis_report.md", "hash": "1542542377250099719"}, {"file": "docs/KMDF Driver1/tests/src/utils/Queue.md", "hash": "4664907013583346375"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchDebug.md", "hash": "1115055897695565800"}, {"file": "tmp_structure/tests/unit/reliabilitymanager.c", "hash": "16962898220370878971"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/GlobalVariables.md", "hash": "3478961572820031987"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "tests/unit/dmamanager.c", "hash": "16085011483513293531"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "tmp_structure/tests/unit/touchprocessor.h", "hash": "11281579014250633771"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "tests/unit/device_monitor.c", "hash": "10802691851731843766"}, {"file": "tests/src/utils/GamepadCore.c", "hash": "15479958173847767625"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_init_test.md", "hash": "17396170189008255718"}, {"file": "tmp_structure/docs/project_structure.md", "hash": "3281558196775761998"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorPrevention.md", "hash": "17818079868441769260"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_manager.md", "hash": "968320189468067565"}, {"file": "tmp_structure/tests/unit/compressionpredictor.c", "hash": "11817161568988905271"}, {"file": "tests/performance/performancepredictor.c", "hash": "16358558668992702664"}, {"file": "tests/unit/event_trace.c", "hash": "10188444021102792748"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "tests/src/utils/device/device_io.c", "hash": "9774509186909211676"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/module_manager.md", "hash": "11840949454089485922"}, {"file": "test_huoshan_api.py", "hash": "11998932597004611737"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "build/unit_tests.dir/Debug/unit_tests.tlog/CustomBuild.command.1.tlog", "hash": "13279721637047200964"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceExport.md", "hash": "17280862790576496424"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_health_monitor.md", "hash": "10750751687875650870"}, {"file": "tests/unit/adaptive_scheduler.c", "hash": "10385198795992790880"}, {"file": "docs/MACE-S工作指挥计划.md", "hash": "13392322616008266012"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ConfigManager.md", "hash": "14798362301014302758"}, {"file": "docs/KMDF Driver1/backup/backup/include/device/device.md", "hash": "15950813879397350842"}, {"file": "tmp_structure/tools/fix/fix_source_files.py", "hash": "8518775545233258652"}, {"file": "tmp_structure/tests/performance/performancemonitor.c", "hash": "17506105073300868858"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchController.md", "hash": "5893240157153592323"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_monitor_manager.md", "hash": "16650903172073960168"}, {"file": "test_doubao_fixed.py", "hash": "15588857141454609785"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ModuleManager.md", "hash": "6642213020461104442"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt.md", "hash": "1138018929248212817"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "docs/KMDF Driver1/backup/include/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "tests/src/driver/KMDFLogger.c", "hash": "11681378835085244101"}, {"file": "docs/KMDF Driver1/tests/src/io/IOOptimizer.md", "hash": "10089800447872604097"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFSPI.md", "hash": "5079633368373070158"}, {"file": "gitattributes", "hash": "7779088979552154010"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_resource_monitor.md", "hash": "9018393555267135129"}, {"file": "tests/src/security/error_handling.c", "hash": "16257673885949715857"}, {"file": "tmp_structure/tests/unit/errorcore.c", "hash": "8426091125501211666"}, {"file": "tests/src/security/error_core.c", "hash": "13244377760009713122"}, {"file": "tmp_structure/tools/utils/check_code_style.ps1", "hash": "2063858751040355594"}, {"file": "tests/unit/event_tracer.c", "hash": "7710005633789833902"}, {"file": "docs/project_md/ai_collaboration_roles.md", "hash": "9550531567769795845"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/ml_optimizer.md", "hash": "1811472538079544434"}, {"file": "test_moonshot_api.py", "hash": "16792902775065019301"}, {"file": "tests/unit/memorypool.c", "hash": "8309557649383373614"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "tmp_structure/tests/unit/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "build/analyze-dependencies.vcxproj.filters", "hash": "51460067980102346"}, {"file": "tests/src/device/HardwareManager.h", "hash": "3697009021781778473"}, {"file": "tests/src/device/Hardware.c", "hash": "17567173754634071352"}, {"file": "tmp_structure/tools/fix/quick_fix.py", "hash": "13203920727107741020"}, {"file": "tools/utils/file_research_teaching_annotator.py", "hash": "11519010549794262179"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/usb_interface.md", "hash": "15608419832055058263"}, {"file": "tmp_structure/tests/unit/predictor.c", "hash": "15488274478411287487"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_prevention.md", "hash": "14119463069258361082"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "tmp_structure/tests/unit/hardwaremanager.h", "hash": "17760884888994478549"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_monitor.md", "hash": "17046761877009662786"}, {"file": "tmp_structure/tests/performance/touchperformance.c", "hash": "5007490272852559090"}, {"file": "tests/unit/kmdfhal.c", "hash": "13443276317294174339"}, {"file": "docs/KMDF Driver1/backup/include/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.secondary.1.tlog", "hash": "4846258542514509865"}, {"file": "tests/src/utils/config_manager.c", "hash": "18100728998672094806"}, {"file": "tests/src/memory/DMAManager.c", "hash": "8084048833832484340"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_operations.md", "hash": "7330894205852388285"}, {"file": "tests/unit/errormanager.h", "hash": "18142214723588776258"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_performance_monitor.md", "hash": "2458298706816984463"}, {"file": "runtest.bat", "hash": "14285696330554629230"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "tests/src/utils/ml/advanced_models.c", "hash": "10802020780109055119"}, {"file": "test_doubao_api.py", "hash": "6072182743631665945"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handling.md", "hash": "11368279126817252286"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "tests/src/utils/ml/model.c", "hash": "13330488231876145168"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery_framework.md", "hash": "4481649399000513017"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "config/models/chatanywhere.json", "hash": "14294184777396776436"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery_framework.md", "hash": "4481649399000513017"}, {"file": "tests/src/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "tmp_structure/tests/unit/commoninternal.c", "hash": "12692290799640821342"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/device/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/include/device/base/device_context.md", "hash": "11117283112430833157"}, {"file": "tests/src/utils/device/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "tmp_structure/tools/utils/full_verify.ps1", "hash": "9828792850310277588"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "docs/KMDF Driver1/backup/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "test_huoshan_official.py", "hash": "9163753873127853420"}, {"file": "build/.cmake/api/v1/reply/target-format-code-MinSizeRel-86279ed80debd9c7b8b0.json", "hash": "10186950633527459120"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Touch.md", "hash": "6473562417806726533"}, {"file": "tmp_structure/tools/fix/fix_code_issues.py", "hash": "16428652738469116471"}, {"file": "tmp_structure/tools/fix/fix_tabs_v2.ps1", "hash": "11887283907230863418"}, {"file": "tmp_structure/tools/fix/fix_all_headers.py", "hash": "4555455160791820434"}, {"file": "tmp_structure/tools/utils/cursor_machine_id.py", "hash": "11784640157050564506"}, {"file": "tmp_structure/tools/utils/check_encoding.py", "hash": "13948401717445501161"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/resource_optimizer_1.md", "hash": "9689921769101350801"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_queue.md", "hash": "7516366818546283918"}, {"file": "src/hal/devices/i2c_device.c", "hash": "212178996774832595"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchDebug.md", "hash": "13795303564617599994"}, {"file": "test_huoshan_final.py", "hash": "16129081120139665508"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CommonUtils.md", "hash": "13690485749395410695"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "requirements.txt", "hash": "2447290102340481311"}, {"file": "rustup-init.exe", "hash": "6615076961598520463"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": ".cursor/rules/my-custom-rule.mdc", "hash": "9681079700429432384"}, {"file": "include/core/error/error_handling.h", "hash": "13762159866028634621"}, {"file": "tmp_structure/tests/unit/registry_callback.c", "hash": "17205669410881855421"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_test.md", "hash": "11557057547972990945"}, {"file": "tools/scripts/run_project_optimization.bat", "hash": "8415366508469273981"}, {"file": "tmp_structure/tests/unit/cache_manager.c", "hash": "868028145507112649"}, {"file": "tmp_structure/tools/test/test_annotator.py", "hash": "15337323273844871360"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.write.1.tlog", "hash": "13088518092928041168"}, {"file": "tmp_structure/tools/fix/fix_all.py", "hash": "3583425257282363557"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_power.md", "hash": "3395931921895444161"}, {"file": "templates/basic_prompt.json", "hash": "10539456766932864799"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryPool.md", "hash": "10702931801727958310"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_handler.md", "hash": "12498787032465535448"}, {"file": "docs/KMDF Driver1/tmp_structure/include/driver/driver_core.md", "hash": "1145859136680343544"}, {"file": "build/.cmake/api/v1/reply/cache-v2-7b7b6041a281fd83566c.json", "hash": "7225232178678760474"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_optimizer.md", "hash": "15191805584477784060"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_engine.md", "hash": "7704895161237555146"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/auto_optimizer.md", "hash": "11758342447071272246"}, {"file": "docs/api/星火大模型.txt", "hash": "6546154230992474501"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": "tmp_structure/tests/unit/device_resource.c", "hash": "2198688305377271422"}, {"file": "docs/KMDF Driver1/tests/src/core/GlobalVariables.md", "hash": "17948367196669321453"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.read.1.tlog", "hash": "510436365969713848"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "tests/src/core/GlobalVariables.c", "hash": "703525128182974641"}, {"file": "tmp_structure/tools/fix/verify_fixes.ps1", "hash": "10675361795171464049"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_data_collector.md", "hash": "2064764203318285168"}, {"file": "tmp_structure/include/log/trace.h", "hash": "15279218248740019239"}, {"file": "tmp_structure/tests/unit/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "tests/src/utils/device/device_resource.c", "hash": "2198688305377271422"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_power.md", "hash": "3395931921895444161"}, {"file": "tmp_structure/tests/unit/queueoperations.c", "hash": "8016806957506324853"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_cache.md", "hash": "10201547265023507712"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": "rundrivertest.bat", "hash": "8778339928498419027"}, {"file": "tmp_structure/tests/unit/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "tests/unit/error_recovery.c", "hash": "6818450905492757794"}, {"file": "docs/KMDF Driver1/tests/src/utils/notification_manager.md", "hash": "774001524088173601"}, {"file": ".vs/ProjectSettings.json", "hash": "3239194559763511211"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery.md", "hash": "5511226053469075265"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "tests/unit/driverlog.h", "hash": "10155223240312540344"}, {"file": "tests/src/utils/interrupt/interrupt.c", "hash": "438091713417069452"}, {"file": "tests/unit/iomanager.c", "hash": "6435585653620536882"}, {"file": "tmp_structure/tests/unit/resource_optimizer1.c", "hash": "9728211600653272904"}, {"file": "tests/src/test_main.c", "hash": "17856805622170871630"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_state_manager.md", "hash": "1635534176611093163"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_ops.md", "hash": "16405194461971140783"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/event_tracer.md", "hash": "4590536025397448341"}, {"file": "gitmessage", "hash": "8748897939388348871"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_codes.md", "hash": "61239640176245628"}, {"file": "tests/src/utils/power/power_test.c", "hash": "9102832245856542684"}, {"file": "tests/src/utils/ml/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.read.1.tlog", "hash": "2978925625545870786"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceMonitor.md", "hash": "9216184883811794928"}, {"file": "tests/unit/globalvariables.c", "hash": "703525128182974641"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "tmp_structure/tests/unit/device_timer.c", "hash": "3835497012896294941"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-MinSizeRel-8b0fbee096a0b5f88b1f.json", "hash": "4287772611924132176"}, {"file": "tests/src/driver/driver_manager.c", "hash": "18213335034763467499"}, {"file": "tests/performance/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryCompression.md", "hash": "11093687460873692203"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_analyzer.md", "hash": "14568771719083238013"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "tests/unit/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "build/.cmake/api/v1/reply/toolchains-v1-06b4e756c8bd416e0e57.json", "hash": "13008094141361006255"}, {"file": "tests/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "tests/src/utils/ml/prediction_context.c", "hash": "8771136116938717181"}, {"file": "tmp_structure/include/error/error_types.h", "hash": "2895476709031031151"}, {"file": "tests/src/utils/device/device_monitor.c", "hash": "10802691851731843766"}, {"file": "tmp_structure/tools/fix/fix_line_endings.py", "hash": "9496055858282095405"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchProcessor.md", "hash": "5462925402941158058"}, {"file": "tests/performance/globalperformancemanager.c", "hash": "13783737954691746004"}, {"file": "docs/KMDF Driver1/tmp_structure/include/hardware/kmdf_hal.md", "hash": "13397201630394238542"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/event_buffer.md", "hash": "11762678649378503503"}, {"file": "tests/src/utils/device/device_context.c", "hash": "3876629123896102954"}, {"file": "tmp_structure/include/bus/kmdf_i2c.h", "hash": "13876872804077991685"}, {"file": "docs/KMDF Driver1/include/core/common/Common.md", "hash": "11735538654281835954"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_performance_monitor.md", "hash": "4004437680223110827"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.write.1.tlog", "hash": "11169757439148457193"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/recovery_impl.md", "hash": "13993772039987120939"}, {"file": "tests/unit/errorhandler.c", "hash": "638403771659111855"}, {"file": "tests/src/utils/Predictor.c", "hash": "15488274478411287487"}, {"file": "tests/src/utils/ConfigManager.c", "hash": "12407172891497760359"}, {"file": "tmp_structure/tools/utils/ai_teacher_comments.py", "hash": "3774556522186816347"}, {"file": "scripts/fix_newlines.ps1", "hash": "16931800921766998893"}, {"file": "docs/MCP使用方法/context7_mcp使用指南.md", "hash": "13953553922861588048"}, {"file": "tmp_structure/tests/unit/driverlog.h", "hash": "18313408282782209901"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_core.md", "hash": "12158579485392829125"}, {"file": "tmp_structure/tests/unit/error_prevention.c", "hash": "6292741100613388313"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/notification_manager.md", "hash": "15727167061045092053"}, {"file": "docs/KMDF Driver1/backup/src/error/error_handler.md", "hash": "130901189025584605"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/power_optimizer.md", "hash": "8344076760809889555"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_history.md", "hash": "15652233555471193452"}, {"file": "docs/project_md/how_to_run_as_admin.txt", "hash": "6816337553749047106"}, {"file": "docs/KMDF Driver1/backup/backup/include/hardware/kmdf_hal.md", "hash": "7178785057074432366"}, {"file": "docs/project_md/ai_roles.md", "hash": "16678242018000698954"}, {"file": ".vs/VSWorkspaceState.json", "hash": "12966325862263698138"}, {"file": "tmp_structure/tools/utils/core/model_adapters.py", "hash": "8795703199135127069"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_cache.md", "hash": "10642137261929724889"}, {"file": "tmp_structure/tests/unit/kmdfi2c.c", "hash": "13687270665700358473"}, {"file": "tests/test_prompt_engine.py", "hash": "1752670602706898972"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/manager.md", "hash": "12857642143589749316"}, {"file": "config/templates/teaching_prompt.json", "hash": "1945385970006069574"}, {"file": "tests/src/performance/monitor.h", "hash": "16679749735999399734"}, {"file": "tests/unit/driver_test.c", "hash": "14081732213465014022"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/GyroHwSim.md", "hash": "1957359040119195426"}, {"file": "tmp_structure/tests/unit/driver_stubs.c", "hash": "770506472769498247"}, {"file": "tmp_structure/tools/fix/fix_header_guards.py", "hash": "13283570998575127369"}], "projectFileMap": {}}}