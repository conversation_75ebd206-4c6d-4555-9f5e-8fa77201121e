<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li class="current"><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_h" name="index_h"></a>- h -</h3><ul>
<li>HAL_DEVICE_CONFIG&#160;:&#160;<a class="el" href="hal__interface_8h.html#a9de221d82717e7709cdfc025378bc222">hal_interface.h</a></li>
<li>HAL_DEVICE_HANDLE&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2f4ba870132c1fd57e2d74ba94e39805">hal_interface.h</a></li>
<li>HAL_DEVICE_TYPE&#160;:&#160;<a class="el" href="hal__interface_8h.html#a4fb1c59de2a49258bb670fc1716cb5f3">hal_interface.h</a></li>
<li>HAL_RESOURCE&#160;:&#160;<a class="el" href="hal__interface_8h.html#ac75ea43249513ed2ca45aed1e8415d6d">hal_interface.h</a></li>
<li>HAL_RESOURCE_TYPE&#160;:&#160;<a class="el" href="hal__interface_8h.html#ae5253bf6d647ca59aa8ee595a35dc0de">hal_interface.h</a></li>
<li>HalCleanup()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a55e44eef19627b9eadd548b034caaf44">hal_interface.h</a></li>
<li>HalDeviceClose()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189">hal_interface.h</a></li>
<li>HalDeviceGeneric&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a4780c3839c83cca387af037262132c96">hal_interface.h</a></li>
<li>HalDeviceGPIO&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a92d06b3f91454ca875df06efdbfbbc2a">hal_interface.h</a></li>
<li>HalDeviceI2C&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d">hal_interface.h</a></li>
<li>HalDeviceIoControl()&#160;:&#160;<a class="el" href="hal__interface_8h.html#ab3ade1341d2d05db754ab4a9e3ee7198">hal_interface.h</a></li>
<li>HalDeviceMax&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5acfd2dca79da10a5294a8cce839f047ce">hal_interface.h</a></li>
<li>HalDeviceOpen()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a77d5908ab2a098166969c80028859e28">hal_interface.h</a></li>
<li>HalDevicePCI&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5af20883315e8d4369dd6d1e09991730d2">hal_interface.h</a></li>
<li>HalDeviceRead()&#160;:&#160;<a class="el" href="hal__interface_8h.html#afb35be12c0383de71b24a9eb633a74c8">hal_interface.h</a></li>
<li>HalDeviceSPI&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab">hal_interface.h</a></li>
<li>HalDeviceUART&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a0c8d0e211b11297f195283b8bab8f1e4">hal_interface.h</a></li>
<li>HalDeviceUSB&#160;:&#160;<a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5abfe18c22646c59a962b974f757552536">hal_interface.h</a></li>
<li>HalDeviceWrite()&#160;:&#160;<a class="el" href="hal__interface_8h.html#aca328a12c9937b54319d93eeaa258fab">hal_interface.h</a></li>
<li>HalHandle&#160;:&#160;<a class="el" href="i2c__device_8c.html#a96ba6885a1d23da9ee577cfc9b91ae60">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a96ba6885a1d23da9ee577cfc9b91ae60">spi_device.c</a></li>
<li>HalInitialize()&#160;:&#160;<a class="el" href="hal__interface_8h.html#aaecd38d42dd4177629b9d3318e61fd61">hal_interface.h</a></li>
<li>HalMapDeviceMemory()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a64f394cd78ae9c84f7519ea4e7be36fc">hal_interface.h</a></li>
<li>HalRegisterInterruptHandler()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a30a54ad84d36a2c154d0c0161e3585b2">hal_interface.h</a></li>
<li>HalResourceBus&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfaabdad3aabbcf2fc65e87805cb57de9ba">hal_interface.h</a></li>
<li>HalResourceDMA&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfa7b4c15222686b48c4d896822c310ec81">hal_interface.h</a></li>
<li>HalResourceInterrupt&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfa092133df88694c792610b1ae82ec7b65">hal_interface.h</a></li>
<li>HalResourceMax&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfa2397695c434dce8fe4329bf99cb0fbc3">hal_interface.h</a></li>
<li>HalResourceMemory&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfa4205b6598b2886f8862862c92f4809fc">hal_interface.h</a></li>
<li>HalResourcePort&#160;:&#160;<a class="el" href="hal__interface_8h.html#a2001a4956cfc97e42093954f9867d8dfa72db3265ff4cc83ce6897d9ca4377445">hal_interface.h</a></li>
<li>HalUnmapDeviceMemory()&#160;:&#160;<a class="el" href="hal__interface_8h.html#a5b5334cd6270916df7647c684fe192dd">hal_interface.h</a></li>
<li>HANDLE_ERROR&#160;:&#160;<a class="el" href="Common_8h.html#ab47772eb758c3b2a1a990360eaf8ad5c">Common.h</a></li>
<li>HandleStateChange()&#160;:&#160;<a class="el" href="gpio__device_8c.html#ab3fc244423e0b04d6b75007d82be3b1b">gpio_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
