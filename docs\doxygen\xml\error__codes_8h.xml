<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="error__codes_8h" kind="file" language="C++">
    <compoundname>error_codes.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includedby refid="device__manager_8h" local="yes">C:/KMDF Driver1/include/core/device/device_manager.h</includedby>
    <includedby refid="driver__core_8h" local="yes">C:/KMDF Driver1/include/core/driver/driver_core.h</includedby>
    <includedby refid="driver__entry_8h" local="yes">C:/KMDF Driver1/include/core/driver/driver_entry.h</includedby>
    <includedby refid="error__handling_8h" local="yes">C:/KMDF Driver1/include/core/error/error_handling.h</includedby>
    <includedby refid="kmdf__bus__common_8h" local="yes">C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h</includedby>
    <includedby refid="hal__interface_8h" local="yes">C:/KMDF Driver1/include/hal/hal_interface.h</includedby>
    <includedby refid="device__manager_8c" local="yes">C:/KMDF Driver1/src/core/device/device_manager.c</includedby>
    <includedby refid="driver__core_8c" local="yes">C:/KMDF Driver1/src/core/driver/driver_core.c</includedby>
    <includedby refid="error__handling_8c" local="yes">C:/KMDF Driver1/src/core/error/error_handling.c</includedby>
    <includedby refid="driver__main_8c" local="yes">C:/KMDF Driver1/src/driver_main.c</includedby>
    <includedby refid="gpio__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/gpio_core.c</includedby>
    <includedby refid="i2c__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/i2c_core.c</includedby>
    <includedby refid="spi__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/spi_core.c</includedby>
    <includedby refid="gpio__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/gpio_device.c</includedby>
    <includedby refid="i2c__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/i2c_device.c</includedby>
    <includedby refid="spi__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/spi_device.c</includedby>
    <incdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="2">
        <label>C:/KMDF Driver1/include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>C:/KMDF Driver1/include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/include/core/driver/driver_entry.h</label>
        <link refid="driver__entry_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="26" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="27" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="22" relation="include">
        </childnode>
        <childnode refid="25" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>C:/KMDF Driver1/include/core/error/error_handling.h</label>
        <link refid="error__handling_8h"/>
      </node>
      <node id="16">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
        <childnode refid="20" relation="include">
        </childnode>
        <childnode refid="23" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="17">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
      </node>
      <node id="20">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="21" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="22" relation="include">
        </childnode>
      </node>
      <node id="23">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="24" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="25" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>C:/KMDF Driver1/include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="19" relation="include">
        </childnode>
      </node>
      <node id="21">
        <label>C:/KMDF Driver1/include/hal/devices/i2c_device.h</label>
        <link refid="i2c__device_8h"/>
      </node>
      <node id="24">
        <label>C:/KMDF Driver1/include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="25" relation="include">
        </childnode>
      </node>
      <node id="26">
        <label>C:/KMDF Driver1/include/hal/hal_interface.h</label>
        <link refid="hal__interface_8h"/>
        <childnode refid="22" relation="include">
        </childnode>
        <childnode refid="25" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
      </node>
      <node id="7">
        <label>C:/KMDF Driver1/src/core/driver/driver_core.c</label>
        <link refid="driver__core_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/core/driver/driver_entry.c</label>
        <link refid="driver__entry_8c"/>
      </node>
      <node id="27">
        <label>C:/KMDF Driver1/src/core/error/error_handling.c</label>
        <link refid="error__handling_8c"/>
      </node>
      <node id="9">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="10">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="11">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="12">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="19">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
      </node>
      <node id="22">
        <label>C:/KMDF Driver1/src/hal/devices/i2c_device.c</label>
        <link refid="i2c__device_8c"/>
      </node>
      <node id="25">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
      </node>
      <node id="13">
        <label>C:/KMDF Driver1/src/precomp.c</label>
        <link refid="precomp_8c"/>
      </node>
      <node id="8">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="error__codes_8h_1affd17c80938eed361b46758b3ab696cd" prot="public" static="no">
        <name>ERROR_BUS_PROTOCOL_ERROR</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0008)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="29" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="29" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a254cfc13872e4cf4fe0f89067d00f86a" prot="public" static="no">
        <name>ERROR_DEVICE_HARDWARE_ERROR</name>
        <initializer>0x0000001BL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="56" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="56" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" prot="public" static="no">
        <name>ERROR_DEVICE_INIT_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0002)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="23" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="23" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a869e1fe828b9b51d971bcfe6595101bd" prot="public" static="no">
        <name>ERROR_DEVICE_NOT_AVAILABLE</name>
        <initializer>0x00000013L</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="62" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="62" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" prot="public" static="no">
        <name>ERROR_DEVICE_NOT_READY</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0009)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="30" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="30" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" prot="public" static="no">
        <name>ERROR_DRIVER_BASE</name>
        <initializer>((NTSTATUS)0xE0000000L)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="19" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="19" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1af5020ced0fe4cabcbd18b06de7b4fd7f" prot="public" static="no">
        <name>ERROR_DRIVER_INIT_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0001)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="22" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="22" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a1ee37513091f1fa4fd23ad7a31815169" prot="public" static="no">
        <name>ERROR_HARDWARE_INIT_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0005)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="26" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="26" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1abf05011deed9a7688487446dcedb7734" prot="public" static="no">
        <name>ERROR_I2C_ARBITRATION_LOST</name>
        <initializer>(<ref refid="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" kindref="member">ERROR_I2C_BASE</ref> + 0x0003)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="38" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="38" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" prot="public" static="no">
        <name>ERROR_I2C_BASE</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0100)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="35" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="35" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a361a624ddf35d38e3673f8fd648bc533" prot="public" static="no">
        <name>ERROR_I2C_BUS_BUSY</name>
        <initializer>(<ref refid="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" kindref="member">ERROR_I2C_BASE</ref> + 0x0002)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="37" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="37" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a8977c5e15064527b2c7e501feb3915de" prot="public" static="no">
        <name>ERROR_I2C_DEVICE_NOT_RESPONDING</name>
        <initializer>(<ref refid="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" kindref="member">ERROR_I2C_BASE</ref> + 0x0001)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="36" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="36" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a117a0e9b2ae10c0775f947eb6aaf2e43" prot="public" static="no">
        <name>ERROR_INSTALL_FAILURE</name>
        <initializer>0x00000642L</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="53" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="53" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" prot="public" static="no">
        <name>ERROR_INVALID_PARAMETER</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0007)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="28" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="28" bodyend="-1"/>
        <referencedby refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</referencedby>
        <referencedby refid="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" compoundref="driver__core_8c" startline="194" endline="204">DriverCoreGetDevice</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
        <referencedby refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" compoundref="gpio__device_8c" startline="367" endline="385">GpioDeviceGetState</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" compoundref="gpio__device_8c" startline="391" endline="413">GpioDeviceRegisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" compoundref="gpio__device_8c" startline="419" endline="439">GpioDeviceUnregisterCallback</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" compoundref="i2c__device_8c" startline="362" endline="372">I2cDeviceGetStatistics</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" compoundref="i2c__device_8c" startline="314" endline="326">I2cDeviceTransfer</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" compoundref="spi__device_8c" startline="298" endline="308">SpiDeviceGetStatistics</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" compoundref="spi__device_8c" startline="148" endline="160">SpiDeviceTransfer</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a1d5275aca16a6e82736bccadf6d54f51" prot="public" static="no">
        <name>ERROR_IO_DEVICE</name>
        <initializer>0x0000001EL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="59" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="59" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1ab62592f2d0fe2a25aceff8a2aea7120d" prot="public" static="no">
        <name>ERROR_IO_OPERATION_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x000A)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="31" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="31" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a8c7e35c51169754be6ad9a33b4d0ede3" prot="public" static="no">
        <name>ERROR_MEMORY_ALLOCATION_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0006)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="27" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="27" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" prot="public" static="no">
        <name>ERROR_NOT_ENOUGH_MEMORY</name>
        <initializer>0x00000008L</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="65" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="65" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
        <referencedby refid="spi__device_8c_1afff80b1a0000ef578da0277667a994ff" compoundref="spi__device_8c" startline="267" endline="270">if</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a61a95049738405a02053f09a5707e1c9" prot="public" static="no">
        <name>ERROR_NOT_READY</name>
        <initializer>0x00000015L</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="68" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="68" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a1dc03de0e9017ea32c0bab1658627cab" prot="public" static="no">
        <name>ERROR_QUEUE_INIT_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0003)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="24" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="24" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1add601dcfffde2047b7051c4990d109e6" prot="public" static="no">
        <name>ERROR_RESOURCE_NOT_FOUND</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0004)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="25" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="25" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a224c1c26e72afd5d9b1eb990a3808ba0" prot="public" static="no">
        <name>ERROR_SPI_BASE</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0200)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="40" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="40" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a8a14c6072318a642cb973e5e1b3749cf" prot="public" static="no">
        <name>ERROR_SPI_TRANSFER_FAILED</name>
        <initializer>(<ref refid="error__codes_8h_1a224c1c26e72afd5d9b1eb990a3808ba0" kindref="member">ERROR_SPI_BASE</ref> + 0x0001)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="41" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="41" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1aea0ae801b7d25c979655a7eb20d034af" prot="public" static="no">
        <name>ERROR_SUCCESS</name>
        <initializer>((NTSTATUS)0x00000000L)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="18" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="18" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1ad58fd51c3e56b5d9555ce48be5cc53bd" prot="public" static="no">
        <name>ERROR_TIMEOUT</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x000B)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="32" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="32" bodyend="-1"/>
        <referencedby refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" compoundref="error__handling_8c" startline="138" endline="163">GetLastErrorFromNTStatus</referencedby>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a3b6ff704bf43afbc0cd69efc9d429f64" prot="public" static="no">
        <name>ERROR_USB_BASE</name>
        <initializer>(<ref refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kindref="member">ERROR_DRIVER_BASE</ref> + 0x0300)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="43" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="43" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__codes_8h_1a23a63dc8ca4262eacbd1b165c5c9de52" prot="public" static="no">
        <name>ERROR_USB_ENDPOINT_NOT_FOUND</name>
        <initializer>(<ref refid="error__codes_8h_1a3b6ff704bf43afbc0cd69efc9d429f64" kindref="member">ERROR_USB_BASE</ref> + 0x0001)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="44" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_codes.h" bodystart="44" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="error__codes_8h_1a14083fcce33766b91f8d08998cde8487" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>PCSTR</type>
        <definition>PCSTR ErrorCodeToString</definition>
        <argsstring>(_In_ NTSTATUS ErrorCode)</argsstring>
        <name>ErrorCodeToString</name>
        <param>
          <type>_In_ NTSTATUS</type>
          <declname>ErrorCode</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_codes.h" line="48" column="1" bodyfile="C:/KMDF Driver1/src/core/error/error_handling.c" bodystart="92" bodyend="107" declfile="C:/KMDF Driver1/include/core/error/error_codes.h" declline="48" declcolumn="1"/>
        <references refid="error__handling_8c_1aecb63565dd13e1edddcd31120d17a148" compoundref="error__handling_8c" startline="20" endline="54">ErrorTable</references>
        <referencedby refid="error__handling_8c_1adbc9d5ccc2721eecfe34ae5dfcc62bed" compoundref="error__handling_8c" startline="115" endline="130">FormatErrorMessage</referencedby>
        <referencedby refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</referencedby>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>error_codes.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>错误代码定义头文件</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>定义驱动程序中使用的错误代码和错误处理宏</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_CODES_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_CODES_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#ifdef<sp/>__cplusplus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="keyword">extern</highlight><highlight class="normal"><sp/></highlight><highlight class="stringliteral">&quot;C&quot;</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>基础错误代码范围定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="error__codes_8h_1aea0ae801b7d25c979655a7eb20d034af" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_SUCCESS<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>((NTSTATUS)0x00000000L)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DRIVER_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>((NTSTATUS)0xE0000000L)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>驱动程序特定错误代码</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="error__codes_8h_1af5020ced0fe4cabcbd18b06de7b4fd7f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DRIVER_INIT_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0001)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DEVICE_INIT_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0002)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="error__codes_8h_1a1dc03de0e9017ea32c0bab1658627cab" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_QUEUE_INIT_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0003)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="error__codes_8h_1add601dcfffde2047b7051c4990d109e6" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_RESOURCE_NOT_FOUND<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0004)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="error__codes_8h_1a1ee37513091f1fa4fd23ad7a31815169" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_HARDWARE_INIT_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0005)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="error__codes_8h_1a8c7e35c51169754be6ad9a33b4d0ede3" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_MEMORY_ALLOCATION_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0006)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_INVALID_PARAMETER<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0007)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="error__codes_8h_1affd17c80938eed361b46758b3ab696cd" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_BUS_PROTOCOL_ERROR<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0008)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DEVICE_NOT_READY<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0009)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="error__codes_8h_1ab62592f2d0fe2a25aceff8a2aea7120d" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_IO_OPERATION_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x000A)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="error__codes_8h_1ad58fd51c3e56b5d9555ce48be5cc53bd" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_TIMEOUT<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x000B)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="comment">//<sp/>总线相关错误代码</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_I2C_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0100)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="error__codes_8h_1a8977c5e15064527b2c7e501feb3915de" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_I2C_DEVICE_NOT_RESPONDING<sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_I2C_BASE<sp/>+<sp/>0x0001)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="error__codes_8h_1a361a624ddf35d38e3673f8fd648bc533" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_I2C_BUS_BUSY<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_I2C_BASE<sp/>+<sp/>0x0002)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="error__codes_8h_1abf05011deed9a7688487446dcedb7734" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_I2C_ARBITRATION_LOST<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_I2C_BASE<sp/>+<sp/>0x0003)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="error__codes_8h_1a224c1c26e72afd5d9b1eb990a3808ba0" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_SPI_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0200)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="error__codes_8h_1a8a14c6072318a642cb973e5e1b3749cf" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_SPI_TRANSFER_FAILED<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_SPI_BASE<sp/>+<sp/>0x0001)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42"><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="error__codes_8h_1a3b6ff704bf43afbc0cd69efc9d429f64" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_USB_BASE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_DRIVER_BASE<sp/>+<sp/>0x0300)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="error__codes_8h_1a23a63dc8ca4262eacbd1b165c5c9de52" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_USB_ENDPOINT_NOT_FOUND<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ERROR_USB_BASE<sp/>+<sp/>0x0001)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45"><highlight class="normal"></highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight><highlight class="comment">//<sp/>错误代码转换为字符串</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal">PCSTR</highlight></codeline>
<codeline lineno="48"><highlight class="normal"><ref refid="error__codes_8h_1a14083fcce33766b91f8d08998cde8487" kindref="member">ErrorCodeToString</ref>(</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>NTSTATUS<sp/>ErrorCode</highlight></codeline>
<codeline lineno="50"><highlight class="normal">);</highlight></codeline>
<codeline lineno="51"><highlight class="normal"></highlight></codeline>
<codeline lineno="52"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_INSTALL_FAILURE</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53" refid="error__codes_8h_1a117a0e9b2ae10c0775f947eb6aaf2e43" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_INSTALL_FAILURE<sp/>0x00000642L</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_DEVICE_HARDWARE_ERROR</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="56" refid="error__codes_8h_1a254cfc13872e4cf4fe0f89067d00f86a" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DEVICE_HARDWARE_ERROR<sp/>0x0000001BL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_IO_DEVICE</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59" refid="error__codes_8h_1a1d5275aca16a6e82736bccadf6d54f51" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_IO_DEVICE<sp/>0x0000001EL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="60"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="61"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_DEVICE_NOT_AVAILABLE</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="62" refid="error__codes_8h_1a869e1fe828b9b51d971bcfe6595101bd" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_DEVICE_NOT_AVAILABLE<sp/>0x00000013L</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="63"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_NOT_ENOUGH_MEMORY</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="65" refid="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_NOT_ENOUGH_MEMORY<sp/>0x00000008L</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="67"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>ERROR_NOT_READY</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="68" refid="error__codes_8h_1a61a95049738405a02053f09a5707e1c9" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ERROR_NOT_READY<sp/>0x00000015L</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="69"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"></highlight><highlight class="preprocessor">#ifdef<sp/>__cplusplus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="72"><highlight class="normal">}</highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>ERROR_CODES_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/core/error/error_codes.h"/>
  </compounddef>
</doxygen>
