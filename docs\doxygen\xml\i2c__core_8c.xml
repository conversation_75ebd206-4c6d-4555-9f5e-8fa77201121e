<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="i2c__core_8c" kind="file" language="C++">
    <compoundname>i2c_core.c</compoundname>
    <includes refid="precomp_8h" local="yes">../../precomp.h</includes>
    <includes refid="kmdf__i2c_8h" local="yes">../../../include/hal/bus/kmdf_i2c.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="19">
        <label>../../../include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <innerclass refid="struct__I2C__DEVICE__CONTEXT" prot="public">_I2C_DEVICE_CONTEXT</innerclass>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__DEVICE__CONTEXT" kindref="compound">_I2C_DEVICE_CONTEXT</ref></type>
        <definition>typedef struct _I2C_DEVICE_CONTEXT I2C_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>I2C_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="32" column="20"/>
      </memberdef>
      <memberdef kind="typedef" id="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__DEVICE__CONTEXT" kindref="compound">_I2C_DEVICE_CONTEXT</ref> *</type>
        <definition>typedef struct _I2C_DEVICE_CONTEXT * PI2C_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>PI2C_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="32" column="41"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="i2c__core_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" prot="public" static="no" mutable="no">
        <type>Exit</type>
        <definition>Exit __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="134" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="135" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__core_8c_1a465308d666d8357287980a516e216910" prot="public" static="no" mutable="no">
        <type>EVT_WDF_TIMER</type>
        <definition>EVT_WDF_TIMER I2CTransferTimerExpired</definition>
        <argsstring></argsstring>
        <name>I2CTransferTimerExpired</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="38" column="15" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="38" bodyend="-1"/>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗竴姝ワ細I2CInitialize 鍒濆鍖朓2C鎬荤嚎鎺ュ彛 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗竴姝ワ細I2CInitialize 鍒濆鍖朓2C鎬荤嚎鎺ュ彛 *WDFAPI NTSTATUS I2CInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</argsstring>
        <name>I2CInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref></type>
          <declname>I2cConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="44" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="44" bodyend="105"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <references refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</references>
        <references refid="i2c__core_8c_1a465308d666d8357287980a516e216910" compoundref="i2c__core_8c" startline="38">I2CTransferTimerExpired</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗叚姝ワ細I2CReadRegister 璇诲彇I2C璁惧瀵勫瓨鍣 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗叚姝ワ細I2CReadRegister 璇诲彇I2C璁惧瀵勫瓨鍣 *WDFAPI NTSTATUS I2CReadRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CReadRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref></type>
          <declname>SlaveAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_ PUCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="321" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="321" bodyend="355"/>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" compoundref="kmdf__i2c_8h" startline="27">I2CWriteRead</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" compoundref="kmdf__i2c_8h" startline="37">_I2C_TRANSFER_PACKET::ReadBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" compoundref="kmdf__i2c_8h" startline="38">_I2C_TRANSFER_PACKET::ReadBufferLength</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗竷姝ワ細I2CScanBus 鎵弿I2C鎬荤嚎鏌ユ壘璁惧 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗竷姝ワ細I2CScanBus 鎵弿I2C鎬荤嚎鏌ユ壘璁惧 *WDFAPI NTSTATUS I2CScanBus</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount)</argsstring>
        <name>I2CScanBus</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <ref refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kindref="member">PI2C_ADDRESS</ref></type>
          <declname>DeviceAddresses</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>MaxDeviceAddresses</declname>
        </param>
        <param>
          <type>_Out_ PULONG</type>
          <declname>DeviceCount</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="361" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="361" bodyend="431"/>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" compoundref="kmdf__bus__common_8h" startline="25">BusTransferSuccess</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" compoundref="kmdf__i2c_8h" startline="32">_I2C_TRANSFER_PACKET::Common</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" compoundref="error__codes_8h" startline="30">ERROR_DEVICE_NOT_READY</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" compoundref="kmdf__bus__common_8h" startline="46">_BUS_TRANSFER_PACKET::Status</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗洓姝ワ細I2CTransferAsynchronous 鎵ц寮傛I2C浼犺緭 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗洓姝ワ細I2CTransferAsynchronous 鎵ц寮傛I2C浼犺緭 *WDFAPI NTSTATUS I2CTransferAsynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</argsstring>
        <name>I2CTransferAsynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref></type>
          <declname>CompletionCallback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="250" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="250" bodyend="278"/>
        <references refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" compoundref="gpio__device_8c" startline="23">Config</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗笁姝ワ細I2CTransferSynchronous 鎵ц鍚屾I2C浼犺緭 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗笁姝ワ細I2CTransferSynchronous 鎵ц鍚屾I2C浼犺緭 *WDFAPI NTSTATUS I2CTransferSynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CTransferSynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="142" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="142" bodyend="244"/>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" compoundref="kmdf__bus__common_8h" startline="26">BusTransferFailed</references>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" compoundref="kmdf__bus__common_8h" startline="25">BusTransferSuccess</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" compoundref="error__codes_8h" startline="30">ERROR_DEVICE_NOT_READY</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" compoundref="kmdf__i2c_8h" startline="26">I2CRead</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" compoundref="kmdf__i2c_8h" startline="27">I2CWriteRead</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1aefdc06b9d942e6b102424a8a81c0be8a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>I2C浼犺緭瓒呮椂澶勭悊鍥炶皟 *VOID</type>
        <definition>I2C浼犺緭瓒呮椂澶勭悊鍥炶皟 *VOID I2CTransferTimerExpired</definition>
        <argsstring>(_In_ WDFTIMER Timer)</argsstring>
        <name>I2CTransferTimerExpired</name>
        <param>
          <type>_In_ WDFTIMER</type>
          <declname>Timer</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="437" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="437" bodyend="447"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗簩姝ワ細I2CUninitialize 娓呯悊I2C鎬荤嚎鎺ュ彛 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>绗簩姝ワ細I2CUninitialize 娓呯悊I2C鎬荤嚎鎺ュ彛 *WDFAPI VOID I2CUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>I2CUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="111" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="111" bodyend="132"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
      </memberdef>
      <memberdef kind="function" id="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>绗簲姝ワ細I2CWriteRegister 鍐欏叆I2C璁惧瀵勫瓨鍣 *<ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>绗簲姝ワ細I2CWriteRegister 鍐欏叆I2C璁惧瀵勫瓨鍣 *WDFAPI NTSTATUS I2CWriteRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CWriteRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref></type>
          <declname>SlaveAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c" line="283" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="283" bodyend="316"/>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>i2c_core.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>I2C鎬荤嚎鏍稿績鍔熻兘瀹炵幇</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/></highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*<sp/>鐩殑锛欼2C鎬荤嚎鏍稿績鍔熻兘瀹炵幇</highlight></codeline>
<codeline lineno="7"><highlight class="comment"><sp/>*<sp/>鎬濊矾锛?<sp/>*<sp/>1.<sp/>鎻愪緵I2C鎬荤嚎鍒濆鍖栥€佷紶杈撳拰绠＄悊鍔熻兘</highlight></codeline>
<codeline lineno="8"><highlight class="comment"><sp/>*<sp/>2.<sp/>瀹炵幇鍚屾鍜屽紓姝ヤ紶杈撴満鍒讹紝骞舵彁渚涜秴鏃朵繚鎶?<sp/>*<sp/>3.<sp/>鏀寔鏍囧噯I2C鎿嶄綔锛屽寘鎷鍐欍€佹€荤嚎鎵弿绛?<sp/>*<sp/>4.<sp/>浣跨敤WDF妗嗘灦瀹炵幇I2C鎺у埗鍣ㄦ娊璞?<sp/>*</highlight></codeline>
<codeline lineno="9"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------------------+</highlight></codeline>
<codeline lineno="10"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/>I2C鎬荤嚎鎺у埗鎺ュ彛<sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="11"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------+-----------+</highlight></codeline>
<codeline lineno="12"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="13"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------v-----------+</highlight></codeline>
<codeline lineno="14"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/><sp/><sp/>I2C浼犺緭鎺у埗鍣?<sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="15"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------+-----------+</highlight></codeline>
<codeline lineno="16"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="17"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------v-----------+</highlight></codeline>
<codeline lineno="18"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/><sp/><sp/>纭欢鎶借薄灞?HAL)<sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="19"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------------------+</highlight></codeline>
<codeline lineno="20"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">../../precomp.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__i2c_8h" kindref="compound">../../../include/hal/bus/kmdf_i2c.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C璁惧涓婁笅鏂囩粨鏋勪綋<sp/>-<sp/>璺熻釜璁惧鐗瑰畾鏁版嵁</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="struct__I2C__DEVICE__CONTEXT" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__I2C__DEVICE__CONTEXT" kindref="compound">_I2C_DEVICE_CONTEXT</ref><sp/>{</highlight></codeline>
<codeline lineno="29" refid="struct__I2C__DEVICE__CONTEXT_1acf0fff6904f00f7611172d1e8b9c2bcd" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="struct__I2C__DEVICE__CONTEXT_1acf0fff6904f00f7611172d1e8b9c2bcd" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF璁惧瀵硅薄</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="struct__I2C__DEVICE__CONTEXT_1a4096eb1e4fb3e430840ed07109bec38b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref><sp/><ref refid="struct__I2C__DEVICE__CONTEXT_1a4096eb1e4fb3e430840ed07109bec38b" kindref="member">Config</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>I2C閰嶇疆淇℃伅</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="struct__I2C__DEVICE__CONTEXT_1a3897e76d5d5a45297f03bdaa74c2280c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref><sp/><ref refid="struct__I2C__DEVICE__CONTEXT_1a3897e76d5d5a45297f03bdaa74c2280c" kindref="member">TransferLock</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>浼犺緭鍚屾閿?<sp/><sp/><sp/><sp/>BOOLEAN<sp/>DeviceInitialized;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>鍒濆鍖栨爣蹇?<sp/><sp/><sp/><sp/>WDFTIMER<sp/>WatchdogTimer;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>瓒呮椂鐪嬮棬鐙楀畾鏃跺櫒</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32" refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" refkind="member"><highlight class="normal">}<sp/><ref refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" kindref="member">I2C_DEVICE_CONTEXT</ref>,<sp/>*<ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref>;</highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="comment">//<sp/>璁惧涓婁笅鏂囪闂櫒</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal">WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(<ref refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" kindref="member">I2C_DEVICE_CONTEXT</ref>,<sp/>GetI2CContext)</highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C浼犺緭瓒呮椂澶勭悊鍥炶皟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="i2c__core_8c_1a465308d666d8357287980a516e216910" refkind="member"><highlight class="normal">EVT_WDF_TIMER<sp/><ref refid="i2c__core_8c_1a465308d666d8357287980a516e216910" kindref="member">I2CTransferTimerExpired</ref>;</highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="41"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗竴姝ワ細I2CInitialize<sp/>-<sp/>鍒濆鍖朓2C鎬荤嚎鎺ュ彛</highlight></codeline>
<codeline lineno="42"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="43"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="44" refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" kindref="member">I2CInitialize</ref>(</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref><sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></highlight></codeline>
<codeline lineno="47"><highlight class="normal">)</highlight></codeline>
<codeline lineno="48"><highlight class="normal">{</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG<sp/>timerConfig;</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>timerAttributes;</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG<sp/>lockConfig;</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>lockAttributes;</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>楠岃瘉鍙傛暟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(Device,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(<ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>I2C<sp/>configuration<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Initializing<sp/>I2C<sp/>bus<sp/>with<sp/>clock<sp/>frequency<sp/>%d<sp/>Hz&quot;</highlight><highlight class="normal">,<sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>-&gt;ClockFrequency);</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍒嗛厤鍜屽垵濮嬪寲涓婁笅鏂?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2CContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NULL<sp/>==<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)<sp/>{</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>I2C<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" kindref="member">ERROR_DEVICE_INIT_FAILED</ref>;</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config,<sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>));</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍒涘缓鑷棆閿佷互淇濇姢I2C浼犺緭</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG_INIT(&amp;lockConfig,<sp/>SpinlockRelease);</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;lockAttributes);</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/>lockAttributes.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfSpinLockCreate(&amp;lockAttributes,<sp/>&amp;lockConfig,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransferLock);</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>spinlock<sp/>for<sp/>I2C<sp/>transfers,<sp/>status=%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍒涘缓鐪嬮棬鐙楀畾鏃跺櫒浠ュ鐞咺2C瓒呮椂</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG_INIT(&amp;timerConfig,<sp/><ref refid="i2c__core_8c_1a465308d666d8357287980a516e216910" kindref="member">I2CTransferTimerExpired</ref>);</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;timerAttributes);</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>timerAttributes.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfTimerCreate(&amp;timerConfig,<sp/>&amp;timerAttributes,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer);</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>watchdog<sp/>timer<sp/>for<sp/>I2C,<sp/>status=%X&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閰嶇疆I2C鎺у埗鍣ㄧ‖浠?瀹為檯瀹炵幇鍙兘闇€瑕佷慨鏀圭‖浠跺瘎瀛樺櫒绛?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>娣诲姞瀹為檯鐨勭‖浠堕厤缃唬鐮?<sp/><sp/><sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>bus<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="102"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="105"><highlight class="normal">}</highlight></codeline>
<codeline lineno="106"><highlight class="normal"></highlight></codeline>
<codeline lineno="107"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="108"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗簩姝ワ細I2CUninitialize<sp/>-<sp/>娓呯悊I2C鎬荤嚎鎺ュ彛</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="111" refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" kindref="member">I2CUninitialize</ref>(</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="113"><highlight class="normal">)</highlight></codeline>
<codeline lineno="114"><highlight class="normal">{</highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2CContext(Device);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NULL<sp/>==<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)<sp/>{</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kindref="member">LOG_WARNING</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>device<sp/>context<sp/>is<sp/>NULL&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍋滄浠讳綍姝ｅ湪杩涜鐨勪紶杈?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(deviceContext-&gt;WatchdogTimer)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStop(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer,<sp/>TRUE);</highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>娣诲姞浠讳綍蹇呰鐨勭‖浠跺叧闂唬鐮?<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>bus<sp/>uninitialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="134" refid="i2c__core_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" refkind="member"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="135"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>);</highlight></codeline>
<codeline lineno="136"><highlight class="normal">}</highlight></codeline>
<codeline lineno="137"><highlight class="normal"></highlight></codeline>
<codeline lineno="138"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="139"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗笁姝ワ細I2CTransferSynchronous<sp/>-<sp/>鎵ц鍚屾I2C浼犺緭</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="142" refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kindref="member">I2CTransferSynchronous</ref>(</highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="144"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="146"><highlight class="normal">)</highlight></codeline>
<codeline lineno="147"><highlight class="normal">{</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2CContext(Device);</highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>lockAcquired<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="152"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍙傛暟楠岃瘉</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(Device,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(TransferPacket,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>transfer<sp/>packet<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NULL<sp/>==<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)<sp/>{</highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>device<sp/>context<sp/>is<sp/>NULL&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" kindref="member">ERROR_DEVICE_INIT_FAILED</ref>;</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>bus<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" kindref="member">ERROR_DEVICE_NOT_READY</ref>;</highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>transfer<sp/>to<sp/>slave<sp/>0x%04X&quot;</highlight><highlight class="normal">,<sp/>TransferPacket-&gt;SlaveAddress);</highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鑾峰彇浼犺緭閿佷椒浜掔涵顔荤箽閸氬本顒炵拋鍧楁６</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfSpinLockAcquire(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransferLock);</highlight></codeline>
<codeline lineno="174"><highlight class="normal"><sp/><sp/><sp/><sp/>lockAcquired<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍚姩鐪嬮棬鐙楀畾鏃跺櫒</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TimeoutMs<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStart(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer,<sp/>WDF_REL_TIMEOUT_IN_MS(TimeoutMs));</highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="180"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="181"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽鐎圭偤妾惃鍑?C娴肩姾绶?(鐎圭偤妾€圭偟骞囬棁鈧憰浣圭壌閹诡喚鈥栨禒鑸垫惙娴?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="182"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(TransferPacket-&gt;Type)<sp/>{</highlight></codeline>
<codeline lineno="183"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" kindref="member">I2CWrite</ref>:</highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TransferPacket-&gt;WriteBuffer<sp/>&amp;&amp;<sp/>TransferPacket-&gt;WriteBufferLength<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="185"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>鐎圭偟骞囩€圭偤妾惃鍑?C閸愭瑥鍙嗛幙宥勭稊</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="186"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>write<sp/>%d<sp/>bytes&quot;</highlight><highlight class="normal">,<sp/>TransferPacket-&gt;WriteBufferLength);</highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;<sp/></highlight><highlight class="comment">//<sp/>閺囨寧宕叉稉鍝勭杽闂勫懐娈戦幙宥勭稊閻樿埖鈧?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="188"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="189"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="190"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Invalid<sp/>write<sp/>buffer<sp/>for<sp/>I2C<sp/>write<sp/>operation&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="191"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="192"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" kindref="member">I2CRead</ref>:</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TransferPacket-&gt;ReadBuffer<sp/>&amp;&amp;<sp/>TransferPacket-&gt;ReadBufferLength<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>鐎圭偟骞囩€圭偤妾惃鍑?C鐠囪褰囬幙宥勭稊</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>read<sp/>%d<sp/>bytes&quot;</highlight><highlight class="normal">,<sp/>TransferPacket-&gt;ReadBufferLength);</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;<sp/></highlight><highlight class="comment">//<sp/>閺囨寧宕叉稉鍝勭杽闂勫懐娈戦幙宥勭稊閻樿埖鈧?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="200"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Invalid<sp/>read<sp/>buffer<sp/>for<sp/>I2C<sp/>read<sp/>operation&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" kindref="member">I2CWriteRead</ref>:</highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TransferPacket-&gt;WriteBuffer<sp/>&amp;&amp;<sp/>TransferPacket-&gt;WriteBufferLength<sp/>&gt;<sp/>0<sp/>&amp;&amp;</highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket-&gt;ReadBuffer<sp/>&amp;&amp;<sp/>TransferPacket-&gt;ReadBufferLength<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>鐎圭偟骞囩€圭偤妾惃鍑?C閸愭瑥鎮楃拠缁樻惙娴?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>write<sp/>%d<sp/>bytes<sp/>then<sp/>read<sp/>%d<sp/>bytes&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="210"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket-&gt;WriteBufferLength,</highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket-&gt;ReadBufferLength);</highlight></codeline>
<codeline lineno="212"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;<sp/></highlight><highlight class="comment">//<sp/>閺囨寧宕叉稉鍝勭杽闂勫懐娈戦幙宥勭稊閻樿埖鈧?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="213"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Invalid<sp/>buffers<sp/>for<sp/>I2C<sp/>write-read<sp/>operation&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="217"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="218"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="219"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="221"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Unknown<sp/>I2C<sp/>transfer<sp/>type:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>TransferPacket-&gt;Type);</highlight></codeline>
<codeline lineno="222"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="223"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="224"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸嬫粍顒涢惇瀣，閻欐鐣鹃弮璺烘珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(TimeoutMs<sp/>&gt;<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStop(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer,<sp/>FALSE);</highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺囧瓨鏌婃导鐘虹翻閸栧懐濮搁幀?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket-&gt;Common.Status<sp/>=<sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" kindref="member">BusTransferSuccess</ref>;</highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>TransferPacket-&gt;Common.Status<sp/>=<sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" kindref="member">BusTransferFailed</ref>;</highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="237"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(lockAcquired)<sp/>{</highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransferLock);</highlight></codeline>
<codeline lineno="240"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="241"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="244"><highlight class="normal">}</highlight></codeline>
<codeline lineno="245"><highlight class="normal"></highlight></codeline>
<codeline lineno="246"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="247"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗洓姝ワ細I2CTransferAsynchronous<sp/>-<sp/>鎵ц寮傛I2C浼犺緭</highlight></codeline>
<codeline lineno="248"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="249"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="250" refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" kindref="member">I2CTransferAsynchronous</ref>(</highlight></codeline>
<codeline lineno="251"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="252"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="253"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref><sp/>CompletionCallback,</highlight></codeline>
<codeline lineno="254"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="255"><highlight class="normal">)</highlight></codeline>
<codeline lineno="256"><highlight class="normal">{</highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸︺劌鐤勯梽鍛杽閻滈鑵戦敍宀冪箹闁插苯绨茬拠銉ユ儙閸斻劌绱撳銉ょ炊鏉堟搫绱濆▔銊ュ斀閸ョ偠鐨熼獮鎯扮箲閸?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>缁犫偓閸栨牞鎹ｇ憴渚婄礉濮濄倗銇氭笟瀣閺堫兛绮庨幍褑顢戦崥灞绢劄娴肩姾绶獮璺虹磽濮濄儴鐨熼悽銊ユ礀鐠?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="261"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍙傛暟楠岃瘉</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="264"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(Device,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="265"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(TransferPacket,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>transfer<sp/>packet<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="266"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(CompletionCallback,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>completion<sp/>callback<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="267"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="268"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽閸氬本顒炴导鐘虹翻</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="269"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kindref="member">I2CTransferSynchronous</ref>(Device,<sp/>TransferPacket,<sp/></highlight></codeline>
<codeline lineno="270"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GetI2CContext(Device)-&gt;<ref refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" kindref="member">Config</ref>.TimeoutMs);</highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="272"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娣囨繂鐡ㄦ稉濠佺瑓閺傚洤鑻熺拫鍐暏閸ョ偠鐨熼崙鑺ユ殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="273"><highlight class="normal"><sp/><sp/><sp/><sp/>TransferPacket-&gt;Common.Context<sp/>=<sp/>Context;</highlight></codeline>
<codeline lineno="274"><highlight class="normal"><sp/><sp/><sp/><sp/>CompletionCallback(&amp;TransferPacket-&gt;Common);</highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="278"><highlight class="normal">}</highlight></codeline>
<codeline lineno="279"><highlight class="normal"></highlight></codeline>
<codeline lineno="280"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="281"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗簲姝ワ細I2CWriteRegister<sp/>-<sp/>鍐欏叆I2C璁惧瀵勫瓨鍣?<sp/>*/</highlight></codeline>
<codeline lineno="282"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="283" refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" kindref="member">I2CWriteRegister</ref>(</highlight></codeline>
<codeline lineno="284"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="285"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="288"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="289"><highlight class="normal">)</highlight></codeline>
<codeline lineno="290"><highlight class="normal">{</highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="292"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref><sp/>transferPacket;</highlight></codeline>
<codeline lineno="293"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[2];</highlight></codeline>
<codeline lineno="294"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="295"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="296"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="297"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸戝棗顦崘娆忓弳缂傛挸鍟块崠?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="298"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[0]<sp/>=<sp/>RegisterAddress;</highlight></codeline>
<codeline lineno="299"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>[1]<sp/>=<sp/>Value;</highlight></codeline>
<codeline lineno="300"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="301"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍑嗗浼犺緭鍖?<sp/><sp/><sp/><sp/>RtlZeroMemory(&amp;transferPacket,<sp/>sizeof(I2C_TRANSFER_PACKET));</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="302"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" kindref="member">SlaveAddress</ref><sp/>=<sp/>SlaveAddress;</highlight></codeline>
<codeline lineno="303"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" kindref="member">Type</ref><sp/>=<sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" kindref="member">I2CWrite</ref>;</highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" kindref="member">WriteBuffer</ref><sp/>=<sp/><ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>;</highlight></codeline>
<codeline lineno="305"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" kindref="member">WriteBufferLength</ref><sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kindref="member">writeBuffer</ref>);</highlight></codeline>
<codeline lineno="306"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鎵ц浼犺緭</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="308"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kindref="member">I2CTransferSynchronous</ref>(Device,<sp/>&amp;transferPacket,<sp/>TimeoutMs);</highlight></codeline>
<codeline lineno="309"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="310"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>write<sp/>register<sp/>0x%02X<sp/>=<sp/>0x%02X<sp/>to<sp/>slave<sp/>0x%04X:<sp/>%s&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="311"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>RegisterAddress,<sp/>Value,<sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="312"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>?<sp/></highlight><highlight class="stringliteral">&quot;Success&quot;</highlight><highlight class="normal"><sp/>:<sp/></highlight><highlight class="stringliteral">&quot;Failed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="313"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="314"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="315"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="316"><highlight class="normal">}</highlight></codeline>
<codeline lineno="317"><highlight class="normal"></highlight></codeline>
<codeline lineno="318"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="319"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗叚姝ワ細I2CReadRegister<sp/>-<sp/>璇诲彇I2C璁惧瀵勫瓨鍣?<sp/>*/</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="321" refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" kindref="member">I2CReadRegister</ref>(</highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="323"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="325"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PUCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="326"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="327"><highlight class="normal">)</highlight></codeline>
<codeline lineno="328"><highlight class="normal">{</highlight></codeline>
<codeline lineno="329"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="330"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref><sp/>transferPacket;</highlight></codeline>
<codeline lineno="331"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>regAddr<sp/>=<sp/>RegisterAddress;</highlight></codeline>
<codeline lineno="332"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="333"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="334"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="335"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(Value,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>value<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="336"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="337"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍑嗗浼犺緭鍖?<sp/><sp/><sp/><sp/>RtlZeroMemory(&amp;transferPacket,<sp/>sizeof(I2C_TRANSFER_PACKET));</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="338"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" kindref="member">SlaveAddress</ref><sp/>=<sp/>SlaveAddress;</highlight></codeline>
<codeline lineno="339"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" kindref="member">Type</ref><sp/>=<sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" kindref="member">I2CWriteRead</ref>;</highlight></codeline>
<codeline lineno="340"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" kindref="member">WriteBuffer</ref><sp/>=<sp/>&amp;regAddr;</highlight></codeline>
<codeline lineno="341"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" kindref="member">WriteBufferLength</ref><sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(regAddr);</highlight></codeline>
<codeline lineno="342"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" kindref="member">ReadBuffer</ref><sp/>=<sp/>Value;</highlight></codeline>
<codeline lineno="343"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" kindref="member">ReadBufferLength</ref><sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(UCHAR);</highlight></codeline>
<codeline lineno="344"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="345"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鎵ц浼犺緭</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="346"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kindref="member">I2CTransferSynchronous</ref>(Device,<sp/>&amp;transferPacket,<sp/>TimeoutMs);</highlight></codeline>
<codeline lineno="347"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="348"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kindref="member">LOG_VERBOSE</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>read<sp/>register<sp/>0x%02X<sp/>from<sp/>slave<sp/>0x%04X:<sp/>%s,<sp/>value=0x%02X&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="349"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>RegisterAddress,<sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="350"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>?<sp/></highlight><highlight class="stringliteral">&quot;Success&quot;</highlight><highlight class="normal"><sp/>:<sp/></highlight><highlight class="stringliteral">&quot;Failed&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="351"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>?<sp/>*Value<sp/>:<sp/>0);</highlight></codeline>
<codeline lineno="352"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="353"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="354"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="355"><highlight class="normal">}</highlight></codeline>
<codeline lineno="356"><highlight class="normal"></highlight></codeline>
<codeline lineno="357"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="358"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>绗竷姝ワ細I2CScanBus<sp/>-<sp/>鎵弿I2C鎬荤嚎鏌ユ壘璁惧</highlight></codeline>
<codeline lineno="359"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="360"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="361" refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" kindref="member">I2CScanBus</ref>(</highlight></codeline>
<codeline lineno="362"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="363"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_to_(MaxDeviceAddresses,<sp/>*DeviceCount)<sp/><ref refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kindref="member">PI2C_ADDRESS</ref><sp/>DeviceAddresses,</highlight></codeline>
<codeline lineno="364"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>MaxDeviceAddresses,</highlight></codeline>
<codeline lineno="365"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PULONG<sp/>DeviceCount</highlight></codeline>
<codeline lineno="366"><highlight class="normal">)</highlight></codeline>
<codeline lineno="367"><highlight class="normal">{</highlight></codeline>
<codeline lineno="368"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="369"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2CContext(Device);</highlight></codeline>
<codeline lineno="370"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref><sp/>transferPacket;</highlight></codeline>
<codeline lineno="371"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>count<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="372"><highlight class="normal"><sp/><sp/><sp/><sp/>UCHAR<sp/>dummyData<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="373"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="374"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kindref="member">FUNCTION_ENTRY</ref>();</highlight></codeline>
<codeline lineno="375"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="376"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍙傛暟楠岃瘉</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="377"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(Device,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="378"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(DeviceAddresses,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>addresses<sp/>buffer&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="379"><highlight class="normal"><sp/><sp/><sp/><sp/>RETURN_IF_NULL(DeviceCount,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>count<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="380"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="381"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(MaxDeviceAddresses<sp/>==<sp/>0)<sp/>{</highlight></codeline>
<codeline lineno="382"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;Invalid<sp/>MaxDeviceAddresses<sp/>(0)&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="383"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="384"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="385"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="386"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="387"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(NULL<sp/>==<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>)<sp/>{</highlight></codeline>
<codeline lineno="388"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>device<sp/>context<sp/>is<sp/>NULL&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="389"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" kindref="member">ERROR_DEVICE_INIT_FAILED</ref>;</highlight></codeline>
<codeline lineno="390"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="391"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="392"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="393"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="394"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>bus<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="395"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" kindref="member">ERROR_DEVICE_NOT_READY</ref>;</highlight></codeline>
<codeline lineno="396"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="397"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="398"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="399"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Scanning<sp/>I2C<sp/>bus<sp/>for<sp/>devices&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="400"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="401"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鍑嗗浼犺緭鍖呯敤浜庢帰娴?<sp/><sp/><sp/><sp/>RtlZeroMemory(&amp;transferPacket,<sp/>sizeof(I2C_TRANSFER_PACKET));</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="402"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" kindref="member">Type</ref><sp/>=<sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" kindref="member">I2CWrite</ref>;</highlight></codeline>
<codeline lineno="403"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" kindref="member">WriteBuffer</ref><sp/>=<sp/>&amp;dummyData;</highlight></codeline>
<codeline lineno="404"><highlight class="normal"><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" kindref="member">WriteBufferLength</ref><sp/>=<sp/>0;<sp/><sp/></highlight><highlight class="comment">//<sp/>0闂€鍨閸愭瑥鍙嗛柅姘埗閻劋绨幒銏＄ゴ</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="405"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="406"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鎵弿鏈夋晥I2C鍦板潃鑼冨洿<sp/>(0x08-0x77)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="407"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>璺宠繃淇濈暀鍦板潃</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="408"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(<ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/>addr<sp/>=<sp/>0x08;<sp/>addr<sp/>&lt;=<sp/>0x77<sp/>&amp;&amp;<sp/>count<sp/>&lt;<sp/>MaxDeviceAddresses;<sp/>addr++)<sp/>{</highlight></codeline>
<codeline lineno="409"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>璺宠繃淇濈暀鍦板潃</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="410"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(addr<sp/>&gt;=<sp/>0x30<sp/>&amp;&amp;<sp/>addr<sp/>&lt;=<sp/>0x37)<sp/>{</highlight></codeline>
<codeline lineno="411"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">continue</highlight><highlight class="normal">;<sp/><sp/></highlight><highlight class="comment">//<sp/>淇濈暀鐢ㄦ稉鍦板潃</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="412"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="413"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="414"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" kindref="member">SlaveAddress</ref><sp/>=<sp/>addr;</highlight></codeline>
<codeline lineno="415"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="416"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>璁剧疆杈冪煭鐨勮秴鏃朵互鍔犲揩鎵弿閫熷害</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="417"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kindref="member">I2CTransferSynchronous</ref>(Device,<sp/>&amp;transferPacket,<sp/>10);</highlight></codeline>
<codeline lineno="418"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="419"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)<sp/>&amp;&amp;<sp/>transferPacket.<ref refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" kindref="member">Common</ref>.<ref refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" kindref="member">Status</ref><sp/>==<sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" kindref="member">BusTransferSuccess</ref>)<sp/>{</highlight></codeline>
<codeline lineno="420"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>I2C<sp/>device<sp/>at<sp/>address<sp/>0x%02X&quot;</highlight><highlight class="normal">,<sp/>addr);</highlight></codeline>
<codeline lineno="421"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DeviceAddresses[count++]<sp/>=<sp/>addr;</highlight></codeline>
<codeline lineno="422"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="423"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="424"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="425"><highlight class="normal"><sp/><sp/><sp/><sp/>*DeviceCount<sp/>=<sp/>count;</highlight></codeline>
<codeline lineno="426"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kindref="member">LOG_INFO</ref>(</highlight><highlight class="stringliteral">&quot;Found<sp/>%d<sp/>I2C<sp/>devices<sp/>on<sp/>the<sp/>bus&quot;</highlight><highlight class="normal">,<sp/>count);</highlight></codeline>
<codeline lineno="427"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="428"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="429"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kindref="member">FUNCTION_EXIT</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="430"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="431"><highlight class="normal">}</highlight></codeline>
<codeline lineno="432"><highlight class="normal"></highlight></codeline>
<codeline lineno="433"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="434"><highlight class="comment"><sp/>*/</highlight><highlight class="normal">/<sp/>I2C浼犺緭瓒呮椂澶勭悊鍥炶皟</highlight></codeline>
<codeline lineno="435"><highlight class="normal"><sp/>*/</highlight></codeline>
<codeline lineno="436"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="437" refid="i2c__core_8c_1aefdc06b9d942e6b102424a8a81c0be8a" refkind="member"><highlight class="normal"><ref refid="i2c__core_8c_1a465308d666d8357287980a516e216910" kindref="member">I2CTransferTimerExpired</ref>(</highlight></codeline>
<codeline lineno="438"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFTIMER<sp/>Timer</highlight></codeline>
<codeline lineno="439"><highlight class="normal">)</highlight></codeline>
<codeline lineno="440"><highlight class="normal">{</highlight></codeline>
<codeline lineno="441"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device<sp/>=<sp/>(<ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref>)WdfTimerGetParentObject(Timer);</highlight></codeline>
<codeline lineno="442"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2CContext(device);</highlight></codeline>
<codeline lineno="443"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="444"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(</highlight><highlight class="stringliteral">&quot;I2C<sp/>transfer<sp/>timed<sp/>out&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="445"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="446"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>鍦ㄥ疄闄呭疄鐜颁唬鐮侀厤鍚堜娇鐢?<sp/>*<sp/>-<sp/>浼犺緭鎿嶄綔鍚屾閿佸彲鑳芥垚涓烘€ц兘鐡堕</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="447"><highlight class="normal">}</highlight></codeline>
<codeline lineno="448"><highlight class="normal"></highlight></codeline>
<codeline lineno="449"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="450"><highlight class="comment"><sp/>*<sp/>鎬荤粨涓庝紭缂虹偣璇勫垽</highlight></codeline>
<codeline lineno="451"><highlight class="comment"><sp/>*<sp/>----------------------------</highlight></codeline>
<codeline lineno="452"><highlight class="comment"><sp/>*<sp/>**<sp/>缁撴瀯鍖栬璁?**:</highlight></codeline>
<codeline lineno="453"><highlight class="comment"><sp/>*<sp/>-<sp/>鏈唬鐮侀噰鐢ㄥ眰娆″寲鏋舵瀯锛屽皢I2C鎬荤嚎鎿嶄綔鎶借薄涓烘槗鐢ㄧ殑API</highlight></codeline>
<codeline lineno="454"><highlight class="comment"><sp/>*<sp/>**<sp/>妯″潡鍒掑垎<sp/>**:</highlight></codeline>
<codeline lineno="455"><highlight class="comment"><sp/>*<sp/>-<sp/>鍒濆鍖栥€佷紶杈撴帶鍒躲€佸瘎瀛樺櫒鎿嶄綔鍜屾€荤嚎鎵弿鍚勮嚜鐙珛</highlight></codeline>
<codeline lineno="456"><highlight class="comment"><sp/>*<sp/>**<sp/>鏍稿績鏈哄埗<sp/>**:</highlight></codeline>
<codeline lineno="457"><highlight class="comment"><sp/>*<sp/>-<sp/>浣跨敤鍚屾閿佸拰鐪嬮棬鐙楀畾鏃跺櫒纭繚浼犺緭瀹夊叏</highlight></codeline>
<codeline lineno="458"><highlight class="comment"><sp/>*<sp/>-<sp/>鎻愪緵鍚屾鍜屽紓姝ヤ袱绉嶄紶杈撴ā寮忔弧瓒充笉鍚岄渶姹?<sp/>*<sp/>**<sp/>鍏宠仈鏂囦欢<sp/>**:</highlight></codeline>
<codeline lineno="459"><highlight class="comment"><sp/>*<sp/>-<sp/>kmdf_i2c.h:<sp/>鎻愪緵I2C鎬荤嚎鎺ュ彛澹版槑</highlight></codeline>
<codeline lineno="460"><highlight class="comment"><sp/>*<sp/>-<sp/>driver_log.h:<sp/>鏃ュ織璁板綍鍔熻兘</highlight></codeline>
<codeline lineno="461"><highlight class="comment"><sp/>*<sp/>-<sp/>error_codes.h:<sp/>閿欒鐮佸畾涔?<sp/>*</highlight></codeline>
<codeline lineno="462"><highlight class="comment"><sp/>*<sp/>**<sp/>浼樼偣<sp/>**:</highlight></codeline>
<codeline lineno="463"><highlight class="comment"><sp/>*<sp/>-<sp/>瀹屾暣灏佽I2C鎬荤嚎鎿嶄綔锛岀畝鍖栬澶囬┍鍔ㄥ紑鍙?<sp/>*<sp/>-<sp/>瓒呮椂淇濇姢鏈哄埗纭繚浼犺緭鍙潬鎬?<sp/>*<sp/>-<sp/>鏀寔鏍囧噯I2C鎿嶄綔锛屾帴鍙ｇ粺涓€瑙勮寖</highlight></codeline>
<codeline lineno="464"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="465"><highlight class="comment"><sp/>*<sp/>**<sp/>缂虹偣<sp/>**:</highlight></codeline>
<codeline lineno="466"><highlight class="comment"><sp/>*<sp/>-<sp/>闇€瑕佺‖浠剁壒瀹氬疄鐜颁唬鐮侀厤鍚堜娇鐢?<sp/>*<sp/>-<sp/>浼犺緭鎿嶄綔鍚屾閿佸彲鑳芥垚涓烘€ц兘鐡堕</highlight></codeline>
<codeline lineno="467"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="468"><highlight class="comment"><sp/>*<sp/>**<sp/>寤鸿<sp/>**:</highlight></codeline>
<codeline lineno="469"><highlight class="comment"><sp/>*<sp/>-<sp/>瀹炵幇瀹屾暣鐨勯敊璇鐞嗗拰鎭㈠鏈哄埗</highlight></codeline>
<codeline lineno="470"><highlight class="comment"><sp/>*<sp/>-<sp/>鑰冭檻鍔犲叆鎬荤嚎浠茶鏈哄埗澶勭悊澶氫富璁惧鎯呭喌</highlight></codeline>
<codeline lineno="471"><highlight class="comment"><sp/>*<sp/>-<sp/>浼樺寲閿佹満鍒朵互鎻愬崌澶氱嚎绋嬭闂€ц兘</highlight></codeline>
<codeline lineno="472"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/bus/i2c_core.c"/>
  </compounddef>
</doxygen>
