<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/bus/spi_core.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('spi__core_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">spi_core.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="precomp_8h_source.html">../../precomp.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="kmdf__spi_8h_source.html">../../../include/hal/bus/kmdf_spi.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for spi_core.c:</div>
<div class="dyncontent">
<div class="center"><img src="spi__core_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2bus_2spi__core_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2spi__core_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2spi__core_8c">
<area shape="rect" title=" " alt="" coords="1099,5,1245,48"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="563,96,669,123"/>
<area shape="poly" title=" " alt="" coords="1099,41,685,101,684,95,1098,36"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="1202,427,1361,453"/>
<area shape="poly" title=" " alt="" coords="1245,29,1393,42,1479,53,1565,69,1642,88,1706,113,1731,128,1750,144,1762,163,1767,182,1767,268,1764,284,1755,299,1725,326,1680,350,1624,371,1497,405,1377,427,1376,422,1496,399,1622,366,1677,345,1722,321,1751,295,1759,282,1761,268,1761,183,1757,165,1746,148,1728,132,1704,118,1641,94,1563,74,1479,59,1393,47,1245,34"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="315,344,437,371"/>
<area shape="poly" title=" " alt="" coords="1099,35,869,53,575,83,433,102,311,124,221,147,191,160,174,172,162,197,164,221,178,244,201,266,231,287,264,306,328,335,326,340,261,311,228,292,198,271,174,247,159,222,157,196,170,169,188,155,219,142,310,118,433,97,574,78,869,48,1099,30"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="1399,253,1521,280"/>
<area shape="poly" title=" " alt="" coords="1245,37,1350,57,1469,87,1525,104,1574,124,1613,145,1640,169,1645,184,1639,199,1623,210,1592,223,1515,251,1513,246,1590,218,1621,205,1635,196,1640,184,1635,172,1610,150,1572,129,1523,109,1467,92,1349,63,1244,42"/>
<area shape="rect" title=" " alt="" coords="194,171,278,197"/>
<area shape="poly" title=" " alt="" coords="563,123,297,173,293,174,292,169,295,168,562,118"/>
<area shape="rect" title=" " alt="" coords="1078,501,1143,528"/>
<area shape="poly" title=" " alt="" coords="563,115,413,126,321,137,228,152,143,171,73,197,45,212,25,228,12,246,8,266,8,358,10,367,17,376,43,394,86,410,141,425,284,450,455,470,635,486,808,497,1063,509,1063,514,808,503,635,491,454,476,284,455,140,430,84,415,41,398,13,380,5,370,3,359,3,265,8,244,21,224,42,207,70,192,142,166,227,146,320,132,412,121,562,110"/>
<area shape="rect" title=" " alt="" coords="788,427,841,453"/>
<area shape="poly" title=" " alt="" coords="563,113,492,118,422,131,394,142,376,156,370,173,377,196,404,232,432,256,461,272,493,283,564,300,604,313,647,334,784,416,781,420,644,338,602,318,562,305,491,288,459,277,429,261,400,236,372,199,364,173,372,153,392,137,420,126,491,113,562,108"/>
<area shape="rect" title=" " alt="" coords="402,171,486,197"/>
<area shape="poly" title=" " alt="" coords="587,126,489,167,487,162,585,121"/>
<area shape="rect" title=" " alt="" coords="510,171,592,197"/>
<area shape="poly" title=" " alt="" coords="607,125,574,161,570,157,603,121"/>
<area shape="rect" href="src_2core_2log_2driver__log_8h.html" title=" " alt="" coords="154,344,292,371"/>
<area shape="poly" title=" " alt="" coords="563,112,464,113,346,121,290,128,240,139,200,154,174,172,163,190,159,210,159,231,165,253,183,295,206,330,201,333,179,298,160,255,154,232,153,210,158,188,170,169,198,149,238,134,289,123,346,116,463,108,563,107"/>
<area shape="rect" href="error__handling_8h.html" title="驱动程序错误处理和断言宏定义" alt="" coords="460,344,633,371"/>
<area shape="poly" title=" " alt="" coords="562,112,466,114,352,122,297,129,248,140,210,154,185,172,180,181,181,191,197,212,231,235,277,259,388,303,490,336,488,342,386,308,275,264,228,240,193,216,176,192,175,180,181,169,207,150,247,135,296,124,351,116,465,109,562,107"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="662,245,780,288"/>
<area shape="poly" title=" " alt="" coords="563,116,495,123,428,137,403,148,388,161,383,176,393,196,409,209,433,220,498,238,575,251,647,258,646,263,574,256,497,243,431,225,406,213,389,199,378,177,383,158,401,143,427,132,494,118,562,110"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="1546,245,1676,288"/>
<area shape="poly" title=" " alt="" coords="670,109,903,115,1218,127,1501,144,1595,156,1624,162,1639,169,1647,184,1648,200,1637,233,1632,231,1643,200,1642,185,1636,173,1622,167,1594,161,1501,150,1218,132,903,121,670,114"/>
<area shape="rect" title=" " alt="" coords="616,171,750,197"/>
<area shape="poly" title=" " alt="" coords="630,121,663,157,659,161,626,125"/>
<area shape="rect" title=" " alt="" coords="774,171,901,197"/>
<area shape="poly" title=" " alt="" coords="655,121,785,163,783,168,654,126"/>
<area shape="rect" title=" " alt="" coords="926,171,1053,197"/>
<area shape="poly" title=" " alt="" coords="670,118,911,165,910,170,669,124"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="1077,171,1248,197"/>
<area shape="poly" title=" " alt="" coords="670,115,1062,167,1061,172,669,120"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="1272,171,1437,197"/>
<area shape="poly" title=" " alt="" coords="670,112,919,134,1257,168,1257,173,918,139,669,118"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1460,171,1625,197"/>
<area shape="poly" title=" " alt="" coords="670,111,994,131,1445,168,1445,173,993,136,670,116"/>
<area shape="poly" title=" " alt="" coords="251,369,353,410,418,433,485,451,649,480,814,498,958,507,1063,510,1063,516,958,512,813,503,649,485,483,456,416,438,351,415,249,374"/>
<area shape="poly" title=" " alt="" coords="270,369,304,376,425,394,519,403,614,411,735,424,773,429,773,435,734,429,613,416,519,409,424,400,304,381,269,374"/>
<area shape="poly" title=" " alt="" coords="549,371,552,411,560,433,574,451,599,462,647,472,785,489,1063,508,1063,514,784,494,646,477,598,467,570,455,555,436,547,412,543,371"/>
<area shape="poly" title=" " alt="" coords="589,369,774,424,772,429,588,374"/>
<area shape="poly" title=" " alt="" coords="634,365,1187,426,1186,431,633,371"/>
<area shape="rect" title=" " alt="" coords="584,427,722,453"/>
<area shape="poly" title=" " alt="" coords="565,369,626,415,623,419,562,373"/>
<area shape="poly" title=" " alt="" coords="1253,456,1156,497,1154,492,1251,451"/>
<area shape="poly" title=" " alt="" coords="725,288,731,331,739,355,749,377,765,398,785,416,781,419,761,402,744,380,734,357,726,332,720,289"/>
<area shape="poly" title=" " alt="" coords="782,284,1226,419,1225,424,780,289"/>
<area shape="rect" title=" " alt="" coords="759,344,820,371"/>
<area shape="poly" title=" " alt="" coords="739,287,773,330,768,333,735,290"/>
<area shape="poly" title=" " alt="" coords="663,285,449,339,436,342,434,337,447,333,661,280"/>
<area shape="poly" title=" " alt="" coords="395,369,461,411,504,433,550,451,621,470,694,485,838,503,967,511,1063,512,1063,517,967,516,838,508,693,490,619,475,548,456,502,438,458,415,392,373"/>
<area shape="poly" title=" " alt="" coords="419,369,449,376,603,403,773,430,772,435,602,409,447,381,418,374"/>
<area shape="poly" title=" " alt="" coords="1598,290,1563,334,1515,381,1472,411,1425,436,1376,457,1326,474,1233,498,1159,511,1158,505,1232,493,1325,469,1374,452,1422,431,1469,406,1512,377,1559,331,1594,287"/>
<area shape="poly" title=" " alt="" coords="1567,291,1448,343,1345,381,1206,410,1068,427,946,437,857,441,856,435,946,431,1067,422,1205,404,1343,376,1446,338,1565,286"/>
<area shape="poly" title=" " alt="" coords="1590,291,1535,335,1464,381,1406,406,1349,425,1348,420,1404,401,1461,376,1532,331,1586,286"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="1212,336,1332,379"/>
<area shape="poly" title=" " alt="" coords="1546,288,1348,339,1346,334,1544,282"/>
<area shape="poly" title=" " alt="" coords="1212,372,857,434,856,429,1211,367"/>
<area shape="poly" title=" " alt="" coords="1277,379,1281,411,1275,412,1272,380"/>
<area shape="poly" title=" " alt="" coords="1103,200,1033,221,1004,233,987,247,973,278,971,311,980,345,996,379,1017,412,1041,442,1086,489,1083,492,1037,445,1013,415,991,382,975,347,966,312,968,277,983,244,1002,229,1031,216,1101,195"/>
<area shape="poly" title=" " alt="" coords="1089,201,967,224,917,237,891,247,878,270,860,315,826,413,821,411,855,313,873,268,888,243,915,232,965,219,1087,195"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="997,253,1126,280"/>
<area shape="poly" title=" " alt="" coords="1148,200,1090,246,1087,242,1145,196"/>
<area shape="poly" title=" " alt="" coords="1093,278,1210,327,1208,332,1090,283"/>
<area shape="poly" title=" " alt="" coords="1277,200,1231,218,1210,231,1191,247,1170,275,1153,305,1129,372,1118,435,1114,486,1108,485,1112,435,1124,370,1148,303,1165,272,1187,243,1207,227,1229,213,1275,195"/>
<area shape="poly" title=" " alt="" coords="1272,198,1100,223,1027,236,987,248,961,271,920,317,836,416,832,413,916,314,957,267,984,243,1026,231,1100,218,1272,193"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="1202,253,1324,280"/>
<area shape="poly" title=" " alt="" coords="1342,200,1290,245,1286,241,1339,196"/>
<area shape="poly" title=" " alt="" coords="1267,280,1271,320,1266,321,1261,281"/>
<area shape="poly" title=" " alt="" coords="1618,196,1658,213,1676,227,1690,244,1698,266,1690,289,1668,324,1642,355,1613,383,1580,407,1508,446,1430,475,1352,494,1277,507,1159,516,1158,511,1277,501,1351,489,1428,470,1505,441,1577,402,1609,379,1639,351,1664,321,1686,287,1693,266,1686,247,1672,231,1655,218,1615,200"/>
<area shape="poly" title=" " alt="" coords="1499,200,1445,220,1388,248,1363,269,1353,280,1337,290,1260,318,1124,358,857,430,856,425,1123,352,1258,313,1335,286,1350,276,1360,265,1385,243,1443,215,1498,195"/>
<area shape="poly" title=" " alt="" coords="1532,200,1485,244,1482,241,1528,196"/>
<area shape="poly" title=" " alt="" coords="1434,283,1331,331,1329,327,1432,278"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FSPI_5FDEVICE_5FCONTEXT_5Fstruct_5F_5FSPI_5F_5FDEVICE_5F_5FCONTEXT" id="r__5FSPI_5FDEVICE_5FCONTEXT_5Fstruct_5F_5FSPI_5F_5FDEVICE_5F_5FCONTEXT"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__SPI__DEVICE__CONTEXT">_SPI_DEVICE_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a5e8b4813a61b999753aee353d4944c23" id="r_a5e8b4813a61b999753aee353d4944c23"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__DEVICE__CONTEXT">_SPI_DEVICE_CONTEXT</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5e8b4813a61b999753aee353d4944c23">PSPI_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:a2a4a689dbe0ef33045635ddfa5db3194" id="r_a2a4a689dbe0ef33045635ddfa5db3194"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__DEVICE__CONTEXT">_SPI_DEVICE_CONTEXT</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2a4a689dbe0ef33045635ddfa5db3194">SPI_DEVICE_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a685d8d7731e750c1512b975df16cc030" id="r_a685d8d7731e750c1512b975df16cc030"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a685d8d7731e750c1512b975df16cc030">SPIInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="el" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>)</td></tr>
<tr class="memitem:ad756f8e3b06fdfa545a7048661038513" id="r_ad756f8e3b06fdfa545a7048661038513"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad756f8e3b06fdfa545a7048661038513">SPIUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a85e21cb755e2b8afb53a12e0413ddfb1" id="r_a85e21cb755e2b8afb53a12e0413ddfb1"><td class="memItemLeft" align="right" valign="top">EVT_WDF_TIMER&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a85e21cb755e2b8afb53a12e0413ddfb1">SPITransferTimerExpired</a></td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__SPI__DEVICE__CONTEXT" id="struct__SPI__DEVICE__CONTEXT"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__SPI__DEVICE__CONTEXT">&#9670;&#160;</a></span>_SPI_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _SPI_DEVICE_CONTEXT</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><div class="dynheader">
Collaboration diagram for _SPI_DEVICE_CONTEXT:</div>
<div class="dyncontent">
<div class="center"><img src="struct__SPI__DEVICE__CONTEXT__coll__graph.png" border="0" usemap="#a__SPI__DEVICE__CONTEXT_coll__map" loading="lazy" alt="Collaboration graph"/></div>
<map name="a__SPI__DEVICE__CONTEXT_coll__map" id="a__SPI__DEVICE__CONTEXT_coll__map">
<area shape="rect" title=" " alt="" coords="5,104,185,131"/>
<area shape="rect" href="kmdf__spi_8h.html#struct__SPI__CONFIG" title=" " alt="" coords="40,5,150,32"/>
<area shape="poly" title=" " alt="" coords="98,48,98,104,92,104,92,48"/>
</map>
<center><span class="legend">[<a target="top" href="graph_legend.html">legend</a>]</span></center></div>
<table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ad94201a4a15b3f381e7016679cfb5231" name="ad94201a4a15b3f381e7016679cfb5231"></a><a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a></td>
<td class="fieldname">
Config</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a764c0d4c344ec67dc804a438695d17bd" name="a764c0d4c344ec67dc804a438695d17bd"></a>WDFIOTARGET</td>
<td class="fieldname">
SpbIoTarget</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a081bab7f80eae29e5a23517be063bbdc" name="a081bab7f80eae29e5a23517be063bbdc"></a><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a></td>
<td class="fieldname">
TransferLock</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a1d960500b2572de5188fa339fba643cb" name="a1d960500b2572de5188fa339fba643cb"></a><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>
<td class="fieldname">
WdfDevice</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a5e8b4813a61b999753aee353d4944c23" name="a5e8b4813a61b999753aee353d4944c23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e8b4813a61b999753aee353d4944c23">&#9670;&#160;</a></span>PSPI_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__DEVICE__CONTEXT">_SPI_DEVICE_CONTEXT</a> * <a class="el" href="#a5e8b4813a61b999753aee353d4944c23">PSPI_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2a4a689dbe0ef33045635ddfa5db3194" name="a2a4a689dbe0ef33045635ddfa5db3194"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a4a689dbe0ef33045635ddfa5db3194">&#9670;&#160;</a></span>SPI_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__DEVICE__CONTEXT">_SPI_DEVICE_CONTEXT</a> <a class="el" href="#a2a4a689dbe0ef33045635ddfa5db3194">SPI_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a685d8d7731e750c1512b975df16cc030" name="a685d8d7731e750c1512b975df16cc030"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a685d8d7731e750c1512b975df16cc030">&#9670;&#160;</a></span>SPIInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPIInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>SpiConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__core_8c_a685d8d7731e750c1512b975df16cc030_cgraph.png" border="0" usemap="#aspi__core_8c_a685d8d7731e750c1512b975df16cc030_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__core_8c_a685d8d7731e750c1512b975df16cc030_cgraph" id="aspi__core_8c_a685d8d7731e750c1512b975df16cc030_cgraph">
<area shape="rect" title=" " alt="" coords="5,67,97,93"/>
<area shape="poly" title=" " alt="" coords="24,67,20,58,23,48,34,42,51,40,69,43,79,49,76,54,68,48,51,45,35,47,27,52,25,57,29,65"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="320,29,426,56"/>
<area shape="poly" title=" " alt="" coords="95,63,144,52,228,43,305,40,305,45,228,48,145,57,96,69"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="168,117,238,144"/>
<area shape="poly" title=" " alt="" coords="94,91,154,112,152,117,93,97"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="145,67,261,93"/>
<area shape="poly" title=" " alt="" coords="97,77,129,77,129,83,97,83"/>
<area shape="poly" title=" " alt="" coords="344,30,339,21,342,11,354,5,373,3,394,5,405,12,402,16,392,10,373,8,356,10,346,15,344,20,348,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="309,92,437,119"/>
<area shape="poly" title=" " alt="" coords="238,123,293,114,294,120,239,128"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="326,143,421,169"/>
<area shape="poly" title=" " alt="" coords="239,133,311,144,310,149,238,138"/>
<area shape="poly" title=" " alt="" coords="261,65,305,55,306,60,262,70"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__core_8c_a685d8d7731e750c1512b975df16cc030_icgraph.png" border="0" usemap="#aspi__core_8c_a685d8d7731e750c1512b975df16cc030_icgraph" loading="lazy" alt=""/></div>
<map name="aspi__core_8c_a685d8d7731e750c1512b975df16cc030_icgraph" id="aspi__core_8c_a685d8d7731e750c1512b975df16cc030_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,97,56"/>
<area shape="poly" title=" " alt="" coords="65,15,60,10,51,8,42,10,38,14,37,20,39,28,34,30,31,20,33,11,40,5,51,3,63,5,69,12"/>
</map>
</div>

</div>
</div>
<a id="ad756f8e3b06fdfa545a7048661038513" name="ad756f8e3b06fdfa545a7048661038513"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad756f8e3b06fdfa545a7048661038513">&#9670;&#160;</a></span>SPIUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID SPIUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__core_8c_ad756f8e3b06fdfa545a7048661038513_cgraph.png" border="0" usemap="#aspi__core_8c_ad756f8e3b06fdfa545a7048661038513_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__core_8c_ad756f8e3b06fdfa545a7048661038513_cgraph" id="aspi__core_8c_ad756f8e3b06fdfa545a7048661038513_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,113,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="161,29,266,56"/>
<area shape="poly" title=" " alt="" coords="113,40,145,40,145,45,113,45"/>
<area shape="poly" title=" " alt="" coords="186,30,182,21,185,11,196,5,213,3,232,5,242,12,239,16,230,10,213,8,198,10,189,15,187,20,191,28"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="a85e21cb755e2b8afb53a12e0413ddfb1" name="a85e21cb755e2b8afb53a12e0413ddfb1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85e21cb755e2b8afb53a12e0413ddfb1">&#9670;&#160;</a></span>SPITransferTimerExpired</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_TIMER SPITransferTimerExpired</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_43b43b2f79854d1934869d5a4aaeb79e.html">bus</a></li><li class="navelem"><a href="spi__core_8c.html">spi_core.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
