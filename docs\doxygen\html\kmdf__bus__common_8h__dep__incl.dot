digraph "C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h"
{
 // LATEX_PDF_SIZE
  bgcolor="transparent";
  edge [fontname=Helvetica,fontsize=10,labelfontname=Helvetica,labelfontsize=10];
  node [fontname=Helvetica,fontsize=10,shape=box,height=0.2,width=0.4];
  Node1 [id="Node000001",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_bus_common.h",height=0.2,width=0.4,color="gray40", fillcolor="grey60", style="filled", fontcolor="black",tooltip=" "];
  Node1 -> Node2 [id="edge1_Node000001_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 [id="Node000002",label="C:/KMDF Driver1/include\l/core/device/device_manager.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8h.html",tooltip="Brief description."];
  Node2 -> Node3 [id="edge2_Node000002_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node3 [id="Node000003",label="C:/KMDF Driver1/include\l/core/driver/driver_entry.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8h.html",tooltip="Brief description."];
  Node3 -> Node4 [id="edge3_Node000003_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node4 [id="Node000004",label="C:/KMDF Driver1/src\l/core/driver/driver\l_entry.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8c.html",tooltip=" "];
  Node3 -> Node5 [id="edge4_Node000003_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node5 [id="Node000005",label="C:/KMDF Driver1/src\l/driver_main.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__main_8c.html",tooltip=" "];
  Node2 -> Node6 [id="edge5_Node000002_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 [id="Node000006",label="C:/KMDF Driver1/src\l/core/device/device\l_manager.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8c.html",tooltip=" "];
  Node2 -> Node7 [id="edge6_Node000002_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node7 [id="Node000007",label="C:/KMDF Driver1/src\l/core/driver/driver\l_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8c.html",tooltip=" "];
  Node2 -> Node5 [id="edge7_Node000002_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 -> Node8 [id="edge8_Node000002_Node000008",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 [id="Node000008",label="C:/KMDF Driver1/src\l/precomp.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8h.html",tooltip=" "];
  Node8 -> Node6 [id="edge9_Node000008_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node4 [id="edge10_Node000008_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node9 [id="edge11_Node000008_Node000009",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node9 [id="Node000009",label="C:/KMDF Driver1/src\l/core/log/driver_log.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__log_8c.html",tooltip=" "];
  Node8 -> Node5 [id="edge12_Node000008_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 -> Node10 [id="edge13_Node000008_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node10 [id="Node000010",label="C:/KMDF Driver1/src\l/hal/bus/gpio_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__core_8c.html",tooltip=" "];
  Node8 -> Node11 [id="edge14_Node000008_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node11 [id="Node000011",label="C:/KMDF Driver1/src\l/hal/bus/i2c_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__core_8c.html",tooltip=" "];
  Node8 -> Node12 [id="edge15_Node000008_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node12 [id="Node000012",label="C:/KMDF Driver1/src\l/hal/bus/spi_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__core_8c.html",tooltip=" "];
  Node8 -> Node13 [id="edge16_Node000008_Node000013",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node13 [id="Node000013",label="C:/KMDF Driver1/src\l/precomp.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8c.html",tooltip=" "];
  Node1 -> Node3 [id="edge17_Node000001_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node14 [id="edge18_Node000001_Node000014",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 [id="Node000014",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_gpio.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__gpio_8h.html",tooltip=" "];
  Node14 -> Node15 [id="edge19_Node000014_Node000015",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node15 [id="Node000015",label="C:/KMDF Driver1/include\l/hal/devices/gpio_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__device_8h.html",tooltip=" "];
  Node15 -> Node16 [id="edge20_Node000015_Node000016",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 [id="Node000016",label="C:/KMDF Driver1/src\l/hal/devices/gpio_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__device_8c.html",tooltip=" "];
  Node14 -> Node10 [id="edge21_Node000014_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node17 [id="edge22_Node000001_Node000017",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node17 [id="Node000017",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_i2c.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__i2c_8h.html",tooltip=" "];
  Node17 -> Node18 [id="edge23_Node000017_Node000018",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node18 [id="Node000018",label="C:/KMDF Driver1/include\l/hal/devices/i2c_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__device_8h.html",tooltip=" "];
  Node17 -> Node5 [id="edge24_Node000017_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node17 -> Node11 [id="edge25_Node000017_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node17 -> Node19 [id="edge26_Node000017_Node000019",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node19 [id="Node000019",label="C:/KMDF Driver1/src\l/hal/devices/i2c_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__device_8c.html",tooltip=" "];
  Node1 -> Node20 [id="edge27_Node000001_Node000020",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node20 [id="Node000020",label="C:/KMDF Driver1/include\l/hal/bus/kmdf_spi.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__spi_8h.html",tooltip=" "];
  Node20 -> Node21 [id="edge28_Node000020_Node000021",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node21 [id="Node000021",label="C:/KMDF Driver1/include\l/hal/devices/spi_device.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__device_8h.html",tooltip=" "];
  Node21 -> Node22 [id="edge29_Node000021_Node000022",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node22 [id="Node000022",label="C:/KMDF Driver1/src\l/hal/devices/spi_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__device_8c.html",tooltip=" "];
  Node20 -> Node12 [id="edge30_Node000020_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node20 -> Node22 [id="edge31_Node000020_Node000022",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node5 [id="edge32_Node000001_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
}
