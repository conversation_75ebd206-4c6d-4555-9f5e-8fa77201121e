digraph "C:/KMDF Driver1/src/hal/bus/gpio_core.c"
{
 // LATEX_PDF_SIZE
  bgcolor="transparent";
  edge [fontname=Helvetica,fontsize=10,labelfontname=Helvetica,labelfontsize=10];
  node [fontname=Helvetica,fontsize=10,shape=box,height=0.2,width=0.4];
  Node1 [id="Node000001",label="C:/KMDF Driver1/src\l/hal/bus/gpio_core.c",height=0.2,width=0.4,color="gray40", fillcolor="grey60", style="filled", fontcolor="black",tooltip=" "];
  Node1 -> Node2 [id="edge1_Node000001_Node000002",color="steelblue1",style="solid",tooltip=" "];
  Node2 [id="Node000002",label="../../precomp.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8h.html",tooltip=" "];
  Node2 -> Node3 [id="edge2_Node000002_Node000003",color="steelblue1",style="solid",tooltip=" "];
  Node3 [id="Node000003",label="ntddk.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node4 [id="edge3_Node000002_Node000004",color="steelblue1",style="solid",tooltip=" "];
  Node4 [id="Node000004",label="wdf.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node5 [id="edge4_Node000002_Node000005",color="steelblue1",style="solid",tooltip=" "];
  Node5 [id="Node000005",label="ntstrsafe.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node6 [id="edge5_Node000002_Node000006",color="steelblue1",style="solid",tooltip=" "];
  Node6 [id="Node000006",label="wdfusb.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node7 [id="edge6_Node000002_Node000007",color="steelblue1",style="solid",tooltip=" "];
  Node7 [id="Node000007",label="usbspec.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node8 [id="edge7_Node000002_Node000008",color="steelblue1",style="solid",tooltip=" "];
  Node8 [id="Node000008",label="usb.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node9 [id="edge8_Node000002_Node000009",color="steelblue1",style="solid",tooltip=" "];
  Node9 [id="Node000009",label="wdfldr.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node10 [id="edge9_Node000002_Node000010",color="steelblue1",style="solid",tooltip=" "];
  Node10 [id="Node000010",label="wdfinstaller.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node2 -> Node11 [id="edge10_Node000002_Node000011",color="steelblue1",style="solid",tooltip=" "];
  Node11 [id="Node000011",label="../include/core/driver\l/driver_core.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8h.html",tooltip=" "];
  Node11 -> Node12 [id="edge11_Node000011_Node000012",color="steelblue1",style="solid",tooltip=" "];
  Node12 [id="Node000012",label="wdm.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node11 -> Node4 [id="edge12_Node000011_Node000004",color="steelblue1",style="solid",tooltip=" "];
  Node11 -> Node13 [id="edge13_Node000011_Node000013",color="steelblue1",style="solid",tooltip=" "];
  Node13 [id="Node000013",label="../error/error_codes.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$error__codes_8h.html",tooltip=" "];
  Node13 -> Node3 [id="edge14_Node000013_Node000003",color="steelblue1",style="solid",tooltip=" "];
  Node11 -> Node14 [id="edge15_Node000011_Node000014",color="steelblue1",style="solid",tooltip=" "];
  Node14 [id="Node000014",label="../log/driver_log.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$include_2core_2log_2driver__log_8h.html",tooltip=" "];
  Node14 -> Node3 [id="edge16_Node000014_Node000003",color="steelblue1",style="solid",tooltip=" "];
  Node14 -> Node4 [id="edge17_Node000014_Node000004",color="steelblue1",style="solid",tooltip=" "];
  Node2 -> Node15 [id="edge18_Node000002_Node000015",color="steelblue1",style="solid",tooltip=" "];
  Node15 [id="Node000015",label="../include/core/device\l/device_manager.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8h.html",tooltip="Brief description."];
  Node15 -> Node3 [id="edge19_Node000015_Node000003",color="steelblue1",style="solid",tooltip=" "];
  Node15 -> Node4 [id="edge20_Node000015_Node000004",color="steelblue1",style="solid",tooltip=" "];
  Node15 -> Node16 [id="edge21_Node000015_Node000016",color="steelblue1",style="solid",tooltip=" "];
  Node16 [id="Node000016",label="../../hal/bus/kmdf\l_bus_common.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__bus__common_8h.html",tooltip=" "];
  Node16 -> Node4 [id="edge22_Node000016_Node000004",color="steelblue1",style="solid",tooltip=" "];
  Node16 -> Node13 [id="edge23_Node000016_Node000013",color="steelblue1",style="solid",tooltip=" "];
  Node15 -> Node13 [id="edge24_Node000015_Node000013",color="steelblue1",style="solid",tooltip=" "];
  Node2 -> Node14 [id="edge25_Node000002_Node000014",color="steelblue1",style="solid",tooltip=" "];
  Node2 -> Node17 [id="edge26_Node000002_Node000017",color="steelblue1",style="solid",tooltip=" "];
  Node17 [id="Node000017",label="../include/common/ioctl.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$ioctl_8h.html",tooltip=" "];
  Node17 -> Node18 [id="edge27_Node000017_Node000018",color="steelblue1",style="solid",tooltip=" "];
  Node18 [id="Node000018",label="devioctl.h",height=0.2,width=0.4,color="grey60", fillcolor="#E0E0E0", style="filled",tooltip=" "];
  Node1 -> Node19 [id="edge28_Node000001_Node000019",color="steelblue1",style="solid",tooltip=" "];
  Node19 [id="Node000019",label="../../../include/hal\l/bus/kmdf_gpio.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$kmdf__gpio_8h.html",tooltip=" "];
  Node19 -> Node16 [id="edge29_Node000019_Node000016",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node14 [id="edge30_Node000001_Node000014",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node13 [id="edge31_Node000001_Node000013",color="steelblue1",style="solid",tooltip=" "];
}
