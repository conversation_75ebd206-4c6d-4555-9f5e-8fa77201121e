digraph "C:/KMDF Driver1/include/core/log/driver_log.h"
{
 // LATEX_PDF_SIZE
  bgcolor="transparent";
  edge [fontname=Helvetica,fontsize=10,labelfontname=Helvetica,labelfontsize=10];
  node [fontname=Helvetica,fontsize=10,shape=box,height=0.2,width=0.4];
  Node1 [id="Node000001",label="C:/KMDF Driver1/include\l/core/log/driver_log.h",height=0.2,width=0.4,color="gray40", fillcolor="grey60", style="filled", fontcolor="black",tooltip=" "];
  Node1 -> Node2 [id="edge1_Node000001_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 [id="Node000002",label="C:/KMDF Driver1/include\l/core/driver/driver_core.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8h.html",tooltip=" "];
  Node2 -> Node3 [id="edge2_Node000002_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node3 [id="Node000003",label="C:/KMDF Driver1/src\l/core/driver/driver\l_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__core_8c.html",tooltip=" "];
  Node2 -> Node4 [id="edge3_Node000002_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node4 [id="Node000004",label="C:/KMDF Driver1/src\l/core/driver/driver\l_entry.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8c.html",tooltip=" "];
  Node2 -> Node5 [id="edge4_Node000002_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node5 [id="Node000005",label="C:/KMDF Driver1/src\l/driver_main.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__main_8c.html",tooltip=" "];
  Node2 -> Node6 [id="edge5_Node000002_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 [id="Node000006",label="C:/KMDF Driver1/src\l/precomp.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8h.html",tooltip=" "];
  Node6 -> Node7 [id="edge6_Node000006_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node7 [id="Node000007",label="C:/KMDF Driver1/src\l/core/device/device\l_manager.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8c.html",tooltip=" "];
  Node6 -> Node4 [id="edge7_Node000006_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 -> Node8 [id="edge8_Node000006_Node000008",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 [id="Node000008",label="C:/KMDF Driver1/src\l/core/log/driver_log.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__log_8c.html",tooltip=" "];
  Node6 -> Node5 [id="edge9_Node000006_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 -> Node9 [id="edge10_Node000006_Node000009",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node9 [id="Node000009",label="C:/KMDF Driver1/src\l/hal/bus/gpio_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__core_8c.html",tooltip=" "];
  Node6 -> Node10 [id="edge11_Node000006_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node10 [id="Node000010",label="C:/KMDF Driver1/src\l/hal/bus/i2c_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__core_8c.html",tooltip=" "];
  Node6 -> Node11 [id="edge12_Node000006_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node11 [id="Node000011",label="C:/KMDF Driver1/src\l/hal/bus/spi_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__core_8c.html",tooltip=" "];
  Node6 -> Node12 [id="edge13_Node000006_Node000012",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node12 [id="Node000012",label="C:/KMDF Driver1/src\l/precomp.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8c.html",tooltip=" "];
  Node1 -> Node13 [id="edge14_Node000001_Node000013",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node13 [id="Node000013",label="C:/KMDF Driver1/include\l/core/driver/driver_entry.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8h.html",tooltip="Brief description."];
  Node13 -> Node4 [id="edge15_Node000013_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node13 -> Node5 [id="edge16_Node000013_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node7 [id="edge17_Node000001_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node3 [id="edge18_Node000001_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node14 [id="edge19_Node000001_Node000014",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node14 [id="Node000014",label="C:/KMDF Driver1/src\l/core/error/error_handling.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$error__handling_8c.html",tooltip=" "];
  Node1 -> Node5 [id="edge20_Node000001_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node9 [id="edge21_Node000001_Node000009",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node10 [id="edge22_Node000001_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node11 [id="edge23_Node000001_Node000011",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node1 -> Node15 [id="edge24_Node000001_Node000015",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node15 [id="Node000015",label="C:/KMDF Driver1/src\l/hal/devices/gpio_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__device_8c.html",tooltip=" "];
  Node1 -> Node16 [id="edge25_Node000001_Node000016",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node16 [id="Node000016",label="C:/KMDF Driver1/src\l/hal/devices/i2c_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__device_8c.html",tooltip=" "];
  Node1 -> Node17 [id="edge26_Node000001_Node000017",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node17 [id="Node000017",label="C:/KMDF Driver1/src\l/hal/devices/spi_device.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__device_8c.html",tooltip=" "];
  Node1 -> Node6 [id="edge27_Node000001_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
}
