<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li class="current"><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_g.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_g" name="index_g"></a>- g -</h3><ul>
<li>g_DriverContext&#160;:&#160;<a class="el" href="driver__core_8c.html#abd136e6aae106e58c949c867cbb92a41">driver_core.c</a></li>
<li>g_LogConfig&#160;:&#160;<a class="el" href="driver__log_8c.html#af114289be71fc27e5ce43d55d4d6622c">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#af114289be71fc27e5ce43d55d4d6622c">driver_log.h</a></li>
<li>g_LogFileHandle&#160;:&#160;<a class="el" href="driver__log_8c.html#a0ac5b4438a064ed7f81749a6fdb8c459">driver_log.c</a></li>
<li>g_LogFilePath&#160;:&#160;<a class="el" href="driver__log_8c.html#a20c97100dfe2e67b76338f86895b9514">driver_log.c</a></li>
<li>g_LogFilePathBuffer&#160;:&#160;<a class="el" href="driver__log_8c.html#a1dbaa3e63f60a2732e920d5fa35b54e4">driver_log.c</a></li>
<li>g_LogInitialized&#160;:&#160;<a class="el" href="driver__log_8c.html#acd7f22e672d8bbb5ac97c70459b869fb">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#acd7f22e672d8bbb5ac97c70459b869fb">driver_log.h</a></li>
<li>g_LogLevelNames&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ad2099051d14962ced91f03017c7021f3">driver_log.h</a></li>
<li>g_LogLock&#160;:&#160;<a class="el" href="driver__log_8c.html#a9bcd4366f4ea891736b831d3d798ab36">driver_log.c</a></li>
<li>GENERIC_WRITE&#160;:&#160;<a class="el" href="gpio__core_8c.html#a319be52f8fb7536ca4d2f35163ab0ad3">gpio_core.c</a></li>
<li>GetDriverVersion()&#160;:&#160;<a class="el" href="driver__core_8h.html#a6c0d04af706f5b9f31c9698344dc6104">driver_core.h</a></li>
<li>GetLastErrorFromNTStatus()&#160;:&#160;<a class="el" href="error__handling_8c.html#afd059b655e91e68d030b34f16ee84647">error_handling.c</a></li>
<li>gLock&#160;:&#160;<a class="el" href="driver__core_8c.html#a7ca8aeb3b88c194b24cfb4066bcc60e7">driver_core.c</a></li>
<li>GPIO_DEVICE_CONFIG&#160;:&#160;<a class="el" href="gpio__device_8h.html#a062e6f1e503256e897b3f6642fdce349">gpio_device.h</a></li>
<li>GPIO_DEVICE_EVENT&#160;:&#160;<a class="el" href="gpio__device_8h.html#aa63f80dfdfff636a324aac08eab189a0">gpio_device.h</a></li>
<li>GPIO_DEVICE_EVENT_CALLBACK&#160;:&#160;<a class="el" href="gpio__device_8h.html#a7f38f134552248bad184ebb15479ad70">gpio_device.h</a></li>
<li>GPIO_DEVICE_POOL_TAG&#160;:&#160;<a class="el" href="gpio__device_8c.html#a6acc285bc10925e95287d1821e20f2f7">gpio_device.c</a></li>
<li>GPIO_DEVICE_STATE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a8be6b3f1a7d49373c162209cfdfb0bc5">gpio_device.h</a></li>
<li>GPIO_DEVICE_TYPE&#160;:&#160;<a class="el" href="gpio__device_8h.html#a2ddd51479fb5937e5f4ba2afa3e1504b">gpio_device.h</a></li>
<li>GPIO_INTERRUPT_CALLBACK&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a9643691e5b435f14c69e6016c2fae45a">kmdf_gpio.h</a></li>
<li>GPIO_INTERRUPT_TYPE&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">kmdf_gpio.h</a></li>
<li>GPIO_PIN_CONFIG&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">kmdf_gpio.h</a></li>
<li>GPIO_PIN_DIRECTION&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">kmdf_gpio.h</a></li>
<li>GPIO_PIN_POLARITY&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a96127f2b5d39ef12184a23f9ad42999b">kmdf_gpio.h</a></li>
<li>GPIO_POOL_TAG&#160;:&#160;<a class="el" href="gpio__core_8c.html#aaccace669b39ad606306ac907224ae82">gpio_core.c</a></li>
<li>GpioDeviceCleanup()&#160;:&#160;<a class="el" href="gpio__device_8c.html#ac3d3347067d7be6497e71b6ba4b389c2">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#ac3d3347067d7be6497e71b6ba4b389c2">gpio_device.h</a></li>
<li>GpioDeviceGetState()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a0bd26e3410adfbb26bba326602f8fec6">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#a0bd26e3410adfbb26bba326602f8fec6">gpio_device.h</a></li>
<li>GpioDeviceInitialize()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a21428e126bf8c8996ea3405e6a8be2f2">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#a21428e126bf8c8996ea3405e6a8be2f2">gpio_device.h</a></li>
<li>GpioDeviceInterruptCallback()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a9b4681b0c2d7f8009a387b9104735752">gpio_device.c</a></li>
<li>GpioDevicePulse()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a1e860d4292f8df84e5d3101f8a415d6c">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#a1e860d4292f8df84e5d3101f8a415d6c">gpio_device.h</a></li>
<li>GpioDeviceRegisterCallback()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a6448a7e48d735f67501f3273b75485fd">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#a6448a7e48d735f67501f3273b75485fd">gpio_device.h</a></li>
<li>GpioDeviceSetState()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a731812dec996a670e7d557a282535d3d">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#a731812dec996a670e7d557a282535d3d">gpio_device.h</a></li>
<li>GpioDeviceUnregisterCallback()&#160;:&#160;<a class="el" href="gpio__device_8c.html#afbd2bd91a99c594504ca275fd8b45825">gpio_device.c</a>, <a class="el" href="gpio__device_8h.html#afbd2bd91a99c594504ca275fd8b45825">gpio_device.h</a></li>
<li>GpioDirectionIn&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2">kmdf_gpio.h</a></li>
<li>GpioDirectionOut&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c">kmdf_gpio.h</a></li>
<li>GPIODisableInterrupt()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4">kmdf_gpio.h</a></li>
<li>GPIOEnableInterrupt()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a818408e823499cbc8bf3a09f74062a48">kmdf_gpio.h</a></li>
<li>GpioEventChange&#160;:&#160;<a class="el" href="gpio__device_8h.html#ad2f2cb92e3331b0a63b482671ae9ff0fa71136dfd97b017cb0b6288ef5889cf71">gpio_device.h</a></li>
<li>GpioEventOff&#160;:&#160;<a class="el" href="gpio__device_8h.html#ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652">gpio_device.h</a></li>
<li>GpioEventOn&#160;:&#160;<a class="el" href="gpio__device_8h.html#ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d">gpio_device.h</a></li>
<li>GpioEventPress&#160;:&#160;<a class="el" href="gpio__device_8h.html#ad2f2cb92e3331b0a63b482671ae9ff0fade992ff1fe1b3d791821a97aaafd5789">gpio_device.h</a></li>
<li>GpioEventRelease&#160;:&#160;<a class="el" href="gpio__device_8h.html#ad2f2cb92e3331b0a63b482671ae9ff0fa3b928ecf3b3a5198bb6a552f62814d3e">gpio_device.h</a></li>
<li>GPIOGetValue()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#adcddf2e62a93fe5cc3fa7f46b67845bb">kmdf_gpio.h</a></li>
<li>GPIOInitialize()&#160;:&#160;<a class="el" href="gpio__core_8c.html#a50ea4c2976c29c52387cd273dc289c3b">gpio_core.c</a>, <a class="el" href="kmdf__gpio_8h.html#a0a73c23c89291af0e81cef7098d10e29">kmdf_gpio.h</a></li>
<li>GpioInterruptBoth&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64">kmdf_gpio.h</a></li>
<li>GpioInterruptFalling&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84">kmdf_gpio.h</a></li>
<li>GpioInterruptLevel&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42">kmdf_gpio.h</a></li>
<li>GpioInterruptNone&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76">kmdf_gpio.h</a></li>
<li>GpioInterruptRising&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560">kmdf_gpio.h</a></li>
<li>GpioPolarityActiveHigh&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d">kmdf_gpio.h</a></li>
<li>GpioPolarityActiveLow&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78">kmdf_gpio.h</a></li>
<li>GPIOPulse()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#ade1cb652014fc4a3984567ff49900d81">kmdf_gpio.h</a></li>
<li>GPIOSetDirection()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#aab159dfcef06f528d7ebdb9fa3ce8be4">kmdf_gpio.h</a></li>
<li>GPIOSetValue()&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#a81265230a3fa84b9f3a7851d8c9ebe3d">kmdf_gpio.h</a></li>
<li>GpioStateError&#160;:&#160;<a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813">gpio_device.h</a></li>
<li>GpioStateOff&#160;:&#160;<a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834">gpio_device.h</a></li>
<li>GpioStateOn&#160;:&#160;<a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6">gpio_device.h</a></li>
<li>GpioStateUnknown&#160;:&#160;<a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f">gpio_device.h</a></li>
<li>GpioTypeButton&#160;:&#160;<a class="el" href="gpio__device_8h.html#a6f2f394001e2dd84a10864922d1672c6a9f11eddd6db1aa56c00b47541c54802f">gpio_device.h</a></li>
<li>GpioTypeGeneric&#160;:&#160;<a class="el" href="gpio__device_8h.html#a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe">gpio_device.h</a></li>
<li>GpioTypeLed&#160;:&#160;<a class="el" href="gpio__device_8h.html#a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60">gpio_device.h</a></li>
<li>GpioTypeSensor&#160;:&#160;<a class="el" href="gpio__device_8h.html#a6f2f394001e2dd84a10864922d1672c6af84e71ef287fe86c36e80f06a7826d7f">gpio_device.h</a></li>
<li>GPIOUninitialize()&#160;:&#160;<a class="el" href="gpio__core_8c.html#a785f00e9c0879fb478077d2cdce99906">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#a6184bcc868fec6d16949da5c95315be4">gpio_device.c</a>, <a class="el" href="kmdf__gpio_8h.html#a785f00e9c0879fb478077d2cdce99906">kmdf_gpio.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
