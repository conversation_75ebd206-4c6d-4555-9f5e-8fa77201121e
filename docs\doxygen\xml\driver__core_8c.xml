<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="driver__core_8c" kind="file" language="C++">
    <compoundname>driver_core.c</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="driver__core_8h" local="yes">../../../include/core/driver/driver_core.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="device__manager_8h" local="yes">../../../include/core/device/device_manager.h</includes>
    <incdepgraph>
      <node id="8">
        <label>../../../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../../../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="7">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="9">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/core/driver/driver_core.c</label>
        <link refid="driver__core_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
      <node id="5">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <innerclass refid="struct__DRIVER__CONTEXT" prot="public">_DRIVER_CONTEXT</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="driver__core_8c_1af11aade3f3741fb554915d10d3f514eb" prot="public" static="no">
        <name>INITGUID</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="1" column="9" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="1" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" prot="public" static="no">
        <type>struct <ref refid="struct__DRIVER__CONTEXT" kindref="compound">_DRIVER_CONTEXT</ref></type>
        <definition>typedef struct _DRIVER_CONTEXT DRIVER_CONTEXT</definition>
        <argsstring></argsstring>
        <name>DRIVER_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="23" column="16"/>
      </memberdef>
      <memberdef kind="typedef" id="driver__core_8c_1a703dc89b9de7ff2f89e1467ae811be52" prot="public" static="no">
        <type>struct <ref refid="struct__DRIVER__CONTEXT" kindref="compound">_DRIVER_CONTEXT</ref> *</type>
        <definition>typedef struct _DRIVER_CONTEXT * PDRIVER_CONTEXT</definition>
        <argsstring></argsstring>
        <name>PDRIVER_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="23" column="33"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" prot="public" static="yes" mutable="no">
        <type><ref refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" kindref="member">DRIVER_CONTEXT</ref></type>
        <definition>DRIVER_CONTEXT g_DriverContext</definition>
        <argsstring></argsstring>
        <name>g_DriverContext</name>
        <initializer>= {0}</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="26" column="23" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="26" bodyend="26"/>
        <referencedby refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</referencedby>
        <referencedby refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" compoundref="driver__core_8c" startline="221" endline="249">DriverCoreCleanup</referencedby>
        <referencedby refid="driver__core_8c_1a7eca1538f567ab5a5add60bb6d2d03b5" compoundref="driver__core_8c" startline="210" endline="215">DriverCoreGetConfiguration</referencedby>
        <referencedby refid="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" compoundref="driver__core_8c" startline="194" endline="204">DriverCoreGetDevice</referencedby>
        <referencedby refid="driver__core_8c_1a222752d15e46b43b3a4ae0c787d4018d" compoundref="driver__core_8c" startline="183" endline="188">DriverCoreGetDeviceCount</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" compoundref="driver__core_8c" startline="255" endline="343">DriverCoreProcessIoControl</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" prot="public" static="no" mutable="no">
        <type>FAST_MUTEX</type>
        <definition>FAST_MUTEX gLock</definition>
        <argsstring></argsstring>
        <name>gLock</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="29" column="12" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="29" bodyend="-1"/>
        <referencedby refid="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" compoundref="driver__core_8c" startline="40" endline="56">DirectISR</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>void NTAPI</type>
        <definition>void NTAPI DirectISR</definition>
        <argsstring>(IN PKINTERRUPT Interrupt, IN PVOID Context)</argsstring>
        <name>DirectISR</name>
        <param>
          <type><ref refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" kindref="member">IN</ref> PKINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type><ref refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" kindref="member">IN</ref> PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="40" column="12" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="40" bodyend="56"/>
        <references refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" compoundref="driver__core_8c" startline="29">gLock</references>
        <references refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" compoundref="precomp_8h" startline="52">IN</references>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DriverCoreAddDevice</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>DriverCoreAddDevice</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreAddDevice - 鍚戦┍鍔ㄦ牳蹇冩坊鍔犺澶? * <parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>瑕佹坊鍔犵殑璁惧</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="113" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="113" bodyend="134"/>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="driver__core_8h_1adb9000833a6dada8f3fd1e267c320b2b" compoundref="driver__core_8h" startline="25">MAX_DRIVER_DEVICES</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID DriverCoreCleanup</definition>
        <argsstring>(VOID)</argsstring>
        <name>DriverCoreCleanup</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreCleanup - 娓呯悊椹卞姩鏍稿績璧勬簮 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="221" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="221" bodyend="249"/>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" compoundref="driver__core_8h" startline="18">DriverTypeNetwork</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" compoundref="driver__core_8h" startline="19">DriverTypeStorage</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
        <referencedby refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__entry_8c" startline="148" endline="157">EvtDriverUnload</referencedby>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1a7eca1538f567ab5a5add60bb6d2d03b5" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kindref="member">PDRIVER_CONFIG</ref></type>
        <definition>PDRIVER_CONFIG DriverCoreGetConfiguration</definition>
        <argsstring>(VOID)</argsstring>
        <name>DriverCoreGetConfiguration</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreGetConfiguration - 鑾峰彇椹卞姩閰嶇疆</para>
<para><simplesect kind="return"><para><ref refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kindref="member">PDRIVER_CONFIG</ref> 椹卞姩閰嶇疆 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="210" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="210" bodyend="215"/>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>WDFDEVICE DriverCoreGetDevice</definition>
        <argsstring>(_In_ ULONG Index)</argsstring>
        <name>DriverCoreGetDevice</name>
        <param>
          <type>_In_ ULONG</type>
          <declname>Index</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreGetDevice - 鏍规嵁绱㈠紩鑾峰彇璁惧</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Index</parametername>
</parameternamelist>
<parameterdescription>
<para>璁惧绱㈠紩</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref> 璁惧瀵硅薄 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="194" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="194" bodyend="204"/>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1a222752d15e46b43b3a4ae0c787d4018d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>ULONG</type>
        <definition>ULONG DriverCoreGetDeviceCount</definition>
        <argsstring>(VOID)</argsstring>
        <name>DriverCoreGetDeviceCount</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreGetDeviceCount - 鑾峰彇椹卞姩绋嬪簭绠＄悊鐨勮澶囨暟閲? * <simplesect kind="return"><para>ULONG 璁惧鏁伴噺 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="183" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="183" bodyend="188"/>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1acdb452dbcae039af8967376463c758b9" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DriverCoreInitialize</definition>
        <argsstring>(_In_ WDFDRIVER Driver, _In_ PDRIVER_CONFIG DriverConfig)</argsstring>
        <name>DriverCoreInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>Driver</declname>
        </param>
        <param>
          <type>_In_ <ref refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kindref="member">PDRIVER_CONFIG</ref></type>
          <declname>DriverConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreInitialize - 鍒濆鍖栭┍鍔ㄦ牳蹇冩ā鍧? * <parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Driver</parametername>
</parameternamelist>
<parameterdescription>
<para>椹卞姩瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">DriverConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>椹卞姩閰嶇疆</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="62" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="62" bodyend="107"/>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b" compoundref="driver__core_8h" startline="17">DriverTypeGeneric</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" compoundref="driver__core_8h" startline="18">DriverTypeNetwork</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" compoundref="driver__core_8h" startline="19">DriverTypeStorage</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DriverCoreProcessIoControl</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ WDFREQUEST Request, _In_ ULONG IoControlCode, _In_opt_ PVOID InputBuffer, _In_ SIZE_T InputBufferLength, _Out_opt_ PVOID OutputBuffer, _In_ SIZE_T OutputBufferLength, _Out_opt_ PSIZE_T BytesReturned)</argsstring>
        <name>DriverCoreProcessIoControl</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref></type>
          <declname>Request</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>IoControlCode</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>InputBuffer</declname>
        </param>
        <param>
          <type>_In_ SIZE_T</type>
          <declname>InputBufferLength</declname>
        </param>
        <param>
          <type>_Out_opt_ PVOID</type>
          <declname>OutputBuffer</declname>
        </param>
        <param>
          <type>_In_ SIZE_T</type>
          <declname>OutputBufferLength</declname>
        </param>
        <param>
          <type>_Out_opt_ PSIZE_T</type>
          <declname>BytesReturned</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreProcessIoControl - 澶勭悊椹卞姩绋嬪簭绾у埆鐨処O鎺у埗璇锋眰</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Request</parametername>
</parameternamelist>
<parameterdescription>
<para>璇锋眰瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">IoControlCode</parametername>
</parameternamelist>
<parameterdescription>
<para>IO鎺у埗鐮? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">InputBuffer</parametername>
</parameternamelist>
<parameterdescription>
<para>杈撳叆缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">InputBufferLength</parametername>
</parameternamelist>
<parameterdescription>
<para>杈撳叆缂撳啿鍖哄ぇ灏? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">OutputBuffer</parametername>
</parameternamelist>
<parameterdescription>
<para>杈撳嚭缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">OutputBufferLength</parametername>
</parameternamelist>
<parameterdescription>
<para>杈撳嚭缂撳啿鍖哄ぇ灏? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesReturned</parametername>
</parameternamelist>
<parameterdescription>
<para>杩斿洖鐨勫瓧鑺傛暟</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="255" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="255" bodyend="343"/>
        <references refid="struct__DRIVER__VERSION_1a0f5edd5b90ea7d0767d56293b15a36f5" compoundref="driver__core_8h" startline="31">_DRIVER_VERSION::Build</references>
        <references refid="struct__DRIVER__STATISTICS_1a752819e9a3d5e0c0807f1ad785cf8b95" compoundref="driver__core_8h" startline="37">_DRIVER_STATISTICS::DeviceCount</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" compoundref="driver__core_8h" startline="18">DriverTypeNetwork</references>
        <references refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" compoundref="driver__core_8h" startline="19">DriverTypeStorage</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="driver__core_8h_1a6d4bdae2f23ab96032a14098fb28b1f2" compoundref="driver__core_8h" startline="71">IOCTL_DRIVER_GET_STATISTICS</references>
        <references refid="driver__core_8h_1a07c561db6f9baf7d9f5452605546aed9" compoundref="driver__core_8h" startline="70">IOCTL_DRIVER_GET_VERSION</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
        <references refid="struct__DRIVER__VERSION_1a94c8bf9509d348f5cd15c358a92eb0ce" compoundref="driver__core_8h" startline="29">_DRIVER_VERSION::Major</references>
        <references refid="struct__DRIVER__VERSION_1aaa3047faacbaba04237817cbf6c9f0e9" compoundref="driver__core_8h" startline="30">_DRIVER_VERSION::Minor</references>
        <references refid="struct__DRIVER__VERSION_1a7869e35e89c7ca48a53d1e555a4123cc" compoundref="driver__core_8h" startline="32">_DRIVER_VERSION::Revision</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="driver__core_8c_1a89627789114e389118ee51bda8684ab6" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS DriverCoreRemoveDevice</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>DriverCoreRemoveDevice</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>DriverCoreRemoveDevice - 浠庨┍鍔ㄦ牳蹇冪Щ闄よ澶? * <parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>瑕佺Щ闄ょ殑璁惧</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/driver/driver_core.c" line="140" column="1" bodyfile="C:/KMDF Driver1/src/core/driver/driver_core.c" bodystart="140" bodyend="177"/>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" compoundref="driver__core_8c" startline="26" endline="26">g_DriverContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1" refid="driver__core_8c_1af11aade3f3741fb554915d10d3f514eb" refkind="member"><highlight class="preprocessor">#define<sp/>INITGUID</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*<sp/>driver_core.c</highlight></codeline>
<codeline lineno="7"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="8"><highlight class="comment"><sp/>*<sp/>驱动核心模块实现</highlight></codeline>
<codeline lineno="9"><highlight class="comment"><sp/>*<sp/>提供驱动程序核心功能实现，包括设备初始化、调度接口和通用功能</highlight></codeline>
<codeline lineno="10"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="driver__core_8h" kindref="compound">../../../include/core/driver/driver_core.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="device__manager_8h" kindref="compound">../../../include/core/device/device_manager.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>驱动程序实例参数</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="struct__DRIVER__CONTEXT" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__DRIVER__CONTEXT" kindref="compound">_DRIVER_CONTEXT</ref><sp/>{</highlight></codeline>
<codeline lineno="19" refid="struct__DRIVER__CONTEXT_1a36a8e8b359a2c287561b83b1fee106f8" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/><ref refid="struct__DRIVER__CONTEXT_1a36a8e8b359a2c287561b83b1fee106f8" kindref="member">Driver</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF驱动对象</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="struct__DRIVER__CONTEXT_1aa7ddcfc01ffa413221702e66f502fc1d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8h_1ab3f3478bc4dcb804303c86d537979d13" kindref="member">DRIVER_CONFIG</ref><sp/><ref refid="struct__DRIVER__CONTEXT_1aa7ddcfc01ffa413221702e66f502fc1d" kindref="member">DriverConfig</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>驱动配置</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="struct__DRIVER__CONTEXT_1a47ece9e7bf05f77968bf674a4342396a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__DRIVER__CONTEXT_1a47ece9e7bf05f77968bf674a4342396a" kindref="member">DeviceCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>已创建的设备数量</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="struct__DRIVER__CONTEXT_1a008388fc65c89b1ae3992fc6a4aa27f3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="struct__DRIVER__CONTEXT_1a008388fc65c89b1ae3992fc6a4aa27f3" kindref="member">Devices</ref>[<ref refid="driver__core_8h_1adb9000833a6dada8f3fd1e267c320b2b" kindref="member">MAX_DRIVER_DEVICES</ref>];<sp/></highlight><highlight class="comment">//<sp/>已创建的设备列表</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" refkind="member"><highlight class="normal">}<sp/><ref refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" kindref="member">DRIVER_CONTEXT</ref>,<sp/>*<ref refid="driver__core_8c_1a703dc89b9de7ff2f89e1467ae811be52" kindref="member">PDRIVER_CONTEXT</ref>;</highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//<sp/>全局驱动上下文（仅本文件可见）</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">static</highlight><highlight class="normal"><sp/><ref refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" kindref="member">DRIVER_CONTEXT</ref><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref><sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal"></highlight><highlight class="comment">//<sp/>声明一个快速互斥体，用于同步对共享资源的访问，防止竞态条件。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" refkind="member"><highlight class="normal">FAST_MUTEX<sp/><ref refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" kindref="member">gLock</ref>;</highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="comment">//<sp/>中断上下文优化（driver.c）</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight><highlight class="comment">//<sp/>这个代码片段演示了在驱动程序中直接处理硬件中断的方式，旨在降低中断响应延迟。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">//<sp/>通过绕过WDF框架，直接使用WDK底层API来处理中断，从而最大化性能。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>CODE_SEG(&quot;PAGE&quot;)<sp/></highlight><highlight class="comment">//<sp/>将代码段标记为可分页，以便在不使用时可以交换到磁盘，节省物理内存。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight><highlight class="comment">//<sp/>DirectISR<sp/>函数是中断服务例程（ISR）。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="comment">//<sp/>它在硬件中断发生时由系统调用。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight><highlight class="comment">//<sp/>IN<sp/>PKINTERRUPT<sp/>Interrupt:<sp/>指向表示中断对象的指针，包含了中断的相关信息。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="comment">//<sp/>IN<sp/>PVOID<sp/>Context:<sp/>中断对象的上下文指针，这里可以用于传递硬件寄存器地址或其他需要访问的数据。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" refkind="member"><highlight class="normal"></highlight><highlight class="keywordtype">void</highlight><highlight class="normal"><sp/>NTAPI<sp/><ref refid="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" kindref="member">DirectISR</ref>(<ref refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" kindref="member">IN</ref><sp/>PKINTERRUPT<sp/>Interrupt,<sp/><ref refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" kindref="member">IN</ref><sp/>PVOID<sp/>Context)</highlight></codeline>
<codeline lineno="41"><highlight class="normal">{</highlight></codeline>
<codeline lineno="42"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>KeAcquireFastMutex:<sp/>尝试获取快速互斥体。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>如果互斥体已被持有，当前线程将等待直到互斥体可用。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>这是为了确保在处理中断时对共享硬件寄存器的独占访问。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/>KeAcquireFastMutex(&amp;<ref refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" kindref="member">gLock</ref>);</highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WRITE_REGISTER_ULONG:<sp/>向硬件寄存器写入一个<sp/>ULONG<sp/>值。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>(PULONG)Context:<sp/>将上下文指针转换为<sp/>ULONG<sp/>指针，假定上下文指针指向硬件寄存器地址。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>0x1:<sp/>写入寄存器的值，这里通常用于向硬件发送一个确认信号（ACK），告知硬件中断已被处理。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/>WRITE_REGISTER_ULONG((PULONG)Context,<sp/>0x1);<sp/></highlight><highlight class="comment">//<sp/>硬件ACK</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal"></highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>KeReleaseFastMutex:<sp/>释放之前获取的快速互斥体。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>这允许其他等待该互斥体的线程继续执行。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>在中断处理完成后释放锁，以便其他部分的代码可以访问共享资源。</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/>KeReleaseFastMutex(&amp;<ref refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" kindref="member">gLock</ref>);</highlight></codeline>
<codeline lineno="56"><highlight class="normal">}</highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight></codeline>
<codeline lineno="58"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="59"><highlight class="comment"><sp/>*<sp/>DriverCoreInitialize<sp/>-<sp/>初始化驱动核心</highlight></codeline>
<codeline lineno="60"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="61"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="62" refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" kindref="member">DriverCoreInitialize</ref>(</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>Driver,</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kindref="member">PDRIVER_CONFIG</ref><sp/>DriverConfig</highlight></codeline>
<codeline lineno="65"><highlight class="normal">)</highlight></codeline>
<codeline lineno="66"><highlight class="normal">{</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="68"><highlight class="normal"></highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>driver<sp/>core&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>参数校验</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(Driver<sp/>==<sp/>NULL<sp/>||<sp/>DriverConfig<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="76"><highlight class="normal"></highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化驱动上下文</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Driver<sp/>=<sp/>Driver;</highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig,<sp/>DriverConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1ab3f3478bc4dcb804303c86d537979d13" kindref="member">DRIVER_CONFIG</ref>));</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="81"><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>初始化驱动类型相关资源</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(DriverConfig-&gt;DriverType)<sp/>{</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b" kindref="member">DriverTypeGeneric</ref>:</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>通用驱动无需额外初始化</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="87"><highlight class="normal"></highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" kindref="member">DriverTypeNetwork</ref>:</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>网络驱动初始化</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>network<sp/>driver<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现网络驱动特定初始化</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="93"><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" kindref="member">DriverTypeStorage</ref>:</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>存储驱动初始化</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>storage<sp/>driver<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现存储驱动特定初始化</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Unknown<sp/>driver<sp/>type:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>DriverConfig-&gt;DriverType);</highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="104"><highlight class="normal"></highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Driver<sp/>core<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="107"><highlight class="normal">}</highlight></codeline>
<codeline lineno="108"><highlight class="normal"></highlight></codeline>
<codeline lineno="109"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="110"><highlight class="comment"><sp/>*<sp/>DriverCoreAddDevice<sp/>-<sp/>向驱动核心添加设备</highlight></codeline>
<codeline lineno="111"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="112"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="113" refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" kindref="member">DriverCoreAddDevice</ref>(</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="115"><highlight class="normal">)</highlight></codeline>
<codeline lineno="116"><highlight class="normal">{</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>参数校验</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(Device<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="122"><highlight class="normal"></highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>检查设备数量上限</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount<sp/>&gt;=<sp/><ref refid="driver__core_8h_1adb9000833a6dada8f3fd1e267c320b2b" kindref="member">MAX_DRIVER_DEVICES</ref>)<sp/>{</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(STATUS_INSUFFICIENT_RESOURCES,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Maximum<sp/>device<sp/>limit<sp/>reached&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INSUFFICIENT_RESOURCES;</highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="128"><highlight class="normal"></highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>添加设备到设备列表</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount++]<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="131"><highlight class="normal"></highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>added<sp/>to<sp/>driver<sp/>core,<sp/>total<sp/>devices:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount);</highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="134"><highlight class="normal">}</highlight></codeline>
<codeline lineno="135"><highlight class="normal"></highlight></codeline>
<codeline lineno="136"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="137"><highlight class="comment"><sp/>*<sp/>DriverCoreRemoveDevice<sp/>-<sp/>从驱动核心移除设备</highlight></codeline>
<codeline lineno="138"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="139"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="140" refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" kindref="member">DriverCoreRemoveDevice</ref>(</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="142"><highlight class="normal">)</highlight></codeline>
<codeline lineno="143"><highlight class="normal">{</highlight></codeline>
<codeline lineno="144"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>i;</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>found<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="146"><highlight class="normal"></highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>参数校验</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(Device<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>parameter&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="152"><highlight class="normal"></highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>在设备列表中查找设备</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(i<sp/>=<sp/>0;<sp/>i<sp/>&lt;<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount;<sp/>i++)<sp/>{</highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[i]<sp/>==<sp/>Device)<sp/>{</highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>找到设备，移除它</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>found<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="161"><highlight class="normal"></highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!found)<sp/>{</highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>found<sp/>in<sp/>driver<sp/>core&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_NOT_FOUND;</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="166"><highlight class="normal"></highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>移除设备，将后面的设备前移</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">for</highlight><highlight class="normal"><sp/>(;<sp/>i<sp/>&lt;<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount<sp/>-<sp/>1;<sp/>i++)<sp/>{</highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[i]<sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[i<sp/>+<sp/>1];</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="171"><highlight class="normal"></highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清除最后一个设备并减少数量</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[--<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount]<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="174"><highlight class="normal"></highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>removed<sp/>from<sp/>driver<sp/>core,<sp/>remaining<sp/>devices:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount);</highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="177"><highlight class="normal">}</highlight></codeline>
<codeline lineno="178"><highlight class="normal"></highlight></codeline>
<codeline lineno="179"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="180"><highlight class="comment"><sp/>*<sp/>DriverCoreGetDeviceCount<sp/>-<sp/>获取驱动管理的设备数量</highlight></codeline>
<codeline lineno="181"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="182"><highlight class="normal">ULONG</highlight></codeline>
<codeline lineno="183" refid="driver__core_8c_1a222752d15e46b43b3a4ae0c787d4018d" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1a222752d15e46b43b3a4ae0c787d4018d" kindref="member">DriverCoreGetDeviceCount</ref>(</highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="185"><highlight class="normal">)</highlight></codeline>
<codeline lineno="186"><highlight class="normal">{</highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount;</highlight></codeline>
<codeline lineno="188"><highlight class="normal">}</highlight></codeline>
<codeline lineno="189"><highlight class="normal"></highlight></codeline>
<codeline lineno="190"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="191"><highlight class="comment"><sp/>*<sp/>DriverCoreGetDevice<sp/>-<sp/>根据索引获取设备</highlight></codeline>
<codeline lineno="192"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="193"><highlight class="normal"><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></highlight></codeline>
<codeline lineno="194" refid="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" kindref="member">DriverCoreGetDevice</ref>(</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Index</highlight></codeline>
<codeline lineno="196"><highlight class="normal">)</highlight></codeline>
<codeline lineno="197"><highlight class="normal">{</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(Index<sp/>&gt;=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount)<sp/>{</highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>device<sp/>index&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="200"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>NULL;</highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="202"><highlight class="normal"></highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.Devices[Index];</highlight></codeline>
<codeline lineno="204"><highlight class="normal">}</highlight></codeline>
<codeline lineno="205"><highlight class="normal"></highlight></codeline>
<codeline lineno="206"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="207"><highlight class="comment"><sp/>*<sp/>DriverCoreGetConfiguration<sp/>-<sp/>获取驱动配置</highlight></codeline>
<codeline lineno="208"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="209"><highlight class="normal"><ref refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kindref="member">PDRIVER_CONFIG</ref></highlight></codeline>
<codeline lineno="210" refid="driver__core_8c_1a7eca1538f567ab5a5add60bb6d2d03b5" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1a7eca1538f567ab5a5add60bb6d2d03b5" kindref="member">DriverCoreGetConfiguration</ref>(</highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="212"><highlight class="normal">)</highlight></codeline>
<codeline lineno="213"><highlight class="normal">{</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>&amp;<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig;</highlight></codeline>
<codeline lineno="215"><highlight class="normal">}</highlight></codeline>
<codeline lineno="216"><highlight class="normal"></highlight></codeline>
<codeline lineno="217"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="218"><highlight class="comment"><sp/>*<sp/>DriverCoreCleanup<sp/>-<sp/>清理驱动核心资源</highlight></codeline>
<codeline lineno="219"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="220"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="221" refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" kindref="member">DriverCoreCleanup</ref>(</highlight></codeline>
<codeline lineno="222"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="223"><highlight class="normal">)</highlight></codeline>
<codeline lineno="224"><highlight class="normal">{</highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>driver<sp/>core<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="226"><highlight class="normal"></highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清理特定驱动类型相关资源</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.DriverType)<sp/>{</highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" kindref="member">DriverTypeNetwork</ref>:</highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清理网络驱动特定资源</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>network<sp/>driver<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现网络驱动特定清理</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="234"><highlight class="normal"></highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" kindref="member">DriverTypeStorage</ref>:</highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>清理存储驱动特定资源</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="237"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>storage<sp/>driver<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现存储驱动特定清理</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="240"><highlight class="normal"></highlight></codeline>
<codeline lineno="241"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="244"><highlight class="normal"></highlight></codeline>
<codeline lineno="245"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>重置驱动上下文</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(&amp;<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" kindref="member">DRIVER_CONTEXT</ref>));</highlight></codeline>
<codeline lineno="247"><highlight class="normal"></highlight></codeline>
<codeline lineno="248"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Driver<sp/>core<sp/>resources<sp/>cleaned<sp/>up&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="249"><highlight class="normal">}</highlight></codeline>
<codeline lineno="250"><highlight class="normal"></highlight></codeline>
<codeline lineno="251"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="252"><highlight class="comment"><sp/>*<sp/>DriverCoreProcessIoControl<sp/>-<sp/>处理驱动程序级别的IO控制请求</highlight></codeline>
<codeline lineno="253"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="254"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="255" refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" refkind="member"><highlight class="normal"><ref refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" kindref="member">DriverCoreProcessIoControl</ref>(</highlight></codeline>
<codeline lineno="256"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref><sp/>Request,</highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>IoControlCode,</highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>InputBuffer,</highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>SIZE_T<sp/>InputBufferLength,</highlight></codeline>
<codeline lineno="261"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PVOID<sp/>OutputBuffer,</highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>SIZE_T<sp/>OutputBufferLength,</highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PSIZE_T<sp/>BytesReturned</highlight></codeline>
<codeline lineno="264"><highlight class="normal">)</highlight></codeline>
<codeline lineno="265"><highlight class="normal">{</highlight></codeline>
<codeline lineno="266"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_NOT_SUPPORTED;</highlight></codeline>
<codeline lineno="267"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/>handled<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="268"><highlight class="normal"></highlight></codeline>
<codeline lineno="269"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Device);</highlight></codeline>
<codeline lineno="270"><highlight class="normal"></highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Processing<sp/>driver<sp/>IOCTL:<sp/>0x%08X&quot;</highlight><highlight class="normal">,<sp/>IoControlCode);</highlight></codeline>
<codeline lineno="272"><highlight class="normal"></highlight></codeline>
<codeline lineno="273"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>根据IOCTL代码处理驱动程序级别的请求</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="274"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(IoControlCode)<sp/>{</highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a07c561db6f9baf7d9f5452605546aed9" kindref="member">IOCTL_DRIVER_GET_VERSION</ref>:</highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>{</highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>获取驱动程序版本信息</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="278"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(OutputBufferLength<sp/>&lt;<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1aea65724e2a4566373216b88a7aa3fe62" kindref="member">DRIVER_VERSION</ref>))<sp/>{</highlight></codeline>
<codeline lineno="279"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_BUFFER_TOO_SMALL;</highlight></codeline>
<codeline lineno="280"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="281"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="282"><highlight class="normal"></highlight></codeline>
<codeline lineno="283"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__core_8h_1af034d45237796be01f25611c95f992b4" kindref="member">PDRIVER_VERSION</ref><sp/>versionInfo<sp/>=<sp/>(<ref refid="driver__core_8h_1af034d45237796be01f25611c95f992b4" kindref="member">PDRIVER_VERSION</ref>)OutputBuffer;</highlight></codeline>
<codeline lineno="284"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>versionInfo-&gt;<ref refid="struct__DRIVER__VERSION_1a94c8bf9509d348f5cd15c358a92eb0ce" kindref="member">Major</ref><sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.Version.Major;</highlight></codeline>
<codeline lineno="285"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>versionInfo-&gt;<ref refid="struct__DRIVER__VERSION_1aaa3047faacbaba04237817cbf6c9f0e9" kindref="member">Minor</ref><sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.Version.Minor;</highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>versionInfo-&gt;<ref refid="struct__DRIVER__VERSION_1a0f5edd5b90ea7d0767d56293b15a36f5" kindref="member">Build</ref><sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.Version.Build;</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>versionInfo-&gt;<ref refid="struct__DRIVER__VERSION_1a7869e35e89c7ca48a53d1e555a4123cc" kindref="member">Revision</ref><sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.Version.Revision;</highlight></codeline>
<codeline lineno="288"><highlight class="normal"></highlight></codeline>
<codeline lineno="289"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(BytesReturned<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="290"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>*BytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1aea65724e2a4566373216b88a7aa3fe62" kindref="member">DRIVER_VERSION</ref>);</highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="292"><highlight class="normal"></highlight></codeline>
<codeline lineno="293"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="294"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>handled<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="295"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="296"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="297"><highlight class="normal"></highlight></codeline>
<codeline lineno="298"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a6d4bdae2f23ab96032a14098fb28b1f2" kindref="member">IOCTL_DRIVER_GET_STATISTICS</ref>:</highlight></codeline>
<codeline lineno="299"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>{</highlight></codeline>
<codeline lineno="300"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>获取驱动程序统计信息</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="301"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(OutputBufferLength<sp/>&lt;<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1a0a4763a4c06b82137b4f23d6d2a62dd3" kindref="member">DRIVER_STATISTICS</ref>))<sp/>{</highlight></codeline>
<codeline lineno="302"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>STATUS_BUFFER_TOO_SMALL;</highlight></codeline>
<codeline lineno="303"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="305"><highlight class="normal"></highlight></codeline>
<codeline lineno="306"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__core_8h_1a2c85f1797924669249bdab6c64fc6f07" kindref="member">PDRIVER_STATISTICS</ref><sp/>stats<sp/>=<sp/>(<ref refid="driver__core_8h_1a2c85f1797924669249bdab6c64fc6f07" kindref="member">PDRIVER_STATISTICS</ref>)OutputBuffer;</highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>stats-&gt;<ref refid="struct__DRIVER__STATISTICS_1a752819e9a3d5e0c0807f1ad785cf8b95" kindref="member">DeviceCount</ref><sp/>=<sp/><ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DeviceCount;</highlight></codeline>
<codeline lineno="308"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现获取其他统计信息</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="309"><highlight class="normal"></highlight></codeline>
<codeline lineno="310"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(BytesReturned<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="311"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>*BytesReturned<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__core_8h_1a0a4763a4c06b82137b4f23d6d2a62dd3" kindref="member">DRIVER_STATISTICS</ref>);</highlight></codeline>
<codeline lineno="312"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="313"><highlight class="normal"></highlight></codeline>
<codeline lineno="314"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="315"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>handled<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="316"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="317"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="318"><highlight class="normal"></highlight></codeline>
<codeline lineno="319"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>根据驱动类型处理特定类型的IOCTL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="321"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">switch</highlight><highlight class="normal"><sp/>(<ref refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kindref="member">g_DriverContext</ref>.DriverConfig.DriverType)<sp/>{</highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" kindref="member">DriverTypeNetwork</ref>:</highlight></codeline>
<codeline lineno="323"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>处理网络驱动特定类型的IOCTL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现网络驱动特定类型的IOCTL处理</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="325"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="326"><highlight class="normal"></highlight></codeline>
<codeline lineno="327"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">case</highlight><highlight class="normal"><sp/><ref refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" kindref="member">DriverTypeStorage</ref>:</highlight></codeline>
<codeline lineno="328"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>处理存储驱动特定类型的IOCTL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="329"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>实现存储驱动特定类型的IOCTL处理</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="330"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="331"><highlight class="normal"></highlight></codeline>
<codeline lineno="332"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">default</highlight><highlight class="normal">:</highlight></codeline>
<codeline lineno="333"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="334"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="335"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">break</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="336"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="337"><highlight class="normal"></highlight></codeline>
<codeline lineno="338"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!handled)<sp/>{</highlight></codeline>
<codeline lineno="339"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Unsupported<sp/>IOCTL:<sp/>0x%08X&quot;</highlight><highlight class="normal">,<sp/>IoControlCode);</highlight></codeline>
<codeline lineno="340"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="341"><highlight class="normal"></highlight></codeline>
<codeline lineno="342"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="343"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/core/driver/driver_core.c"/>
  </compounddef>
</doxygen>
