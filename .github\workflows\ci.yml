name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov cppcheck lizard

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Build driver
      run: |
        msbuild.exe KMDF Driver1.sln /p:Configuration=Debug /p:Platform=x64

    - name: Static Analysis
      run: |
        python tools/scripts/enhanced_code_analyzer.py

    - name: Run Tests
      run: |
        python tools/scripts/automated_test_framework.py

    - name: Upload Test Results
      uses: actions/upload-artifact@v2
      with:
        name: test-results
        path: |
          test_report.html
          enhanced_analysis_report.html

    - name: Check Test Results
      run: |
        python -c "import json; exit(0 if json.load(open('test_report.json'))['statistics']['failed_tests'] == 0 else 1)"

  deploy:
    needs: build-and-test
    runs-on: windows-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - uses: actions/checkout@v2

    - name: Download Build Artifacts
      uses: actions/download-artifact@v2
      with:
        name: test-results

    - name: Generate Documentation
      run: |
        pip install sphinx sphinx_rtd_theme
        cd docs
        make html

    - name: Deploy Documentation
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/build/html
