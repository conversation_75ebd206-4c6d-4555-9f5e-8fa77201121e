var searchData=
[
  ['spidevicecleanup_0',['SpiDeviceCleanup',['../spi__device_8c.html#a052b57a96b994325a574bcb9f3db837a',1,'SpiDeviceCleanup(_In_ WDFDEVICE Device):&#160;spi_device.c'],['../spi__device_8h.html#a052b57a96b994325a574bcb9f3db837a',1,'SpiDeviceCleanup(_In_ WDFDEVICE Device):&#160;spi_device.c']]],
  ['spidevicegetstatistics_1',['SpiDeviceGetStatistics',['../spi__device_8c.html#ae2be7c6b48ddf5b08876e1115879469d',1,'SpiDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PSPI_STATISTICS Statistics):&#160;spi_device.c'],['../spi__device_8h.html#ae2be7c6b48ddf5b08876e1115879469d',1,'SpiDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PSPI_STATISTICS Statistics):&#160;spi_device.c']]],
  ['spideviceinitialize_2',['SpiDeviceInitialize',['../spi__device_8c.html#a6939e12311ec72f975bcd03a4250a3e2',1,'SpiDeviceInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig):&#160;spi_device.c'],['../spi__device_8h.html#a6939e12311ec72f975bcd03a4250a3e2',1,'SpiDeviceInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig):&#160;spi_device.c']]],
  ['spideviceread_3',['SpiDeviceRead',['../spi__device_8c.html#a3bc98267d67ee8988179bde952efaa87',1,'SpiDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;spi_device.c'],['../spi__device_8h.html#a3bc98267d67ee8988179bde952efaa87',1,'SpiDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;spi_device.c']]],
  ['spidevicesetclockfrequency_4',['SpiDeviceSetClockFrequency',['../spi__device_8h.html#a30b9d7f482d2a1343e50a60ea8d4135a',1,'spi_device.h']]],
  ['spidevicesetmode_5',['SpiDeviceSetMode',['../spi__device_8h.html#a3c91b33450d309fa46affa22959f8607',1,'spi_device.h']]],
  ['spidevicetransfer_6',['SpiDeviceTransfer',['../spi__device_8c.html#a2428921b9d71ab9d24f34e0a7b23487c',1,'SpiDeviceTransfer(_In_ WDFDEVICE Device, _In_ PSPI_DEVICE_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;spi_device.c'],['../spi__device_8h.html#a2428921b9d71ab9d24f34e0a7b23487c',1,'SpiDeviceTransfer(_In_ WDFDEVICE Device, _In_ PSPI_DEVICE_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;spi_device.c']]],
  ['spidevicewrite_7',['SpiDeviceWrite',['../spi__device_8c.html#ae90ccf3d865bebb54c2c76e10fcbcaa8',1,'SpiDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;spi_device.c'],['../spi__device_8h.html#ae90ccf3d865bebb54c2c76e10fcbcaa8',1,'SpiDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;spi_device.c']]],
  ['spiinitialize_8',['SPIInitialize',['../spi__core_8c.html#a685d8d7731e750c1512b975df16cc030',1,'SPIInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig):&#160;spi_core.c'],['../kmdf__spi_8h.html#a685d8d7731e750c1512b975df16cc030',1,'SPIInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig):&#160;spi_core.c']]],
  ['spireadregister_9',['SPIReadRegister',['../kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885',1,'kmdf_spi.h']]],
  ['spitransferasynchronous_10',['SPITransferAsynchronous',['../kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033',1,'kmdf_spi.h']]],
  ['spitransfersynchronous_11',['SPITransferSynchronous',['../kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c',1,'kmdf_spi.h']]],
  ['spiuninitialize_12',['SPIUninitialize',['../spi__core_8c.html#ad756f8e3b06fdfa545a7048661038513',1,'SPIUninitialize(_In_ WDFDEVICE Device):&#160;spi_core.c'],['../kmdf__spi_8h.html#ad756f8e3b06fdfa545a7048661038513',1,'SPIUninitialize(_In_ WDFDEVICE Device):&#160;spi_core.c']]],
  ['spiwriteread_13',['SPIWriteRead',['../kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f',1,'kmdf_spi.h']]],
  ['spiwriteregister_14',['SPIWriteRegister',['../kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8',1,'kmdf_spi.h']]]
];
