<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/devices/spi_device.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('spi__device_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">spi_device.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="kmdf__spi_8h_source.html">../bus/kmdf_spi.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for spi_device.h:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" title=" " alt="" coords="89,336,154,363"/>
<area shape="poly" title=" " alt="" coords="83,50,66,94,51,155,46,188,46,222,50,255,60,287,73,308,91,325,87,328,69,311,55,289,45,256,40,222,41,188,45,154,61,93,78,48"/>
<area shape="rect" title=" " alt="" coords="70,253,123,280"/>
<area shape="poly" title=" " alt="" coords="93,49,98,238,93,238,87,49"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="132,96,255,123"/>
<area shape="poly" title=" " alt="" coords="118,46,167,84,164,88,115,51"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="127,171,273,197"/>
<area shape="poly" title=" " alt="" coords="197,123,200,155,195,155,192,123"/>
<area shape="poly" title=" " alt="" coords="186,200,126,246,122,242,182,196"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="147,245,277,288"/>
<area shape="poly" title=" " alt="" coords="205,197,210,230,204,231,199,198"/>
<area shape="poly" title=" " alt="" coords="191,291,149,328,145,324,187,287"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8hdep" id="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2spi__device_8hdep">
<area shape="rect" title=" " alt="" coords="495,5,664,48"/>
<area shape="rect" href="spi__device_8c.html" title=" " alt="" coords="404,96,573,139"/>
<area shape="poly" title=" " alt="" coords="549,61,511,98,508,94,545,57"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="597,96,743,139"/>
<area shape="poly" title=" " alt="" coords="613,57,651,94,647,98,609,61"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="5,187,151,245"/>
<area shape="poly" title=" " alt="" coords="583,142,463,156,370,162,278,169,163,189,152,192,150,187,161,184,277,164,369,157,463,151,582,136"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,187,320,245"/>
<area shape="poly" title=" " alt="" coords="583,142,455,164,400,173,332,189,321,192,320,187,331,184,399,168,454,159,582,136"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,195,489,237"/>
<area shape="poly" title=" " alt="" coords="603,147,472,197,470,192,601,142"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="513,195,659,237"/>
<area shape="poly" title=" " alt="" coords="644,152,606,196,602,193,640,149"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="683,195,828,237"/>
<area shape="poly" title=" " alt="" coords="700,148,739,193,735,196,696,152"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="852,195,997,237"/>
<area shape="poly" title=" " alt="" coords="740,142,871,192,869,197,738,147"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1021,195,1167,237"/>
<area shape="poly" title=" " alt="" coords="758,130,878,153,1011,184,1036,191,1034,197,1009,189,877,159,757,136"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="1191,195,1336,237"/>
<area shape="poly" title=" " alt="" coords="759,123,953,144,1067,162,1180,184,1207,192,1206,197,1179,189,1066,167,952,149,758,128"/>
</map>
</div>
</div>
<p><a href="spi__device_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FSPI_5FDEVICE_5FTRANSFER_5FPACKET_5Fstruct_5F_5FSPI_5F_5FDEVICE_5F_5FTRANSFER_5F_5FPACKET" id="r__5FSPI_5FDEVICE_5FTRANSFER_5FPACKET_5Fstruct_5F_5FSPI_5F_5FDEVICE_5F_5FTRANSFER_5F_5FPACKET"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:_5FSPI_5FSTATISTICS_5Fstruct_5F_5FSPI_5F_5FSTATISTICS" id="r__5FSPI_5FSTATISTICS_5Fstruct_5F_5FSPI_5F_5FSTATISTICS"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__SPI__STATISTICS">_SPI_STATISTICS</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aad9cdee9a56a867985b5110add53ed94" id="r_aad9cdee9a56a867985b5110add53ed94"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a>&#160;&#160;&#160;0x8200</td></tr>
<tr class="memitem:a97fe5a41276df38e46922527c4b9baf9" id="r_a97fe5a41276df38e46922527c4b9baf9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a97fe5a41276df38e46922527c4b9baf9">IOCTL_SPI_GET_STATISTICS</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a8442695a715b85f6516ff535bc5d1409" id="r_a8442695a715b85f6516ff535bc5d1409"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8442695a715b85f6516ff535bc5d1409">IOCTL_SPI_RESET</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:aaba9d20f35713a3c0dd088bfdb433a0b" id="r_aaba9d20f35713a3c0dd088bfdb433a0b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaba9d20f35713a3c0dd088bfdb433a0b">IOCTL_SPI_SET_BUS_SPEED</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a471b24a3583fd2212e30e4619ae701be" id="r_a471b24a3583fd2212e30e4619ae701be"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a471b24a3583fd2212e30e4619ae701be">IOCTL_SPI_SET_MODE</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 5, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a72feec97101aca3be161a59ffe40cc2c" id="r_a72feec97101aca3be161a59ffe40cc2c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a72feec97101aca3be161a59ffe40cc2c">IOCTL_SPI_TRANSFER</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a21382b2df65b9cb8a11da17114ab9491" id="r_a21382b2df65b9cb8a11da17114ab9491"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21382b2df65b9cb8a11da17114ab9491">IOCTL_SPI_TRANSFER_FULL_DUPLEX</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:aa7a1a825b415e6aa12c47463eefc0bb7" id="r_aa7a1a825b415e6aa12c47463eefc0bb7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa7a1a825b415e6aa12c47463eefc0bb7">SPI_TRANSFER_FULL_DUPLEX</a>&#160;&#160;&#160;0x00000004</td></tr>
<tr class="memitem:a8595e49b5f8ddb021462587455bd2ff5" id="r_a8595e49b5f8ddb021462587455bd2ff5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8595e49b5f8ddb021462587455bd2ff5">SPI_TRANSFER_NO_CHIPSEL</a>&#160;&#160;&#160;0x00000008</td></tr>
<tr class="memitem:a935c3756801c209968fb16a7be795396" id="r_a935c3756801c209968fb16a7be795396"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a935c3756801c209968fb16a7be795396">SPI_TRANSFER_READ</a>&#160;&#160;&#160;0x00000001</td></tr>
<tr class="memitem:af27a6537c3222c6796876ff953298b42" id="r_af27a6537c3222c6796876ff953298b42"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af27a6537c3222c6796876ff953298b42">SPI_TRANSFER_WRITE</a>&#160;&#160;&#160;0x00000002</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a8d5f40e6c769c7d8420d120a84cd711a" id="r_a8d5f40e6c769c7d8420d120a84cd711a"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:ad0637463ce63cba4d22faa4adab1949d" id="r_ad0637463ce63cba4d22faa4adab1949d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__STATISTICS">_SPI_STATISTICS</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a></td></tr>
<tr class="memitem:a22213828588d3110e34d053c7f191fc0" id="r_a22213828588d3110e34d053c7f191fc0"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a22213828588d3110e34d053c7f191fc0">SPI_DEVICE_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:aa83effe237db8be37288dceaeeab8d57" id="r_aa83effe237db8be37288dceaeeab8d57"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__STATISTICS">_SPI_STATISTICS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa83effe237db8be37288dceaeeab8d57">SPI_STATISTICS</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a052b57a96b994325a574bcb9f3db837a" id="r_a052b57a96b994325a574bcb9f3db837a"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a052b57a96b994325a574bcb9f3db837a">SpiDeviceCleanup</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:ae2be7c6b48ddf5b08876e1115879469d" id="r_ae2be7c6b48ddf5b08876e1115879469d"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae2be7c6b48ddf5b08876e1115879469d">SpiDeviceGetStatistics</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_ <a class="el" href="#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a> Statistics)</td></tr>
<tr class="memitem:a6939e12311ec72f975bcd03a4250a3e2" id="r_a6939e12311ec72f975bcd03a4250a3e2"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6939e12311ec72f975bcd03a4250a3e2">SpiDeviceInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="el" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>)</td></tr>
<tr class="memitem:a3bc98267d67ee8988179bde952efaa87" id="r_a3bc98267d67ee8988179bde952efaa87"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3bc98267d67ee8988179bde952efaa87">SpiDeviceRead</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</td></tr>
<tr class="memitem:a30b9d7f482d2a1343e50a60ea8d4135a" id="r_a30b9d7f482d2a1343e50a60ea8d4135a"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a30b9d7f482d2a1343e50a60ea8d4135a">SpiDeviceSetClockFrequency</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG ClockFrequency)</td></tr>
<tr class="memitem:a3c91b33450d309fa46affa22959f8607" id="r_a3c91b33450d309fa46affa22959f8607"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3c91b33450d309fa46affa22959f8607">SpiDeviceSetMode</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a> Mode)</td></tr>
<tr class="memitem:a2428921b9d71ab9d24f34e0a7b23487c" id="r_a2428921b9d71ab9d24f34e0a7b23487c"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2428921b9d71ab9d24f34e0a7b23487c">SpiDeviceTransfer</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:ae90ccf3d865bebb54c2c76e10fcbcaa8" id="r_ae90ccf3d865bebb54c2c76e10fcbcaa8"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae90ccf3d865bebb54c2c76e10fcbcaa8">SpiDeviceWrite</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__SPI__DEVICE__TRANSFER__PACKET" id="struct__SPI__DEVICE__TRANSFER__PACKET"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__SPI__DEVICE__TRANSFER__PACKET">&#9670;&#160;</a></span>_SPI_DEVICE_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _SPI_DEVICE_TRANSFER_PACKET</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a744606c6d58de9e0a6e0fe4e4acfbec6" name="a744606c6d58de9e0a6e0fe4e4acfbec6"></a>ULONG</td>
<td class="fieldname">
DelayInMicroseconds</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a5ce41e65ae89fce3ef5107acc4fb11d2" name="a5ce41e65ae89fce3ef5107acc4fb11d2"></a>ULONG</td>
<td class="fieldname">
Flags</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a182672e24da62e3c9fcbc4baf9b285a1" name="a182672e24da62e3c9fcbc4baf9b285a1"></a>PVOID</td>
<td class="fieldname">
ReadBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab93e815b31b5919f672691f7f01b06af" name="ab93e815b31b5919f672691f7f01b06af"></a>ULONG</td>
<td class="fieldname">
ReadLength</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ace00b8a3ea9807f1b300e84fd6a8d7be" name="ace00b8a3ea9807f1b300e84fd6a8d7be"></a>PVOID</td>
<td class="fieldname">
WriteBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a8575b3351fe18405d3eb99caf11e503b" name="a8575b3351fe18405d3eb99caf11e503b"></a>ULONG</td>
<td class="fieldname">
WriteLength</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="struct__SPI__STATISTICS" id="struct__SPI__STATISTICS"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__SPI__STATISTICS">&#9670;&#160;</a></span>_SPI_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _SPI_STATISTICS</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="aea910d0fe28ff0978ec2ecfec60b8c52" name="aea910d0fe28ff0978ec2ecfec60b8c52"></a>ULONG</td>
<td class="fieldname">
ClockFrequency</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a254521d63fa6f195accebd432ad9a2c9" name="a254521d63fa6f195accebd432ad9a2c9"></a>BOOLEAN</td>
<td class="fieldname">
DeviceInitialized</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="af7be253804f923ed13df884c7c93cdeb" name="af7be253804f923ed13df884c7c93cdeb"></a>ULONG</td>
<td class="fieldname">
ErrorCount</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aaddc6c47a910b922a46c02072a72b0ef" name="aaddc6c47a910b922a46c02072a72b0ef"></a><a class="el" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></td>
<td class="fieldname">
Mode</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aed0da9f11638910222e8bcf387a1d38f" name="aed0da9f11638910222e8bcf387a1d38f"></a>ULONG</td>
<td class="fieldname">
TransactionCount</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="aad9cdee9a56a867985b5110add53ed94" name="aad9cdee9a56a867985b5110add53ed94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad9cdee9a56a867985b5110add53ed94">&#9670;&#160;</a></span>IOCTL_SPI_BASE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_BASE&#160;&#160;&#160;0x8200</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a97fe5a41276df38e46922527c4b9baf9" name="a97fe5a41276df38e46922527c4b9baf9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97fe5a41276df38e46922527c4b9baf9">&#9670;&#160;</a></span>IOCTL_SPI_GET_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_GET_STATISTICS&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8442695a715b85f6516ff535bc5d1409" name="a8442695a715b85f6516ff535bc5d1409"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8442695a715b85f6516ff535bc5d1409">&#9670;&#160;</a></span>IOCTL_SPI_RESET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_RESET&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaba9d20f35713a3c0dd088bfdb433a0b" name="aaba9d20f35713a3c0dd088bfdb433a0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaba9d20f35713a3c0dd088bfdb433a0b">&#9670;&#160;</a></span>IOCTL_SPI_SET_BUS_SPEED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_SET_BUS_SPEED&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a471b24a3583fd2212e30e4619ae701be" name="a471b24a3583fd2212e30e4619ae701be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a471b24a3583fd2212e30e4619ae701be">&#9670;&#160;</a></span>IOCTL_SPI_SET_MODE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_SET_MODE&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 5, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a72feec97101aca3be161a59ffe40cc2c" name="a72feec97101aca3be161a59ffe40cc2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72feec97101aca3be161a59ffe40cc2c">&#9670;&#160;</a></span>IOCTL_SPI_TRANSFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_TRANSFER&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a21382b2df65b9cb8a11da17114ab9491" name="a21382b2df65b9cb8a11da17114ab9491"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21382b2df65b9cb8a11da17114ab9491">&#9670;&#160;</a></span>IOCTL_SPI_TRANSFER_FULL_DUPLEX</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_SPI_TRANSFER_FULL_DUPLEX&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, <a class="el" href="#aad9cdee9a56a867985b5110add53ed94">IOCTL_SPI_BASE</a> + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa7a1a825b415e6aa12c47463eefc0bb7" name="aa7a1a825b415e6aa12c47463eefc0bb7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7a1a825b415e6aa12c47463eefc0bb7">&#9670;&#160;</a></span>SPI_TRANSFER_FULL_DUPLEX</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SPI_TRANSFER_FULL_DUPLEX&#160;&#160;&#160;0x00000004</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8595e49b5f8ddb021462587455bd2ff5" name="a8595e49b5f8ddb021462587455bd2ff5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8595e49b5f8ddb021462587455bd2ff5">&#9670;&#160;</a></span>SPI_TRANSFER_NO_CHIPSEL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SPI_TRANSFER_NO_CHIPSEL&#160;&#160;&#160;0x00000008</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a935c3756801c209968fb16a7be795396" name="a935c3756801c209968fb16a7be795396"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a935c3756801c209968fb16a7be795396">&#9670;&#160;</a></span>SPI_TRANSFER_READ</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SPI_TRANSFER_READ&#160;&#160;&#160;0x00000001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af27a6537c3222c6796876ff953298b42" name="af27a6537c3222c6796876ff953298b42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27a6537c3222c6796876ff953298b42">&#9670;&#160;</a></span>SPI_TRANSFER_WRITE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SPI_TRANSFER_WRITE&#160;&#160;&#160;0x00000002</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a8d5f40e6c769c7d8420d120a84cd711a" name="a8d5f40e6c769c7d8420d120a84cd711a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d5f40e6c769c7d8420d120a84cd711a">&#9670;&#160;</a></span>PSPI_DEVICE_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a> * <a class="el" href="#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad0637463ce63cba4d22faa4adab1949d" name="ad0637463ce63cba4d22faa4adab1949d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad0637463ce63cba4d22faa4adab1949d">&#9670;&#160;</a></span>PSPI_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__STATISTICS">_SPI_STATISTICS</a> * <a class="el" href="#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a22213828588d3110e34d053c7f191fc0" name="a22213828588d3110e34d053c7f191fc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22213828588d3110e34d053c7f191fc0">&#9670;&#160;</a></span>SPI_DEVICE_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a> <a class="el" href="#a22213828588d3110e34d053c7f191fc0">SPI_DEVICE_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa83effe237db8be37288dceaeeab8d57" name="aa83effe237db8be37288dceaeeab8d57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa83effe237db8be37288dceaeeab8d57">&#9670;&#160;</a></span>SPI_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__STATISTICS">_SPI_STATISTICS</a> <a class="el" href="#aa83effe237db8be37288dceaeeab8d57">SPI_STATISTICS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a052b57a96b994325a574bcb9f3db837a" name="a052b57a96b994325a574bcb9f3db837a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a052b57a96b994325a574bcb9f3db837a">&#9670;&#160;</a></span>SpiDeviceCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID SpiDeviceCleanup </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceCleanup - u6e05u7406SPIu8bbeu5907u8d44u6e90</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_a052b57a96b994325a574bcb9f3db837a_cgraph.png" border="0" usemap="#aspi__device_8h_a052b57a96b994325a574bcb9f3db837a_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_a052b57a96b994325a574bcb9f3db837a_cgraph" id="aspi__device_8h_a052b57a96b994325a574bcb9f3db837a_cgraph">
<area shape="rect" title=" " alt="" coords="5,80,135,107"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="188,29,294,56"/>
<area shape="poly" title=" " alt="" coords="117,77,178,58,180,63,119,82"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="183,80,299,107"/>
<area shape="poly" title=" " alt="" coords="135,91,167,91,167,96,135,96"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="206,131,276,157"/>
<area shape="poly" title=" " alt="" coords="119,105,192,127,190,132,117,110"/>
<area shape="poly" title=" " alt="" coords="211,30,206,21,210,11,222,5,241,3,261,5,272,12,270,16,260,10,241,8,223,10,214,15,212,20,216,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="347,105,475,132"/>
<area shape="poly" title=" " alt="" coords="276,136,331,128,332,133,277,141"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="364,156,459,183"/>
<area shape="poly" title=" " alt="" coords="277,147,349,157,348,163,276,152"/>
</map>
</div>

</div>
</div>
<a id="ae2be7c6b48ddf5b08876e1115879469d" name="ae2be7c6b48ddf5b08876e1115879469d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2be7c6b48ddf5b08876e1115879469d">&#9670;&#160;</a></span>SpiDeviceGetStatistics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceGetStatistics </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ <a class="el" href="#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a></td>          <td class="paramname"><span class="paramname"><em>Statistics</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceGetStatistics - u83b7u53d6SPIu8bbeu5907u7edfu8ba1u4fe1u606f</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Statistics</td><td>u7edfu8ba1u4fe1u606fu7ed3u6784</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_ae2be7c6b48ddf5b08876e1115879469d_cgraph.png" border="0" usemap="#aspi__device_8h_ae2be7c6b48ddf5b08876e1115879469d_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_ae2be7c6b48ddf5b08876e1115879469d_cgraph" id="aspi__device_8h_ae2be7c6b48ddf5b08876e1115879469d_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,163,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="211,29,316,56"/>
<area shape="poly" title=" " alt="" coords="162,54,195,50,196,55,163,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="228,80,298,107"/>
<area shape="poly" title=" " alt="" coords="163,76,213,84,212,89,162,82"/>
<area shape="poly" title=" " alt="" coords="235,30,230,21,233,11,245,5,263,3,283,5,294,12,291,16,281,10,263,8,246,10,237,15,235,20,239,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="364,55,492,81"/>
<area shape="poly" title=" " alt="" coords="299,85,348,78,349,83,299,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="380,105,476,132"/>
<area shape="poly" title=" " alt="" coords="299,96,366,106,365,112,299,101"/>
</map>
</div>

</div>
</div>
<a id="a6939e12311ec72f975bcd03a4250a3e2" name="a6939e12311ec72f975bcd03a4250a3e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6939e12311ec72f975bcd03a4250a3e2">&#9670;&#160;</a></span>SpiDeviceInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>SpiConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceInitialize - u521du59cbu5316SPIu8bbeu5907</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">SpiConfig</td><td>SPIu914du7f6e</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_a6939e12311ec72f975bcd03a4250a3e2_cgraph.png" border="0" usemap="#aspi__device_8h_a6939e12311ec72f975bcd03a4250a3e2_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_a6939e12311ec72f975bcd03a4250a3e2_cgraph" id="aspi__device_8h_a6939e12311ec72f975bcd03a4250a3e2_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,135,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="183,29,288,56"/>
<area shape="poly" title=" " alt="" coords="135,55,167,50,168,56,136,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="200,80,270,107"/>
<area shape="poly" title=" " alt="" coords="136,75,185,83,185,88,135,81"/>
<area shape="poly" title=" " alt="" coords="207,30,202,21,205,11,217,5,235,3,255,5,266,12,263,16,253,10,235,8,218,10,209,15,207,20,211,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="336,55,464,81"/>
<area shape="poly" title=" " alt="" coords="271,85,320,78,321,83,271,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,448,132"/>
<area shape="poly" title=" " alt="" coords="271,96,338,106,337,112,271,101"/>
</map>
</div>

</div>
</div>
<a id="a3bc98267d67ee8988179bde952efaa87" name="a3bc98267d67ee8988179bde952efaa87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3bc98267d67ee8988179bde952efaa87">&#9670;&#160;</a></span>SpiDeviceRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceRead </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesRead</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceRead - u4eceSu5907u8bfbu53d6u6570u636e</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>u5bc4u5b58u5668u5730u5740 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Buffer</td><td>u6570u636eu7f13u51b2u533a </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>u7f13u51b2u533au957fu5ea6 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesRead</td><td>u5b9eu9645u8bfbu53d6u7684u5b57u8282u6570</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_a3bc98267d67ee8988179bde952efaa87_cgraph.png" border="0" usemap="#aspi__device_8h_a3bc98267d67ee8988179bde952efaa87_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_a3bc98267d67ee8988179bde952efaa87_cgraph" id="aspi__device_8h_a3bc98267d67ee8988179bde952efaa87_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,118,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="166,29,271,56"/>
<area shape="poly" title=" " alt="" coords="118,56,150,51,151,56,118,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="183,80,254,107"/>
<area shape="poly" title=" " alt="" coords="118,74,168,83,167,88,118,80"/>
<area shape="poly" title=" " alt="" coords="191,30,186,21,190,11,201,5,218,3,237,5,248,12,245,16,236,10,218,8,202,10,194,15,192,20,196,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="319,55,447,81"/>
<area shape="poly" title=" " alt="" coords="254,85,303,78,304,83,254,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="336,105,431,132"/>
<area shape="poly" title=" " alt="" coords="254,96,321,106,320,112,254,101"/>
</map>
</div>

</div>
</div>
<a id="a30b9d7f482d2a1343e50a60ea8d4135a" name="a30b9d7f482d2a1343e50a60ea8d4135a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a30b9d7f482d2a1343e50a60ea8d4135a">&#9670;&#160;</a></span>SpiDeviceSetClockFrequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceSetClockFrequency </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>ClockFrequency</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceSetClockFrequency - u8bbeu7f6eSPIu8bbeu5907u65f6u949fu9891u7387</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ClockFrequency</td><td>u65f6u949fu9891u7387(Hz)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>

</div>
</div>
<a id="a3c91b33450d309fa46affa22959f8607" name="a3c91b33450d309fa46affa22959f8607"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c91b33450d309fa46affa22959f8607">&#9670;&#160;</a></span>SpiDeviceSetMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceSetMode </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></td>          <td class="paramname"><span class="paramname"><em>Mode</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceSetMode - u8bbeu7f6eSPIu8bbeu5907u5de5u4f5cu6a21u5f0f</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Mode</td><td>SPIu6a21u5f0f</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>

</div>
</div>
<a id="a2428921b9d71ab9d24f34e0a7b23487c" name="a2428921b9d71ab9d24f34e0a7b23487c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2428921b9d71ab9d24f34e0a7b23487c">&#9670;&#160;</a></span>SpiDeviceTransfer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceTransfer </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceTransfer - u6267u884cSPIu4f20u8f93</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TransferPacket</td><td>u4f20u8f93u6570u636eu5305 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TimeoutMs</td><td>u8d85u65f6u65f6u95f4(u6bebu79d2)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_a2428921b9d71ab9d24f34e0a7b23487c_cgraph.png" border="0" usemap="#aspi__device_8h_a2428921b9d71ab9d24f34e0a7b23487c_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_a2428921b9d71ab9d24f34e0a7b23487c_cgraph" id="aspi__device_8h_a2428921b9d71ab9d24f34e0a7b23487c_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,135,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="183,29,288,56"/>
<area shape="poly" title=" " alt="" coords="135,55,167,50,168,56,136,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="200,80,270,107"/>
<area shape="poly" title=" " alt="" coords="136,75,185,83,185,88,135,81"/>
<area shape="poly" title=" " alt="" coords="207,30,202,21,205,11,217,5,235,3,255,5,266,12,263,16,253,10,235,8,218,10,209,15,207,20,211,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="336,55,464,81"/>
<area shape="poly" title=" " alt="" coords="271,85,320,78,321,83,271,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,448,132"/>
<area shape="poly" title=" " alt="" coords="271,96,338,106,337,112,271,101"/>
</map>
</div>

</div>
</div>
<a id="ae90ccf3d865bebb54c2c76e10fcbcaa8" name="ae90ccf3d865bebb54c2c76e10fcbcaa8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae90ccf3d865bebb54c2c76e10fcbcaa8">&#9670;&#160;</a></span>SpiDeviceWrite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceWrite </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesWritten</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceWrite - u5411SPIu8bbeu5907u5199u5165u6570u636e</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>u5bc4u5b58u5668u5730u5740 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Buffer</td><td>u6570u636eu7f13u51b2u533a </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>u7f13u51b2u533au957fu5ea6 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesWritten</td><td>u5b9eu9645u5199u5165u7684u5b57u8282u6570</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8h_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph.png" border="0" usemap="#aspi__device_8h_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8h_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph" id="aspi__device_8h_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,117,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="165,29,270,56"/>
<area shape="poly" title=" " alt="" coords="117,56,149,51,150,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="182,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,167,83,166,88,117,80"/>
<area shape="poly" title=" " alt="" coords="190,30,185,21,189,11,200,5,217,3,236,5,246,12,243,16,234,10,217,8,201,10,193,15,191,20,195,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="318,55,446,81"/>
<area shape="poly" title=" " alt="" coords="253,85,302,78,303,83,253,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,430,132"/>
<area shape="poly" title=" " alt="" coords="253,96,320,106,319,112,253,101"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_f51f2e86ea53a1a257ee2ea690474c95.html">devices</a></li><li class="navelem"><a href="spi__device_8h.html">spi_device.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
