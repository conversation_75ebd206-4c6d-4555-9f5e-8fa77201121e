<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/core/common/Common.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('Common_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Common.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for Common.h:</div>
<div class="dyncontent">
<div class="center"><img src="Common_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2core_2common_2Common_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2core_2common_2Common_8h" id="aC_1_2KMDF_01Driver1_2include_2core_2common_2Common_8h">
<area shape="rect" title=" " alt="" coords="5,5,178,48"/>
<area shape="rect" title=" " alt="" coords="18,96,82,123"/>
<area shape="poly" title=" " alt="" coords="83,50,66,83,61,81,78,47"/>
<area shape="rect" title=" " alt="" coords="106,96,160,123"/>
<area shape="poly" title=" " alt="" coords="105,47,122,81,117,83,100,50"/>
</map>
</div>
</div>
<p><a href="Common_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ab47772eb758c3b2a1a990360eaf8ad5c" id="r_ab47772eb758c3b2a1a990360eaf8ad5c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab47772eb758c3b2a1a990360eaf8ad5c">HANDLE_ERROR</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>,  message)</td></tr>
<tr class="memitem:a9ee9b45a31a1e12103d9ea23cc983d0c" id="r_a9ee9b45a31a1e12103d9ea23cc983d0c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9ee9b45a31a1e12103d9ea23cc983d0c">KMDF_POOL_TAG</a>&#160;&#160;&#160;'FMDK'</td></tr>
<tr class="memitem:acfe39a25e08737b535dc881071ebf149" id="r_acfe39a25e08737b535dc881071ebf149"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acfe39a25e08737b535dc881071ebf149">LOG_DEBUG</a>(Format, ...)</td></tr>
<tr class="memitem:ab90a3a515b5c728d627bf24ac17c3702" id="r_ab90a3a515b5c728d627bf24ac17c3702"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab90a3a515b5c728d627bf24ac17c3702">SAFE_FREE</a>(Ptr)</td></tr>
<tr class="memitem:a6038f7bdb274c2e159988a57dedbf93d" id="r_a6038f7bdb274c2e159988a57dedbf93d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6038f7bdb274c2e159988a57dedbf93d">SAFE_RELEASE</a>(p)</td></tr>
</table>
<a name="details" id="details"></a><h2 id="header-details" class="groupheader">Detailed Description</h2>
<div class="textblock"><p>该文件定义了...</p>
<dl class="section author"><dt>Author</dt><dd>KMDF团队 </dd></dl>
<dl class="section date"><dt>Date</dt><dd>2025-05-03 </dd></dl>
</div><a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="ab47772eb758c3b2a1a990360eaf8ad5c" name="ab47772eb758c3b2a1a990360eaf8ad5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab47772eb758c3b2a1a990360eaf8ad5c">&#9670;&#160;</a></span>HANDLE_ERROR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define HANDLE_ERROR</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em><a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>message</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">if</span> (!<a class="code hl_define" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="code hl_variable" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>)) { \</div>
<div class="line">        KmdfHandleError(<a class="code hl_variable" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>, message, __LINE__, __FILE__); \</div>
<div class="line">        <span class="keywordflow">return</span> <a class="code hl_variable" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>; \</div>
<div class="line">    }</div>
<div class="ttc" id="agpio__core_8c_html_a9611b3a00430a86619b5923de30f9fdb"><div class="ttname"><a href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a></div><div class="ttdeci">status</div><div class="ttdef"><b>Definition</b> gpio_core.c:127</div></div>
<div class="ttc" id="aprecomp_8h_html_ad14231612b7a675d33f0ead0b695d21a"><div class="ttname"><a href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a></div><div class="ttdeci">#define NT_SUCCESS(Status)</div><div class="ttdef"><b>Definition</b> precomp.h:57</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a9ee9b45a31a1e12103d9ea23cc983d0c" name="a9ee9b45a31a1e12103d9ea23cc983d0c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ee9b45a31a1e12103d9ea23cc983d0c">&#9670;&#160;</a></span>KMDF_POOL_TAG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define KMDF_POOL_TAG&#160;&#160;&#160;'FMDK'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="acfe39a25e08737b535dc881071ebf149" name="acfe39a25e08737b535dc881071ebf149"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acfe39a25e08737b535dc881071ebf149">&#9670;&#160;</a></span>LOG_DEBUG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LOG_DEBUG</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Format</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, <span class="stringliteral">&quot;[KMDF] &quot;</span> Format <span class="stringliteral">&quot;\n&quot;</span>, __VA_ARGS__)</div>
</div><!-- fragment -->
</div>
</div>
<a id="ab90a3a515b5c728d627bf24ac17c3702" name="ab90a3a515b5c728d627bf24ac17c3702"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab90a3a515b5c728d627bf24ac17c3702">&#9670;&#160;</a></span>SAFE_FREE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SAFE_FREE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>Ptr</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">if</span> (Ptr) { \</div>
<div class="line">        ExFreePoolWithTag((Ptr), <a class="code hl_define" href="#a9ee9b45a31a1e12103d9ea23cc983d0c">KMDF_POOL_TAG</a>); \</div>
<div class="line">        (Ptr) = NULL; \</div>
<div class="line">    }</div>
<div class="ttc" id="aCommon_8h_html_a9ee9b45a31a1e12103d9ea23cc983d0c"><div class="ttname"><a href="#a9ee9b45a31a1e12103d9ea23cc983d0c">KMDF_POOL_TAG</a></div><div class="ttdeci">#define KMDF_POOL_TAG</div><div class="ttdef"><b>Definition</b> Common.h:18</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a6038f7bdb274c2e159988a57dedbf93d" name="a6038f7bdb274c2e159988a57dedbf93d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6038f7bdb274c2e159988a57dedbf93d">&#9670;&#160;</a></span>SAFE_RELEASE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define SAFE_RELEASE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>p</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">if</span> (p) { \</div>
<div class="line">        (p)-&gt;Release(); \</div>
<div class="line">        (p) = NULL; \</div>
<div class="line">    }</div>
</div><!-- fragment -->
</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_3d69f64eaf81436fe2b22361382717e5.html">core</a></li><li class="navelem"><a href="dir_fedb270071c245cf6cf50dce60f14a28.html">common</a></li><li class="navelem"><a href="Common_8h.html">Common.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
