<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/devices/gpio_device.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('gpio__device_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">gpio_device.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="gpio__device_8h_source.html">../../../include/hal/devices/gpio_device.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for gpio_device.c:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2devices_2gpio__device_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2gpio__device_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2gpio__device_8c">
<area shape="rect" title=" " alt="" coords="133,5,309,48"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="144,96,298,139"/>
<area shape="poly" title=" " alt="" coords="224,48,224,81,218,81,218,48"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="285,352,416,395"/>
<area shape="poly" title=" " alt="" coords="260,46,297,70,338,102,376,140,407,185,422,217,430,244,430,272,419,305,408,326,392,343,388,340,403,322,415,303,424,271,425,245,417,219,403,188,372,144,334,106,294,74,257,51"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="5,261,133,304"/>
<area shape="poly" title=" " alt="" coords="186,51,158,71,134,98,112,135,96,174,77,247,72,246,91,173,107,132,130,94,155,67,183,46"/>
<area shape="rect" title=" " alt="" coords="138,443,202,469"/>
<area shape="poly" title=" " alt="" coords="221,140,177,427,172,427,215,139"/>
<area shape="rect" title=" " alt="" coords="208,360,261,387"/>
<area shape="poly" title=" " alt="" coords="225,139,236,344,230,345,219,139"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="263,187,392,213"/>
<area shape="poly" title=" " alt="" coords="250,137,300,175,297,179,247,141"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="258,269,405,296"/>
<area shape="poly" title=" " alt="" coords="331,214,333,254,328,254,326,214"/>
<area shape="poly" title=" " alt="" coords="320,299,261,351,258,347,316,295"/>
<area shape="poly" title=" " alt="" coords="337,296,346,336,340,338,332,297"/>
<area shape="poly" title=" " alt="" coords="305,398,214,438,211,434,303,393"/>
<area shape="poly" title=" " alt="" coords="84,303,158,428,153,431,79,306"/>
<area shape="poly" title=" " alt="" coords="109,302,199,350,196,355,107,307"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a6acc285bc10925e95287d1821e20f2f7" id="r_a6acc285bc10925e95287d1821e20f2f7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6acc285bc10925e95287d1821e20f2f7">GPIO_DEVICE_POOL_TAG</a>&#160;&#160;&#160;'DOPG'</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a7b6a29716fe6f8117de54edaedc57974" id="r_a7b6a29716fe6f8117de54edaedc57974"><td class="memItemLeft" align="right" valign="top"><a class="el" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> (<a class="el" href="gpio__device_8h.html#a062e6f1e503256e897b3f6642fdce349">GPIO_DEVICE_CONFIG</a>)</td></tr>
<tr class="memitem:a3df79e59d8cd67d1b0ea2bdd7b7e2662" id="r_a3df79e59d8cd67d1b0ea2bdd7b7e2662"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3df79e59d8cd67d1b0ea2bdd7b7e2662">EvtDebounceTimerFunc</a> (_In_ WDFTIMER Timer)</td></tr>
<tr class="memitem:ac3d3347067d7be6497e71b6ba4b389c2" id="r_ac3d3347067d7be6497e71b6ba4b389c2"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac3d3347067d7be6497e71b6ba4b389c2">GpioDeviceCleanup</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a0bd26e3410adfbb26bba326602f8fec6" id="r_a0bd26e3410adfbb26bba326602f8fec6"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0bd26e3410adfbb26bba326602f8fec6">GpioDeviceGetState</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_ PGPIO_DEVICE_STATE State)</td></tr>
<tr class="memitem:a21428e126bf8c8996ea3405e6a8be2f2" id="r_a21428e126bf8c8996ea3405e6a8be2f2"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21428e126bf8c8996ea3405e6a8be2f2">GpioDeviceInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="gpio__device_8h.html#a3225b6d40b644661d13ce4d49580cc08">PGPIO_DEVICE_CONFIG</a> GpioConfig)</td></tr>
<tr class="memitem:a3d13bafdbad06cbdfb7f43c4c1b098d4" id="r_a3d13bafdbad06cbdfb7f43c4c1b098d4"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3d13bafdbad06cbdfb7f43c4c1b098d4">GpioDeviceInterruptCallback</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a9b4681b0c2d7f8009a387b9104735752" id="r_a9b4681b0c2d7f8009a387b9104735752"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9b4681b0c2d7f8009a387b9104735752">GpioDeviceInterruptCallback</a> (<a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, ULONG <a class="el" href="#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>, BOOLEAN PinValue, PVOID Context)</td></tr>
<tr class="memitem:a1e860d4292f8df84e5d3101f8a415d6c" id="r_a1e860d4292f8df84e5d3101f8a415d6c"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1e860d4292f8df84e5d3101f8a415d6c">GpioDevicePulse</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG PulseDurationMs)</td></tr>
<tr class="memitem:a6448a7e48d735f67501f3273b75485fd" id="r_a6448a7e48d735f67501f3273b75485fd"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6448a7e48d735f67501f3273b75485fd">GpioDeviceRegisterCallback</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="gpio__device_8h.html#a7f38f134552248bad184ebb15479ad70">GPIO_DEVICE_EVENT_CALLBACK</a> Callback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a731812dec996a670e7d557a282535d3d" id="r_a731812dec996a670e7d557a282535d3d"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a731812dec996a670e7d557a282535d3d">GpioDeviceSetState</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="gpio__device_8h.html#a8be6b3f1a7d49373c162209cfdfb0bc5">GPIO_DEVICE_STATE</a> State)</td></tr>
<tr class="memitem:afbd2bd91a99c594504ca275fd8b45825" id="r_afbd2bd91a99c594504ca275fd8b45825"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afbd2bd91a99c594504ca275fd8b45825">GpioDeviceUnregisterCallback</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a6184bcc868fec6d16949da5c95315be4" id="r_a6184bcc868fec6d16949da5c95315be4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6184bcc868fec6d16949da5c95315be4">GPIOUninitialize</a> (Device, <a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a2105af29d2c177b4d5c5d8e589b1caa3">Config.PinNumber</a>)</td></tr>
<tr class="memitem:ab3fc244423e0b04d6b75007d82be3b1b" id="r_ab3fc244423e0b04d6b75007d82be3b1b"><td class="memItemLeft" align="right" valign="top">STATIC VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab3fc244423e0b04d6b75007d82be3b1b">HandleStateChange</a> (_In_ PGPIO_DEVICE_CONTEXT DeviceContext, _In_ BOOLEAN PinValue)</td></tr>
<tr class="memitem:a1a243a15dd793b6d0f7b7011461a8641" id="r_a1a243a15dd793b6d0f7b7011461a8641"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a243a15dd793b6d0f7b7011461a8641">if</a> (!<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>))</td></tr>
<tr class="memitem:aac20ced732c198d7484287c9eb39e413" id="r_aac20ced732c198d7484287c9eb39e413"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aac20ced732c198d7484287c9eb39e413">if</a> (<a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;Config.EnableInterrupt)</td></tr>
<tr class="memitem:ad94ec7eba667568bcd9afe3483282304" id="r_ad94ec7eba667568bcd9afe3483282304"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad94ec7eba667568bcd9afe3483282304">if</a> (GpioConfig-&gt;DebounceTime &gt; 0)</td></tr>
<tr class="memitem:a9eef4336d2e5308042aaa49f8966c7fa" id="r_a9eef4336d2e5308042aaa49f8966c7fa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9eef4336d2e5308042aaa49f8966c7fa">if</a> (GpioConfig-&gt;EnableInterrupt)</td></tr>
<tr class="memitem:a14190d6765d5c660291c1d6839cc5428" id="r_a14190d6765d5c660291c1d6839cc5428"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a14190d6765d5c660291c1d6839cc5428">if</a> (<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>))</td></tr>
<tr class="memitem:ac410021632e38928f5eb4b3bb6939ab9" id="r_ac410021632e38928f5eb4b3bb6939ab9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac410021632e38928f5eb4b3bb6939ab9">LogInfo</a> (__FUNCTION__, __LINE__, &quot;GPIO device initialized successfully, type=%d, pin=%d&quot;, GpioConfig-&gt;<a class="el" href="i2c__device_8c.html#abf852046373359fb294f66a784b38263">DeviceType</a>, GpioConfig-&gt;<a class="el" href="#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a3156531cfce23bfe597c3aaad2f23aad" id="r_a3156531cfce23bfe597c3aaad2f23aad"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3156531cfce23bfe597c3aaad2f23aad">LogInfo</a> (__FUNCTION__, __LINE__, &quot;GPIO device resources cleaned up&quot;)</td></tr>
<tr class="memitem:a1fbae9652e19914386dbb4fc4848b63d" id="r_a1fbae9652e19914386dbb4fc4848b63d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1fbae9652e19914386dbb4fc4848b63d">LogWarning</a> (__FUNCTION__, __LINE__, &quot;Failed to get initial GPIO pin value&quot;)</td></tr>
<tr class="memitem:a546ebaed609861f7aa12740071033d54" id="r_a546ebaed609861f7aa12740071033d54"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__core_8c.html#aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a> (<a class="el" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a>)</td></tr>
<tr class="memitem:a714d770abd4d38214bae1604c752369a" id="r_a714d770abd4d38214bae1604c752369a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a714d770abd4d38214bae1604c752369a">RtlZeroMemory</a> (<a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>, sizeof(GPIO_DEVICE_CONTEXT))</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:af02ced4aae3ad21bbe67ffd9742cda5b" id="r_af02ced4aae3ad21bbe67ffd9742cda5b"><td class="memItemLeft" align="right" valign="top">PVOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af02ced4aae3ad21bbe67ffd9742cda5b">CallbackContext</a></td></tr>
<tr class="memitem:afee8ca080129faeb1d5683d9b67a1aa6" id="r_afee8ca080129faeb1d5683d9b67a1aa6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__device_8h.html#a062e6f1e503256e897b3f6642fdce349">GPIO_DEVICE_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afee8ca080129faeb1d5683d9b67a1aa6">Config</a></td></tr>
<tr class="memitem:a351f54e1c120ac29191cd55267d05e00" id="r_a351f54e1c120ac29191cd55267d05e00"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__device_8h.html#a8be6b3f1a7d49373c162209cfdfb0bc5">GPIO_DEVICE_STATE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a351f54e1c120ac29191cd55267d05e00">CurrentState</a> = <a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f">GpioStateUnknown</a></td></tr>
<tr class="memitem:a130124259198fee8d71747e31a529e96" id="r_a130124259198fee8d71747e31a529e96"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a130124259198fee8d71747e31a529e96">Direction</a> = GpioConfig-&gt;Direction</td></tr>
<tr class="memitem:a0544c3fe466e421738dae463968b70ba" id="r_a0544c3fe466e421738dae463968b70ba"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0544c3fe466e421738dae463968b70ba">else</a></td></tr>
<tr class="memitem:addddad69ceb0e8964c3018cd853b60d6" id="r_addddad69ceb0e8964c3018cd853b60d6"><td class="memItemLeft" align="right" valign="top">EVT_WDF_TIMER&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#addddad69ceb0e8964c3018cd853b60d6">EvtDebounceTimerFunc</a></td></tr>
<tr class="memitem:af4f2a770562d41e27d370140ab383393" id="r_af4f2a770562d41e27d370140ab383393"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af4f2a770562d41e27d370140ab383393">LastPinValue</a> = PinValue</td></tr>
<tr class="memitem:a2105af29d2c177b4d5c5d8e589b1caa3" id="r_a2105af29d2c177b4d5c5d8e589b1caa3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a> = GpioConfig-&gt;PinNumber</td></tr>
<tr class="memitem:a8704f4f2bf5b602d6b300432f561fe4b" id="r_a8704f4f2bf5b602d6b300432f561fe4b"><td class="memItemLeft" align="right" valign="top">BOOLEAN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8704f4f2bf5b602d6b300432f561fe4b">pinValue</a></td></tr>
<tr class="memitem:ad6e10a2dc0aebdabca7a9c76612727a3" id="r_ad6e10a2dc0aebdabca7a9c76612727a3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad6e10a2dc0aebdabca7a9c76612727a3">Polarity</a> = GpioConfig-&gt;Polarity</td></tr>
<tr class="memitem:a9611b3a00430a86619b5923de30f9fdb" id="r_a9611b3a00430a86619b5923de30f9fdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9611b3a00430a86619b5923de30f9fdb">status</a> = WdfObjectAllocateContext(Device, &amp;<a class="el" href="gpio__core_8c.html#ac5d07e96f745ca56ee79420f0f039f66">attributes</a>, (PVOID*)&amp;<a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>)</td></tr>
<tr class="memitem:a77b4762318f24dff847f94f382cfeea6" id="r_a77b4762318f24dff847f94f382cfeea6"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77b4762318f24dff847f94f382cfeea6">STATUS_SUCCESS</a></td></tr>
<tr class="memitem:a11ec07dcb5c1cea421134a0b149443a5" id="r_a11ec07dcb5c1cea421134a0b149443a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11ec07dcb5c1cea421134a0b149443a5">WdfDevice</a> = Device</td></tr>
</table>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="a6acc285bc10925e95287d1821e20f2f7" name="a6acc285bc10925e95287d1821e20f2f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6acc285bc10925e95287d1821e20f2f7">&#9670;&#160;</a></span>GPIO_DEVICE_POOL_TAG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GPIO_DEVICE_POOL_TAG&#160;&#160;&#160;'DOPG'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a7b6a29716fe6f8117de54edaedc57974" name="a7b6a29716fe6f8117de54edaedc57974"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b6a29716fe6f8117de54edaedc57974">&#9670;&#160;</a></span>deviceContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a> &amp; deviceContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8h.html#a062e6f1e503256e897b3f6642fdce349">GPIO_DEVICE_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a7b6a29716fe6f8117de54edaedc57974_cgraph.png" border="0" usemap="#agpio__device_8c_a7b6a29716fe6f8117de54edaedc57974_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a7b6a29716fe6f8117de54edaedc57974_cgraph" id="agpio__device_8c_a7b6a29716fe6f8117de54edaedc57974_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,111,56"/>
<area shape="poly" title=" " alt="" coords="39,30,36,20,38,11,46,5,58,3,71,5,78,12,75,16,69,10,58,8,48,10,42,14,41,20,44,28"/>
</map>
</div>

</div>
</div>
<a id="a3df79e59d8cd67d1b0ea2bdd7b7e2662" name="a3df79e59d8cd67d1b0ea2bdd7b7e2662"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3df79e59d8cd67d1b0ea2bdd7b7e2662">&#9670;&#160;</a></span>EvtDebounceTimerFunc()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID EvtDebounceTimerFunc </td>
          <td>(</td>
          <td class="paramtype">_In_ WDFTIMER</td>          <td class="paramname"><span class="paramname"><em>Timer</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a3df79e59d8cd67d1b0ea2bdd7b7e2662_cgraph.png" border="0" usemap="#agpio__device_8c_a3df79e59d8cd67d1b0ea2bdd7b7e2662_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a3df79e59d8cd67d1b0ea2bdd7b7e2662_cgraph" id="agpio__device_8c_a3df79e59d8cd67d1b0ea2bdd7b7e2662_cgraph">
<area shape="rect" title=" " alt="" coords="5,104,168,131"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="232,29,338,56"/>
<area shape="poly" title=" " alt="" coords="123,101,215,66,231,59,233,64,217,70,125,106"/>
<area shape="rect" href="gpio__device_8c.html#ab3fc244423e0b04d6b75007d82be3b1b" title=" " alt="" coords="216,104,354,131"/>
<area shape="poly" title=" " alt="" coords="168,115,200,115,200,120,168,120"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="250,155,320,181"/>
<area shape="poly" title=" " alt="" coords="142,129,235,153,234,158,141,134"/>
<area shape="poly" title=" " alt="" coords="254,30,248,21,252,11,265,5,285,3,307,5,318,12,316,16,305,10,285,8,266,10,256,15,254,20,258,28"/>
<area shape="poly" title=" " alt="" coords="254,105,248,95,252,86,265,79,285,77,307,80,318,86,316,91,305,85,285,83,266,85,256,89,254,95,258,102"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="402,129,530,156"/>
<area shape="poly" title=" " alt="" coords="320,160,386,151,387,156,321,166"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="418,180,514,207"/>
<area shape="poly" title=" " alt="" coords="321,170,404,182,403,187,320,176"/>
</map>
</div>

</div>
</div>
<a id="ac3d3347067d7be6497e71b6ba4b389c2" name="ac3d3347067d7be6497e71b6ba4b389c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3d3347067d7be6497e71b6ba4b389c2">&#9670;&#160;</a></span>GpioDeviceCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID GpioDeviceCleanup </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceCleanup - 濞撳懐鎮奊PIO鐠佹儳顦挧鍕爱 </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_ac3d3347067d7be6497e71b6ba4b389c2_cgraph.png" border="0" usemap="#agpio__device_8c_ac3d3347067d7be6497e71b6ba4b389c2_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_ac3d3347067d7be6497e71b6ba4b389c2_cgraph" id="agpio__device_8c_ac3d3347067d7be6497e71b6ba4b389c2_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,143,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="191,29,296,56"/>
<area shape="poly" title=" " alt="" coords="143,40,175,40,175,45,143,45"/>
<area shape="poly" title=" " alt="" coords="214,30,209,21,213,11,224,5,243,3,264,5,275,12,272,16,262,10,243,8,226,10,217,15,214,20,219,28"/>
</map>
</div>

</div>
</div>
<a id="a0bd26e3410adfbb26bba326602f8fec6" name="a0bd26e3410adfbb26bba326602f8fec6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bd26e3410adfbb26bba326602f8fec6">&#9670;&#160;</a></span>GpioDeviceGetState()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDeviceGetState </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PGPIO_DEVICE_STATE</td>          <td class="paramname"><span class="paramname"><em>State</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceGetState - 閼惧嘲褰嘒PIO鐠佹儳顦悩鑸碘偓? </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a0bd26e3410adfbb26bba326602f8fec6_cgraph.png" border="0" usemap="#agpio__device_8c_a0bd26e3410adfbb26bba326602f8fec6_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a0bd26e3410adfbb26bba326602f8fec6_cgraph" id="agpio__device_8c_a0bd26e3410adfbb26bba326602f8fec6_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,147,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="195,29,300,56"/>
<area shape="poly" title=" " alt="" coords="147,55,179,50,179,55,147,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="212,80,282,107"/>
<area shape="poly" title=" " alt="" coords="147,76,197,83,196,89,147,81"/>
<area shape="poly" title=" " alt="" coords="219,30,214,21,217,11,229,5,247,3,267,5,278,12,275,16,265,10,247,8,230,10,221,15,219,20,223,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="348,55,476,81"/>
<area shape="poly" title=" " alt="" coords="283,85,332,78,333,83,283,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="364,105,460,132"/>
<area shape="poly" title=" " alt="" coords="283,96,350,106,349,112,283,101"/>
</map>
</div>

</div>
</div>
<a id="a21428e126bf8c8996ea3405e6a8be2f2" name="a21428e126bf8c8996ea3405e6a8be2f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21428e126bf8c8996ea3405e6a8be2f2">&#9670;&#160;</a></span>GpioDeviceInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDeviceInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="gpio__device_8h.html#a3225b6d40b644661d13ce4d49580cc08">PGPIO_DEVICE_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>GpioConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceInitialize - 閸掓繂顫愰崠鏈慞IO鐠佹儳顦? </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a21428e126bf8c8996ea3405e6a8be2f2_cgraph.png" border="0" usemap="#agpio__device_8c_a21428e126bf8c8996ea3405e6a8be2f2_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a21428e126bf8c8996ea3405e6a8be2f2_cgraph" id="agpio__device_8c_a21428e126bf8c8996ea3405e6a8be2f2_cgraph">
<area shape="rect" title=" " alt="" coords="5,80,143,107"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="191,29,296,56"/>
<area shape="poly" title=" " alt="" coords="121,77,181,58,183,63,122,82"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="208,80,278,107"/>
<area shape="poly" title=" " alt="" coords="143,91,192,91,192,96,143,96"/>
<area shape="rect" href="gpio__device_8c.html#a546ebaed609861f7aa12740071033d54" title=" " alt="" coords="205,155,282,181"/>
<area shape="poly" title=" " alt="" coords="107,105,198,146,196,150,105,110"/>
<area shape="poly" title=" " alt="" coords="215,30,210,21,213,11,225,5,243,3,263,5,274,12,271,16,261,10,243,8,226,10,217,15,215,20,219,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="344,55,472,81"/>
<area shape="poly" title=" " alt="" coords="279,85,328,78,329,83,279,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="360,105,456,132"/>
<area shape="poly" title=" " alt="" coords="279,96,346,106,345,112,279,101"/>
<area shape="poly" title=" " alt="" coords="215,155,210,146,213,136,225,130,243,128,263,131,274,137,271,142,261,136,243,133,226,135,217,140,215,145,219,153"/>
</map>
</div>

</div>
</div>
<a id="a3d13bafdbad06cbdfb7f43c4c1b098d4" name="a3d13bafdbad06cbdfb7f43c4c1b098d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3d13bafdbad06cbdfb7f43c4c1b098d4">&#9670;&#160;</a></span>GpioDeviceInterruptCallback() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID GpioDeviceInterruptCallback </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ BOOLEAN</td>          <td class="paramname"><span class="paramname"><em>PinValue</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a3d13bafdbad06cbdfb7f43c4c1b098d4_cgraph.png" border="0" usemap="#agpio__device_8c_a3d13bafdbad06cbdfb7f43c4c1b098d4_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a3d13bafdbad06cbdfb7f43c4c1b098d4_cgraph" id="agpio__device_8c_a3d13bafdbad06cbdfb7f43c4c1b098d4_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,194,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="242,29,347,56"/>
<area shape="poly" title=" " alt="" coords="194,40,226,40,226,45,194,45"/>
<area shape="poly" title=" " alt="" coords="261,30,255,21,259,11,273,5,294,3,318,5,330,12,328,16,316,10,294,8,274,10,263,15,261,20,266,27"/>
</map>
</div>

</div>
</div>
<a id="a9b4681b0c2d7f8009a387b9104735752" name="a9b4681b0c2d7f8009a387b9104735752"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b4681b0c2d7f8009a387b9104735752">&#9670;&#160;</a></span>GpioDeviceInterruptCallback() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID GpioDeviceInterruptCallback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">BOOLEAN</td>          <td class="paramname"><span class="paramname"><em>PinValue</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_cgraph.png" border="0" usemap="#agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_cgraph" id="agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,194,56"/>
<area shape="poly" title=" " alt="" coords="67,30,62,21,66,11,79,5,100,3,122,5,134,12,132,16,120,10,99,8,80,10,69,15,67,20,72,27"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_icgraph.png" border="0" usemap="#agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_icgraph" id="agpio__device_8c_a9b4681b0c2d7f8009a387b9104735752_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,194,56"/>
<area shape="poly" title=" " alt="" coords="132,16,120,10,99,8,80,10,69,15,67,20,72,27,67,30,62,21,66,11,79,5,100,3,122,5,134,12"/>
</map>
</div>

</div>
</div>
<a id="a1e860d4292f8df84e5d3101f8a415d6c" name="a1e860d4292f8df84e5d3101f8a415d6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e860d4292f8df84e5d3101f8a415d6c">&#9670;&#160;</a></span>GpioDevicePulse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDevicePulse </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PulseDurationMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDevicePulse - 鐠佹儳顦崣鎴濆毉閼村鍟块懘澶婂暱 </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a1e860d4292f8df84e5d3101f8a415d6c_cgraph.png" border="0" usemap="#agpio__device_8c_a1e860d4292f8df84e5d3101f8a415d6c_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a1e860d4292f8df84e5d3101f8a415d6c_cgraph" id="agpio__device_8c_a1e860d4292f8df84e5d3101f8a415d6c_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,128,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="176,29,281,56"/>
<area shape="poly" title=" " alt="" coords="128,56,160,51,161,56,128,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="193,80,264,107"/>
<area shape="poly" title=" " alt="" coords="128,75,178,83,177,88,128,80"/>
<area shape="poly" title=" " alt="" coords="200,30,195,21,199,11,210,5,228,3,248,5,258,12,256,16,246,10,228,8,212,10,203,15,201,20,205,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="329,55,457,81"/>
<area shape="poly" title=" " alt="" coords="264,85,313,78,314,83,264,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="346,105,441,132"/>
<area shape="poly" title=" " alt="" coords="264,96,331,106,330,112,264,101"/>
</map>
</div>

</div>
</div>
<a id="a6448a7e48d735f67501f3273b75485fd" name="a6448a7e48d735f67501f3273b75485fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6448a7e48d735f67501f3273b75485fd">&#9670;&#160;</a></span>GpioDeviceRegisterCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDeviceRegisterCallback </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="gpio__device_8h.html#a7f38f134552248bad184ebb15479ad70">GPIO_DEVICE_EVENT_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>Callback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceRegisterCallback - 濞夈劌鍞紾PIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a6448a7e48d735f67501f3273b75485fd_cgraph.png" border="0" usemap="#agpio__device_8c_a6448a7e48d735f67501f3273b75485fd_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a6448a7e48d735f67501f3273b75485fd_cgraph" id="agpio__device_8c_a6448a7e48d735f67501f3273b75485fd_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,195,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="243,29,348,56"/>
<area shape="poly" title=" " alt="" coords="194,53,227,49,228,54,195,58"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="260,80,330,107"/>
<area shape="poly" title=" " alt="" coords="195,78,245,84,244,89,194,83"/>
<area shape="poly" title=" " alt="" coords="267,30,262,21,265,11,277,5,295,3,315,5,326,12,323,16,313,10,295,8,278,10,269,15,267,20,271,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="396,55,524,81"/>
<area shape="poly" title=" " alt="" coords="331,85,380,78,381,83,331,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="412,105,508,132"/>
<area shape="poly" title=" " alt="" coords="331,96,398,106,397,112,331,101"/>
</map>
</div>

</div>
</div>
<a id="a731812dec996a670e7d557a282535d3d" name="a731812dec996a670e7d557a282535d3d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a731812dec996a670e7d557a282535d3d">&#9670;&#160;</a></span>GpioDeviceSetState()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDeviceSetState </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="gpio__device_8h.html#a8be6b3f1a7d49373c162209cfdfb0bc5">GPIO_DEVICE_STATE</a></td>          <td class="paramname"><span class="paramname"><em>State</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceSetState - 鐠佸墽鐤咷PIO鐠佹儳顦悩鑸碘偓? </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a731812dec996a670e7d557a282535d3d_cgraph.png" border="0" usemap="#agpio__device_8c_a731812dec996a670e7d557a282535d3d_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a731812dec996a670e7d557a282535d3d_cgraph" id="agpio__device_8c_a731812dec996a670e7d557a282535d3d_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,146,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="194,29,299,56"/>
<area shape="poly" title=" " alt="" coords="146,55,178,50,179,55,147,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="211,80,282,107"/>
<area shape="poly" title=" " alt="" coords="147,76,196,83,196,89,146,81"/>
<area shape="poly" title=" " alt="" coords="218,30,213,21,216,11,228,5,246,3,266,5,277,12,274,16,264,10,246,8,229,10,220,15,218,20,222,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="347,55,475,81"/>
<area shape="poly" title=" " alt="" coords="282,85,331,78,332,83,282,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="364,105,459,132"/>
<area shape="poly" title=" " alt="" coords="282,96,349,106,348,112,282,101"/>
</map>
</div>

</div>
</div>
<a id="afbd2bd91a99c594504ca275fd8b45825" name="afbd2bd91a99c594504ca275fd8b45825"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbd2bd91a99c594504ca275fd8b45825">&#9670;&#160;</a></span>GpioDeviceUnregisterCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS GpioDeviceUnregisterCallback </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>GpioDeviceUnregisterCallback - 閸欐牗绉峰▔銊ュ斀GPIO鐠佹儳顦禍瀣╂閸ョ偠鐨? </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_afbd2bd91a99c594504ca275fd8b45825_cgraph.png" border="0" usemap="#agpio__device_8c_afbd2bd91a99c594504ca275fd8b45825_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_afbd2bd91a99c594504ca275fd8b45825_cgraph" id="agpio__device_8c_afbd2bd91a99c594504ca275fd8b45825_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,206,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="254,29,359,56"/>
<area shape="poly" title=" " alt="" coords="206,53,238,49,239,54,206,58"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="271,80,342,107"/>
<area shape="poly" title=" " alt="" coords="206,78,256,84,256,90,206,83"/>
<area shape="poly" title=" " alt="" coords="278,30,273,21,276,11,288,5,306,3,326,5,337,12,334,16,324,10,306,8,289,10,280,15,278,20,282,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="407,55,535,81"/>
<area shape="poly" title=" " alt="" coords="342,85,391,78,392,83,342,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="424,105,519,132"/>
<area shape="poly" title=" " alt="" coords="342,96,409,106,408,112,342,101"/>
</map>
</div>

</div>
</div>
<a id="a6184bcc868fec6d16949da5c95315be4" name="a6184bcc868fec6d16949da5c95315be4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6184bcc868fec6d16949da5c95315be4">&#9670;&#160;</a></span>GPIOUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GPIOUninitialize </td>
          <td>(</td>
          <td class="paramtype">Device</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;Config.</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a6184bcc868fec6d16949da5c95315be4_cgraph.png" border="0" usemap="#agpio__device_8c_a6184bcc868fec6d16949da5c95315be4_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a6184bcc868fec6d16949da5c95315be4_cgraph" id="agpio__device_8c_a6184bcc868fec6d16949da5c95315be4_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,124,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="172,29,277,56"/>
<area shape="poly" title=" " alt="" coords="124,40,156,40,156,45,124,45"/>
<area shape="poly" title=" " alt="" coords="197,30,192,21,195,11,206,5,224,3,244,5,254,12,251,16,242,10,224,8,208,10,199,15,197,20,201,28"/>
</map>
</div>

</div>
</div>
<a id="ab3fc244423e0b04d6b75007d82be3b1b" name="ab3fc244423e0b04d6b75007d82be3b1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab3fc244423e0b04d6b75007d82be3b1b">&#9670;&#160;</a></span>HandleStateChange()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">STATIC VOID HandleStateChange </td>
          <td>(</td>
          <td class="paramtype">_In_ PGPIO_DEVICE_CONTEXT</td>          <td class="paramname"><span class="paramname"><em>DeviceContext</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ BOOLEAN</td>          <td class="paramname"><span class="paramname"><em>PinValue</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_cgraph.png" border="0" usemap="#agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_cgraph" id="agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,144,56"/>
<area shape="poly" title=" " alt="" coords="50,30,46,21,49,11,59,5,75,3,92,5,101,12,97,16,89,10,74,8,61,10,53,14,51,20,55,28"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_icgraph.png" border="0" usemap="#agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_icgraph" id="agpio__device_8c_ab3fc244423e0b04d6b75007d82be3b1b_icgraph">
<area shape="rect" title=" " alt="" coords="216,31,354,57"/>
<area shape="poly" title=" " alt="" coords="319,18,307,12,285,9,264,11,253,16,250,21,256,29,251,32,245,22,249,12,263,6,285,4,309,7,321,13"/>
<area shape="rect" href="gpio__device_8c.html#a3df79e59d8cd67d1b0ea2bdd7b7e2662" title=" " alt="" coords="5,5,168,32"/>
<area shape="poly" title=" " alt="" coords="200,36,168,32,169,26,200,31"/>
<area shape="rect" href="gpio__device_8c.html#a14190d6765d5c660291c1d6839cc5428" title=" " alt="" coords="67,56,106,83"/>
<area shape="poly" title=" " alt="" coords="201,57,106,70,106,64,200,52"/>
</map>
</div>

</div>
</div>
<a id="a1a243a15dd793b6d0f7b7011461a8641" name="a1a243a15dd793b6d0f7b7011461a8641"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a243a15dd793b6d0f7b7011461a8641">&#9670;&#160;</a></span>if() <span class="overload">[1/5]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>NT_SUCCESS</em></span>status</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph.png" border="0" usemap="#agpio__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" id="agpio__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,44,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="92,31,162,57"/>
<area shape="poly" title=" " alt="" coords="44,41,76,41,76,47,44,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="210,5,338,32"/>
<area shape="poly" title=" " alt="" coords="162,35,195,30,195,35,163,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="227,56,322,83"/>
<area shape="poly" title=" " alt="" coords="163,47,212,56,211,61,162,53"/>
</map>
</div>

</div>
</div>
<a id="aac20ced732c198d7484287c9eb39e413" name="aac20ced732c198d7484287c9eb39e413"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac20ced732c198d7484287c9eb39e413">&#9670;&#160;</a></span>if() <span class="overload">[2/5]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;Config.</td>          <td class="paramname"><span class="paramname"><em>EnableInterrupt</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_aac20ced732c198d7484287c9eb39e413_cgraph.png" border="0" usemap="#agpio__device_8c_aac20ced732c198d7484287c9eb39e413_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_aac20ced732c198d7484287c9eb39e413_cgraph" id="agpio__device_8c_aac20ced732c198d7484287c9eb39e413_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="112,29,218,56"/>
<area shape="poly" title=" " alt="" coords="43,62,96,52,97,58,44,67"/>
<area shape="rect" href="kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4" title=" " alt="" coords="92,80,238,107"/>
<area shape="poly" title=" " alt="" coords="44,69,77,75,76,80,43,74"/>
<area shape="poly" title=" " alt="" coords="140,30,136,21,139,11,149,5,165,3,182,5,191,12,188,16,180,10,165,8,151,10,143,14,141,20,145,28"/>
</map>
</div>

</div>
</div>
<a id="ad94ec7eba667568bcd9afe3483282304" name="ad94ec7eba667568bcd9afe3483282304"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad94ec7eba667568bcd9afe3483282304">&#9670;&#160;</a></span>if() <span class="overload">[3/5]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">GpioConfig-&gt;</td>          <td class="paramname"><span class="paramname"><em>DebounceTime</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_ad94ec7eba667568bcd9afe3483282304_cgraph.png" border="0" usemap="#agpio__device_8c_ad94ec7eba667568bcd9afe3483282304_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_ad94ec7eba667568bcd9afe3483282304_cgraph" id="agpio__device_8c_ad94ec7eba667568bcd9afe3483282304_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,61,76,54,77,60,45,67"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="109,80,180,107"/>
<area shape="poly" title=" " alt="" coords="45,69,94,80,93,85,44,75"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="245,55,373,81"/>
<area shape="poly" title=" " alt="" coords="180,85,229,78,230,83,181,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="262,105,357,132"/>
<area shape="poly" title=" " alt="" coords="181,96,247,106,246,112,180,101"/>
</map>
</div>

</div>
</div>
<a id="a9eef4336d2e5308042aaa49f8966c7fa" name="a9eef4336d2e5308042aaa49f8966c7fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9eef4336d2e5308042aaa49f8966c7fa">&#9670;&#160;</a></span>if() <span class="overload">[4/5]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">GpioConfig-&gt;</td>          <td class="paramname"><span class="paramname"><em>EnableInterrupt</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a9eef4336d2e5308042aaa49f8966c7fa_cgraph.png" border="0" usemap="#agpio__device_8c_a9eef4336d2e5308042aaa49f8966c7fa_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a9eef4336d2e5308042aaa49f8966c7fa_cgraph" id="agpio__device_8c_a9eef4336d2e5308042aaa49f8966c7fa_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,44,56"/>
<area shape="rect" href="gpio__device_8c.html#a546ebaed609861f7aa12740071033d54" title=" " alt="" coords="92,29,168,56"/>
<area shape="poly" title=" " alt="" coords="44,40,76,40,76,45,44,45"/>
<area shape="poly" title=" " alt="" coords="111,30,108,20,110,11,117,5,130,3,143,5,150,12,147,16,141,10,130,8,120,10,114,14,113,20,116,28"/>
</map>
</div>

</div>
</div>
<a id="a14190d6765d5c660291c1d6839cc5428" name="a14190d6765d5c660291c1d6839cc5428"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14190d6765d5c660291c1d6839cc5428">&#9670;&#160;</a></span>if() <span class="overload">[5/5]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>)</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a14190d6765d5c660291c1d6839cc5428_cgraph.png" border="0" usemap="#agpio__device_8c_a14190d6765d5c660291c1d6839cc5428_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a14190d6765d5c660291c1d6839cc5428_cgraph" id="agpio__device_8c_a14190d6765d5c660291c1d6839cc5428_cgraph">
<area shape="rect" title=" " alt="" coords="5,67,44,93"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="108,29,214,56"/>
<area shape="poly" title=" " alt="" coords="43,72,95,58,96,63,45,78"/>
<area shape="rect" href="gpio__device_8c.html#ab3fc244423e0b04d6b75007d82be3b1b" title=" " alt="" coords="92,104,230,131"/>
<area shape="poly" title=" " alt="" coords="45,82,96,97,95,102,43,88"/>
<area shape="poly" title=" " alt="" coords="137,30,133,21,136,11,145,5,161,3,178,5,187,12,183,16,176,10,161,8,147,10,140,14,138,20,142,28"/>
<area shape="poly" title=" " alt="" coords="137,105,133,95,136,86,145,80,161,77,178,80,187,87,183,91,176,85,161,83,147,85,140,89,138,95,142,102"/>
</map>
</div>

</div>
</div>
<a id="ac410021632e38928f5eb4b3bb6939ab9" name="ac410021632e38928f5eb4b3bb6939ab9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac410021632e38928f5eb4b3bb6939ab9">&#9670;&#160;</a></span>LogInfo() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;GPIO device initialized</td>          <td class="paramname"><span class="paramname"><em>successfully</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">type</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">%d</span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3156531cfce23bfe597c3aaad2f23aad" name="a3156531cfce23bfe597c3aaad2f23aad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3156531cfce23bfe597c3aaad2f23aad">&#9670;&#160;</a></span>LogInfo() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;GPIO device resources cleaned up&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1fbae9652e19914386dbb4fc4848b63d" name="a1fbae9652e19914386dbb4fc4848b63d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1fbae9652e19914386dbb4fc4848b63d">&#9670;&#160;</a></span>LogWarning()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogWarning </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;Failed to get initial GPIO pin value&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a546ebaed609861f7aa12740071033d54" name="a546ebaed609861f7aa12740071033d54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a546ebaed609861f7aa12740071033d54">&#9670;&#160;</a></span>pinConfig()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__core_8c.html#aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</a> &amp; pinConfig </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a546ebaed609861f7aa12740071033d54_cgraph.png" border="0" usemap="#agpio__device_8c_a546ebaed609861f7aa12740071033d54_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a546ebaed609861f7aa12740071033d54_cgraph" id="agpio__device_8c_a546ebaed609861f7aa12740071033d54_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,82,56"/>
<area shape="poly" title=" " alt="" coords="29,29,28,12,34,5,44,3,54,5,59,12,55,15,51,10,43,8,37,10,33,14,34,29"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a546ebaed609861f7aa12740071033d54_icgraph.png" border="0" usemap="#agpio__device_8c_a546ebaed609861f7aa12740071033d54_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a546ebaed609861f7aa12740071033d54_icgraph" id="agpio__device_8c_a546ebaed609861f7aa12740071033d54_icgraph">
<area shape="rect" title=" " alt="" coords="191,31,267,57"/>
<area shape="poly" title=" " alt="" coords="255,18,246,12,229,9,213,11,205,16,203,21,207,29,202,31,197,22,200,12,211,6,229,4,248,7,258,13"/>
<area shape="rect" href="gpio__device_8c.html#a21428e126bf8c8996ea3405e6a8be2f2" title=" " alt="" coords="5,5,143,32"/>
<area shape="poly" title=" " alt="" coords="175,38,143,33,143,27,176,33"/>
<area shape="rect" href="gpio__device_8c.html#a9eef4336d2e5308042aaa49f8966c7fa" title=" " alt="" coords="55,56,93,83"/>
<area shape="poly" title=" " alt="" coords="176,55,94,69,93,64,175,50"/>
</map>
</div>

</div>
</div>
<a id="a714d770abd4d38214bae1604c752369a" name="a714d770abd4d38214bae1604c752369a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a714d770abd4d38214bae1604c752369a">&#9670;&#160;</a></span>RtlZeroMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RtlZeroMemory </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">sizeof(GPIO_DEVICE_CONTEXT)</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__device_8c_a714d770abd4d38214bae1604c752369a_cgraph.png" border="0" usemap="#agpio__device_8c_a714d770abd4d38214bae1604c752369a_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__device_8c_a714d770abd4d38214bae1604c752369a_cgraph" id="agpio__device_8c_a714d770abd4d38214bae1604c752369a_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,117,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="165,29,270,56"/>
<area shape="poly" title=" " alt="" coords="117,40,149,40,149,45,117,45"/>
<area shape="poly" title=" " alt="" coords="190,30,185,21,189,11,200,5,217,3,236,5,246,12,243,16,234,10,217,8,201,10,193,15,191,20,195,28"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="af02ced4aae3ad21bbe67ffd9742cda5b" name="af02ced4aae3ad21bbe67ffd9742cda5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af02ced4aae3ad21bbe67ffd9742cda5b">&#9670;&#160;</a></span>CallbackContext</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PVOID CallbackContext</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afee8ca080129faeb1d5683d9b67a1aa6" name="afee8ca080129faeb1d5683d9b67a1aa6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afee8ca080129faeb1d5683d9b67a1aa6">&#9670;&#160;</a></span>Config</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8h.html#a062e6f1e503256e897b3f6642fdce349">GPIO_DEVICE_CONFIG</a> Config</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a351f54e1c120ac29191cd55267d05e00" name="a351f54e1c120ac29191cd55267d05e00"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a351f54e1c120ac29191cd55267d05e00">&#9670;&#160;</a></span>CurrentState</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> CurrentState = <a class="el" href="gpio__device_8h.html#af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f">GpioStateUnknown</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a130124259198fee8d71747e31a529e96" name="a130124259198fee8d71747e31a529e96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a130124259198fee8d71747e31a529e96">&#9670;&#160;</a></span>Direction</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a> Direction = GpioConfig-&gt;Direction</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0544c3fe466e421738dae463968b70ba" name="a0544c3fe466e421738dae463968b70ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0544c3fe466e421738dae463968b70ba">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        </div>
<div class="line">            <a class="code hl_function" href="#ab3fc244423e0b04d6b75007d82be3b1b">HandleStateChange</a>(<a class="code hl_function" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>, PinValue)</div>
<div class="ttc" id="agpio__device_8c_html_a7b6a29716fe6f8117de54edaedc57974"><div class="ttname"><a href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></div><div class="ttdeci">RtlCopyMemory &amp; deviceContext(GPIO_DEVICE_CONFIG)</div></div>
<div class="ttc" id="agpio__device_8c_html_ab3fc244423e0b04d6b75007d82be3b1b"><div class="ttname"><a href="#ab3fc244423e0b04d6b75007d82be3b1b">HandleStateChange</a></div><div class="ttdeci">STATIC VOID HandleStateChange(_In_ PGPIO_DEVICE_CONTEXT DeviceContext, _In_ BOOLEAN PinValue)</div><div class="ttdef"><b>Definition</b> gpio_device.c:46</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="addddad69ceb0e8964c3018cd853b60d6" name="addddad69ceb0e8964c3018cd853b60d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#addddad69ceb0e8964c3018cd853b60d6">&#9670;&#160;</a></span>EvtDebounceTimerFunc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_TIMER EvtDebounceTimerFunc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af4f2a770562d41e27d370140ab383393" name="af4f2a770562d41e27d370140ab383393"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af4f2a770562d41e27d370140ab383393">&#9670;&#160;</a></span>LastPinValue</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> LastPinValue = PinValue</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2105af29d2c177b4d5c5d8e589b1caa3" name="a2105af29d2c177b4d5c5d8e589b1caa3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2105af29d2c177b4d5c5d8e589b1caa3">&#9670;&#160;</a></span>PinNumber</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a> PinNumber = GpioConfig-&gt;PinNumber</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8704f4f2bf5b602d6b300432f561fe4b" name="a8704f4f2bf5b602d6b300432f561fe4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8704f4f2bf5b602d6b300432f561fe4b">&#9670;&#160;</a></span>pinValue</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">BOOLEAN pinValue</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad6e10a2dc0aebdabca7a9c76612727a3" name="ad6e10a2dc0aebdabca7a9c76612727a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6e10a2dc0aebdabca7a9c76612727a3">&#9670;&#160;</a></span>Polarity</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a546ebaed609861f7aa12740071033d54">pinConfig</a> Polarity = GpioConfig-&gt;Polarity</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9611b3a00430a86619b5923de30f9fdb" name="a9611b3a00430a86619b5923de30f9fdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9611b3a00430a86619b5923de30f9fdb">&#9670;&#160;</a></span>status</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return status = WdfObjectAllocateContext(Device, &amp;<a class="el" href="gpio__core_8c.html#ac5d07e96f745ca56ee79420f0f039f66">attributes</a>, (PVOID*)&amp;<a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a77b4762318f24dff847f94f382cfeea6" name="a77b4762318f24dff847f94f382cfeea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77b4762318f24dff847f94f382cfeea6">&#9670;&#160;</a></span>STATUS_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return STATUS_SUCCESS</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a11ec07dcb5c1cea421134a0b149443a5" name="a11ec07dcb5c1cea421134a0b149443a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11ec07dcb5c1cea421134a0b149443a5">&#9670;&#160;</a></span>WdfDevice</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> WdfDevice = Device</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_340a9c6f51eab01289f9b188a5d35565.html">devices</a></li><li class="navelem"><a href="gpio__device_8c.html">gpio_device.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
