<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="kmdf__bus__common_8h" kind="file" language="C++">
    <compoundname>kmdf_bus_common.h</compoundname>
    <includes local="no">wdf.h</includes>
    <includes refid="error__codes_8h" local="yes">../../core/error/error_codes.h</includes>
    <includedby refid="device__manager_8h" local="yes">C:/KMDF Driver1/include/core/device/device_manager.h</includedby>
    <includedby refid="driver__entry_8h" local="yes">C:/KMDF Driver1/include/core/driver/driver_entry.h</includedby>
    <includedby refid="kmdf__gpio_8h" local="yes">C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h</includedby>
    <includedby refid="kmdf__i2c_8h" local="yes">C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h</includedby>
    <includedby refid="kmdf__spi_8h" local="yes">C:/KMDF Driver1/include/hal/bus/kmdf_spi.h</includedby>
    <includedby refid="driver__main_8c" local="yes">C:/KMDF Driver1/src/driver_main.c</includedby>
    <incdepgraph>
      <node id="3">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>ntddk.h</label>
      </node>
      <node id="2">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="2">
        <label>C:/KMDF Driver1/include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/include/core/driver/driver_entry.h</label>
        <link refid="driver__entry_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
        <childnode refid="20" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
      </node>
      <node id="17">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
      </node>
      <node id="20">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="21" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="22" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>C:/KMDF Driver1/include/hal/devices/gpio_device.h</label>
        <link refid="gpio__device_8h"/>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>C:/KMDF Driver1/include/hal/devices/i2c_device.h</label>
        <link refid="i2c__device_8h"/>
      </node>
      <node id="21">
        <label>C:/KMDF Driver1/include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="22" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
      </node>
      <node id="7">
        <label>C:/KMDF Driver1/src/core/driver/driver_core.c</label>
        <link refid="driver__core_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/core/driver/driver_entry.c</label>
        <link refid="driver__entry_8c"/>
      </node>
      <node id="9">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="10">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="11">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="12">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="16">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
      </node>
      <node id="19">
        <label>C:/KMDF Driver1/src/hal/devices/i2c_device.c</label>
        <link refid="i2c__device_8c"/>
      </node>
      <node id="22">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
      </node>
      <node id="13">
        <label>C:/KMDF Driver1/src/precomp.c</label>
        <link refid="precomp_8c"/>
      </node>
      <node id="8">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__BUS__CONFIG" prot="public">_BUS_CONFIG</innerclass>
    <innerclass refid="struct__BUS__TRANSFER__PACKET" prot="public">_BUS_TRANSFER_PACKET</innerclass>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52f" prot="public" static="no" strong="no">
        <type></type>
        <name>_BUS_TRANSFER_STATUS</name>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" prot="public">
          <name>BusTransferSuccess</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" prot="public">
          <name>BusTransferFailed</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726" prot="public">
          <name>BusTransferCancelled</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c" prot="public">
          <name>BusTransferInvalidParameter</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b" prot="public">
          <name>BusTransferDeviceNotReady</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519" prot="public">
          <name>BusTransferTimeout</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="24" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" bodystart="24" bodyend="31"/>
      </memberdef>
      <memberdef kind="enum" id="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123" prot="public" static="no" strong="no">
        <type></type>
        <name>_BUS_TYPE</name>
        <enumvalue id="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" prot="public">
          <name>BusTypeI2C</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" prot="public">
          <name>BusTypeSPI</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" prot="public">
          <name>BusTypeUSB</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38" prot="public">
          <name>BusTypeMax</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="16" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" bodystart="16" bodyend="21"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1a90f573bacff80fd27b824b98e3e4fb9a" prot="public" static="no">
        <type>struct <ref refid="struct__BUS__CONFIG" kindref="compound">_BUS_CONFIG</ref></type>
        <definition>typedef struct _BUS_CONFIG BUS_CONFIG</definition>
        <argsstring></argsstring>
        <name>BUS_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="39" column="12"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" prot="public" static="no">
        <type>VOID(*</type>
        <definition>typedef VOID(* BUS_OPERATION_CALLBACK) (PBUS_TRANSFER_PACKET TransferPacket)</definition>
        <argsstring>)(PBUS_TRANSFER_PACKET TransferPacket)</argsstring>
        <name>BUS_OPERATION_CALLBACK</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="50" column="9" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" bodystart="50" bodyend="-1"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" prot="public" static="no">
        <type>struct <ref refid="struct__BUS__TRANSFER__PACKET" kindref="compound">_BUS_TRANSFER_PACKET</ref></type>
        <definition>typedef struct _BUS_TRANSFER_PACKET BUS_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>BUS_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="47" column="21"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1ab61a790fb09aa3a337c89ea002b5a76f" prot="public" static="no">
        <type>enum <ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52f" kindref="member">_BUS_TRANSFER_STATUS</ref></type>
        <definition>typedef enum _BUS_TRANSFER_STATUS BUS_TRANSFER_STATUS</definition>
        <argsstring></argsstring>
        <name>BUS_TRANSFER_STATUS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="31" column="21"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1a070973cba55289cdfb72a260dc529d59" prot="public" static="no">
        <type>enum <ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123" kindref="member">_BUS_TYPE</ref></type>
        <definition>typedef enum _BUS_TYPE BUS_TYPE</definition>
        <argsstring></argsstring>
        <name>BUS_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="21" column="10"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1aeca4f45b7bb946c601f53bb52b08b170" prot="public" static="no">
        <type>struct <ref refid="struct__BUS__CONFIG" kindref="compound">_BUS_CONFIG</ref> *</type>
        <definition>typedef struct _BUS_CONFIG * PBUS_CONFIG</definition>
        <argsstring></argsstring>
        <name>PBUS_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="39" column="25"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" prot="public" static="no">
        <type>struct <ref refid="struct__BUS__TRANSFER__PACKET" kindref="compound">_BUS_TRANSFER_PACKET</ref> *</type>
        <definition>typedef struct _BUS_TRANSFER_PACKET * PBUS_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>PBUS_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="47" column="43"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="kmdf__bus__common_8h_1a4f54d258241aeda6b5d01ee12110870f" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS BusCancelTransfer</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PBUS_TRANSFER_PACKET TransferPacket)</argsstring>
        <name>BusCancelTransfer</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="82" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" declline="82" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="kmdf__bus__common_8h_1ad5aa0e171a9d72e28ab08d06935ab2f5" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS BusInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PBUS_CONFIG BusConfig)</argsstring>
        <name>BusInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1aeca4f45b7bb946c601f53bb52b08b170" kindref="member">PBUS_CONFIG</ref></type>
          <declname>BusConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="56" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" declline="56" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__bus__common_8h_1af23a4d40f37f1cf45dd8b2fc40cf6dff" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS BusTransferAsynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PBUS_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</argsstring>
        <name>BusTransferAsynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref></type>
          <declname>CompletionCallback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="74" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" declline="74" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__bus__common_8h_1a79b8f6be307f48971e34bb6cabfba958" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS BusTransferSynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PBUS_TRANSFER_PACKET TransferPacket, _In_ ULONG Timeout)</argsstring>
        <name>BusTransferSynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Timeout</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="67" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" declline="67" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__bus__common_8h_1a2878b61cfb78224301b7d25175aa243e" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID BusUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>BusUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" line="62" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h" declline="62" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>kmdf_bus_common.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>Common<sp/>definitions<sp/>for<sp/>bus<sp/>interfaces</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>Provides<sp/>shared<sp/>interfaces<sp/>and<sp/>data<sp/>structures<sp/>for<sp/>all<sp/>bus<sp/>types</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_BUS_COMMON_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_BUS_COMMON_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="comment">//<sp/>#include<sp/>&lt;wdm.h&gt;<sp/>//<sp/>Removed,<sp/>wdf.h<sp/>should<sp/>suffice<sp/>or<sp/>ntddk.h<sp/>via<sp/>other<sp/>headers<sp/>if<sp/>in<sp/>kernel<sp/>mode</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>This<sp/>includes<sp/>ntddk.h<sp/>(conditionally)<sp/>and<sp/>wdf.h</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Bus<sp/>type<sp/>enumeration</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123" kindref="member">_BUS_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="17" refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" kindref="member">BusTypeI2C</ref>,<sp/><sp/></highlight><highlight class="comment">//<sp/>I2C<sp/>bus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" kindref="member">BusTypeSPI</ref>,<sp/><sp/></highlight><highlight class="comment">//<sp/>SPI<sp/>bus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" kindref="member">BusTypeUSB</ref>,<sp/><sp/></highlight><highlight class="comment">//<sp/>USB<sp/>bus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38" kindref="member">BusTypeMax</ref><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Enum<sp/>boundary<sp/>check<sp/>value</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="kmdf__bus__common_8h_1a070973cba55289cdfb72a260dc529d59" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__bus__common_8h_1a070973cba55289cdfb72a260dc529d59" kindref="member">BUS_TYPE</ref>;</highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Bus<sp/>transfer<sp/>status</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52f" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52f" kindref="member">_BUS_TRANSFER_STATUS</ref><sp/>{</highlight></codeline>
<codeline lineno="25" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" kindref="member">BusTransferSuccess</ref><sp/>=<sp/>0,<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>successful</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" kindref="member">BusTransferFailed</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>failed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726" kindref="member">BusTransferCancelled</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>cancelled</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c" kindref="member">BusTransferInvalidParameter</ref>,<sp/></highlight><highlight class="comment">//<sp/>Invalid<sp/>parameter</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b" kindref="member">BusTransferDeviceNotReady</ref>,<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Device<sp/>not<sp/>ready</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519" kindref="member">BusTransferTimeout</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>timeout</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="kmdf__bus__common_8h_1ab61a790fb09aa3a337c89ea002b5a76f" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__bus__common_8h_1ab61a790fb09aa3a337c89ea002b5a76f" kindref="member">BUS_TRANSFER_STATUS</ref>;</highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Common<sp/>bus<sp/>configuration<sp/>structure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="struct__BUS__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__BUS__CONFIG" kindref="compound">_BUS_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="35" refid="struct__BUS__CONFIG_1ab3667ea857ae85e39b62c3a39b8d6761" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a070973cba55289cdfb72a260dc529d59" kindref="member">BUS_TYPE</ref><sp/><ref refid="struct__BUS__CONFIG_1ab3667ea857ae85e39b62c3a39b8d6761" kindref="member">BusType</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Bus<sp/>type</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__BUS__CONFIG_1a4dc4b392465c874dc7b75256fa2ac00a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__BUS__CONFIG_1a4dc4b392465c874dc7b75256fa2ac00a" kindref="member">Speed</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Bus<sp/>speed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__BUS__CONFIG_1a76efcd63df259ff464d8b6e9be040134" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__BUS__CONFIG_1a76efcd63df259ff464d8b6e9be040134" kindref="member">Flags</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Configuration<sp/>flags</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__BUS__CONFIG_1ae19319720b3c056dc60261c26e1dd43e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__BUS__CONFIG_1ae19319720b3c056dc60261c26e1dd43e" kindref="member">BusSpecificConfig</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Bus<sp/>specific<sp/>configuration</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="kmdf__bus__common_8h_1a90f573bacff80fd27b824b98e3e4fb9a" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__bus__common_8h_1a90f573bacff80fd27b824b98e3e4fb9a" kindref="member">BUS_CONFIG</ref>,<sp/>*<ref refid="kmdf__bus__common_8h_1aeca4f45b7bb946c601f53bb52b08b170" kindref="member">PBUS_CONFIG</ref>;</highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Common<sp/>bus<sp/>transfer<sp/>packet<sp/>structure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="struct__BUS__TRANSFER__PACKET" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__BUS__TRANSFER__PACKET" kindref="compound">_BUS_TRANSFER_PACKET</ref><sp/>{</highlight></codeline>
<codeline lineno="43" refid="struct__BUS__TRANSFER__PACKET_1ab2d70f7d13f1b96499d1ed9fa11b881b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__BUS__TRANSFER__PACKET_1ab2d70f7d13f1b96499d1ed9fa11b881b" kindref="member">Buffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Data<sp/>buffer</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="struct__BUS__TRANSFER__PACKET_1acd85870608d805b91015bd3e0c4302c4" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>SIZE_T<sp/><ref refid="struct__BUS__TRANSFER__PACKET_1acd85870608d805b91015bd3e0c4302c4" kindref="member">BufferLength</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Buffer<sp/>length</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="struct__BUS__TRANSFER__PACKET_1ab3b945cbfe042fe6d4cf96ab9517ae6a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__BUS__TRANSFER__PACKET_1ab3b945cbfe042fe6d4cf96ab9517ae6a" kindref="member">Context</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>context</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46" refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1ab61a790fb09aa3a337c89ea002b5a76f" kindref="member">BUS_TRANSFER_STATUS</ref><sp/><ref refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" kindref="member">Status</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Transfer<sp/>status</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47" refid="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" kindref="member">BUS_TRANSFER_PACKET</ref>,<sp/>*<ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref>;</highlight></codeline>
<codeline lineno="48"><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Bus<sp/>operation<sp/>callback<sp/>function<sp/>type</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/>VOID<sp/>(*<ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref>)(</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref><sp/>TransferPacket</highlight></codeline>
<codeline lineno="52"><highlight class="normal">);</highlight></codeline>
<codeline lineno="53"><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Common<sp/>bus<sp/>interface<sp/>function<sp/>declarations</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="56" refid="kmdf__bus__common_8h_1ad5aa0e171a9d72e28ab08d06935ab2f5" refkind="member"><highlight class="normal"><ref refid="kmdf__bus__common_8h_1ad5aa0e171a9d72e28ab08d06935ab2f5" kindref="member">BusInitialize</ref>(</highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1aeca4f45b7bb946c601f53bb52b08b170" kindref="member">PBUS_CONFIG</ref><sp/>BusConfig</highlight></codeline>
<codeline lineno="59"><highlight class="normal">);</highlight></codeline>
<codeline lineno="60"><highlight class="normal"></highlight></codeline>
<codeline lineno="61"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="62" refid="kmdf__bus__common_8h_1a2878b61cfb78224301b7d25175aa243e" refkind="member"><highlight class="normal"><ref refid="kmdf__bus__common_8h_1a2878b61cfb78224301b7d25175aa243e" kindref="member">BusUninitialize</ref>(</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="64"><highlight class="normal">);</highlight></codeline>
<codeline lineno="65"><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="67" refid="kmdf__bus__common_8h_1a79b8f6be307f48971e34bb6cabfba958" refkind="member"><highlight class="normal"><ref refid="kmdf__bus__common_8h_1a79b8f6be307f48971e34bb6cabfba958" kindref="member">BusTransferSynchronous</ref>(</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Timeout</highlight></codeline>
<codeline lineno="71"><highlight class="normal">);</highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="74" refid="kmdf__bus__common_8h_1af23a4d40f37f1cf45dd8b2fc40cf6dff" refkind="member"><highlight class="normal"><ref refid="kmdf__bus__common_8h_1af23a4d40f37f1cf45dd8b2fc40cf6dff" kindref="member">BusTransferAsynchronous</ref>(</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref><sp/>CompletionCallback,</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="79"><highlight class="normal">);</highlight></codeline>
<codeline lineno="80"><highlight class="normal"></highlight></codeline>
<codeline lineno="81"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="82" refid="kmdf__bus__common_8h_1a4f54d258241aeda6b5d01ee12110870f" refkind="member"><highlight class="normal"><ref refid="kmdf__bus__common_8h_1a4f54d258241aeda6b5d01ee12110870f" kindref="member">BusCancelTransfer</ref>(</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kindref="member">PBUS_TRANSFER_PACKET</ref><sp/>TransferPacket</highlight></codeline>
<codeline lineno="85"><highlight class="normal">);</highlight></codeline>
<codeline lineno="86"><highlight class="normal"></highlight></codeline>
<codeline lineno="87"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_BUS_COMMON_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h"/>
  </compounddef>
</doxygen>
