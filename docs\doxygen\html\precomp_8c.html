<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/precomp.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('precomp_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">precomp.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="precomp_8h_source.html">precomp.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for precomp.c:</div>
<div class="dyncontent">
<div class="center"><img src="precomp_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2precomp_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2precomp_8c" id="aC_1_2KMDF_01Driver1_2src_2precomp_8c">
<area shape="rect" title=" " alt="" coords="1198,5,1344,48"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="1230,96,1312,123"/>
<area shape="poly" title=" " alt="" coords="1274,49,1274,80,1268,80,1268,49"/>
<area shape="rect" title=" " alt="" coords="5,171,89,197"/>
<area shape="poly" title=" " alt="" coords="1230,113,1052,115,776,123,446,141,275,155,107,173,104,174,103,168,107,168,274,150,445,136,775,118,1052,110,1230,108"/>
<area shape="rect" title=" " alt="" coords="698,501,762,528"/>
<area shape="poly" title=" " alt="" coords="1289,121,1335,164,1363,194,1388,228,1409,265,1422,303,1424,342,1419,361,1411,380,1394,400,1370,418,1340,434,1304,448,1219,471,1123,488,1023,500,927,508,777,515,777,510,927,503,1023,495,1122,483,1218,466,1302,443,1337,429,1367,414,1390,396,1406,377,1414,360,1418,342,1417,304,1404,267,1384,231,1358,198,1331,168,1285,125"/>
<area shape="rect" title=" " alt="" coords="870,427,923,453"/>
<area shape="poly" title=" " alt="" coords="1272,123,1265,159,1248,199,1200,262,1154,304,1101,339,1032,381,985,407,939,426,936,421,982,402,1030,376,1098,335,1151,300,1196,259,1243,196,1260,157,1267,122"/>
<area shape="rect" title=" " alt="" coords="1409,171,1493,197"/>
<area shape="poly" title=" " alt="" coords="1303,121,1406,162,1404,167,1301,126"/>
<area shape="rect" title=" " alt="" coords="1516,171,1599,197"/>
<area shape="poly" title=" " alt="" coords="1313,119,1502,166,1500,171,1312,124"/>
<area shape="rect" href="src_2core_2log_2driver__log_8h.html" title=" " alt="" coords="1258,344,1396,371"/>
<area shape="poly" title=" " alt="" coords="1278,122,1292,170,1325,328,1320,329,1287,171,1273,124"/>
<area shape="rect" href="error__handling_8h.html" title="驱动程序错误处理和断言宏定义" alt="" coords="41,344,213,371"/>
<area shape="poly" title=" " alt="" coords="1229,113,1094,116,895,124,662,142,542,156,425,173,342,181,274,184,244,190,214,200,184,219,153,247,141,265,133,286,128,329,123,328,128,285,136,263,149,244,181,215,212,196,242,185,273,179,341,176,424,168,541,151,661,137,895,119,1094,110,1229,108"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="865,245,984,288"/>
<area shape="poly" title=" " alt="" coords="1229,113,1140,115,1031,123,979,131,932,141,895,155,870,172,864,186,867,202,876,218,889,233,885,236,871,221,862,204,859,186,866,169,892,150,930,136,978,125,1031,118,1140,110,1229,108"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="163,245,294,288"/>
<area shape="poly" title=" " alt="" coords="1229,113,1109,116,936,125,735,143,632,156,532,173,406,205,297,242,295,237,404,200,530,168,631,151,734,137,936,119,1109,111,1229,108"/>
<area shape="rect" title=" " alt="" coords="1622,171,1757,197"/>
<area shape="poly" title=" " alt="" coords="1313,115,1608,166,1607,171,1312,120"/>
<area shape="rect" title=" " alt="" coords="1781,171,1908,197"/>
<area shape="poly" title=" " alt="" coords="1313,112,1504,134,1766,168,1765,173,1503,140,1312,118"/>
<area shape="rect" title=" " alt="" coords="1933,171,2060,197"/>
<area shape="poly" title=" " alt="" coords="1313,111,1564,131,1917,168,1917,173,1563,136,1312,116"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="543,171,714,197"/>
<area shape="poly" title=" " alt="" coords="1230,118,730,174,729,169,1229,113"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="881,171,1045,197"/>
<area shape="poly" title=" " alt="" coords="1230,123,1032,169,1031,164,1229,118"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1069,171,1233,197"/>
<area shape="poly" title=" " alt="" coords="1251,125,1186,165,1184,160,1249,121"/>
<area shape="poly" title=" " alt="" coords="1322,373,1290,415,1267,437,1240,456,1201,470,1149,482,1020,499,885,509,777,515,777,509,885,504,1019,494,1148,476,1200,465,1238,451,1264,433,1286,411,1317,370"/>
<area shape="poly" title=" " alt="" coords="1260,374,939,434,938,428,1259,369"/>
<area shape="poly" title=" " alt="" coords="110,373,88,390,69,411,59,432,59,442,65,451,76,459,96,466,157,479,334,496,530,506,682,510,682,515,530,511,333,502,156,484,95,471,74,464,61,455,54,444,53,431,64,408,84,386,106,369"/>
<area shape="poly" title=" " alt="" coords="214,365,855,432,854,437,213,370"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="289,427,448,453"/>
<area shape="poly" title=" " alt="" coords="166,369,317,419,316,424,164,374"/>
<area shape="rect" title=" " alt="" coords="75,427,214,453"/>
<area shape="poly" title=" " alt="" coords="132,371,141,411,136,412,127,372"/>
<area shape="poly" title=" " alt="" coords="432,451,683,501,682,507,431,457"/>
<area shape="poly" title=" " alt="" coords="935,288,945,330,946,355,942,379,933,399,921,416,916,413,928,396,936,378,940,355,939,331,930,289"/>
<area shape="poly" title=" " alt="" coords="865,283,750,309,697,324,659,338,643,349,633,359,622,370,606,381,535,409,464,427,462,422,533,404,603,376,619,366,629,355,640,344,656,334,695,319,749,304,864,278"/>
<area shape="rect" title=" " alt="" coords="866,344,926,371"/>
<area shape="poly" title=" " alt="" coords="920,289,907,330,902,328,915,288"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="669,344,790,371"/>
<area shape="poly" title=" " alt="" coords="880,291,772,340,770,335,878,286"/>
<area shape="poly" title=" " alt="" coords="732,371,732,486,727,486,727,371"/>
<area shape="poly" title=" " alt="" coords="757,369,858,417,856,422,755,374"/>
<area shape="poly" title=" " alt="" coords="230,288,230,324,236,369,251,414,263,434,278,451,308,469,351,482,404,493,463,500,583,509,682,511,682,516,583,514,462,506,403,498,350,487,305,473,275,455,258,437,246,416,230,370,225,325,224,288"/>
<area shape="poly" title=" " alt="" coords="264,286,352,332,406,356,461,376,571,402,681,420,779,430,854,435,854,440,779,435,680,425,570,408,460,381,404,361,349,337,261,291"/>
<area shape="poly" title=" " alt="" coords="247,287,351,413,347,416,243,290"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="472,336,592,379"/>
<area shape="poly" title=" " alt="" coords="295,284,458,332,456,337,293,290"/>
<area shape="poly" title=" " alt="" coords="593,369,855,427,854,432,592,375"/>
<area shape="poly" title=" " alt="" coords="491,382,409,422,407,417,489,377"/>
<area shape="poly" title=" " alt="" coords="543,195,487,203,432,214,386,229,370,237,358,247,353,257,352,265,359,287,386,333,422,372,465,406,511,434,558,458,605,476,683,500,682,505,603,481,556,463,508,439,462,411,419,376,382,336,354,289,346,266,348,255,354,244,367,233,384,224,431,209,486,198,542,190"/>
<area shape="poly" title=" " alt="" coords="711,195,760,213,784,227,805,243,819,260,828,276,837,307,842,340,856,377,879,413,875,416,851,380,837,341,832,308,823,278,815,263,801,247,781,231,758,218,709,200"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="369,253,498,280"/>
<area shape="poly" title=" " alt="" coords="599,200,479,250,477,245,597,195"/>
<area shape="poly" title=" " alt="" coords="450,279,500,323,496,327,446,283"/>
<area shape="poly" title=" " alt="" coords="981,196,1001,217,1017,244,1026,282,1023,313,1011,344,993,380,971,423,957,439,936,456,897,477,855,493,778,511,776,506,854,488,895,473,934,451,954,435,967,420,989,377,1006,342,1018,312,1021,283,1013,246,997,220,977,200"/>
<area shape="poly" title=" " alt="" coords="974,197,987,218,998,245,1001,267,998,288,986,337,962,380,927,418,923,415,958,377,981,335,992,288,995,267,992,246,982,221,970,199"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="522,253,644,280"/>
<area shape="poly" title=" " alt="" coords="891,201,657,248,642,251,641,246,656,243,890,195"/>
<area shape="poly" title=" " alt="" coords="578,282,554,324,549,321,573,279"/>
<area shape="poly" title=" " alt="" coords="1149,199,1128,247,1091,317,1038,392,1006,426,970,456,922,481,871,498,821,508,778,514,777,508,820,503,870,493,920,476,967,451,1002,422,1034,389,1086,314,1123,245,1144,197"/>
<area shape="poly" title=" " alt="" coords="1145,199,1086,280,1042,332,993,381,935,421,932,416,989,377,1038,328,1082,277,1141,196"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="668,253,791,280"/>
<area shape="poly" title=" " alt="" coords="1085,201,807,253,806,248,1084,195"/>
<area shape="poly" title=" " alt="" coords="702,283,593,332,591,327,700,278"/>
</map>
</div>
</div></div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="precomp_8c.html">precomp.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
