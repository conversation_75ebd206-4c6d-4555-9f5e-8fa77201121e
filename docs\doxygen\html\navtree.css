#nav-tree .children_ul {
  margin:0;
  padding:4px;
}

#nav-tree ul {
  list-style:none outside none;
  margin:0px;
  padding:0px;
}

#nav-tree li {
  white-space:nowrap;
  margin:0;
  padding:0;
}

#nav-tree .plus {
  margin:0px;
}

#nav-tree .selected {
  position: relative;
  background-color: var(--nav-menu-active-bg);
  border-radius: 0 6px 6px 0;
  /*margin-right: 5px;*/
}

#nav-tree img {
  margin:0px;
  padding:0px;
  border:0px;
  vertical-align: middle;
}

#nav-tree a {
  text-decoration:none;
  padding:0px;
  margin:0px;
}

#nav-tree .label {
  margin:0px;
  padding:0px;
  font: 12px var(--font-family-nav);
  line-height: 22px;
}

#nav-tree .label a {
  padding:2px;
}

#nav-tree .selected a {
  text-decoration:none;
  color:var(--page-link-color);
}

#nav-tree .children_ul {
  margin:0px;
  padding:0px;
}

#nav-tree .item {
  margin: 0 6px 0 -5px;
  padding: 0 0 0 5px;
  height: 22px;
}

#nav-tree {
  padding: 0px 0px;
  font-size:14px;
  overflow:auto;
}

#doc-content {
  overflow:auto;
  display:block;
  padding:0px;
  margin:0px;
  -webkit-overflow-scrolling : touch; /* iOS 5+ */
}

#side-nav {
  padding:0 6px 0 0;
  margin: 0px;
  display:block;
  position: absolute;
  left: 0px;
  overflow : hidden;
}

.ui-resizable .ui-resizable-handle {
  display:block;
}

.ui-resizable-e {
  transition: opacity 0.5s ease;
  background-color: var(--nav-splitbar-bg-color);
  opacity:0;
  cursor:col-resize;
  height:100%;
  right:0;
  top:0;
  width:6px;
  position: relative;
}

.ui-resizable-e:after {
  content: '';
  display: block;
  top: 50%;
  left: 1px;
  width: 2px;
  height: 15px;
  border-left: 1px solid var(--nav-splitbar-handle-color);
  border-right: 1px solid var(--nav-splitbar-handle-color);
  position: absolute;
}

.ui-resizable-e:hover {
  opacity: 1;
}

.ui-resizable-handle {
  display:none;
  font-size:0.1px;
  position:absolute;
  z-index:1;
}

#nav-tree-contents {
  margin: 6px 0px 0px 0px;
}

#nav-tree {
  background-color: var(--nav-background-color);
  -webkit-overflow-scrolling : touch; /* iOS 5+ */
  scrollbar-width: thin;
  border-right: 1px solid var(--nav-border-color);
  padding-left: 5px;
}

#nav-sync {
  position:absolute;
  top:0px;
  right:0px;
  z-index:1;
}

#nav-sync img {
  opacity:0.3;
}

div.nav-sync-icon {
	position: relative;
	width: 24px;
	height: 17px;
	left: -6px;
	top: -1px;
	opacity: 0.7;
	display: inline-block;
	background-color: var(--sync-icon-background-color);
	border: 1px solid var(--sync-icon-border-color);
	box-sizing: content-box;
}

div.nav-sync-icon:hover {
	background-color: var(--sync-icon-selected-background-color);
	opacity: 1.0;
}

div.nav-sync-icon.active:after {
	content: '';
	background-color: var(--sync-icon-background-color);
	border-top: 2px solid var(--sync-icon-color);
	position: absolute;
	width: 16px;
	height: 0px;
	top: 7px;
	left: 4px;
}

div.nav-sync-icon.active:hover:after {
	border-top: 2px solid var(--sync-icon-selected-color);
}

span.sync-icon-left {
	position: absolute;
	padding: 0;
	margin: 0;
	top: 3px;
	left: 4px;
	display: inline-block;
	width: 8px;
	height: 8px;
	border-left: 2px solid var(--sync-icon-color);
	border-top: 2px solid var(--sync-icon-color);
	transform: rotate(-45deg);
}

span.sync-icon-right {
	position: absolute;
	padding: 0;
	margin: 0;
	top: 3px;
	left: 10px;
	display: inline-block;
	width: 8px;
	height: 8px;
	border-right: 2px solid var(--sync-icon-color);
	border-bottom: 2px solid var(--sync-icon-color);
	transform: rotate(-45deg);
}

div.nav-sync-icon:hover span.sync-icon-left {
	border-left: 2px solid var(--sync-icon-selected-color);
	border-top: 2px solid var(--sync-icon-selected-color);
}

div.nav-sync-icon:hover span.sync-icon-right {
	border-right: 2px solid var(--sync-icon-selected-color);
	border-bottom: 2px solid var(--sync-icon-selected-color);
}

#nav-path ul {
  border-top: 1px solid var(--nav-breadcrumb-separator-color);
}

@media print
{
  #nav-tree { display: none; }
  div.ui-resizable-handle { display: none; position: relative; }
}

/*---------------------------*/
#container {
	display: grid;
	grid-template-columns: auto auto;
	overflow: hidden;
}

#page-nav {
	background: var(--nav-background-color);
	display: block;
	width: 250px;
	box-sizing: content-box;
        position: relative;
	border-left: 1px solid var(--nav-border-color);
}

#page-nav-tree {
	display: inline-block;
}

#page-nav-resize-handle {
  transition: opacity 0.5s ease;
  background-color: var(--nav-splitbar-bg-color);
  opacity:0;
  cursor:col-resize;
  height:100%;
  right:0;
  top:0;
  width:6px;
  position: relative;
  z-index: 1;
  user-select: none;
}

#page-nav-resize-handle:after {
  content: '';
  display: block;
  top: 50%;
  left: 1px;
  width: 2px;
  height: 15px;
  border-left: 1px solid var(--nav-splitbar-handle-color);
  border-right: 1px solid var(--nav-splitbar-handle-color);
  position: absolute;
}

#page-nav-resize-handle.dragging,
#page-nav-resize-handle:hover {
  opacity: 1;
}

#page-nav-contents {
  padding: 0;
  margin: 0;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  position: absolute;
  overflow: auto;
  scrollbar-width: thin;
  -webkit-overflow-scrolling : touch; /* iOS 5+ */
}

ul.page-outline,
ul.page-outline ul {
  text-indent: 0;
  list-style: none outside none;
  padding: 0 0 0 4px;
}

ul.page-outline {
  margin: 0 4px 4px 6px;
}

ul.page-outline div.item {
  font: 12px var(--font-family-nav);
  line-height: 22px;
}

ul.page-outline li {
  white-space: nowrap;
}

ul.page-outline li.vis {
  background-color: var(--nav-breadcrumb-active-bg);
}

#container.resizing {
  cursor: col-resize;
  user-select: none;
}
