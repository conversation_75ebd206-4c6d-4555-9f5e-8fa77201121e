<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="struct__GPIO__PIN__CONTEXT" kind="struct" language="C++" prot="public">
    <compoundname>_GPIO_PIN_CONTEXT</compoundname>
    <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1a44db5b39e5c14ce83ac0637da253fb47" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_DPC</type>
        <definition>EVT_WDF_INTERRUPT_DPC _GPIO_PIN_CONTEXT::EvtGpioInterruptDpc</definition>
        <argsstring></argsstring>
        <name>EvtGpioInterruptDpc</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::EvtGpioInterruptDpc</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="52" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="52" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1a4b4c364430ee4f37c01413a6552df5d0" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_ISR</type>
        <definition>EVT_WDF_INTERRUPT_ISR _GPIO_PIN_CONTEXT::EvtGpioInterruptIsr</definition>
        <argsstring></argsstring>
        <name>EvtGpioInterruptIsr</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::EvtGpioInterruptIsr</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="51" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="51" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1ae1602e3f0f02ccfe7b30fdaa89b017a7" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref></type>
        <definition>WDFSPINLOCK _GPIO_PIN_CONTEXT::Lock</definition>
        <argsstring></argsstring>
        <name>Lock</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::Lock</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="37" column="17" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="37" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1a4dd51420a9c389d77c721c25d4349927" prot="public" static="no" mutable="no">
        <type>PGPIO_PIN_CONTEXT</type>
        <definition>PGPIO_PIN_CONTEXT _GPIO_PIN_CONTEXT::Pins[256]</definition>
        <argsstring>[256]</argsstring>
        <name>Pins</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::Pins</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="36" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="36" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1a81f98266b74a3a8d80a632eead026240" prot="public" static="no" mutable="no">
        <type>WDFIOTARGET</type>
        <definition>WDFIOTARGET _GPIO_PIN_CONTEXT::SpbIoTarget</definition>
        <argsstring></argsstring>
        <name>SpbIoTarget</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::SpbIoTarget</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="33" column="17" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="33" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return _GPIO_PIN_CONTEXT::STATUS_INVALID_PARAMETER</definition>
        <argsstring></argsstring>
        <name>STATUS_INVALID_PARAMETER</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::STATUS_INVALID_PARAMETER</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="109" column="16" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="109" bodyend="-1"/>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">GPIOInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="struct__GPIO__PIN__CONTEXT_1a9e971cde7006142d4b5ac56689228e0b" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>WDFDEVICE _GPIO_PIN_CONTEXT::WdfDevice</definition>
        <argsstring></argsstring>
        <name>WdfDevice</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::WdfDevice</qualifiedname>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="32" column="15" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="32" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="public-func">
      <memberdef kind="function" id="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" prot="public" static="no" const="no" explicit="no" inline="yes" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS _GPIO_PIN_CONTEXT::GPIOInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig)</argsstring>
        <name>GPIOInitialize</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::GPIOInitialize</qualifiedname>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref></type>
          <declname>GpioConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="58" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="58" bodyend="77"/>
        <references refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" compoundref="gpio__core_8c" startline="176">attributes</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" compoundref="gpio__core_8c" startline="169">interruptConfig</references>
        <references refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" compoundref="gpio__core_8c" startline="152">openParams</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" compoundref="gpio__core_8c" startline="153">spbDevicePath</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" compoundref="gpio__core_8c" startline="109">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" prot="public" static="no" const="no" explicit="no" inline="yes" virt="non-virtual">
        <type></type>
        <definition>_GPIO_PIN_CONTEXT::if</definition>
        <argsstring>(gpioManager==NULL)</argsstring>
        <name>if</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::if</qualifiedname>
        <param>
          <type>gpioManager</type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="80" column="5" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="80" bodyend="101"/>
        <references refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" compoundref="gpio__core_8c" startline="176">attributes</references>
        <references refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454">LogError</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>_GPIO_PIN_CONTEXT::LogError</definition>
        <argsstring>(ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;PinNumber)</argsstring>
        <name>LogError</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::LogError</qualifiedname>
        <param>
          <type>ERROR_PIN_ALREADY_REGISTERED</type>
        </param>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;Pin %d is already configured or out of range&quot;</type>
        </param>
        <param>
          <type>GpioConfig-&gt;</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="106" column="9"/>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">GPIOInitialize</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" compoundref="gpio__core_8c" startline="80" endline="101">if</referencedby>
      </memberdef>
      <memberdef kind="function" id="struct__GPIO__PIN__CONTEXT_1a846739f679fc9ddea85cb263aa72dc9f" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>_GPIO_PIN_CONTEXT::WdfSpinLockRelease</definition>
        <argsstring>(gpioManager-&gt;Lock)</argsstring>
        <name>WdfSpinLockRelease</name>
        <qualifiedname>_GPIO_PIN_CONTEXT::WdfSpinLockRelease</qualifiedname>
        <param>
          <type>gpioManager-&gt;</type>
          <declname>Lock</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="108" column="9"/>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="31" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="31" bodyend="110"/>
    <listofallmembers>
      <member refid="struct__GPIO__PIN__CONTEXT_1a44db5b39e5c14ce83ac0637da253fb47" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>EvtGpioInterruptDpc</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a4b4c364430ee4f37c01413a6552df5d0" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>EvtGpioInterruptIsr</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>GPIOInitialize</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>if</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1ae1602e3f0f02ccfe7b30fdaa89b017a7" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>Lock</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>LogError</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a4dd51420a9c389d77c721c25d4349927" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>Pins</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a81f98266b74a3a8d80a632eead026240" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>SpbIoTarget</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>STATUS_INVALID_PARAMETER</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a9e971cde7006142d4b5ac56689228e0b" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>WdfDevice</name></member>
      <member refid="struct__GPIO__PIN__CONTEXT_1a846739f679fc9ddea85cb263aa72dc9f" prot="public" virt="non-virtual"><scope>_GPIO_PIN_CONTEXT</scope><name>WdfSpinLockRelease</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>
