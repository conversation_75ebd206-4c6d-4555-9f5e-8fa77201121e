<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/core/driver/driver_core.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('driver__core_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">driver_core.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="driver__core_8h_source.html">../../../include/core/driver/driver_core.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="device__manager_8h_source.html">../../../include/core/device/device_manager.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for driver_core.c:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2core_2driver_2driver__core_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2core_2driver_2driver__core_8c" id="aC_1_2KMDF_01Driver1_2src_2core_2driver_2driver__core_8c">
<area shape="rect" title=" " alt="" coords="369,5,514,64"/>
<area shape="rect" title=" " alt="" coords="262,368,327,395"/>
<area shape="poly" title=" " alt="" coords="511,62,543,83,570,110,594,144,608,176,610,210,596,247,572,278,544,304,511,326,475,343,404,366,342,378,341,373,403,361,473,338,508,321,540,300,568,274,591,244,605,209,603,178,589,147,566,114,539,87,508,67"/>
<area shape="rect" title=" " alt="" coords="369,293,423,320"/>
<area shape="poly" title=" " alt="" coords="482,63,524,100,562,146,576,171,583,196,582,222,570,247,543,273,508,291,472,301,439,306,438,301,471,296,506,286,539,269,566,244,576,221,578,197,571,173,558,149,520,103,479,67"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="321,112,457,155"/>
<area shape="poly" title=" " alt="" coords="428,66,410,100,405,97,424,63"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="41,293,183,320"/>
<area shape="poly" title=" " alt="" coords="368,41,273,47,166,60,117,69,73,81,38,96,15,114,11,124,9,139,11,177,20,217,32,244,50,266,74,283,70,287,47,270,27,247,15,218,6,178,3,139,5,122,11,110,35,92,71,76,115,64,166,54,272,42,368,35"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="434,211,555,237"/>
<area shape="poly" title=" " alt="" coords="455,64,472,111,492,195,487,196,467,113,450,65"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="25,112,199,155"/>
<area shape="poly" title=" " alt="" coords="369,60,198,110,196,105,367,55"/>
<area shape="poly" title=" " alt="" coords="393,155,398,278,392,278,387,155"/>
<area shape="rect" title=" " alt="" coords="247,211,307,237"/>
<area shape="poly" title=" " alt="" coords="365,157,307,203,303,199,362,153"/>
<area shape="poly" title=" " alt="" coords="384,156,361,202,343,227,322,247,293,265,262,279,199,297,198,292,260,274,291,261,318,243,339,223,356,199,379,154"/>
<area shape="poly" title=" " alt="" coords="415,153,470,198,466,203,412,157"/>
<area shape="poly" title=" " alt="" coords="145,318,249,359,247,364,143,323"/>
<area shape="poly" title=" " alt="" coords="492,239,472,279,456,302,436,322,390,351,342,370,341,365,388,347,433,318,452,298,467,276,487,236"/>
<area shape="poly" title=" " alt="" coords="481,240,424,286,421,282,477,236"/>
<area shape="poly" title=" " alt="" coords="76,157,51,178,32,204,18,236,11,264,15,290,31,318,45,330,66,339,123,356,188,367,247,374,247,379,187,372,122,361,64,344,42,334,27,322,10,292,6,264,13,234,27,201,47,174,73,153"/>
<area shape="poly" title=" " alt="" coords="135,153,236,243,297,272,355,292,354,297,295,277,233,248,132,157"/>
<area shape="poly" title=" " alt="" coords="82,157,59,178,42,204,37,224,42,244,57,265,77,282,73,286,53,269,38,246,31,224,37,202,55,174,78,153"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="52,203,172,245"/>
<area shape="poly" title=" " alt="" coords="115,155,115,187,109,187,109,155"/>
<area shape="poly" title=" " alt="" coords="173,240,355,291,354,296,172,245"/>
<area shape="poly" title=" " alt="" coords="115,246,115,277,109,277,109,246"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FDRIVER_5FCONTEXT_5Fstruct_5F_5FDRIVER_5F_5FCONTEXT" id="r__5FDRIVER_5FCONTEXT_5Fstruct_5F_5FDRIVER_5F_5FCONTEXT"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__DRIVER__CONTEXT">_DRIVER_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:af11aade3f3741fb554915d10d3f514eb" id="r_af11aade3f3741fb554915d10d3f514eb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af11aade3f3741fb554915d10d3f514eb">INITGUID</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a3171f2b55fc099beecef32f0d09d5d5b" id="r_a3171f2b55fc099beecef32f0d09d5d5b"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__DRIVER__CONTEXT">_DRIVER_CONTEXT</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3171f2b55fc099beecef32f0d09d5d5b">DRIVER_CONTEXT</a></td></tr>
<tr class="memitem:a703dc89b9de7ff2f89e1467ae811be52" id="r_a703dc89b9de7ff2f89e1467ae811be52"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__DRIVER__CONTEXT">_DRIVER_CONTEXT</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a703dc89b9de7ff2f89e1467ae811be52">PDRIVER_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aa0699c5861e30ca66c92343c6fe22011" id="r_aa0699c5861e30ca66c92343c6fe22011"><td class="memItemLeft" align="right" valign="top">void NTAPI&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa0699c5861e30ca66c92343c6fe22011">DirectISR</a> (<a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">IN</a> PKINTERRUPT Interrupt, <a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">IN</a> PVOID Context)</td></tr>
<tr class="memitem:abc51f0e6ed5304a27afddf92da4720e6" id="r_abc51f0e6ed5304a27afddf92da4720e6"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abc51f0e6ed5304a27afddf92da4720e6">DriverCoreAddDevice</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:aac97f3e68a787ac88617369283c60b79" id="r_aac97f3e68a787ac88617369283c60b79"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aac97f3e68a787ac88617369283c60b79">DriverCoreCleanup</a> (VOID)</td></tr>
<tr class="memitem:a7eca1538f567ab5a5add60bb6d2d03b5" id="r_a7eca1538f567ab5a5add60bb6d2d03b5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">PDRIVER_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7eca1538f567ab5a5add60bb6d2d03b5">DriverCoreGetConfiguration</a> (VOID)</td></tr>
<tr class="memitem:a24d4768bc415635465e00a5f5a3b4187" id="r_a24d4768bc415635465e00a5f5a3b4187"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a24d4768bc415635465e00a5f5a3b4187">DriverCoreGetDevice</a> (_In_ ULONG Index)</td></tr>
<tr class="memitem:a222752d15e46b43b3a4ae0c787d4018d" id="r_a222752d15e46b43b3a4ae0c787d4018d"><td class="memItemLeft" align="right" valign="top">ULONG&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a222752d15e46b43b3a4ae0c787d4018d">DriverCoreGetDeviceCount</a> (VOID)</td></tr>
<tr class="memitem:acdb452dbcae039af8967376463c758b9" id="r_acdb452dbcae039af8967376463c758b9"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acdb452dbcae039af8967376463c758b9">DriverCoreInitialize</a> (_In_ <a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a> Driver, _In_ <a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">PDRIVER_CONFIG</a> DriverConfig)</td></tr>
<tr class="memitem:ae610e53c6df75743831a2e87ef9e746b" id="r_ae610e53c6df75743831a2e87ef9e746b"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae610e53c6df75743831a2e87ef9e746b">DriverCoreProcessIoControl</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="core__types_8h.html#a5bbb7f7db295e12f4f24ca6ed92554f3">WDFREQUEST</a> Request, _In_ ULONG IoControlCode, _In_opt_ PVOID InputBuffer, _In_ SIZE_T InputBufferLength, _Out_opt_ PVOID OutputBuffer, _In_ SIZE_T OutputBufferLength, _Out_opt_ PSIZE_T BytesReturned)</td></tr>
<tr class="memitem:a89627789114e389118ee51bda8684ab6" id="r_a89627789114e389118ee51bda8684ab6"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a89627789114e389118ee51bda8684ab6">DriverCoreRemoveDevice</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:abd136e6aae106e58c949c867cbb92a41" id="r_abd136e6aae106e58c949c867cbb92a41"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="#a3171f2b55fc099beecef32f0d09d5d5b">DRIVER_CONTEXT</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abd136e6aae106e58c949c867cbb92a41">g_DriverContext</a> = {0}</td></tr>
<tr class="memitem:a7ca8aeb3b88c194b24cfb4066bcc60e7" id="r_a7ca8aeb3b88c194b24cfb4066bcc60e7"><td class="memItemLeft" align="right" valign="top">FAST_MUTEX&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7ca8aeb3b88c194b24cfb4066bcc60e7">gLock</a></td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__DRIVER__CONTEXT" id="struct__DRIVER__CONTEXT"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__DRIVER__CONTEXT">&#9670;&#160;</a></span>_DRIVER_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _DRIVER_CONTEXT</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><div class="dynheader">
Collaboration diagram for _DRIVER_CONTEXT:</div>
<div class="dyncontent">
<div class="center"><img src="struct__DRIVER__CONTEXT__coll__graph.png" border="0" usemap="#a__DRIVER__CONTEXT_coll__map" loading="lazy" alt="Collaboration graph"/></div>
<map name="a__DRIVER__CONTEXT_coll__map" id="a__DRIVER__CONTEXT_coll__map">
<area shape="rect" title=" " alt="" coords="5,203,157,229"/>
<area shape="rect" href="driver__core_8h.html#struct__DRIVER__CONFIG" title=" " alt="" coords="11,104,151,131"/>
<area shape="poly" title=" " alt="" coords="84,146,84,202,78,202,78,146"/>
<area shape="rect" href="driver__core_8h.html#struct__DRIVER__VERSION" title=" " alt="" coords="7,5,155,32"/>
<area shape="poly" title=" " alt="" coords="84,48,84,104,78,104,78,48"/>
</map>
<center><span class="legend">[<a target="top" href="graph_legend.html">legend</a>]</span></center></div>
<table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a47ece9e7bf05f77968bf674a4342396a" name="a47ece9e7bf05f77968bf674a4342396a"></a>ULONG</td>
<td class="fieldname">
DeviceCount</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a008388fc65c89b1ae3992fc6a4aa27f3" name="a008388fc65c89b1ae3992fc6a4aa27f3"></a><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>
<td class="fieldname">
Devices[<a class="el" href="driver__core_8h.html#adb9000833a6dada8f3fd1e267c320b2b">MAX_DRIVER_DEVICES</a>]</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a36a8e8b359a2c287561b83b1fee106f8" name="a36a8e8b359a2c287561b83b1fee106f8"></a><a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a></td>
<td class="fieldname">
Driver</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aa7ddcfc01ffa413221702e66f502fc1d" name="aa7ddcfc01ffa413221702e66f502fc1d"></a><a class="el" href="driver__core_8h.html#ab3f3478bc4dcb804303c86d537979d13">DRIVER_CONFIG</a></td>
<td class="fieldname">
DriverConfig</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="af11aade3f3741fb554915d10d3f514eb" name="af11aade3f3741fb554915d10d3f514eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af11aade3f3741fb554915d10d3f514eb">&#9670;&#160;</a></span>INITGUID</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define INITGUID</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a3171f2b55fc099beecef32f0d09d5d5b" name="a3171f2b55fc099beecef32f0d09d5d5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3171f2b55fc099beecef32f0d09d5d5b">&#9670;&#160;</a></span>DRIVER_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__DRIVER__CONTEXT">_DRIVER_CONTEXT</a> <a class="el" href="#a3171f2b55fc099beecef32f0d09d5d5b">DRIVER_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a703dc89b9de7ff2f89e1467ae811be52" name="a703dc89b9de7ff2f89e1467ae811be52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a703dc89b9de7ff2f89e1467ae811be52">&#9670;&#160;</a></span>PDRIVER_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__DRIVER__CONTEXT">_DRIVER_CONTEXT</a> * <a class="el" href="#a703dc89b9de7ff2f89e1467ae811be52">PDRIVER_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="aa0699c5861e30ca66c92343c6fe22011" name="aa0699c5861e30ca66c92343c6fe22011"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa0699c5861e30ca66c92343c6fe22011">&#9670;&#160;</a></span>DirectISR()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NTAPI DirectISR </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">IN</a> PKINTERRUPT</td>          <td class="paramname"><span class="paramname"><em>Interrupt</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="precomp_8h.html#ac2bbd6d630a06a980d9a92ddb9a49928">IN</a> PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abc51f0e6ed5304a27afddf92da4720e6" name="abc51f0e6ed5304a27afddf92da4720e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc51f0e6ed5304a27afddf92da4720e6">&#9670;&#160;</a></span>DriverCoreAddDevice()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS DriverCoreAddDevice </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreAddDevice - 鍚戦┍鍔ㄦ牳蹇冩坊鍔犺澶? * </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>瑕佹坊鍔犵殑璁惧</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_cgraph.png" border="0" usemap="#adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_cgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_cgraph" id="adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,154,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="202,31,272,57"/>
<area shape="poly" title=" " alt="" coords="154,41,186,41,186,47,154,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="320,5,448,32"/>
<area shape="poly" title=" " alt="" coords="272,35,304,30,305,35,273,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="336,56,432,83"/>
<area shape="poly" title=" " alt="" coords="273,47,322,56,321,61,272,53"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_icgraph.png" border="0" usemap="#adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_icgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_icgraph" id="adriver__core_8c_abc51f0e6ed5304a27afddf92da4720e6_icgraph">
<area shape="rect" title=" " alt="" coords="329,5,477,32"/>
<area shape="rect" href="driver__entry_8c.html#a0776c179fdcbdd09df07ee264e7e78e6" title=" " alt="" coords="141,5,281,32"/>
<area shape="poly" title=" " alt="" coords="313,21,282,21,282,16,313,16"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,5,93,32"/>
<area shape="poly" title=" " alt="" coords="125,21,93,21,93,16,125,16"/>
</map>
</div>

</div>
</div>
<a id="aac97f3e68a787ac88617369283c60b79" name="aac97f3e68a787ac88617369283c60b79"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac97f3e68a787ac88617369283c60b79">&#9670;&#160;</a></span>DriverCoreCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID DriverCoreCleanup </td>
          <td>(</td>
          <td class="paramtype">VOID</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreCleanup - 娓呯悊椹卞姩鏍稿績璧勬簮 </p>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_aac97f3e68a787ac88617369283c60b79_cgraph.png" border="0" usemap="#adriver__core_8c_aac97f3e68a787ac88617369283c60b79_cgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_aac97f3e68a787ac88617369283c60b79_cgraph" id="adriver__core_8c_aac97f3e68a787ac88617369283c60b79_cgraph">
<area shape="rect" title=" " alt="" coords="5,5,138,32"/>
<area shape="rect" href="gpio__core_8c.html#aa5ccd638c5bf670b734784f2601b7ec7" title=" " alt="" coords="186,5,297,32"/>
<area shape="poly" title=" " alt="" coords="138,16,170,16,170,21,138,21"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_aac97f3e68a787ac88617369283c60b79_icgraph.png" border="0" usemap="#adriver__core_8c_aac97f3e68a787ac88617369283c60b79_icgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_aac97f3e68a787ac88617369283c60b79_icgraph" id="adriver__core_8c_aac97f3e68a787ac88617369283c60b79_icgraph">
<area shape="rect" title=" " alt="" coords="306,5,438,32"/>
<area shape="rect" href="driver__entry_8c.html#a075700d7117ddde115f3bb0db54b619e" title=" " alt="" coords="141,5,258,32"/>
<area shape="poly" title=" " alt="" coords="290,21,258,21,258,16,290,16"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,5,93,32"/>
<area shape="poly" title=" " alt="" coords="125,21,93,21,93,16,125,16"/>
</map>
</div>

</div>
</div>
<a id="a7eca1538f567ab5a5add60bb6d2d03b5" name="a7eca1538f567ab5a5add60bb6d2d03b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7eca1538f567ab5a5add60bb6d2d03b5">&#9670;&#160;</a></span>DriverCoreGetConfiguration()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">PDRIVER_CONFIG</a> DriverCoreGetConfiguration </td>
          <td>(</td>
          <td class="paramtype">VOID</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreGetConfiguration - 鑾峰彇椹卞姩閰嶇疆</p>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">PDRIVER_CONFIG</a> 椹卞姩閰嶇疆 </dd></dl>

</div>
</div>
<a id="a24d4768bc415635465e00a5f5a3b4187" name="a24d4768bc415635465e00a5f5a3b4187"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a24d4768bc415635465e00a5f5a3b4187">&#9670;&#160;</a></span>DriverCoreGetDevice()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> DriverCoreGetDevice </td>
          <td>(</td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Index</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreGetDevice - 鏍规嵁绱㈠紩鑾峰彇璁惧</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Index</td><td>璁惧绱㈠紩</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> 璁惧瀵硅薄 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_a24d4768bc415635465e00a5f5a3b4187_cgraph.png" border="0" usemap="#adriver__core_8c_a24d4768bc415635465e00a5f5a3b4187_cgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_a24d4768bc415635465e00a5f5a3b4187_cgraph" id="adriver__core_8c_a24d4768bc415635465e00a5f5a3b4187_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,152,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="200,31,270,57"/>
<area shape="poly" title=" " alt="" coords="152,41,184,41,184,47,152,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="318,5,446,32"/>
<area shape="poly" title=" " alt="" coords="270,35,302,30,303,35,271,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,56,430,83"/>
<area shape="poly" title=" " alt="" coords="271,47,320,56,319,61,270,53"/>
</map>
</div>

</div>
</div>
<a id="a222752d15e46b43b3a4ae0c787d4018d" name="a222752d15e46b43b3a4ae0c787d4018d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a222752d15e46b43b3a4ae0c787d4018d">&#9670;&#160;</a></span>DriverCoreGetDeviceCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ULONG DriverCoreGetDeviceCount </td>
          <td>(</td>
          <td class="paramtype">VOID</td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreGetDeviceCount - 鑾峰彇椹卞姩绋嬪簭绠＄悊鐨勮澶囨暟閲? * </p><dl class="section return"><dt>Returns</dt><dd>ULONG 璁惧鏁伴噺 </dd></dl>

</div>
</div>
<a id="acdb452dbcae039af8967376463c758b9" name="acdb452dbcae039af8967376463c758b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acdb452dbcae039af8967376463c758b9">&#9670;&#160;</a></span>DriverCoreInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS DriverCoreInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a></td>          <td class="paramname"><span class="paramname"><em>Driver</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">PDRIVER_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>DriverConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreInitialize - 鍒濆鍖栭┍鍔ㄦ牳蹇冩ā鍧? * </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Driver</td><td>椹卞姩瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">DriverConfig</td><td>椹卞姩閰嶇疆</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_acdb452dbcae039af8967376463c758b9_cgraph.png" border="0" usemap="#adriver__core_8c_acdb452dbcae039af8967376463c758b9_cgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_acdb452dbcae039af8967376463c758b9_cgraph" id="adriver__core_8c_acdb452dbcae039af8967376463c758b9_cgraph">
<area shape="rect" title=" " alt="" coords="5,93,138,120"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="209,56,279,83"/>
<area shape="poly" title=" " alt="" coords="135,90,193,78,194,83,137,95"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="186,119,302,145"/>
<area shape="poly" title=" " alt="" coords="138,114,171,119,170,124,138,119"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="350,5,478,32"/>
<area shape="poly" title=" " alt="" coords="279,56,352,34,353,39,280,62"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="366,56,462,83"/>
<area shape="poly" title=" " alt="" coords="280,67,351,67,351,72,280,72"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="362,131,467,157"/>
<area shape="poly" title=" " alt="" coords="303,133,346,137,346,142,302,139"/>
<area shape="poly" title=" " alt="" coords="385,132,380,122,383,112,395,106,414,104,435,107,446,113,443,118,433,112,414,109,397,111,387,116,385,121,389,129"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_acdb452dbcae039af8967376463c758b9_icgraph.png" border="0" usemap="#adriver__core_8c_acdb452dbcae039af8967376463c758b9_icgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_acdb452dbcae039af8967376463c758b9_icgraph" id="adriver__core_8c_acdb452dbcae039af8967376463c758b9_icgraph">
<area shape="rect" title=" " alt="" coords="141,5,273,32"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,5,93,32"/>
<area shape="poly" title=" " alt="" coords="125,21,93,21,93,16,125,16"/>
</map>
</div>

</div>
</div>
<a id="ae610e53c6df75743831a2e87ef9e746b" name="ae610e53c6df75743831a2e87ef9e746b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae610e53c6df75743831a2e87ef9e746b">&#9670;&#160;</a></span>DriverCoreProcessIoControl()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS DriverCoreProcessIoControl </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a5bbb7f7db295e12f4f24ca6ed92554f3">WDFREQUEST</a></td>          <td class="paramname"><span class="paramname"><em>Request</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>IoControlCode</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>InputBuffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ SIZE_T</td>          <td class="paramname"><span class="paramname"><em>InputBufferLength</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>OutputBuffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ SIZE_T</td>          <td class="paramname"><span class="paramname"><em>OutputBufferLength</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PSIZE_T</td>          <td class="paramname"><span class="paramname"><em>BytesReturned</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreProcessIoControl - 澶勭悊椹卞姩绋嬪簭绾у埆鐨処O鎺у埗璇锋眰</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Request</td><td>璇锋眰瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">IoControlCode</td><td>IO鎺у埗鐮? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">InputBuffer</td><td>杈撳叆缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">InputBufferLength</td><td>杈撳叆缂撳啿鍖哄ぇ灏? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">OutputBuffer</td><td>杈撳嚭缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">OutputBufferLength</td><td>杈撳嚭缂撳啿鍖哄ぇ灏? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesReturned</td><td>杩斿洖鐨勫瓧鑺傛暟</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>

</div>
</div>
<a id="a89627789114e389118ee51bda8684ab6" name="a89627789114e389118ee51bda8684ab6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a89627789114e389118ee51bda8684ab6">&#9670;&#160;</a></span>DriverCoreRemoveDevice()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS DriverCoreRemoveDevice </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>DriverCoreRemoveDevice - 浠庨┍鍔ㄦ牳蹇冪Щ闄よ澶? * </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>瑕佺Щ闄ょ殑璁惧</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="driver__core_8c_a89627789114e389118ee51bda8684ab6_cgraph.png" border="0" usemap="#adriver__core_8c_a89627789114e389118ee51bda8684ab6_cgraph" loading="lazy" alt=""/></div>
<map name="adriver__core_8c_a89627789114e389118ee51bda8684ab6_cgraph" id="adriver__core_8c_a89627789114e389118ee51bda8684ab6_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,180,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="228,31,298,57"/>
<area shape="poly" title=" " alt="" coords="180,41,212,41,212,47,180,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="346,5,474,32"/>
<area shape="poly" title=" " alt="" coords="298,35,330,30,331,35,299,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="362,56,458,83"/>
<area shape="poly" title=" " alt="" coords="299,47,348,56,347,61,298,53"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="abd136e6aae106e58c949c867cbb92a41" name="abd136e6aae106e58c949c867cbb92a41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd136e6aae106e58c949c867cbb92a41">&#9670;&#160;</a></span>g_DriverContext</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a3171f2b55fc099beecef32f0d09d5d5b">DRIVER_CONTEXT</a> g_DriverContext = {0}</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a id="a7ca8aeb3b88c194b24cfb4066bcc60e7" name="a7ca8aeb3b88c194b24cfb4066bcc60e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ca8aeb3b88c194b24cfb4066bcc60e7">&#9670;&#160;</a></span>gLock</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">FAST_MUTEX gLock</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_aebb8dcc11953d78e620bbef0b9e2183.html">core</a></li><li class="navelem"><a href="dir_67b4da34d574a8dd96cb9765195127fb.html">driver</a></li><li class="navelem"><a href="driver__core_8c.html">driver_core.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
