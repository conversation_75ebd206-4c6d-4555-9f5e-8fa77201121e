var searchData=
[
  ['getdriverversion_0',['GetDriverVersion',['../driver__core_8h.html#a6c0d04af706f5b9f31c9698344dc6104',1,'driver_core.h']]],
  ['getlasterrorfromntstatus_1',['GetLastErrorFromNTStatus',['../error__handling_8c.html#afd059b655e91e68d030b34f16ee84647',1,'error_handling.c']]],
  ['gpiodevicecleanup_2',['GpioDeviceCleanup',['../gpio__device_8c.html#ac3d3347067d7be6497e71b6ba4b389c2',1,'GpioDeviceCleanup(_In_ WDFDEVICE Device):&#160;gpio_device.c'],['../gpio__device_8h.html#ac3d3347067d7be6497e71b6ba4b389c2',1,'GpioDeviceCleanup(_In_ WDFDEVICE Device):&#160;gpio_device.c']]],
  ['gpiodevicegetstate_3',['GpioDeviceGetState',['../gpio__device_8c.html#a0bd26e3410adfbb26bba326602f8fec6',1,'GpioDeviceGetState(_In_ WDFDEVICE Device, _Out_ PGPIO_DEVICE_STATE State):&#160;gpio_device.c'],['../gpio__device_8h.html#a0bd26e3410adfbb26bba326602f8fec6',1,'GpioDeviceGetState(_In_ WDFDEVICE Device, _Out_ PGPIO_DEVICE_STATE State):&#160;gpio_device.c']]],
  ['gpiodeviceinitialize_4',['GpioDeviceInitialize',['../gpio__device_8c.html#a21428e126bf8c8996ea3405e6a8be2f2',1,'GpioDeviceInitialize(_In_ WDFDEVICE Device, _In_ PGPIO_DEVICE_CONFIG GpioConfig):&#160;gpio_device.c'],['../gpio__device_8h.html#a21428e126bf8c8996ea3405e6a8be2f2',1,'GpioDeviceInitialize(_In_ WDFDEVICE Device, _In_ PGPIO_DEVICE_CONFIG GpioConfig):&#160;gpio_device.c']]],
  ['gpiodeviceinterruptcallback_5',['GpioDeviceInterruptCallback',['../gpio__device_8c.html#a9b4681b0c2d7f8009a387b9104735752',1,'GpioDeviceInterruptCallback(WDFDEVICE Device, ULONG PinNumber, BOOLEAN PinValue, PVOID Context):&#160;gpio_device.c'],['../gpio__device_8c.html#a3d13bafdbad06cbdfb7f43c4c1b098d4',1,'GpioDeviceInterruptCallback(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context):&#160;gpio_device.c']]],
  ['gpiodevicepulse_6',['GpioDevicePulse',['../gpio__device_8c.html#a1e860d4292f8df84e5d3101f8a415d6c',1,'GpioDevicePulse(_In_ WDFDEVICE Device, _In_ ULONG PulseDurationMs):&#160;gpio_device.c'],['../gpio__device_8h.html#a1e860d4292f8df84e5d3101f8a415d6c',1,'GpioDevicePulse(_In_ WDFDEVICE Device, _In_ ULONG PulseDurationMs):&#160;gpio_device.c']]],
  ['gpiodeviceregistercallback_7',['GpioDeviceRegisterCallback',['../gpio__device_8c.html#a6448a7e48d735f67501f3273b75485fd',1,'GpioDeviceRegisterCallback(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT_CALLBACK Callback, _In_opt_ PVOID Context):&#160;gpio_device.c'],['../gpio__device_8h.html#a6448a7e48d735f67501f3273b75485fd',1,'GpioDeviceRegisterCallback(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_EVENT_CALLBACK Callback, _In_opt_ PVOID Context):&#160;gpio_device.c']]],
  ['gpiodevicesetstate_8',['GpioDeviceSetState',['../gpio__device_8c.html#a731812dec996a670e7d557a282535d3d',1,'GpioDeviceSetState(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_STATE State):&#160;gpio_device.c'],['../gpio__device_8h.html#a731812dec996a670e7d557a282535d3d',1,'GpioDeviceSetState(_In_ WDFDEVICE Device, _In_ GPIO_DEVICE_STATE State):&#160;gpio_device.c']]],
  ['gpiodeviceunregistercallback_9',['GpioDeviceUnregisterCallback',['../gpio__device_8c.html#afbd2bd91a99c594504ca275fd8b45825',1,'GpioDeviceUnregisterCallback(_In_ WDFDEVICE Device):&#160;gpio_device.c'],['../gpio__device_8h.html#afbd2bd91a99c594504ca275fd8b45825',1,'GpioDeviceUnregisterCallback(_In_ WDFDEVICE Device):&#160;gpio_device.c']]],
  ['gpiodisableinterrupt_10',['GPIODisableInterrupt',['../kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4',1,'kmdf_gpio.h']]],
  ['gpioenableinterrupt_11',['GPIOEnableInterrupt',['../kmdf__gpio_8h.html#a818408e823499cbc8bf3a09f74062a48',1,'kmdf_gpio.h']]],
  ['gpiogetvalue_12',['GPIOGetValue',['../kmdf__gpio_8h.html#adcddf2e62a93fe5cc3fa7f46b67845bb',1,'kmdf_gpio.h']]],
  ['gpioinitialize_13',['GPIOInitialize',['../struct__GPIO__PIN__CONTEXT.html#ab343a956248ebb07de6a72eaeb55ec35',1,'_GPIO_PIN_CONTEXT::GPIOInitialize()'],['../gpio__core_8c.html#a50ea4c2976c29c52387cd273dc289c3b',1,'GPIOInitialize(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig):&#160;gpio_core.c'],['../kmdf__gpio_8h.html#a0a73c23c89291af0e81cef7098d10e29',1,'GPIOInitialize(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig):&#160;gpio_core.c']]],
  ['gpiopulse_14',['GPIOPulse',['../kmdf__gpio_8h.html#ade1cb652014fc4a3984567ff49900d81',1,'kmdf_gpio.h']]],
  ['gpiosetdirection_15',['GPIOSetDirection',['../kmdf__gpio_8h.html#aab159dfcef06f528d7ebdb9fa3ce8be4',1,'kmdf_gpio.h']]],
  ['gpiosetvalue_16',['GPIOSetValue',['../kmdf__gpio_8h.html#a81265230a3fa84b9f3a7851d8c9ebe3d',1,'kmdf_gpio.h']]],
  ['gpiouninitialize_17',['GPIOUninitialize',['../gpio__core_8c.html#a785f00e9c0879fb478077d2cdce99906',1,'GPIOUninitialize(_In_ WDFDEVICE Device, _In_ ULONG PinNumber):&#160;gpio_core.c'],['../gpio__device_8c.html#a6184bcc868fec6d16949da5c95315be4',1,'GPIOUninitialize(Device, deviceContext-&gt;Config.PinNumber):&#160;gpio_device.c'],['../kmdf__gpio_8h.html#a785f00e9c0879fb478077d2cdce99906',1,'GPIOUninitialize(_In_ WDFDEVICE Device, _In_ ULONG PinNumber):&#160;gpio_core.c']]]
];
