<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li class="current"><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>CallbackContext&#160;:&#160;<a class="el" href="gpio__device_8c.html#af02ced4aae3ad21bbe67ffd9742cda5b">gpio_device.c</a></li>
<li>CheckDriverStatus()&#160;:&#160;<a class="el" href="driver__core_8h.html#a27946e799a0fbc6a45703d48bb3fddba">driver_core.h</a></li>
<li>CleanupDriverResources()&#160;:&#160;<a class="el" href="driver__core_8h.html#a84dc8b7aa97027c9d340f5fccc1d0f77">driver_core.h</a></li>
<li>Config&#160;:&#160;<a class="el" href="gpio__device_8c.html#afee8ca080129faeb1d5683d9b67a1aa6">gpio_device.c</a></li>
<li>ConfigurationMemory&#160;:&#160;<a class="el" href="i2c__device_8c.html#aff83b2530e0944d77d4ee0965b41ad89">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#aff83b2530e0944d77d4ee0965b41ad89">spi_device.c</a></li>
<li>CORE_CONFIG&#160;:&#160;<a class="el" href="core__types_8h.html#a87cde496e586af832ac8cd9897ee9197">core_types.h</a></li>
<li>CORE_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="core__types_8h.html#a6c3de50719f00d174c32bab4750ec712">core_types.h</a></li>
<li>CORE_DEVICE_STATUS&#160;:&#160;<a class="el" href="core__types_8h.html#afea8ea357b75e588745d32feff9f0a78">core_types.h</a></li>
<li>CORE_REQUEST_CONTEXT&#160;:&#160;<a class="el" href="core__types_8h.html#ac55b20222f4500db849f020c28e22922">core_types.h</a></li>
<li>CORE_STATISTICS&#160;:&#160;<a class="el" href="core__types_8h.html#a181d21ad9f4ed9573f73613950ff1e57">core_types.h</a></li>
<li>CORE_STATUS&#160;:&#160;<a class="el" href="core__types_8h.html#a2d8b5745731e293616acee4bc7c56d44">core_types.h</a></li>
<li>CoreManagerGetDeviceStatistics()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a62afb7081a8227cdd45b31bc0f4a29bf">CoreManager.h</a></li>
<li>CoreManagerInitialize()&#160;:&#160;<a class="el" href="CoreManager_8h.html#ab30fe932990f8d1244364662da1011d4">CoreManager.h</a></li>
<li>CoreManagerProcessRequest()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a3946620ba635d952a5f8b4fcb10c565a">CoreManager.h</a></li>
<li>CoreManagerRegisterCallback()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a4a6465946715026f03a5d7d24727166c">CoreManager.h</a></li>
<li>CoreManagerRegisterDevice()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a4d8a0547e9b41f1c0da4acfa1b4c81c3">CoreManager.h</a></li>
<li>CoreManagerSetDevicePnPState()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a1a77e92013ea9206b995f17c9b8d52ec">CoreManager.h</a></li>
<li>CoreManagerUninitialize()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a438db4d6ac84cc12dc907619b1f02189">CoreManager.h</a></li>
<li>CoreManagerUnregisterCallback()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a25011824e6d6aae1bf197d4e7c1259d8">CoreManager.h</a></li>
<li>CoreManagerUnregisterDevice()&#160;:&#160;<a class="el" href="CoreManager_8h.html#a6a00c4e7db194d151210817e52953d37">CoreManager.h</a></li>
<li>CurrentState&#160;:&#160;<a class="el" href="gpio__device_8c.html#a351f54e1c120ac29191cd55267d05e00">gpio_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
