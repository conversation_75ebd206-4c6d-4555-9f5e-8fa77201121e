var searchData=
[
  ['lastpinvalue_0',['LastPinValue',['../gpio__device_8c.html#af4f2a770562d41e27d370140ab383393',1,'gpio_device.c']]],
  ['length_1',['Length',['../hal__interface_8h.html#aba2a9c6c8c77e03f83ef8bf543612275',1,'_HAL_RESOURCE.u.Memory::Length'],['../hal__interface_8h.html#aba2a9c6c8c77e03f83ef8bf543612275',1,'_HAL_RESOURCE.u.Port::Length']]],
  ['list_5fentry_2',['LIST_ENTRY',['../core__types_8h.html#afe2819542d515b3631539098349c6d52',1,'core_types.h']]],
  ['listentry_3',['ListEntry',['../core__types_8h.html#a559291b7d96c7f687cc512a50cf451e2',1,'_CORE_REQUEST_CONTEXT']]],
  ['lock_4',['Lock',['../struct__GPIO__PIN__CONTEXT.html#ae1602e3f0f02ccfe7b30fdaa89b017a7',1,'_GPIO_PIN_CONTEXT::Lock'],['../gpio__core_8c.html#ac91080baa5062b15cd46c8e08028fd51',1,'Lock:&#160;gpio_core.c']]],
  ['log_5falert_5',['LOG_ALERT',['../src_2core_2log_2driver__log_8h.html#aa1911455782e83f3b06fab600be0e43e',1,'driver_log.h']]],
  ['log_5falert_5fif_6',['LOG_ALERT_IF',['../src_2core_2log_2driver__log_8h.html#a4c42b3fa94110619ab8458eb672d189d',1,'driver_log.h']]],
  ['log_5fconfig_7',['LOG_CONFIG',['../src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d',1,'LOG_CONFIG:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d',1,'LOG_CONFIG:&#160;driver_log.h']]],
  ['log_5fcritical_8',['LOG_CRITICAL',['../src_2core_2log_2driver__log_8h.html#abc03884460a6987df33fea0d5cae8302',1,'driver_log.h']]],
  ['log_5fcritical_5fif_9',['LOG_CRITICAL_IF',['../src_2core_2log_2driver__log_8h.html#ab48ce4a2ee7f0b5f74153fedf6ad7c25',1,'driver_log.h']]],
  ['log_5fdebug_10',['LOG_DEBUG',['../src_2core_2log_2driver__log_8h.html#acfe39a25e08737b535dc881071ebf149',1,'LOG_DEBUG:&#160;driver_log.h'],['../Common_8h.html#acfe39a25e08737b535dc881071ebf149',1,'LOG_DEBUG:&#160;Common.h'],['../include_2core_2log_2driver__log_8h.html#abd0b0523397fb05f0ed46fc217fb630f',1,'LOG_DEBUG:&#160;driver_log.h']]],
  ['log_5fdebug_5fif_11',['LOG_DEBUG_IF',['../src_2core_2log_2driver__log_8h.html#ae930e4b3ae4e59dc6a7b6a4feefb116f',1,'driver_log.h']]],
  ['log_5femergency_12',['LOG_EMERGENCY',['../src_2core_2log_2driver__log_8h.html#ad706db1253940848e01bbc71ede868ef',1,'driver_log.h']]],
  ['log_5femergency_5fif_13',['LOG_EMERGENCY_IF',['../src_2core_2log_2driver__log_8h.html#a570937723f42dd301b24b631ec455b58',1,'driver_log.h']]],
  ['log_5ferror_14',['LOG_ERROR',['../src_2core_2log_2driver__log_8h.html#a29e75b488d8e8ef5641c5bd16709faec',1,'LOG_ERROR:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#abffaf9cecb61026cac6db71a16ace9c5',1,'LOG_ERROR:&#160;driver_log.h']]],
  ['log_5ferror_5fif_15',['LOG_ERROR_IF',['../src_2core_2log_2driver__log_8h.html#aff0a0cc082f6b2fad9ed0979da6e8a9b',1,'driver_log.h']]],
  ['log_5finfo_16',['LOG_INFO',['../src_2core_2log_2driver__log_8h.html#a7748b322eafa9e058c518fef49b110cb',1,'LOG_INFO:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a89681da4efde0b54dc7f2839665082c8',1,'LOG_INFO:&#160;driver_log.h']]],
  ['log_5finfo_5fif_17',['LOG_INFO_IF',['../src_2core_2log_2driver__log_8h.html#a94d7f96857344352ffbc6ee65e9f2390',1,'driver_log.h']]],
  ['log_5flevel_18',['LOG_LEVEL',['../src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7',1,'LOG_LEVEL:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7',1,'LOG_LEVEL:&#160;driver_log.h']]],
  ['log_5fnotice_19',['LOG_NOTICE',['../src_2core_2log_2driver__log_8h.html#a05bf2404451e701f51d18409e72321fd',1,'driver_log.h']]],
  ['log_5fnotice_5fif_20',['LOG_NOTICE_IF',['../src_2core_2log_2driver__log_8h.html#a6cf4240fc51cea71e901acf9df797b98',1,'driver_log.h']]],
  ['log_5ftrace_21',['LOG_TRACE',['../src_2core_2log_2driver__log_8h.html#a0972af62c9ad7b688924604669d7d762',1,'driver_log.h']]],
  ['log_5ftrace_5fif_22',['LOG_TRACE_IF',['../src_2core_2log_2driver__log_8h.html#a8793c218a97ef927e72271b80a872495',1,'driver_log.h']]],
  ['log_5ftype_23',['LOG_TYPE',['../include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66',1,'driver_log.h']]],
  ['log_5ftypes_24',['LOG_TYPES',['../src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591',1,'driver_log.h']]],
  ['log_5fverbose_25',['LOG_VERBOSE',['../include_2core_2log_2driver__log_8h.html#a6594ece0df59e19da1473edfc079fd45',1,'driver_log.h']]],
  ['log_5fwarning_26',['LOG_WARNING',['../src_2core_2log_2driver__log_8h.html#a1dd05e1ef2b66fc68251edacaa75e9f7',1,'LOG_WARNING:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a1c60134b1702d179d9b86bc618f416fe',1,'LOG_WARNING:&#160;driver_log.h']]],
  ['log_5fwarning_5fif_27',['LOG_WARNING_IF',['../src_2core_2log_2driver__log_8h.html#a238f142a1b0fcbd8378c38d99b233baa',1,'driver_log.h']]],
  ['logcleanup_28',['LogCleanup',['../include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7',1,'driver_log.h']]],
  ['logconfiginit_29',['LogConfigInit',['../driver__log_8c.html#aa7f5f3b01615029c1cb54753c0b03175',1,'LogConfigInit(_Out_ PLOG_CONFIG LogConfig):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aa7f5f3b01615029c1cb54753c0b03175',1,'LogConfigInit(_Out_ PLOG_CONFIG LogConfig):&#160;driver_log.c'],['../include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a',1,'LogConfigInit(PLOG_CONFIG LogConfig):&#160;driver_log.h']]],
  ['logerror_30',['LogError',['../struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454',1,'_GPIO_PIN_CONTEXT::LogError()'],['../error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e',1,'LogError(_In_ NTSTATUS Status, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Message):&#160;error_handling.c'],['../gpio__core_8c.html#a9f7acfc8c5f9d7d32170223f628f766b',1,'LogError(ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;PinNumber):&#160;gpio_core.c']]],
  ['logfilepath_31',['LogFilePath',['../include_2core_2log_2driver__log_8h.html#a21ca685eeee730a0d8a179892b840d5f',1,'_LOG_CONFIG']]],
  ['logfunctionentry_32',['LogFunctionEntry',['../include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81',1,'driver_log.h']]],
  ['logfunctionexit_33',['LogFunctionExit',['../include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740',1,'driver_log.h']]],
  ['loginfo_34',['LogInfo',['../include_2core_2log_2driver__log_8h.html#a1f2322291188e5d028f82ff2f340e5e3',1,'LogInfo:&#160;driver_log.h'],['../gpio__core_8c.html#a8f2aeb9f5f8525345d3f33df4340da13',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO pin %d initialized successfully&quot;, GpioConfig-&gt;PinNumber):&#160;gpio_core.c'],['../gpio__device_8c.html#ac410021632e38928f5eb4b3bb6939ab9',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO device initialized successfully, type=%d, pin=%d&quot;, GpioConfig-&gt;DeviceType, GpioConfig-&gt;PinNumber):&#160;gpio_device.c'],['../gpio__device_8c.html#a3156531cfce23bfe597c3aaad2f23aad',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO device resources cleaned up&quot;):&#160;gpio_device.c'],['../i2c__device_8c.html#a90ec17a9895e508ccdb9077fed539682',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C device initialized successfully&quot;):&#160;i2c_device.c'],['../i2c__device_8c.html#ae957556f2bac1175f6f4b37cd8f268f9',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C read succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesRead ? *BytesRead :0):&#160;i2c_device.c'],['../i2c__device_8c.html#a6e8f3cbefed6c462cd6392131f5f0a29',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C write succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesWritten ? *BytesWritten :0):&#160;i2c_device.c'],['../i2c__device_8c.html#aabdf6e2791329ae7f1d903b2c7b47add',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C transfer sequence succeeded, transfers: %d&quot;, TransferCount):&#160;i2c_device.c'],['../spi__device_8c.html#a92b63e772873a034bea01d26f382ed57',1,'LogInfo(__FUNCTION__, __LINE__, &quot;SPI device initialized successfully&quot;):&#160;spi_device.c'],['../spi__device_8c.html#aef91cc299dacb19ea73324acf8c8ff2c',1,'LogInfo(__FUNCTION__, __LINE__, &quot;SPI transfer succeeded&quot;):&#160;spi_device.c']]],
  ['loginitialize_35',['LogInitialize',['../driver__log_8c.html#aa2e9424857371175fc265253fbabcc5d',1,'LogInitialize(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aa2e9424857371175fc265253fbabcc5d',1,'LogInitialize(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig):&#160;driver_log.c'],['../include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9',1,'LogInitialize(PDRIVER_OBJECT DriverObject, PLOG_CONFIG LogConfig):&#160;driver_log.h']]],
  ['loglevel_36',['LogLevel',['../driver__core_8h.html#a462a9835cac3e90e89501fd667ee0dff',1,'_DRIVER_CONFIG::LogLevel'],['../include_2core_2log_2driver__log_8h.html#ab67683f62a123026ec78fe405cdbb177',1,'_LOG_CONFIG::LogLevel']]],
  ['loglevelalert_37',['LogLevelAlert',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c',1,'driver_log.h']]],
  ['loglevelcritical_38',['LogLevelCritical',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408',1,'driver_log.h']]],
  ['logleveldebug_39',['LogLevelDebug',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053',1,'LogLevelDebug:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053',1,'LogLevelDebug:&#160;driver_log.h']]],
  ['logleveldisabled_40',['LogLevelDisabled',['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16',1,'driver_log.h']]],
  ['loglevelemergency_41',['LogLevelEmergency',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131',1,'driver_log.h']]],
  ['loglevelerror_42',['LogLevelError',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e',1,'LogLevelError:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e',1,'LogLevelError:&#160;driver_log.h']]],
  ['loglevelinfo_43',['LogLevelInfo',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c',1,'LogLevelInfo:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c',1,'LogLevelInfo:&#160;driver_log.h']]],
  ['loglevelmax_44',['LogLevelMax',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f',1,'driver_log.h']]],
  ['loglevelnotice_45',['LogLevelNotice',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0',1,'driver_log.h']]],
  ['logleveltrace_46',['LogLevelTrace',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816',1,'driver_log.h']]],
  ['loglevelverbose_47',['LogLevelVerbose',['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b',1,'driver_log.h']]],
  ['loglevelwarning_48',['LogLevelWarning',['../src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180',1,'LogLevelWarning:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180',1,'LogLevelWarning:&#160;driver_log.h']]],
  ['logmessage_49',['LogMessage',['../include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1',1,'driver_log.h']]],
  ['logmessageva_50',['LogMessageVA',['../driver__log_8c.html#a8e8711da6408af7b3b313f892121215e',1,'LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#a8e8711da6408af7b3b313f892121215e',1,'LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args):&#160;driver_log.c']]],
  ['logsetlevel_51',['LogSetLevel',['../include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185',1,'driver_log.h']]],
  ['logtargets_52',['LogTargets',['../include_2core_2log_2driver__log_8h.html#aa4471d5ed80ebce68218e751798de8dc',1,'_LOG_CONFIG']]],
  ['logtypeall_53',['LogTypeAll',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee',1,'LogTypeAll:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee',1,'LogTypeAll:&#160;driver_log.h']]],
  ['logtypedebugger_54',['LogTypeDebugger',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127',1,'driver_log.h']]],
  ['logtypeetw_55',['LogTypeETW',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3',1,'LogTypeETW:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3',1,'LogTypeETW:&#160;driver_log.h']]],
  ['logtypefile_56',['LogTypeFile',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d',1,'LogTypeFile:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d',1,'LogTypeFile:&#160;driver_log.h']]],
  ['logtypekdprint_57',['LogTypeKdPrint',['../include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543',1,'driver_log.h']]],
  ['logtypenone_58',['LogTypeNone',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1',1,'driver_log.h']]],
  ['logtypes_59',['LogTypes',['../include_2core_2log_2driver__log_8h.html#a4b99237eff4b17c1d4fbfa9697528f70',1,'_LOG_CONFIG']]],
  ['logtypewpp_60',['LogTypeWPP',['../src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7',1,'LogTypeWPP:&#160;driver_log.h'],['../include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7',1,'LogTypeWPP:&#160;driver_log.h']]],
  ['loguninitialize_61',['LogUninitialize',['../driver__log_8c.html#aab8bcb7121136bc236fe5d55778fbaf2',1,'LogUninitialize(VOID):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aab8bcb7121136bc236fe5d55778fbaf2',1,'LogUninitialize(VOID):&#160;driver_log.c']]],
  ['logwarning_62',['LogWarning',['../include_2core_2log_2driver__log_8h.html#aa47a100aaaa86f29c113feda40125d64',1,'LogWarning:&#160;driver_log.h'],['../gpio__device_8c.html#a1fbae9652e19914386dbb4fc4848b63d',1,'LogWarning(__FUNCTION__, __LINE__, &quot;Failed to get initial GPIO pin value&quot;):&#160;gpio_device.c']]]
];
