<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/precomp.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('precomp_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">precomp.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;windows.h&gt;</code><br />
<code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &lt;ntstrsafe.h&gt;</code><br />
<code>#include &lt;ntintsafe.h&gt;</code><br />
<code>#include &quot;<a class="el" href="src_2core_2log_2driver__log_8h_source.html">core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__handling_8h_source.html">core/error/error_handling.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="driver__core_8h_source.html">core/driver/driver_core.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="device__manager_8h_source.html">core/device/device_manager.h</a>&quot;</code><br />
<code>#include &quot;hal/bus/gpio_core.h&quot;</code><br />
<code>#include &quot;hal/bus/i2c_core.h&quot;</code><br />
<code>#include &quot;hal/bus/spi_core.h&quot;</code><br />
<code>#include &quot;<a class="el" href="gpio__device_8h_source.html">hal/devices/gpio_device.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="i2c__device_8h_source.html">hal/devices/i2c_device.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="spi__device_8h_source.html">hal/devices/spi_device.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for precomp.h:</div>
<div class="dyncontent">
<div class="center"><img src="precomp_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2precomp_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2precomp_8h" id="aC_1_2KMDF_01Driver1_2src_2precomp_8h">
<area shape="rect" title=" " alt="" coords="1198,5,1344,48"/>
<area shape="rect" title=" " alt="" coords="5,96,89,123"/>
<area shape="poly" title=" " alt="" coords="1198,31,1007,36,737,46,425,66,264,81,107,99,104,99,103,94,107,93,264,75,424,61,737,41,1007,30,1198,26"/>
<area shape="rect" title=" " alt="" coords="698,427,762,453"/>
<area shape="poly" title=" " alt="" coords="1297,47,1345,93,1371,124,1394,158,1413,194,1424,231,1424,269,1411,305,1394,326,1370,343,1340,359,1304,373,1219,396,1123,413,1023,425,927,433,777,441,777,435,927,428,1023,420,1122,408,1218,391,1302,368,1337,354,1367,339,1390,322,1406,303,1419,268,1418,232,1408,196,1390,160,1367,127,1341,97,1294,50"/>
<area shape="rect" title=" " alt="" coords="870,352,923,379"/>
<area shape="poly" title=" " alt="" coords="1272,49,1264,85,1248,124,1216,167,1178,206,1136,240,1093,271,1008,319,939,351,936,346,1005,315,1090,266,1133,236,1174,202,1212,164,1243,121,1259,84,1266,48"/>
<area shape="rect" title=" " alt="" coords="1409,96,1493,123"/>
<area shape="poly" title=" " alt="" coords="1318,46,1410,87,1408,92,1316,51"/>
<area shape="rect" title=" " alt="" coords="1516,96,1599,123"/>
<area shape="poly" title=" " alt="" coords="1345,46,1502,90,1501,95,1343,51"/>
<area shape="rect" href="src_2core_2log_2driver__log_8h.html" title=" " alt="" coords="1258,269,1396,296"/>
<area shape="poly" title=" " alt="" coords="1279,47,1292,95,1325,253,1319,254,1287,97,1274,49"/>
<area shape="rect" href="error__handling_8h.html" title="驱动程序错误处理和断言宏定义" alt="" coords="41,269,213,296"/>
<area shape="poly" title=" " alt="" coords="1198,32,1055,37,862,48,644,68,425,99,342,107,274,110,244,115,214,126,184,144,153,172,141,191,133,212,128,254,123,253,128,211,136,188,149,169,181,140,212,121,242,110,273,105,341,101,424,93,644,63,862,43,1055,32,1198,27"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="865,171,984,213"/>
<area shape="poly" title=" " alt="" coords="1198,32,1108,37,1010,48,923,68,891,82,870,98,864,112,867,127,876,143,889,158,885,162,872,146,862,129,859,111,866,94,889,77,922,63,1009,43,1108,32,1198,27"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="163,171,294,213"/>
<area shape="poly" title=" " alt="" coords="1198,32,1071,38,905,49,718,69,532,99,406,131,297,167,296,162,405,126,530,93,718,63,904,44,1071,32,1198,27"/>
<area shape="rect" title=" " alt="" coords="1622,96,1757,123"/>
<area shape="poly" title=" " alt="" coords="1344,39,1609,90,1608,95,1343,45"/>
<area shape="rect" title=" " alt="" coords="1781,96,1908,123"/>
<area shape="poly" title=" " alt="" coords="1344,35,1766,93,1765,98,1344,40"/>
<area shape="rect" title=" " alt="" coords="1933,96,2060,123"/>
<area shape="poly" title=" " alt="" coords="1344,32,1594,56,1918,93,1917,98,1593,61,1344,37"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="543,96,714,123"/>
<area shape="poly" title=" " alt="" coords="1198,40,730,98,729,93,1198,34"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="881,96,1045,123"/>
<area shape="poly" title=" " alt="" coords="1199,50,1027,94,1026,89,1197,44"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1069,96,1233,123"/>
<area shape="poly" title=" " alt="" coords="1242,51,1184,89,1181,85,1239,46"/>
<area shape="poly" title=" " alt="" coords="1322,298,1290,340,1267,363,1240,381,1201,395,1149,407,1020,424,885,435,777,440,777,435,885,429,1019,419,1148,402,1200,390,1238,376,1264,359,1286,337,1317,295"/>
<area shape="poly" title=" " alt="" coords="1260,299,939,359,938,354,1259,294"/>
<area shape="poly" title=" " alt="" coords="110,299,88,316,69,336,59,358,59,367,65,377,76,384,96,392,157,404,334,422,530,431,682,435,682,441,530,437,333,427,156,409,95,397,74,389,61,381,54,369,53,357,64,333,84,312,106,294"/>
<area shape="poly" title=" " alt="" coords="214,290,855,357,854,362,213,296"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="289,352,448,379"/>
<area shape="poly" title=" " alt="" coords="166,294,317,344,316,349,164,299"/>
<area shape="rect" title=" " alt="" coords="75,352,214,379"/>
<area shape="poly" title=" " alt="" coords="132,296,141,337,136,338,127,297"/>
<area shape="poly" title=" " alt="" coords="432,377,683,427,682,432,431,382"/>
<area shape="poly" title=" " alt="" coords="935,213,945,256,946,280,942,305,933,324,921,341,916,338,928,322,936,303,940,280,939,256,930,214"/>
<area shape="poly" title=" " alt="" coords="865,208,750,235,697,250,659,264,643,274,633,285,622,296,606,306,535,334,464,352,462,347,533,329,603,302,619,291,629,281,640,270,656,259,695,244,749,229,864,203"/>
<area shape="rect" title=" " alt="" coords="866,269,926,296"/>
<area shape="poly" title=" " alt="" coords="920,215,907,255,902,254,915,213"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="669,269,790,296"/>
<area shape="poly" title=" " alt="" coords="880,216,772,265,770,260,878,212"/>
<area shape="poly" title=" " alt="" coords="732,297,732,411,727,411,727,297"/>
<area shape="poly" title=" " alt="" coords="757,294,858,343,856,348,755,299"/>
<area shape="poly" title=" " alt="" coords="230,214,230,250,236,294,251,339,263,359,278,377,308,394,351,408,404,418,463,426,583,434,682,436,682,442,583,439,462,431,403,423,350,413,305,399,275,381,258,363,246,342,230,295,225,250,224,214"/>
<area shape="poly" title=" " alt="" coords="264,212,352,258,406,282,461,301,571,328,681,345,779,355,854,360,854,365,779,360,680,350,570,333,460,307,404,287,349,263,261,216"/>
<area shape="poly" title=" " alt="" coords="247,212,351,338,347,342,243,216"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="472,261,592,304"/>
<area shape="poly" title=" " alt="" coords="295,210,458,257,456,262,293,215"/>
<area shape="poly" title=" " alt="" coords="593,295,855,352,854,357,592,300"/>
<area shape="poly" title=" " alt="" coords="491,307,409,347,407,342,489,302"/>
<area shape="poly" title=" " alt="" coords="543,121,487,128,432,139,386,154,370,163,358,172,353,182,352,191,359,212,386,258,422,298,465,331,511,360,558,383,605,401,683,426,682,431,603,406,556,388,508,364,462,336,419,302,382,261,354,214,346,191,348,180,354,169,367,158,384,149,431,134,486,123,542,115"/>
<area shape="poly" title=" " alt="" coords="711,121,760,139,784,152,805,169,819,185,828,201,837,232,842,265,856,303,879,338,875,341,851,305,837,266,832,233,823,203,815,188,801,173,781,156,758,143,709,126"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="369,179,498,205"/>
<area shape="poly" title=" " alt="" coords="599,126,479,175,477,170,597,121"/>
<area shape="poly" title=" " alt="" coords="450,204,500,249,496,253,446,208"/>
<area shape="poly" title=" " alt="" coords="981,121,1001,142,1017,170,1026,208,1023,239,1011,269,993,305,971,348,957,364,936,381,897,403,855,419,778,436,776,431,854,413,895,398,934,376,954,360,967,345,989,303,1006,267,1018,238,1021,208,1013,172,997,145,977,125"/>
<area shape="poly" title=" " alt="" coords="974,122,987,144,998,170,1001,192,998,214,986,262,962,306,927,344,923,340,958,302,981,260,992,213,995,192,992,171,982,146,970,125"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="522,179,644,205"/>
<area shape="poly" title=" " alt="" coords="891,126,657,173,642,177,641,172,656,168,890,121"/>
<area shape="poly" title=" " alt="" coords="578,207,554,249,549,246,573,205"/>
<area shape="poly" title=" " alt="" coords="1149,124,1128,173,1091,243,1038,317,1006,352,970,381,922,406,871,423,821,434,778,439,777,434,820,428,870,418,920,401,967,376,1002,348,1034,314,1086,240,1123,170,1144,122"/>
<area shape="poly" title=" " alt="" coords="1145,125,1086,205,1042,257,993,306,935,346,932,342,989,302,1038,253,1082,202,1141,122"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="668,179,791,205"/>
<area shape="poly" title=" " alt="" coords="1085,126,807,179,806,173,1084,121"/>
<area shape="poly" title=" " alt="" coords="702,208,593,257,591,252,700,204"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="precomp_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2precomp_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2precomp_8hdep" id="aC_1_2KMDF_01Driver1_2src_2precomp_8hdep">
<area shape="rect" title=" " alt="" coords="597,5,743,48"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="5,96,151,155"/>
<area shape="poly" title=" " alt="" coords="582,37,388,59,275,76,163,99,152,101,150,96,161,93,274,71,388,53,581,32"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,96,320,155"/>
<area shape="poly" title=" " alt="" coords="582,45,463,68,332,99,321,102,320,96,331,93,462,63,581,40"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,104,489,147"/>
<area shape="poly" title=" " alt="" coords="603,56,472,106,470,101,601,51"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="513,104,659,147"/>
<area shape="poly" title=" " alt="" coords="644,62,606,105,602,102,640,58"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="683,104,828,147"/>
<area shape="poly" title=" " alt="" coords="700,58,739,102,735,105,696,61"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="852,104,997,147"/>
<area shape="poly" title=" " alt="" coords="740,51,871,101,869,106,738,56"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1021,104,1167,147"/>
<area shape="poly" title=" " alt="" coords="758,40,878,63,1011,93,1036,101,1034,106,1009,99,877,68,757,45"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="1191,104,1336,147"/>
<area shape="poly" title=" " alt="" coords="759,32,953,53,1067,71,1180,93,1207,101,1206,106,1179,99,1066,76,952,59,758,37"/>
</map>
</div>
</div>
<p><a href="precomp_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aecf841e6d30b2565cffcb9e35531c7d4" id="r_aecf841e6d30b2565cffcb9e35531c7d4"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aecf841e6d30b2565cffcb9e35531c7d4">_AMD64_</a></td></tr>
<tr class="memitem:a0c797d6741e18c9edcef19d6049b8254" id="r_a0c797d6741e18c9edcef19d6049b8254"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0c797d6741e18c9edcef19d6049b8254">_KERNEL_MODE_</a></td></tr>
<tr class="memitem:ad1cbc0b24485be6857de50cce7e86e5d" id="r_ad1cbc0b24485be6857de50cce7e86e5d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad1cbc0b24485be6857de50cce7e86e5d">_WIN64</a></td></tr>
<tr class="memitem:a691c3e0b7c53581adffe6c2c00c2d3cf" id="r_a691c3e0b7c53581adffe6c2c00c2d3cf"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a691c3e0b7c53581adffe6c2c00c2d3cf">_X86_</a></td></tr>
<tr class="memitem:ac7bef5d85e3dcd73eef56ad39ffc84a9" id="r_ac7bef5d85e3dcd73eef56ad39ffc84a9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac7bef5d85e3dcd73eef56ad39ffc84a9">WIN32_LEAN_AND_MEAN</a></td></tr>
<tr class="memitem:ac655ab401ed399c26e30292b40ca5dea" id="r_ac655ab401ed399c26e30292b40ca5dea"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac655ab401ed399c26e30292b40ca5dea">WIN32_NO_STATUS</a></td></tr>
</table>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="aecf841e6d30b2565cffcb9e35531c7d4" name="aecf841e6d30b2565cffcb9e35531c7d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aecf841e6d30b2565cffcb9e35531c7d4">&#9670;&#160;</a></span>_AMD64_</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define _AMD64_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0c797d6741e18c9edcef19d6049b8254" name="a0c797d6741e18c9edcef19d6049b8254"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c797d6741e18c9edcef19d6049b8254">&#9670;&#160;</a></span>_KERNEL_MODE_</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define _KERNEL_MODE_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad1cbc0b24485be6857de50cce7e86e5d" name="ad1cbc0b24485be6857de50cce7e86e5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1cbc0b24485be6857de50cce7e86e5d">&#9670;&#160;</a></span>_WIN64</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define _WIN64</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a691c3e0b7c53581adffe6c2c00c2d3cf" name="a691c3e0b7c53581adffe6c2c00c2d3cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a691c3e0b7c53581adffe6c2c00c2d3cf">&#9670;&#160;</a></span>_X86_</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define _X86_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac7bef5d85e3dcd73eef56ad39ffc84a9" name="ac7bef5d85e3dcd73eef56ad39ffc84a9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7bef5d85e3dcd73eef56ad39ffc84a9">&#9670;&#160;</a></span>WIN32_LEAN_AND_MEAN</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define WIN32_LEAN_AND_MEAN</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac655ab401ed399c26e30292b40ca5dea" name="ac655ab401ed399c26e30292b40ca5dea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac655ab401ed399c26e30292b40ca5dea">&#9670;&#160;</a></span>WIN32_NO_STATUS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define WIN32_NO_STATUS</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="precomp_8h.html">precomp.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
