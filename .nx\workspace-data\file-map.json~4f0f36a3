{"version": "6.0", "nxVersion": "21.0.4", "pathMappings": {}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "tests/src/driver/KMDFI2C.c", "hash": "16086037633168955850"}, {"file": "tests/unit/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "tmp_structure/tools/utils/core/model_interface.py", "hash": "10371967563584055373"}, {"file": "sail-main/docs/development/spark-tests/index.md", "hash": "16259797288037715360"}, {"file": "tests/src/utils/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "tmp_structure/tests/unit/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "sail-main/docs/development/build/rust.md", "hash": "5400036514907610190"}, {"file": "docs/KMDF Driver1/backup/include/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "sail-main/docs/development/setup/java.md", "hash": "370557893682452616"}, {"file": "tmp_structure/tests/unit/simd_optimization.c", "hash": "1016262082760471380"}, {"file": "config/templates/teaching_prompt.json", "hash": "1945385970006069574"}, {"file": "tests/src/utils/test/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/format.rule", "hash": "1819760324823437288"}, {"file": "sail-main/tsconfig.json", "hash": "14962410771912716264"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/ALL_BUILD.vcxproj", "hash": "11506946039454903982"}, {"file": "sail-main/scripts/spark-tests/wait-for-server.sh", "hash": "5114675898960969918"}, {"file": "tmp_structure/tools/utils/run_checks.ps1", "hash": "13200448761716466509"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_array.rs", "hash": "11631046616003097547"}, {"file": "docs/project_md/generate_driver_comments.md", "hash": "2847487674231699790"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_core.md", "hash": "587477157269630570"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_resource.md", "hash": "474047599558141070"}, {"file": "tmp_structure/tests/unit/error_recovery.c", "hash": "6818450905492757794"}, {"file": "tests/src/utils/.gitkeep", "hash": "3244421341483603138"}, {"file": "tests/unit/driverentry.c", "hash": "15432365288492737233"}, {"file": "tmp_structure/tests/unit/device_io.c", "hash": "9774509186909211676"}, {"file": "convert_encoding.ps1", "hash": "16993032560780310748"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_types.md", "hash": "15916424518037135132"}, {"file": "build/.cmake/api/v1/reply/target-format-Release-81d6e7efb85862f3b20c.json", "hash": "5876608019943124009"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFHAL.md", "hash": "14106586863704401393"}, {"file": "docs/KMDF Driver1/tests/src/utils/ReliabilityManager.md", "hash": "9310313492205542628"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchDebug.md", "hash": "13795303564617599994"}, {"file": "tests/unit/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "tests/__pycache__/test_config_loader.cpython-314.pyc", "hash": "17131360598512678191"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_manager.md", "hash": "6803732204210283668"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "sail-main/python/pysail/tests/spark/test_join.txt", "hash": "15191621108841440868"}, {"file": "docs/MCP使用方法/sequential_thinking_mcp使用指南.md", "hash": "14143681468153017152"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "mace_requirements.txt", "hash": "1191718381784718678"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_operations.md", "hash": "4926486078439259071"}, {"file": "sail-main/python/pysail/data/tpch/queries/q1.sql", "hash": "1414355372117137577"}, {"file": "tests/unit/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "tests/src/utils/ml/resource_optimizer_1.c", "hash": "9728211600653272904"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_timer.md", "hash": "13275692677651774437"}, {"file": "docs/project_md/ai_collaboration_roles.py.teaching.md", "hash": "8825015382185079511"}, {"file": "tests/unit/prediction_context.c", "hash": "8771136116938717181"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_analyze_table.json", "hash": "8833131758356855088"}, {"file": "build/CPackConfig.cmake", "hash": "2896026288084098118"}, {"file": "tmp_structure/tests/unit/device_workitem.c", "hash": "15964808878924499639"}, {"file": "tests/unit/device_resource.c", "hash": "2198688305377271422"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_framework.md", "hash": "15481924532410518968"}, {"file": "tests/src/driver/driver_stubs.c", "hash": "770506472769498247"}, {"file": "tmp_structure/tests/unit/errormanager.c", "hash": "14480009171928608282"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92_RELWITHDEBINFO_loc", "hash": "4050502585630735714"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFLogger.md", "hash": "13534544793886613631"}, {"file": "docs/KMDF Driver1/tests/src/memory/memory_manager.md", "hash": "14583268610010245933"}, {"file": "build/PACKAGE.vcxproj", "hash": "1350041573716045945"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_health_monitor.md", "hash": "11811529164322313522"}, {"file": "tests/src/utils/ConfigManager.c", "hash": "12407172891497760359"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/hardware/KMDFHAL.md", "hash": "18089637407439543636"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.command.1.tlog", "hash": "14995931337178451584"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_manager_1.md", "hash": "5492128948262595846"}, {"file": "include/core/CoreManager.h", "hash": "7120741372824534432"}, {"file": "docs/驱动设计方案.txt", "hash": "13131682799839766864"}, {"file": "docs/KMDF Driver1/backup/include/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/spark_mask.rs", "hash": "15258739012453355599"}, {"file": "sail-main/crates/sail-execution/src/driver/actor/rpc.rs", "hash": "4015867552410587177"}, {"file": "build/CPackSourceConfig.cmake", "hash": "3177544699528980753"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_manager.md", "hash": "17363996027966417800"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": "sail-main/python/pysail/tests/spark/test_sort.py", "hash": "1048028229473194037"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ProcessFunctions.md", "hash": "5303515720344831466"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_manager.md", "hash": "17363996027966417800"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery_kmdf.md", "hash": "12638653863570197085"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "sail-main/python/pysail/examples/__init__.py", "hash": "3244421341483603138"}, {"file": "build/CMakeFiles/3.31.3/CMakeCCompiler.cmake", "hash": "13123115118070135955"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorHandler.md", "hash": "15475208645081206135"}, {"file": "tmp_structure/tools/utils/agents/semantic_agent.py", "hash": "12801280700392191334"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_io.md", "hash": "15280929294455826365"}, {"file": "sail-main/.github/actions/commit-workflow/action.yml", "hash": "12402291347137734673"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_evaluator.md", "hash": "8871547997162145722"}, {"file": "docs/KMDF Driver1/backup/include/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "tests/performance/performance.c", "hash": "542837662377689141"}, {"file": "tests/unit/coremanager.c", "hash": "9001638691650737173"}, {"file": "docs/KMDF Driver1/backup/include/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "tmp_structure/tools/utils/mace_system.py", "hash": "8537759706001371926"}, {"file": "tmp_structure/tools/utils/reorganize_project.py", "hash": "70675705915150794"}, {"file": ".github/config/deployment_config.json", "hash": "6719983897857056232"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "tmp_structure/tools/fix/fix_all.py", "hash": "3583425257282363557"}, {"file": "fix_encoding.ps1", "hash": "15251168971363582810"}, {"file": "tests/src/utils/test/queue_test.c", "hash": "665738556786491367"}, {"file": "test_moonshot_api.py", "hash": "16792902775065019301"}, {"file": "tests/src/utils/module_manager.c", "hash": "6226471683647205323"}, {"file": "tmp_structure/tools/utils/debug_driver_load.ps1", "hash": "7995531304533082782"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "tmp_structure/tests/performance/perfmonitor.c", "hash": "11571150342783768226"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/current.json", "hash": "4306919528317427660"}, {"file": "tmp_structure/tools/utils/smart_annotator.py", "hash": "18279375395162153177"}, {"file": "tests/unit/error_prevention.c", "hash": "6292741100613388313"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.write.1.tlog", "hash": "15389382939781618152"}, {"file": "docs/design/.gitkeep", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_health_monitor.md", "hash": "11811529164322313522"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/io/IOOptimizer.md", "hash": "2425979290851834818"}, {"file": "tests/src/utils/device/device.c", "hash": "6161212960231513500"}, {"file": "docs/project_md/ai_collaboration_roles.md", "hash": "9550531567769795845"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/binary.rs", "hash": "10874381142023996290"}, {"file": "tests/src/device/TouchProcessor.c", "hash": "783968660014355332"}, {"file": "tests/src/utils/device/device_manager.c", "hash": "1823201296405534462"}, {"file": "tmp_structure/tests/unit/memory_manager.c", "hash": "13099111058581566695"}, {"file": "sail-main/docs/public/logo.png", "hash": "15543088037880747139"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/Predictor.md", "hash": "10386180171614306631"}, {"file": "tests/unit/log_manager.c", "hash": "5709293517199487694"}, {"file": "certs/kmdf_driver_dev_cert.cer", "hash": "7785696195850755360"}, {"file": "docs/KMDF Driver1/backup/include/driver/queue.md", "hash": "17411770077844460848"}, {"file": "sail-main/crates/sail-common/src/debug.rs", "hash": "1017602387665879748"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/collection.json", "hash": "16394600891058537668"}, {"file": "sail-main/crates/sail-execution/src/stream/error.rs", "hash": "18413536635754060575"}, {"file": "tests/unit/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "sail-main/crates/sail-plan/src/extension/function/mod.rs", "hash": "16590042953291466139"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_framework.md", "hash": "14221675578553986939"}, {"file": "docs/KMDF Driver1/backup/src/error/error_handler.md", "hash": "130901189025584605"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/CL.command.1.tlog", "hash": "14611637318720516300"}, {"file": "include/driver/log/DriverLog.h", "hash": "10436857440106258735"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q45.sql", "hash": "13529760216854275392"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/resource_optimizer_1.md", "hash": "9689921769101350801"}, {"file": "sail-main/scripts/shell-tools/git-patch.sh", "hash": "2783748550078685368"}, {"file": "tests/src/utils/optimization/auto_tuning.h", "hash": "1159516589502636342"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "cleanup_documentation.md", "hash": "5781163044308498692"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "templates/teaching_prompt.json", "hash": "6745021026298905574"}, {"file": "docs/KMDF Driver1/backup/include/driver/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device.md", "hash": "7894195249420829021"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/memory_manager.md", "hash": "10511834971114926523"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device.md", "hash": "7894195249420829021"}, {"file": "tests/unit/reliabilitymanager.c", "hash": "16962898220370878971"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q18.sql", "hash": "16086216409567419748"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "tests/unit/config_group.c", "hash": "13073677126841465732"}, {"file": ".vs/CMake Overview", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/MACE-S初始实施计划.md", "hash": "4428332049883244925"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q79.sql", "hash": "16668618721494089616"}, {"file": "sail-main/crates/sail-plan/src/extension/function/spark_hash_utils.rs", "hash": "5954020123053316532"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_spi.md", "hash": "15156697141188689173"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CommonUtils.md", "hash": "13690485749395410695"}, {"file": "src/core/log/driver_log.h", "hash": "12187455979018132073"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/queue_test.md", "hash": "9597764611364215491"}, {"file": "tmp_structure/tests/unit/device_manager1.c", "hash": "9178587879886038827"}, {"file": ".vscode/project_config.json", "hash": "10195871694332873408"}, {"file": "sail-main/.github/actions/adjust-swap-space/action.yml", "hash": "17216615388446939962"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceLogger.md", "hash": "14724788428389008979"}, {"file": "docs/project_md/project_structure_full.txt", "hash": "6391899482456897709"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/statement.rs", "hash": "6925952486882325929"}, {"file": "sail-main/crates/sail-telemetry/src/error.rs", "hash": "12213687964992603916"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.write.1.tlog", "hash": "11169757439148457193"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/notification_manager.md", "hash": "15727167061045092053"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFLogger.md", "hash": "13534544793886613631"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84_DEBUG_loc", "hash": "3917696737138517457"}, {"file": "sail-main/crates/sail-server/src/retry.rs", "hash": "4017652015616764768"}, {"file": "sail-main/scripts/spark-tests/show-failed-tests.jq", "hash": "16886886768739284502"}, {"file": "sail-main/crates/sail-python-udf/src/lib.rs", "hash": "11513028229509646155"}, {"file": "docs/Doxyfile", "hash": "2063202288499522875"}, {"file": "build/KMDFDriver1.vcxproj.filters", "hash": "2227496098541449419"}, {"file": "config/models/openai.json", "hash": "1357320890085649587"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q44.sql", "hash": "13603098382639447614"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q68.sql", "hash": "7786101865403888627"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "14979245498152404618"}, {"file": "sail-main/docs/guide/index.data.ts", "hash": "3537855007737088618"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_merge_into.json", "hash": "1793846397932882880"}, {"file": "tests/unit/i2c_interface.c", "hash": "931873009436168421"}, {"file": "tests/unit/health_checker.c", "hash": "13081061987138608445"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/driver_core.md", "hash": "6642007692352427576"}, {"file": "tests/src/utils/simd/simd_implementation.h", "hash": "5016021608320478502"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceMonitor.md", "hash": "9216184883811794928"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model.md", "hash": "5760546782410447305"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/health_checker.md", "hash": "9850915435701639911"}, {"file": "sail-main/crates/sail-plan/src/literal.rs", "hash": "9148070529414820042"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_interval.rs", "hash": "12383741267547070367"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "tests/performance/performancepredictor.c", "hash": "16358558668992702664"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_manager.md", "hash": "1597906743517107"}, {"file": "tmp_structure/tests/unit/gyro.c", "hash": "12598367591223412709"}, {"file": "tests/src/utils/device/device_control.c", "hash": "1983799697968931608"}, {"file": "tmp_structure/tests/unit/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "sail-main/docker/dev/Dockerfile", "hash": "1443331190466184545"}, {"file": "sail-main/python/pysail/tests/spark/literal/test_utc_timestamp.txt", "hash": "6706248916323245487"}, {"file": "build/CMakeFiles/generate.stamp.list", "hash": "7355742560215271970"}, {"file": "sail-main/crates/sail-sql-parser/data/keywords.txt", "hash": "672078930862694491"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q95.sql", "hash": "1076063035593805887"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/misc.json", "hash": "13229284068689836562"}, {"file": "tests/src/utils/CommonUtils.c", "hash": "11647735861415970558"}, {"file": "include/core/error/error_handling.h", "hash": "13762159866028634621"}, {"file": "tmp_structure/test_driver_code.py", "hash": "8892117878506912793"}, {"file": "sail-main/python/pysail/__init__.py", "hash": "11283889926021021016"}, {"file": "sail-main/docs/development/setup/index.md", "hash": "3359523963474181180"}, {"file": "tmp_structure/tools/utils/format.ps1", "hash": "12071962958350408212"}, {"file": "docs/KMDF Driver1/tests/src/utils/GamepadCore.md", "hash": "8501127116693419912"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeFiles/TargetDirectories.txt", "hash": "6021081491133807629"}, {"file": "tmp_structure/tests/performance/performance_collector.c", "hash": "9157021843784231895"}, {"file": "sail-main/crates/sail-python-udf/src/error.rs", "hash": "8466087677019661867"}, {"file": "sail-main/python/pysail/tests/spark/test_dataframe.py", "hash": "7605979599314730182"}, {"file": "test_doubao_fixed.py", "hash": "15588857141454609785"}, {"file": "run_mace_analysis.py", "hash": "11960632128360414116"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q26.sql", "hash": "12502891693173891471"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/SecurityManager.md", "hash": "3884452379323183165"}, {"file": "tests/src/utils/interrupt/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": "tmp_structure/tools/utils/annotate.py", "hash": "14018420197385758215"}, {"file": "tmp_structure/tests/unit/memorycompression.c", "hash": "16305212441378240323"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "sail-main/.github/actions/setup-rust/action.yml", "hash": "13203006912337124895"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_misc.json", "hash": "15231175197501947513"}, {"file": "docs/project_md/ai_optimization_strategies.py.teaching.md", "hash": "12681679724514468251"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q16.sql", "hash": "10252590412565287024"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q28.sql", "hash": "15172480713666112975"}, {"file": "sail-main/crates/sail-plan/src/extension/function/skewness.rs", "hash": "3790050099265192645"}, {"file": "tmp_structure/tests/performance/performance_benchmark.c", "hash": "2916244813509275032"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_performance_monitor.md", "hash": "5561470136532144482"}, {"file": "docs/MACE-S系统改进计划.md", "hash": "2452393628633577333"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/power_optimizer.md", "hash": "1361782774390628471"}, {"file": "docs/project_md/secureboot.txt", "hash": "16256824369678776978"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_scheduler.md", "hash": "12584940170140586153"}, {"file": "sail-main/crates/sail-plan/src/config.rs", "hash": "4033698014388959440"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFI2C.md", "hash": "4110153206783316966"}, {"file": "docs/project_md/ai_deployment_setup.py.teaching.md", "hash": "17306184925241801519"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_abs.rs", "hash": "1078696161606901700"}, {"file": "sail-main/crates/sail-sql-parser/src/container/either.rs", "hash": "14187306484605704775"}, {"file": "sail-main/crates/sail-plan/src/extension/optimizer/mod.rs", "hash": "819095412076799272"}, {"file": "docs/KMDF Driver1/backup/include/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "src/hal/bus/i2c_core.c", "hash": "7473837419301537702"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/link.read.1.tlog", "hash": "16907514882482864320"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_collector.md", "hash": "14684244266011149021"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Touch.md", "hash": "6473562417806726533"}, {"file": "tmp_structure/tests/unit/resource_manager.c", "hash": "157428320305306828"}, {"file": "tmp_structure/tools/utils/code_teacher.py", "hash": "1569871746886746141"}, {"file": "tests/unit/device_manager.c", "hash": "1823201296405534462"}, {"file": "sail-main/crates/sail-plan/Cargo.toml", "hash": "18152040102515432425"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model_ensemble.md", "hash": "8343451949614003054"}, {"file": "tests/unit/resource_manager.c", "hash": "157428320305306828"}, {"file": "sail-main/crates/sail-python-udf/src/stream.rs", "hash": "14603341798866997650"}, {"file": "sail-main/.dockerignore", "hash": "18254871951144066496"}, {"file": "tests/src/utils/ml/prediction_model.c", "hash": "10565836354052141209"}, {"file": "build_driver.bat", "hash": "13926032712558879478"}, {"file": "sail-main/crates/sail-plan/src/extension/function/collection/mod.rs", "hash": "17763809209778200779"}, {"file": "tmp_structure/tools/utils/reorganize_files.py", "hash": "8799389103087372924"}, {"file": "sail-main/compose.yml", "hash": "13430423171801384152"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_bin.rs", "hash": "17361245666619259381"}, {"file": "tmp_structure/tests/unit/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "sail-main/docs/guide/deployment/index.data.ts", "hash": "3139434682244724783"}, {"file": "tests/src/device/HardwareManager.c", "hash": "5852295366093323504"}, {"file": "tests/src/utils/ml/advanced_models.c", "hash": "10802020780109055119"}, {"file": "tests/unit/device_context.c", "hash": "3876629123896102954"}, {"file": "build/CMakeFiles/TargetDirectories.txt", "hash": "14381863118042480671"}, {"file": "tests/src/memory/DMAHandler.c", "hash": "5992585881124374452"}, {"file": ".giti<PERSON>re", "hash": "567407489855212125"}, {"file": "tests/unit/device_dma.c", "hash": "7826498135188615291"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_manager.md", "hash": "17231002221847300177"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_performance.md", "hash": "3905147999090521122"}, {"file": "sail-main/crates/sail-cli/src/spark/mod.rs", "hash": "17988144192545661582"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "源代码.txt", "hash": "2030506255291108531"}, {"file": "docs/KMDF Driver1/backup/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "tests/unit/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/telemetry_processor.md", "hash": "8356681802505862867"}, {"file": "sail-main/crates/sail-telemetry/src/trace_layer.rs", "hash": "16593965622666495945"}, {"file": "sail-main/crates/sail-plan/src/temp_view.rs", "hash": "7440680534299914577"}, {"file": "sail-main/docs/.vitepress/theme/utils/page.ts", "hash": "3499988624869467801"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q59.sql", "hash": "5929880408155972561"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q36.sql", "hash": "2359856155038531119"}, {"file": "sail-main/python/pysail/data/tpch/queries/q5.sql", "hash": "4883064665269771843"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_map_to_array.rs", "hash": "5824670551323406506"}, {"file": "tests/src/driver/KMDFError.c", "hash": "4864198464511138425"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_model.md", "hash": "2200148848594683661"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_power.md", "hash": "16596401005057937692"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "tests/src/utils/trace/trace.h", "hash": "4336778835874238702"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery_kmdf.md", "hash": "7623256312282370166"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryPool.md", "hash": "15635379478257676982"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/show_string.rs", "hash": "4873590477693102997"}, {"file": "tmp_structure/src/driver/drivercore.c", "hash": "4921296672744000056"}, {"file": "generatenotes.bat", "hash": "8638447732626674053"}, {"file": "tmp_structure/tests/unit/dmaoptimizer.h", "hash": "17813436674171689238"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/randn.rs", "hash": "3826285579415610755"}, {"file": "sail-main/crates/sail-common/src/spec/mod.rs", "hash": "4692274360478228812"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q54.sql", "hash": "1581496218596194118"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "tmp_structure/tools/analysis/report_generator.py", "hash": "7014199262691885841"}, {"file": "docs/KMDF Driver1/tests/src/driver/kmdf_core.md", "hash": "6419799093797329322"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model.md", "hash": "11576464091276221100"}, {"file": "tmp_structure/tests/unit/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_usb.md", "hash": "1408341058858284475"}, {"file": "build/CMakeFiles/3.31.3/CMakeSystem.cmake", "hash": "7909334668730673541"}, {"file": "tmp_structure/tools/utils/api_adapter.py", "hash": "5534168103360626276"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/cache_optimizer.md", "hash": "4292989671843767456"}, {"file": "everythingsearch.bat", "hash": "3155300558556554196"}, {"file": "tests/unit/test_framework.c", "hash": "8175968703783348918"}, {"file": "docs/KMDF Driver1/tests/src/utils/config_group.md", "hash": "10471112854900039136"}, {"file": "sail-main/crates/sail-python-udf/src/array.rs", "hash": "12955149720958755746"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q20.sql", "hash": "9271332737970198192"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/PerfMonitor.md", "hash": "7405414634036513776"}, {"file": "tests/src/utils/power/power_manager.c", "hash": "16887739356335473604"}, {"file": "sail-main/crates/sail-server/Cargo.toml", "hash": "5145262820054943147"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_predictor.md", "hash": "13170289443993434604"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_resource.md", "hash": "11019102042198787538"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/queue.md", "hash": "11907408096764236543"}, {"file": "docs/project_md/test_annotator.py.teaching.md", "hash": "2315945858860061231"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_framework.md", "hash": "15481924532410518968"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchPerformance.md", "hash": "6315740490143290014"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/build_script.rule", "hash": "1819760324823437288"}, {"file": "sail-main/crates/sail-execution/src/driver/planner.rs", "hash": "1225830859548385867"}, {"file": "sail-main/crates/sail-server/src/actor.rs", "hash": "1651404624338279471"}, {"file": "sail-main/crates/sail-common-datafusion/src/error.rs", "hash": "2615307052514899410"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.read.1.tlog", "hash": "18137789506982172999"}, {"file": "test_huoshan_new.py", "hash": "4151729223194610259"}, {"file": "sail-main/crates/sail-cli/src/worker/mod.rs", "hash": "8765401309325258147"}, {"file": "docs/KMDF Driver1/include/device/power/device_power.md", "hash": "2872841184312516318"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_model.md", "hash": "2200148848594683661"}, {"file": "sail-main/crates/sail-python-udf/src/lazy.rs", "hash": "12420961779922953236"}, {"file": "tmp_structure/tools/utils/ai_deployment_setup.py", "hash": "17399500009219247070"}, {"file": "docs/KMDF Driver1/tests/src/utils/telemetry_manager.md", "hash": "16760846509114151494"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/i2_c_interface.md", "hash": "9147246998176705712"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/mod.rs", "hash": "17950291241602752925"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q90.sql", "hash": "10997669913406253215"}, {"file": "include/hal/devices/gpio_device.h", "hash": "16393459335135799288"}, {"file": "docs/KMDF Driver1/tests/src/device/GyroHwSim.md", "hash": "16847761403613423154"}, {"file": "docs/KMDF Driver1/tests/src/core/resource_management_test.md", "hash": "12104931785735659015"}, {"file": "tmp_structure/tests/unit/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q82.sql", "hash": "3510109544487339588"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_engine.md", "hash": "7704895161237555146"}, {"file": "tests/unit/kmdfspi.c", "hash": "1159729704795014365"}, {"file": "tmp_structure/tools/test/test_driver.ps1", "hash": "15765134662692261731"}, {"file": "tmp_structure/tests/unit/device_resource.c", "hash": "2198688305377271422"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/spi_controller.md", "hash": "7615035126808120337"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "docs/KMDF Driver1/backup/backup/src/error/error_handler.md", "hash": "130901189025584605"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_core.md", "hash": "1711428132703189616"}, {"file": "tmp_structure/tests/unit/dmamanager.c", "hash": "16085011483513293531"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_reporter.md", "hash": "12587998797618101724"}, {"file": "tmp_structure/tools/utils/dev_toolkit.py", "hash": "8660747273767930454"}, {"file": "analyze_folder.py", "hash": "7672536776940708803"}, {"file": "sail-main/.github/actions/detect-changes/action.yml", "hash": "10590247544936250491"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_usb.md", "hash": "18152046806154247336"}, {"file": "tmp_structure/include/error/error_handling.h", "hash": "2498624322782239441"}, {"file": "sail-main/crates/sail-sql-parser/src/string.rs", "hash": "10311199684992684382"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "docs/KMDF Driver1/backup/tests/src/io/IOManager.md", "hash": "12962392107439165305"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "tests/performance/performancedata.c", "hash": "10364043397620603240"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchProcessor.md", "hash": "4070778693111906027"}, {"file": "sail-main/docs/.vitepress/theme/components/SphinxIndexPage.vue", "hash": "2777187138087472472"}, {"file": "tests/unit/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "tmp_structure/src/log/driver_log.c", "hash": "16668896646093452722"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "tests/unit/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "sail-main/scripts/common-gold-data/metrics.jq", "hash": "18371220831910138354"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_predictor.md", "hash": "13170289443993434604"}, {"file": "sail-main/crates/sail-execution/src/stream/writer.rs", "hash": "298674957292211432"}, {"file": "build/CMakeFiles/3.31.3/CMakeCXXCompiler.cmake", "hash": "5627553917999415467"}, {"file": "sail-main/crates/sail-execution/src/worker/entrypoint.rs", "hash": "8981734962468648645"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.read.1.tlog", "hash": "13495605159538982555"}, {"file": "sail-main/crates/sail-plan/src/function/aggregate.rs", "hash": "8214634538285410619"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/simd_prediction.md", "hash": "7095774571489160819"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "sail-main/pyproject.toml", "hash": "3347643112891449509"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_performance_monitor.md", "hash": "2458298706816984463"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/CustomBuild.read.1.tlog", "hash": "13848395904038413544"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "sail-main/python/pysail/tests/spark/test_pandas_map.py", "hash": "4953334683504944164"}, {"file": "docs/Intelligent code comment system features and usage guide.md", "hash": "16576773019115353639"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/kmdf_core.md", "hash": "14537529512716125567"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/ml_optimizer.md", "hash": "1811472538079544434"}, {"file": "sail-main/crates/sail-plan/src/resolver/tree/explode.rs", "hash": "11536361654070268550"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/Performance.md", "hash": "6708106772424578802"}, {"file": "sail-main/crates/sail-python/src/lib.rs", "hash": "14935314307843364868"}, {"file": "tmp_structure/tests/unit/device_queue1.c", "hash": "330133164890642531"}, {"file": "tests/unit/device_workitem.c", "hash": "15964808878924499639"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_select.json", "hash": "13382341755275950765"}, {"file": "tmp_structure/tests/unit/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_spi.md", "hash": "12023608907578026527"}, {"file": "sail-main/crates/sail-plan/src/extension/mod.rs", "hash": "10670008780055214748"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "tmp_structure/tests/unit/configmanager.c", "hash": "12407172891497760359"}, {"file": "tests/unit/error_module.c", "hash": "10513386486768326845"}, {"file": "scripts/quick_check.ps1", "hash": "9636392711934536876"}, {"file": "sail-main/crates/sail-plan/src/extension/function/spark_murmur3_hash.rs", "hash": "14349835993886704710"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_timestamp.rs", "hash": "10188786906152777527"}, {"file": "sail-main/crates/sail-execution/src/plan/mod.rs", "hash": "4302181437215156746"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.read.1.tlog", "hash": "4010642594423402293"}, {"file": "BUILDING.md", "hash": "18184771395806723256"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/trace_core.md", "hash": "11472634081961337506"}, {"file": "tmp_structure/tools/utils/__pycache__/mace_system.cpython-313.pyc", "hash": "4916069930443939486"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/identifier.rs", "hash": "12462570216696853594"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_spi.md", "hash": "6188852679481726868"}, {"file": "sail-main/docs/.vitepress/theme/components/PySparkApi.vue", "hash": "10831165085888340870"}, {"file": "tests/src/utils/device/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "tests/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_queue_1.md", "hash": "9135148644199135364"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/cmTC_35600.vcxproj.filters", "hash": "8994443904225855276"}, {"file": "sail-main/crates/sail-execution/src/worker_manager/options.rs", "hash": "6369173118056995570"}, {"file": "sail-main/package.json", "hash": "9109790890368806554"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Gyro.md", "hash": "11459815508399627952"}, {"file": "sail-main/.devcontainer/Dockerfile", "hash": "6967358103150180417"}, {"file": "sail-main/docs/development/setup/prerequisites.md", "hash": "10229736720875863983"}, {"file": "docs/KMDF Driver1/tests/src/core/GlobalPerformanceManager.md", "hash": "4214450315185299087"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "tests/src/utils/trace/logger_core.c", "hash": "5596205493550234397"}, {"file": "sail-main/crates/sail-execution/src/driver/actor/core.rs", "hash": "4657995336415853120"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/cast.json", "hash": "3773532730668388696"}, {"file": "tmp_structure/tools/fix/fix_bom.ps1", "hash": "8939596673589547426"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_i2c.md", "hash": "4279440534981425318"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q58.sql", "hash": "11015884815654143814"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/datetime.json", "hash": "3684184732484933402"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model_evaluation.md", "hash": "11583937441064285777"}, {"file": "sail-main/docs/development/recipes/index.md", "hash": "2818218298531564730"}, {"file": "tests/src/utils/ml/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/Cl.items.tlog", "hash": "6087681674888703486"}, {"file": "tmp_structure/tests/performance/performance.c", "hash": "542837662377689141"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/distributed_training.md", "hash": "5129859750226130572"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/timestamp.json", "hash": "5231150669478588122"}, {"file": "sail-main/crates/sail-server/src/builder.rs", "hash": "8321161921051565951"}, {"file": "sail-main/crates/sail-python-udf/src/udf/mod.rs", "hash": "14936417881005129344"}, {"file": "tmp_structure/tests/unit/reliabilitymanager.c", "hash": "16962898220370878971"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/types.proto", "hash": "13262037300051625688"}, {"file": "tests/performance/performance_benchmark.c", "hash": "2916244813509275032"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/i2_c_interface.md", "hash": "17772133008981429129"}, {"file": "tests/unit/securitymanager.c", "hash": "2211632392311551414"}, {"file": "sail-main/crates/sail-cli/src/spark/mcp_server.rs", "hash": "8305927626071269720"}, {"file": "find_duplicates.py", "hash": "2445160768485977829"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q67.sql", "hash": "18251066536073715444"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFSPI.md", "hash": "18131184567748303267"}, {"file": "tests/unit/commoninternal.c", "hash": "12692290799640821342"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q23.sql", "hash": "9554919333838977645"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "requirements.txt", "hash": "2447290102340481311"}, {"file": "generatecomments.bat", "hash": "7046739641919960466"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/map_partitions.rs", "hash": "14526361803590314430"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_workitem.md", "hash": "17613561711832481936"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_callback.md", "hash": "817082525321811673"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAManager.md", "hash": "4927005382091664141"}, {"file": "tests/src/performance/PerformanceLogger.cpp", "hash": "1714423352534226014"}, {"file": "tmp_structure/tests/unit/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "tmp_structure/tools/utils/generate_include_graph.py", "hash": "17821567934912060292"}, {"file": "sail-main/docs/guide/tasks/udf/examples.md", "hash": "13689099690801331138"}, {"file": "tmp_structure/tests/unit/log_manager.c", "hash": "5709293517199487694"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "tmp_structure/mace_system_test.py", "hash": "4332162749339582681"}, {"file": "tests/unit/simd_core.c", "hash": "12729929013449999851"}, {"file": "tests/src/utils/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_monitor.md", "hash": "16894954371874134391"}, {"file": "tests/unit/optimization_history.c", "hash": "11553267181717299345"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.tlog/link.command.1.tlog", "hash": "5350397938509429448"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q81.sql", "hash": "6646497045092608322"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger_core.md", "hash": "3068037791562044206"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.Build.CppClean.log", "hash": "3244421341483603138"}, {"file": "tmp_structure/tests/unit/coremanager.c", "hash": "9001638691650737173"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q9.sql", "hash": "13572762583434779431"}, {"file": "tmp_structure/tests/unit/hardware.h", "hash": "3559458026889427556"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_handler.md", "hash": "5003118662217315445"}, {"file": "tmp_structure/include/error/error_types.h", "hash": "2895476709031031151"}, {"file": "cmake/code_quality.cmake", "hash": "1182718018541730159"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/hal.md", "hash": "6548767525705845893"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/advanced_models.md", "hash": "12496496022757077985"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchPredictor.md", "hash": "12653685510425338727"}, {"file": "docs/project_md/ai_collaboration_process.py.teaching.md", "hash": "7931814780058194392"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/interfaces/i_monitor.md", "hash": "7294814652658691203"}, {"file": "tests/unit/kmdf_core.c", "hash": "11085129429806464239"}, {"file": "tmp_structure/tests/unit/model.c", "hash": "13330488231876145168"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/mod.rs", "hash": "4835725159829249050"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/scheduler_optimizer.md", "hash": "3965136730573772049"}, {"file": "tmp_structure/tests/unit/touchmanager.c", "hash": "2612989593074257093"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q53.sql", "hash": "13986356092352365475"}, {"file": "tests/src/utils/device/device_manager_1.c", "hash": "9178587879886038827"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/spi_interface.md", "hash": "5118231581729686616"}, {"file": "build/INSTALL.vcxproj.filters", "hash": "5419372431529438262"}, {"file": "docs/structured_docs/examples/kmdf_队列示例.md", "hash": "15192262027129395961"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "sail-main/crates/sail-plan/src/resolver/schema.rs", "hash": "13835694357506325673"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "tmp_structure/tools/fix/fix_header_guards_format.py", "hash": "9777199903109658894"}, {"file": "tests/src/security/error_handling.c", "hash": "16257673885949715857"}, {"file": "tests/unit/errorcore.c", "hash": "8426091125501211666"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/spi_controller.md", "hash": "14694618006794652787"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": "tests/unit/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "sail-main/crates/sail-python/src/spark/server.rs", "hash": "6831174928526149652"}, {"file": "build_output.txt", "hash": "15573306999142202302"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CommonInternal.md", "hash": "7520992645958513174"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": ".vscode/alert_thresholds.json", "hash": "6723932485480696586"}, {"file": "tmp_structure/tests/unit/queue.c", "hash": "4795497370184670599"}, {"file": "docs/KMDF Driver1/tests/src/utils/config_manager.md", "hash": "17618036168927765203"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CompilerIdCXX.exe", "hash": "18389082396282325434"}, {"file": "tests/src/utils/ml/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "tests/src/utils/device/device_io.c", "hash": "9774509186909211676"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_insert_into.json", "hash": "15253985384205968623"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_framework_test.md", "hash": "10803349116171378917"}, {"file": "tests/src/performance/PerformanceMetricsImpl.cpp", "hash": "1970069259736763262"}, {"file": "tmp_structure/tools/test/install_test_driver.ps1", "hash": "685135710408998420"}, {"file": "tests/unit/model.c", "hash": "13330488231876145168"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_collector.md", "hash": "14684244266011149021"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceLogger.md", "hash": "10635370005391666013"}, {"file": "docs/KMDF Driver1/backup/include/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "sail-main/crates/sail-telemetry/src/telemetry.rs", "hash": "6585330821084436350"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "tests/src/utils/trace/event_tracer.c", "hash": "7710005633789833902"}, {"file": "tests/unit/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "sail-main/python/pysail/data/tpch/queries/q16.sql", "hash": "17261722017096038553"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q17.sql", "hash": "14753511646725662282"}, {"file": "docs/project_md/generate_driver_comments.py.teaching.md", "hash": "1396230862035816761"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/queue.md", "hash": "17411770077844460848"}, {"file": "tmp_structure/tools/utils/check_critical_issues.py", "hash": "14317396690752310694"}, {"file": "tests/src/device/TouchProcessor.h", "hash": "2829154043187812917"}, {"file": "tmp_structure/tests/unit/distributed_training.c", "hash": "13748589215294334048"}, {"file": "sail-main/python/pysail/tests/spark/utils.py", "hash": "16314391491170527706"}, {"file": "docs/doxygen/generate_docs.bat", "hash": "4440528942964720151"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q37.sql", "hash": "9349225313850274257"}, {"file": "sail-main/crates/sail-cli/src/spark/server.rs", "hash": "11870252951943647819"}, {"file": "tests/unit/memorymanager.h", "hash": "15989478217969481753"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/notification_manager.md", "hash": "15727167061045092053"}, {"file": "sail-main/crates/sail-plan/src/catalog/table.rs", "hash": "5624307193717177397"}, {"file": "tests/unit/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "build/CMakeFiles/generate.stamp.depend", "hash": "9641980745540140858"}, {"file": "sail-main/crates/sail-sql-macro/src/tree/mod.rs", "hash": "4909189013465323140"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/agent_manager.cpython-313.pyc", "hash": "3893011543862468908"}, {"file": "tmp_structure/tools/fix/fix_encoding.py", "hash": "3311722447143053633"}, {"file": "tests/unit/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "tests/unit/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_policy.md", "hash": "9008996255866575673"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q2.sql", "hash": "8752414886603858459"}, {"file": "tmp_structure/tests/unit/optimization_history.c", "hash": "11553267181717299345"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q12.sql", "hash": "2938492530102668491"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/driver_types.md", "hash": "7096294206536811380"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "tests/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "tests/unit/touchprocessor.c", "hash": "8524256194400325748"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/predicate.rs", "hash": "15676517322867955486"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "include/device/io/device_queue.h", "hash": "7815653821389369623"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": "tests/src/performance/PerformanceMonitor.c", "hash": "1073978516217722283"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_context.md", "hash": "14526931168947945277"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/map.json", "hash": "16591388751952245236"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "docs/project_md/fix_all_headers.py.teaching.md", "hash": "15700785300499607381"}, {"file": "sail-main/python/pysail/docs/conf.py", "hash": "9159204580599063856"}, {"file": "tmp_structure/tests/unit/event_tracer.c", "hash": "7710005633789833902"}, {"file": "sail-main/scripts/spark-gold-data/README.md", "hash": "11717839457597302231"}, {"file": "docs/MACE-S具体实施方案.md", "hash": "757424553699069317"}, {"file": "tmp_structure/tests/unit/dmaoptimizer.c", "hash": "12446589818635954550"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/link.command.1.tlog", "hash": "14466223658515098827"}, {"file": "tmp_structure/tests/unit/device_timer.c", "hash": "3835497012896294941"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "14979245498152404618"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "tests/src/utils/ml/model.c", "hash": "13330488231876145168"}, {"file": "tmp_structure/tools/utils/batch_annotate.py", "hash": "16564175115918697176"}, {"file": "src/hal/bus/spi_core.c.bak", "hash": "2402126896693520852"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "tmp_structure/tools/utils/dependency_analyzer.py", "hash": "2403107713714667589"}, {"file": "tmp_structure/tests/unit/hal.c", "hash": "13073342420439847883"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92_RELEASE_loc", "hash": "4429990960329223770"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_database.md", "hash": "3118559368204033654"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_arrow_map_udf.txt", "hash": "4026920265404245167"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/power_optimizer.md", "hash": "1361782774390628471"}, {"file": "build/CMakeFiles/CMakeConfigureLog.yaml", "hash": "12374305876840625152"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/distributed_training.md", "hash": "5668726731270982163"}, {"file": "tests/unit/simd_operations.c", "hash": "2970634337399079429"}, {"file": "docs/KMDF Driver1/backup/include/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "tmp_structure/tools/analysis/html_report_generator.py", "hash": "16765524786776403210"}, {"file": "sail-main/crates/sail-execution/src/worker/server.rs", "hash": "17315623975643288134"}, {"file": "tests/src/driver/kmdf_core.c", "hash": "11085129429806464239"}, {"file": "tests/src/security/error_recovery_framework.c", "hash": "15116463532532570605"}, {"file": "tmp_structure/tests/unit/adaptive_scheduler.c", "hash": "17438257645459382429"}, {"file": "test_output.txt", "hash": "4331091727924570233"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_monitor.md", "hash": "8115688258881368457"}, {"file": "sail-main/python/pysail/tests/spark/catalog/test_temp_view.txt", "hash": "10110149047087237346"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_queue.md", "hash": "7516366818546283918"}, {"file": "docs/api/星火大模型.txt", "hash": "6546154230992474501"}, {"file": "tmp_structure/tools/fix/fix_code_issues.py", "hash": "16428652738469116471"}, {"file": "tmp_structure/tools/fix/fix_all_headers.py", "hash": "4555455160791820434"}, {"file": "tmp_structure/tests/unit/maintenancemanager.c", "hash": "13897789596428706927"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger.md", "hash": "14318949831029739559"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "sail-main/scripts/spark-config/generate.py", "hash": "14143810306440629076"}, {"file": "build/cmake_install.cmake", "hash": "6136203923856828232"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_analyzer.md", "hash": "12003155831058412718"}, {"file": "tests/CMakeLists.txt", "hash": "12196540828055306736"}, {"file": "tmp_structure/tools/utils/core/__init__.py", "hash": "10839681011534471175"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "tmp_structure/tests/unit/resource_optimizer1.c", "hash": "9728211600653272904"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_scheduler.md", "hash": "406089374946636554"}, {"file": "sail-main/crates/sail-plan/src/object_store/s3.rs", "hash": "15635726940527561918"}, {"file": "sail-main/crates/sail-sql-parser/src/container/option.rs", "hash": "232594299958456779"}, {"file": "tmp_structure/tools/fix/fix_line_endings.py", "hash": "9496055858282095405"}, {"file": "sail-main/crates/sail-plan/src/extension/function/raise_error.rs", "hash": "12087388689785295457"}, {"file": "tmp_structure/tools/fix/fix_newlines.py", "hash": "94768762135738281"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/handler.rs", "hash": "7345311978336756367"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/expression.rs", "hash": "3277631967040454810"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q24.sql", "hash": "3996442798188195290"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.exe.recipe", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_init_test.md", "hash": "17333699458054262275"}, {"file": "tests/unit/cache_manager.c", "hash": "868028145507112649"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_last_day.rs", "hash": "15866049659089638064"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/core/error_handling.md", "hash": "8226706213229477559"}, {"file": "tests/unit/buildandruntests.bat", "hash": "9798688693070873269"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryCompression.md", "hash": "11093687460873692203"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "tests/unit/adaptive_scheduler.c", "hash": "10385198795992790880"}, {"file": "start_mcp_servers.bat", "hash": "10276851506467346063"}, {"file": "tmp_structure/tests/unit/compressionpredictor.c", "hash": "11817161568988905271"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/common.rs", "hash": "17215011697096750483"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/relations.proto", "hash": "6876078241441364107"}, {"file": "tests/unit/prediction_model.c", "hash": "10565836354052141209"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/event_buffer.md", "hash": "11762678649378503503"}, {"file": "tmp_structure/tests/unit/memorymanager.c", "hash": "13193390556443584375"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/Debug/cmTC_fba92.exe", "hash": "4274658385013566627"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/cache_optimizer.md", "hash": "17554861544045394829"}, {"file": "tests/unit/advanced_models.c", "hash": "10802020780109055119"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q70.sql", "hash": "14169967687343050615"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/cmTC_35600.vcxproj", "hash": "13441837126936026064"}, {"file": "analyze_dependencies.py", "hash": "8993602809188413502"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/project_md/CMakeLists.txt", "hash": "12807661119778426816"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/best_practice_agent.cpython-313.pyc", "hash": "18373879793777027551"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_monitor_manager.md", "hash": "8280728422518682923"}, {"file": "docs/KMDF Driver1/tests/src/test_main.md", "hash": "5037830315100233995"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "docs/KMDF Driver1/include/device/base/device_context.md", "hash": "11117283112430833157"}, {"file": "tmp_structure/tools/fix/fix_file_format.ps1", "hash": "6779604317763307623"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorHandler.md", "hash": "15475208645081206135"}, {"file": "sail-main/crates/sail-sql-parser/src/combinator.rs", "hash": "11447064743662563302"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Gyro.md", "hash": "11459815508399627952"}, {"file": "tests/src/security/error_module.c", "hash": "10513386486768326845"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/trace_core.md", "hash": "15790226712616423779"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/CMAKE_TRY_COMPILE.sln", "hash": "11910516344956958677"}, {"file": "tmp_structure/tools/fix/fix_header_guards.py", "hash": "13283570998575127369"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery_framework.md", "hash": "9217977908073013080"}, {"file": "build/PACKAGE.vcxproj.filters.tmp7534f", "hash": "9749945271910142052"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_batch_collector.rs", "hash": "10316351777746858963"}, {"file": "sail-main/python/pysail/data/tpch/queries/q3.sql", "hash": "18354501689886146862"}, {"file": "tmp_structure/tools/utils/agents/agent_manager.py", "hash": "12611845702279519081"}, {"file": "tests/unit/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_resource_monitor.md", "hash": "9018393555267135129"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_drop_view.json", "hash": "14923054315714141511"}, {"file": "tests/src/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "sail-main/docs/.vitepress/theme/utils/sphinx.ts", "hash": "16922562129472157250"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/misc.json", "hash": "5085357810371341607"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handling_test.md", "hash": "7236286416758687736"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q76.sql", "hash": "5479240014849405664"}, {"file": "tests/src/driver/KMDFUSB.c", "hash": "3804768512383554498"}, {"file": "sail-main/docs/development/setup/index.data.ts", "hash": "10189948560462376945"}, {"file": "tmp_structure/tests/performance/performancedata.c", "hash": "10364043397620603240"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/simd_prediction.md", "hash": "7095774571489160819"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "sail-main/.github/dependabot.yml", "hash": "15361356469127189712"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/event_tracer.md", "hash": "4590536025397448341"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ProcessFunctions.md", "hash": "5303515720344831466"}, {"file": "version.h", "hash": "5153955185604433877"}, {"file": "sail-main/docs/reference/changelog/index.md", "hash": "1717088531389010092"}, {"file": "tests/test_config_loader.py", "hash": "3011664226933306699"}, {"file": "sail-main/crates/sail-execution/src/worker/event.rs", "hash": "6486566030250469343"}, {"file": "sail-main/crates/sail-execution/proto/sail/driver/service.proto", "hash": "8847764695041304047"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_executor.md", "hash": "8670951633232470291"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/resource_optimizer_1.md", "hash": "9689921769101350801"}, {"file": "tests/src/utils/MaintenanceManager.c", "hash": "13897789596428706927"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_insert_overwrite.json", "hash": "11432465111730487229"}, {"file": "tmp_structure/tests/performance/performancepredictor.c", "hash": "16358558668992702664"}, {"file": "tmp_structure/tests/unit/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "sail-main/scripts/hadoop/hdfs-init.sh", "hash": "10940090888585555957"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "docs/KMDF Driver1/tests/src/io/IOOptimizer.md", "hash": "10089800447872604097"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/CMakeCache.txt", "hash": "15995164659608303044"}, {"file": "sail-main/docs/guide/tasks/index.md", "hash": "6090517938813091219"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q5.sql", "hash": "4526486923329142552"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_context.md", "hash": "5783058402535230175"}, {"file": "sail-main/crates/sail-execution/src/worker/options.rs", "hash": "2778120979832021601"}, {"file": "sail-main/python/pysail/data/tpch/queries/q14.sql", "hash": "15910662366423232107"}, {"file": "docs/KMDF Driver1/include/core/types/CommonTypes.md", "hash": "7598532320659218397"}, {"file": "sail-main/docs/guide/deployment/index.md", "hash": "9148554531719143990"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q99.sql", "hash": "6158936741796426623"}, {"file": "docs/KMDF Driver1/backup/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_create_index.json", "hash": "15992572063852011223"}, {"file": "sail-main/crates/sail-plan/src/object_store/mod.rs", "hash": "2172880419387331264"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q11.sql", "hash": "7436284465809863837"}, {"file": "tmp_structure/tools/utils/driver_manager.ps1", "hash": "14443047261930491096"}, {"file": "tests/src/security/error_handler.c", "hash": "2913023791582526489"}, {"file": "docs/KMDF Driver1/tests/src/utils/QueueOperations.md", "hash": "14464997083434033348"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/CMakeLists.txt", "hash": "8977527736248983758"}, {"file": "tests/src/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_queue_1.md", "hash": "14570869599811644805"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/optimizer.md", "hash": "4900046108333349195"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "tmp_structure/tests/unit/globalvariables.c", "hash": "703525128182974641"}, {"file": "tests/src/security/ErrorPrevention.c", "hash": "10299302956295713904"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/math.json", "hash": "7366642820265574413"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/unpivot_join.json", "hash": "4491865992705705599"}, {"file": "sail-main/.github/workflows/spark-tests.yml", "hash": "1085922901701989229"}, {"file": "sail-main/crates/sail-plan/src/function/common.rs", "hash": "15862815469260672424"}, {"file": "sail-main/crates/sail-python/src/cli.rs", "hash": "14657356345152145846"}, {"file": "docs/KMDF Driver1/tests/src/utils/CompressionPredictor.md", "hash": "1323356466455046451"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/spark_to_binary.rs", "hash": "1731338924427863129"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/event_trace.md", "hash": "12626478951697345873"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model_ensemble.md", "hash": "2913442869778386948"}, {"file": "sail-main/docs/development/recipes/cross-compilation.md", "hash": "3196152638868096400"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/large.json", "hash": "15794938483854295941"}, {"file": "tests/unit/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "tmp_structure/tools/fix/fix_code_style.py", "hash": "9125106425754353024"}, {"file": "sail-main/docs/guide/getting-started/index.md", "hash": "12677521679894799744"}, {"file": "tmp_structure/tools/fix/update_includes.py", "hash": "11519157228217203760"}, {"file": "tests/unit/auto_tuning.h", "hash": "11138856584000903567"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_manager.md", "hash": "15570914612634812124"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_from_utc_timestamp.rs", "hash": "9918826522642225644"}, {"file": "sail-main/crates/sail-execution/src/error.rs", "hash": "6813454191675634345"}, {"file": "tmp_structure/tests/unit/modulemanager.c", "hash": "12705701726995479740"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/KMDFDriver1.lastbuildstate", "hash": "7748267508661821711"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/model_ensemble.md", "hash": "2913442869778386948"}, {"file": "tests/src/utils/registry_manager.c", "hash": "7212363665723535550"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_executor.md", "hash": "14361540514063420667"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.tlog/CL.read.1.tlog", "hash": "16469326179627963140"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "certs/.gitkeep", "hash": "3244421341483603138"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-RelWithDebInfo-e5692e6ed879150c5474.json", "hash": "6502387549155151165"}, {"file": ".cursor/rules/nx-rules.mdc", "hash": "14285768348196013884"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/vc143.pdb", "hash": "8005017109841798006"}, {"file": "sail-main/.github/workflows/build.yml", "hash": "1074659804231817254"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_core.md", "hash": "8262895379536040588"}, {"file": "sail-main/crates/sail-spark-connect/src/error.rs", "hash": "16954521302274511389"}, {"file": "build/ALL_BUILD.vcxproj", "hash": "11416123920263964514"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/resource_optimizer.md", "hash": "3478979109090558593"}, {"file": "build/.cmake/api/v1/reply/index-2025-05-30T12-06-29-0358.json", "hash": "841020221566695322"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_handler.md", "hash": "12498787032465535448"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/lambda.rs", "hash": "16914843075215280750"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_order_by.json", "hash": "11407603601673277017"}, {"file": "tmp_structure/tests/unit/device_monitor.c", "hash": "10802691851731843766"}, {"file": "sail-main/scripts/spark-tests/plugins/__init__.py", "hash": "3244421341483603138"}, {"file": "sail-main/crates/sail-sql-macro/Cargo.toml", "hash": "1618529998085181443"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/Hardware.md", "hash": "12499629258003032487"}, {"file": "docs/KMDF Driver1/tmp_structure/include/bus/kmdf_i2c.md", "hash": "12150332390307086755"}, {"file": "fix_performance_filenames.bat", "hash": "17577916249826410663"}, {"file": "tests/src/driver/driver_core.c", "hash": "2424469765469195657"}, {"file": "sail-main/crates/sail-plan/src/catalog/function.rs", "hash": "5160033384833425804"}, {"file": "docs/KMDF Driver1/tests/src/utils/module_manager.md", "hash": "5061824811813677188"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_create_view.json", "hash": "12596372913737952870"}, {"file": "sail-main/crates/sail-spark-connect/Cargo.toml", "hash": "18218076327774825022"}, {"file": "runqualitycheck.bat", "hash": "6312967091181481470"}, {"file": "tmp_structure/tools/fix/fix_line_endings.ps1", "hash": "8795204320686989167"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "sail-main/crates/sail-plan/src/extension/function/map/map_function.rs", "hash": "15004534784337815083"}, {"file": "tmp_structure/tests/unit/touchai.c", "hash": "6072030623807491314"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "tmp_structure/tests/unit/iooptimizer.c", "hash": "11289350191801585883"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CompressionPredictor.md", "hash": "14348087692511970447"}, {"file": "docs/project_md/smart_annotator.py.teaching.md", "hash": "11123789175305425867"}, {"file": "sail-main/scripts/spark-tests/plugins/ibis.py", "hash": "9543648121446347008"}, {"file": "docs/KMDF Driver1/include/core/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "tests/src/core/GlobalPerformanceManager.c", "hash": "13783737954691746004"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_core.md", "hash": "14131705015021059174"}, {"file": "sail-main/crates/sail-common-datafusion/src/display.rs", "hash": "18012963036222417165"}, {"file": "test_mace_analysis.py", "hash": "4770203628810889220"}, {"file": "docs/KMDF Driver1/tests/src/performance/monitor.md", "hash": "10197168810983286962"}, {"file": "tmp_structure/tools/test/safe_driver_test.ps1", "hash": "17834237941870637588"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-Release-972133ffe60e9be42db0.json", "hash": "7275136208163849129"}, {"file": "sail-main/python/pysail/examples/spark/__init__.py", "hash": "3244421341483603138"}, {"file": "sail-main/crates/sail-cli/src/runner.rs", "hash": "9028067434419314937"}, {"file": "tests/unit/log_manager.h", "hash": "11472669487661912463"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/auto_optimizer.md", "hash": "11758342447071272246"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_cogroup_map_udf.rs", "hash": "6124225123980853364"}, {"file": "tests/unit/drivercore.c", "hash": "2661836926917990059"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.command.1.tlog", "hash": "15703716044304800680"}, {"file": "tests/unit/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "sail-main/crates/sail-sql-parser/src/parser.rs", "hash": "2272301897969759504"}, {"file": "tests/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/unpivot_select.json", "hash": "1351360843150003677"}, {"file": "src/hal/devices/i2c_device.c", "hash": "7182916583799207192"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q34.sql", "hash": "6690451081276100465"}, {"file": "tests/unit/device_queue1.c", "hash": "330133164890642531"}, {"file": "tmp_structure/tests/unit/trace.c", "hash": "7049877013033903552"}, {"file": "tests/unit/kmdfusb.c", "hash": "3804768512383554498"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/queue_test.md", "hash": "1004855502735791525"}, {"file": "tests/performance/touchperformance.c", "hash": "5007490272852559090"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q91.sql", "hash": "14253974805162765622"}, {"file": "sail-main/crates/sail-plan/src/resolver/data_source.rs", "hash": "17177656690615950087"}, {"file": "tmp_structure/tests/unit/error_handling.c", "hash": "15024041996413401458"}, {"file": "tmp_structure/tests/unit/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_i2c.md", "hash": "4279440534981425318"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/mod.rs", "hash": "3989484485480374015"}, {"file": "tmp_structure/tests/unit/hardwaremanager.c", "hash": "5852295366093323504"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_internal.md", "hash": "13825641208428294959"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/event_tracer.md", "hash": "4590536025397448341"}, {"file": "signwithexistingcert.bat", "hash": "14803017271940057798"}, {"file": "sail-main/crates/sail-plan/src/extension/function/spark_aes.rs", "hash": "7651373215054311325"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "sail-main/clippy.toml", "hash": "15544923232137632997"}, {"file": "sail-main/crates/sail-sql-parser/src/token.rs", "hash": "16158139405367826778"}, {"file": "tests/src/utils/optimization/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "sail-main/crates/sail-execution/src/plan/shuffle_write.rs", "hash": "11355741770364352200"}, {"file": "sail-main/crates/sail-plan/src/extension/analyzer/mod.rs", "hash": "5434336355039580644"}, {"file": "sail-main/python/pysail/data/tpch/queries/q20.sql", "hash": "4893231424109716347"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_ceil_floor.rs", "hash": "15518124856993241175"}, {"file": "tmp_structure/tests/unit/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "tests/src/performance/PerformanceManager.c", "hash": "12428470120826957350"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_error_monitor.md", "hash": "2218156172550209497"}, {"file": "sail-main/crates/sail-sql-analyzer/src/parser.rs", "hash": "13548193834872402226"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_database.md", "hash": "3898663799363477466"}, {"file": "tmp_structure/tests/unit/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handler.md", "hash": "130901189025584605"}, {"file": "tests/unit/predictor.c", "hash": "15488274478411287487"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFLogger.md", "hash": "15861020303789370683"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_core.md", "hash": "12158579485392829125"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/driver_core.md", "hash": "2981211088971537176"}, {"file": "sail-main/python/pysail/data/tpch/queries/q13.sql", "hash": "3009024809155455489"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q43.sql", "hash": "3264627047441069378"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/i2_c_interface.md", "hash": "9147246998176705712"}, {"file": "tmp_structure/tools/utils/simple_check.py", "hash": "15682216032295099812"}, {"file": "sail-main/crates/sail-sql-analyzer/src/expression.rs", "hash": "17339836784259174643"}, {"file": "tmp_structure/src/driver/queue.c", "hash": "1510108008238614011"}, {"file": "tmp_structure/tests/unit/error_handler.c", "hash": "2913023791582526489"}, {"file": "tmp_structure/tests/unit/error_core.c", "hash": "13244377760009713122"}, {"file": "sail-main/docs/.vitepress/theme/layouts/MainLayout.vue", "hash": "3109502456277817523"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "tmp_structure/tests/unit/event_dispatcher.c", "hash": "10905284270533332807"}, {"file": "tmp_structure/tests/unit/logger_trace.c", "hash": "9545559538338278246"}, {"file": "tmp_structure/tests/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": "sail-main/crates/sail-python-udf/src/cereal/mod.rs", "hash": "3937822150811625861"}, {"file": "tests/src/utils/ml/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": ".cursor/rules/my-custom-rule.mdc", "hash": "9681079700429432384"}, {"file": "tests/src/utils/Predictor.c", "hash": "15488274478411287487"}, {"file": "tests/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": "docs/KMDF Driver1/tests/src/utils/Queue.md", "hash": "4664907013583346375"}, {"file": "tests/src/security/ErrorCore.c", "hash": "8426091125501211666"}, {"file": "sail-main/crates/sail-execution/src/driver/event.rs", "hash": "12228438755590642115"}, {"file": "tests/performance/performancemonitor.c", "hash": "17506105073300868858"}, {"file": "tests/unit/device_control.c", "hash": "1983799697968931608"}, {"file": "tests/src/device/Hardware.c", "hash": "17567173754634071352"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q32.sql", "hash": "9673131392679213457"}, {"file": "docs/KMDF Driver1/backup/backup/include/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "tmp_structure/tools/utils/setup_env.ps1", "hash": "6182253896198528092"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_manager.md", "hash": "8524736742959489422"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_control.md", "hash": "16289217216158181524"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_create_table.json", "hash": "2065108123624650697"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_prevention.md", "hash": "14119463069258361082"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_predictor.md", "hash": "5785307039557457003"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q62.sql", "hash": "12802074112308615912"}, {"file": "docs/MACE-S工作指挥计划.md", "hash": "13392322616008266012"}, {"file": "docs/KMDF Driver1/tests/src/device/HardwareManager.md", "hash": "4112210358845696142"}, {"file": "sail-main/crates/sail-sql-parser/src/utils.rs", "hash": "14519950226427812993"}, {"file": "sail-main/docs/development/recipes/mcp-server.md", "hash": "4084514863458374716"}, {"file": "build_with_env.bat", "hash": "6579806777269314990"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_load_data.json", "hash": "13471322915630479396"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84_RELEASE_loc", "hash": "8216795909248386735"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_signum.rs", "hash": "6616926868356791116"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorCore.md", "hash": "17367002698232849007"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/link.secondary.1.tlog", "hash": "12038843251073426511"}, {"file": "tests/src/utils/ml/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_alter_table.json", "hash": "12172774818022370028"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-Release-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "tests/src/performance/PerformanceAnalyzer.c", "hash": "16444703254818628465"}, {"file": "sail-main/crates/sail-execution/src/worker/debug.rs", "hash": "2338406794018926407"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handler.md", "hash": "130901189025584605"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_cache.json", "hash": "1988450079131756724"}, {"file": "renametosnakecase.bat", "hash": "13623342575885702600"}, {"file": "LICENSE", "hash": "15092131604194978948"}, {"file": "sail-main/python/pysail/tests/spark/test_basic.py", "hash": "8106530740478917065"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-RelWithDebInfo-9711ed9c4953b41feef1.json", "hash": "2577670228072757400"}, {"file": "tmp_structure/tests/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "tmp_structure/tests/unit/hardwaremanager.h", "hash": "17760884888994478549"}, {"file": "tmp_structure/tests/performance/performancemanager.c", "hash": "12428470120826957350"}, {"file": "sail-main/crates/sail-python/src/spark/mod.rs", "hash": "748959990341523370"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorManager.md", "hash": "3624631757573195188"}, {"file": "tests/src/device/TouchAI.h", "hash": "13168883210181320447"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_queue_1.md", "hash": "14570869599811644805"}, {"file": "tmp_structure/tools/utils/check_organization.py", "hash": "5131914251861155780"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/window.json", "hash": "3512201297019526877"}, {"file": "config/models/glhf.json", "hash": "7514859731510942125"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": "include/hal/devices/i2c_device.h", "hash": "7372793240612711394"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "docs/KMDF Driver1/tests/src/utils/analyzer.md", "hash": "18403376044835528462"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/telemetry_processor.md", "hash": "8356681802505862867"}, {"file": "tests/src/utils/ml/prediction_context.c", "hash": "8771136116938717181"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_test.md", "hash": "11010014707977386257"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchProcessor.md", "hash": "4070778693111906027"}, {"file": "tmp_structure/tests/unit/module_manager.c", "hash": "6226471683647205323"}, {"file": "sail-main/crates/sail-common-datafusion/src/utils.rs", "hash": "10158323828077660666"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_context.md", "hash": "4170528201323571228"}, {"file": "config/models/pollinations.json", "hash": "1667566971839229310"}, {"file": "test_knowledge_agent.py", "hash": "2899840828343350953"}, {"file": "execute_cleanup.py", "hash": "12212647398159307009"}, {"file": "sail-main/crates/sail-execution/src/plan/shuffle_read.rs", "hash": "904218246879298686"}, {"file": "include/device/io/device_io.h", "hash": "10250326044456562543"}, {"file": "tmp_structure/tools/utils/ai_teacher_comments.py", "hash": "3774556522186816347"}, {"file": "tests/performance/performance_analyzer.h", "hash": "244392829769829688"}, {"file": "tests/src/utils/power/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "tmp_structure/tests/unit/kmdfi2c.c", "hash": "13687270665700358473"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "build/.cmake/api/v1/reply/directory-.-Debug-383b301de3917b4ecbef.json", "hash": "16234770097272687001"}, {"file": "tmp_structure/tests/unit/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "tmp_structure/tests/unit/config_manager.c", "hash": "18100728998672094806"}, {"file": "sail-main/crates/sail-execution/src/driver/actor/mod.rs", "hash": "8460909817010730267"}, {"file": "tmp_structure/tools/utils/agents/agent_discussion.py", "hash": "1548734560386962147"}, {"file": "sail-main/crates/sail-gold-test/src/bin/spark-gold-data/main.rs", "hash": "15092240756318368704"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/auto_optimizer.md", "hash": "16736987137357739135"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_show_views.json", "hash": "13232574092994847961"}, {"file": "configure.bat", "hash": "696193800616722376"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_history.md", "hash": "15652233555471193452"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/CMakeLists.txt", "hash": "6441755704823726011"}, {"file": "sail-main/crates/sail-plan/src/extension/source/mod.rs", "hash": "6428929257705462922"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_alter_view.json", "hash": "4492931768304498368"}, {"file": "fix_driver_filenames.bat", "hash": "6743429918490295169"}, {"file": "sail-main/crates/sail-common/src/runtime.rs", "hash": "7767713613807999859"}, {"file": "sail-main/crates/sail-plan/src/function/table/mod.rs", "hash": "1169425937315970746"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92_DEBUG_loc", "hash": "9153940636331968755"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_state_manager.md", "hash": "1635534176611093163"}, {"file": "tmp_structure/tools/fix/fix_chinese_comments.ps1", "hash": "2371586277548138479"}, {"file": "tools/doc_generator.py", "hash": "1894546107717736772"}, {"file": "sail-main/crates/sail-common/src/object.rs", "hash": "6845149813863589323"}, {"file": "tests/unit/simd_optimization.c", "hash": "1016262082760471380"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/bitwise.json", "hash": "6057748766761478122"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_engine.md", "hash": "1707343726405816748"}, {"file": "tests/unit/resource_optimizer1.c", "hash": "9728211600653272904"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/cmTC_58f84.lastbuildstate", "hash": "598020471011969131"}, {"file": "signdriverguidebug.bat", "hash": "6182625504451508622"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/interval.json", "hash": "2385888711102417902"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/GamepadCore.md", "hash": "17415216908876162054"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_uncache.json", "hash": "7826855828487505764"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/Performance.md", "hash": "6708106772424578802"}, {"file": "tmp_structure/tools/test/test_annotator.py", "hash": "15337323273844871360"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/memory_pool.md", "hash": "1168139887190025561"}, {"file": "sail-main/python/pysail/tests/spark/function/test_ceil.txt", "hash": "6949231999523595662"}, {"file": "tests/unit/power_manager.c", "hash": "16887739356335473604"}, {"file": "include/device/base/device_context.h", "hash": "17688454042905726515"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_describe.json", "hash": "9460414566321798094"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_context.md", "hash": "14526931168947945277"}, {"file": "sail-main/crates/sail-python/Cargo.toml", "hash": "5173081095958599251"}, {"file": "tests/src/performance/monitor.h", "hash": "16679749735999399734"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFHAL.md", "hash": "8895607898594294427"}, {"file": "docs/KMDF Driver1/tests/src/utils/event_buffer.md", "hash": "7850820821631957138"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.tlog/cmTC_4f03d.lastbuildstate", "hash": "1853882845690393423"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/random.rs", "hash": "7514508007177824920"}, {"file": "docs/KMDF Driver1/tests/src/utils/ModuleManager.md", "hash": "9355698304463324449"}, {"file": "docs/KMDF Driver1/include/core/common/Common.md", "hash": "11735538654281835954"}, {"file": ".vs/KMDF Driver1/FileContentIndex/81110e3d-8f3e-43f4-959a-2c0399269fac.vsidx", "hash": "10995894794753696794"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "tests/unit/memorycompression.c", "hash": "16305212441378240323"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": "tests/unit/dmaoptimizer.c", "hash": "12446589818635954550"}, {"file": "include/hal/hal_interface.h", "hash": "10656810998201702634"}, {"file": "tmp_structure/include/device/device_state_types.h", "hash": "3888403413223082294"}, {"file": "sail-main/docs/guide/index.md", "hash": "16923778576534150395"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_core.md", "hash": "9042388263742623687"}, {"file": "docs/KMDF Driver1/tests/src/core/error_handling_test.md", "hash": "14543812925109624658"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_history.md", "hash": "10843695273737027041"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/manager.md", "hash": "12857642143589749316"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_callback.md", "hash": "1464403380017115903"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_dma.md", "hash": "15203853900872757579"}, {"file": "sail-main/crates/sail-sql-parser/src/lexer.rs", "hash": "7491935084347817150"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/CL.read.1.tlog", "hash": "13523518774632479896"}, {"file": "sail-main/crates/sail-plan/src/extension/function/explode.rs", "hash": "2884203975442186321"}, {"file": "tmp_structure/tests/unit/touch.c", "hash": "5834024937245580957"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_reporter.md", "hash": "1776717960419419935"}, {"file": "tests/src/device/Hardware.h", "hash": "10373285935336127041"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/mod.rs", "hash": "12803135692579063733"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFUSB.md", "hash": "3454817418977638669"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchManager.md", "hash": "67499987134114106"}, {"file": "configureclean.bat", "hash": "18310326671993069378"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "fix_unit_tests_filenames.bat", "hash": "3664211143947467761"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_monitor.md", "hash": "17046761877009662786"}, {"file": "tmp_structure/tools/utils/optimize_project.py", "hash": "13813192560649161115"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-Debug-e5692e6ed879150c5474.json", "hash": "6502387549155151165"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/trace.md", "hash": "938798632008336272"}, {"file": "docs/doxygen/doc_summary_full.json", "hash": "5020128253340977997"}, {"file": "tmp_structure/src/error/error_handling.c", "hash": "11901117793078601406"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/GyroHwSim.md", "hash": "1957359040119195426"}, {"file": "fix_remaining_headers.py", "hash": "8144367279178154126"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/data_type.rs", "hash": "15111617368145729935"}, {"file": "tmp_structure/include/log/driver_log.h", "hash": "10680616300119219702"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/event_trace.md", "hash": "2482424508797990892"}, {"file": "config/models/deepseek.json", "hash": "14635218583903184388"}, {"file": "docs/KMDF Driver1/backup/include/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "scripts/fix_formatting.ps1", "hash": "4421299483655137804"}, {"file": "sail-main/crates/sail-sql-analyzer/src/data_type.rs", "hash": "17824211853009457748"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "tests/src/memory/memory_manager.c", "hash": "13099111058581566695"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_misc.json", "hash": "9952964726613332057"}, {"file": "sail-main/docs/public/favicon.png", "hash": "2213830851546660910"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/event_dispatcher.md", "hash": "6014464624155038316"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/stream_accessor.rs", "hash": "15066754897015096451"}, {"file": "tests/unit/spi_controller.c", "hash": "15313722384938567487"}, {"file": "sail-main/crates/sail-common-datafusion/Cargo.toml", "hash": "2207674931760130472"}, {"file": "tmp_structure/tools/utils/core/mace_config.py", "hash": "6801869608993043489"}, {"file": "sail-main/docs/index.md", "hash": "4901743385528653792"}, {"file": "tmp_structure/tests/unit/usb_interface.c", "hash": "15002876401871970527"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeFiles/generate.stamp", "hash": "9089816209918881806"}, {"file": "tests/unit/driver.c", "hash": "5937756760430682963"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/case.json", "hash": "11651176017246433399"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_dma.md", "hash": "8057560216873489968"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_operations.md", "hash": "7330894205852388285"}, {"file": "sail-main/docs/guide/tasks/mcp-server.md", "hash": "14361866707146837104"}, {"file": "test_agent.py", "hash": "4860432581673247722"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/GlobalVariables.md", "hash": "3478961572820031987"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q48.sql", "hash": "6706822445793200579"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/recovery_impl.md", "hash": "13993772039987120939"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/simd_optimization.md", "hash": "3740213908017695348"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q74.sql", "hash": "18174336922230541942"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_strategies.md", "hash": "6375213808090771433"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/sample_test.md", "hash": "7667131459515975553"}, {"file": "sail-main/crates/sail-plan/src/extension/function/collection/spark_size.rs", "hash": "4246723272542631936"}, {"file": "docs/Pasted_Text_1747896069730.txt", "hash": "494896830235908498"}, {"file": "test_volcengine_sdk.py", "hash": "4563110209721761775"}, {"file": "docs/project_md/idear3.txt", "hash": "5720518902774854320"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/datetime_utils.rs", "hash": "10899342494534487707"}, {"file": "tests/src/utils/trace/trace_core.c", "hash": "10541209614786999240"}, {"file": "sail-main/crates/sail-execution/proto/sail/plan/physical.proto", "hash": "7128946088890083971"}, {"file": "tmp_structure/tests/unit/model_evaluation.c", "hash": "1699128691635617584"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_monitor.md", "hash": "439363998595416534"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery_framework.md", "hash": "4481649399000513017"}, {"file": "tests/unit/queue_test.c", "hash": "665738556786491367"}, {"file": "tmp_structure/tests/unit/power_test.c", "hash": "9102832245856542684"}, {"file": "sail-main/crates/sail-spark-connect/src/service/plan_analyzer.rs", "hash": "6772980339067649919"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "sail-main/crates/sail-plan/src/extension/function/functions_utils.rs", "hash": "2860654891870899767"}, {"file": "tmp_structure/tests/unit/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "tmp_structure/tests/unit/test_framework.c", "hash": "8175968703783348918"}, {"file": "tests/src/utils/ml/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "project-tree.py", "hash": "13476751054029764548"}, {"file": "tests/src/utils/ConfigManager.cpp", "hash": "4604724285864392303"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "build/KMDFDriver1.sln", "hash": "3423040701943879221"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/event_tracer.md", "hash": "11863437582874565376"}, {"file": "sail-main/crates/sail-plan/src/resolver/tree/mod.rs", "hash": "16934232786494843567"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/advanced_models.md", "hash": "2138160585059009631"}, {"file": "tests/unit/compressionpredictor.c", "hash": "11817161568988905271"}, {"file": "tmp_structure/tools/utils/core/__pycache__/adaptive_output.cpython-313.pyc", "hash": "16586423574870507236"}, {"file": "docs/KMDF Driver1/tests/src/utils/ConfigManager.md", "hash": "4345364523641645633"}, {"file": "tmp_structure/include/driver/driver_core.h", "hash": "7766847313701610631"}, {"file": "sail-main/docs/development/spark-tests/index.data.ts", "hash": "6322108490672849024"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link-rc.read.1.tlog", "hash": "7229721459879588125"}, {"file": "tmp_structure/src/driver/driver_example.c", "hash": "3805329228591924082"}, {"file": "build/KMDFDriver1.vcxproj", "hash": "15476234754245624861"}, {"file": "tmp_structure/tools/fix/fix_comments.py", "hash": "10782642882713634312"}, {"file": "sail-main/python/pysail/data/tpch/queries/q4.sql", "hash": "17666133533623547607"}, {"file": "tmp_structure/tests/unit/power_manager.c", "hash": "16887739356335473604"}, {"file": "tests/src/device/TouchDebug.h", "hash": "1920040118285242429"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorRecovery.md", "hash": "17104977433029554526"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q61.sql", "hash": "4047995991461021307"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_benchmark.md", "hash": "9826305723782864030"}, {"file": ".vscode/settings.json", "hash": "17193397297327703843"}, {"file": "src/precomp.h", "hash": "13941289408288068255"}, {"file": ".vs/KMDF Driver1/v17/DocumentLayout.backup.json", "hash": "8378132551731795648"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_analyze_table.json", "hash": "9776961423719227320"}, {"file": "sail-main/python/pysail/tests/spark/test_udt.txt", "hash": "9989191576232932437"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "tests/unit/notification_manager.c", "hash": "16862955048666143712"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/CMakeCXXCompilerABI.obj", "hash": "5849171664393102212"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/Touch.md", "hash": "1942753************"}, {"file": "sail-main/crates/sail-sql-parser/Cargo.toml", "hash": "18266609985587003257"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_core.md", "hash": "16004942062479818338"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_usb.md", "hash": "18093605669182738302"}, {"file": "tests/src/utils/optimization/optimization_scheduler.c", "hash": "17822174761870538474"}, {"file": "docs/development_environment.md", "hash": "13754982027817254662"}, {"file": "sail-main/docs/guide/deployment/docker-images/quick-start.md", "hash": "6572891428813381162"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/GlobalPerformanceManager.md", "hash": "8455061991712322545"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_types.md", "hash": "8207845898346842127"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_database.md", "hash": "14381915401241937806"}, {"file": "tests/src/utils/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CompilerIdC.lastbuildstate", "hash": "575028930904181673"}, {"file": "sail-main/crates/sail-plan/src/function/table/range.rs", "hash": "8900262557060383910"}, {"file": "sail-main/crates/sail-plan/src/catalog/utils.rs", "hash": "4792416878041748314"}, {"file": "docs/KMDF Driver1/backup/backup/include/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "tests/src/memory/MemoryManager.c", "hash": "2438749304028039406"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "include/hal/bus/kmdf_spi.h", "hash": "8480019041488027114"}, {"file": "tests/unit/touchprocessor.h", "hash": "12389133180863395364"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/xml.json", "hash": "16718896729979545624"}, {"file": "docs/KMDF Driver1/backup/backup/src/utils/logging/Logger.md", "hash": "4387232194484138377"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery.md", "hash": "11567773110083975900"}, {"file": "tests/unit/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "tests/src/utils/registry_callback.c", "hash": "17205669410881855421"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/mod.rs", "hash": "9788052481286023123"}, {"file": "tmp_structure/tools/utils/static_analyzer.py", "hash": "3472089084897019467"}, {"file": "tests/unit/event_buffer.c", "hash": "7571709781436123223"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmake_install.cmake", "hash": "16305625785130719998"}, {"file": "templates/basic_prompt.json", "hash": "10539456766932864799"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-Debug-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "tests/unit/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "sail-main/python/pysail/tests/conftest.py", "hash": "17753629146554729649"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/config_group.md", "hash": "13037418512325033205"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/hal.md", "hash": "6548767525705845893"}, {"file": ".vscode/ci_config.json", "hash": "11090209362633637534"}, {"file": "sail-main/.github/workflows/docs-deploy.yml", "hash": "5516541848575734947"}, {"file": "tests/src/utils/optimization/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/json.json", "hash": "10876631130397738754"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92.vcxproj.filters", "hash": "4555458998453931876"}, {"file": "docs/KMDF Driver1/tests/src/device/Hardware.md", "hash": "2750892896784880619"}, {"file": "docs/KMDF Driver1/tests/src/utils/logging/logger_test.md", "hash": "10138150490318451487"}, {"file": "sail-main/.github/workflows/report.yml", "hash": "9293228340392913594"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/optimizer.md", "hash": "8333203726053157490"}, {"file": "tests/src/utils/device/device_monitor.c", "hash": "10802691851731843766"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/catalog.proto", "hash": "14970079550392399531"}, {"file": "sail-main/docs/development/documentation/index.md", "hash": "572451540525104841"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryManager.md", "hash": "17860426595588317931"}, {"file": "sail-main/crates/sail-common/Cargo.toml", "hash": "16962587031135646453"}, {"file": "test_huoshan_official.py", "hash": "9163753873127853420"}, {"file": "tmp_structure/tests/unit/securitymanager.c", "hash": "2211632392311551414"}, {"file": "tests/src/utils/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "include/core/log/driver_log.h", "hash": "16100790556580503084"}, {"file": "docs/CMakeLists.txt", "hash": "12467392364659254886"}, {"file": "tmp_structure/tools/utils/generate_driver_comments.py", "hash": "5763936872795964941"}, {"file": "tests/unit/logger_trace.c", "hash": "9545559538338278246"}, {"file": "tests/unit/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "tests/unit/dmaoptimizer.h", "hash": "17813436674171689238"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/scheduler_optimizer.md", "hash": "7056918987226729867"}, {"file": "tests/unit/driver_manager.c", "hash": "18213335034763467499"}, {"file": "tmp_structure/tests/unit/driver_core.c", "hash": "2424469765469195657"}, {"file": "tmp_structure/tests/unit/log_manager.h", "hash": "9031062045211023480"}, {"file": "tmp_structure/tests/unit/trace_core.c", "hash": "10541209614786999240"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_interrupt.md", "hash": "8991698687127595156"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "tmp_structure/tests/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "tmp_structure/tests/unit/error_recovery_framework.c", "hash": "13699128380064941463"}, {"file": "docs/KMDF Driver1/tests/src/security/error_core.md", "hash": "12071042476686428773"}, {"file": "tests/__pycache__/test_basic.cpython-312-pytest-7.4.4.pyc", "hash": "4085399756435944108"}, {"file": "docs/KMDF Driver1/backup/backup/src/core/error_handling.md", "hash": "8736094554619853824"}, {"file": "sail-main/docs/development/build/python.md", "hash": "13984712029897854794"}, {"file": "tests/unit/gyro.c", "hash": "12598367591223412709"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_log_test.md", "hash": "5672191744425007329"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "docs/KMDF Driver1/tests/src/utils/Predictor.md", "hash": "16900082115977006923"}, {"file": "sail-main/crates/sail-spark-connect/data/spark_config.json", "hash": "1463412768270592166"}, {"file": "tmp_structure/tests/unit/queue_test.c", "hash": "665738556786491367"}, {"file": "sail-main/crates/sail-execution/src/job/mod.rs", "hash": "7863211831637997605"}, {"file": "src/hal/devices/spi_device.c.bak", "hash": "6866354232947168482"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/array.rs", "hash": "1243479640349874681"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver.md", "hash": "12889633037907936946"}, {"file": "sail-main/crates/sail-plan/src/error.rs", "hash": "14891450081883383037"}, {"file": "sail-main/crates/sail-spark-connect/src/server.rs", "hash": "4819240957932337069"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "include/core/device/device_manager.h", "hash": "10923848530803440452"}, {"file": "tests/src/utils/device/device_power.c", "hash": "12389752397870169629"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model.md", "hash": "5760546782410447305"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link-cvtres.write.1.tlog", "hash": "10770095702357559096"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/core/CoreManager.md", "hash": "3302314418596781527"}, {"file": "sail-main/crates/sail-common-datafusion/src/udf.rs", "hash": "950803541346047776"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_register_udtf.txt", "hash": "8952755718232378843"}, {"file": "build/.cmake/api/v1/reply/directory-.-MinSizeRel-f8f23e032cbfbdbf872b.json", "hash": "5894047238252644675"}, {"file": "docs/硬件方案设计.txt", "hash": "5039807137852333625"}, {"file": "docs/project_md/ai_roles.md", "hash": "16678242018000698954"}, {"file": "sail-main/python/pysail/tests/spark/literal/test_unicode_escape.txt", "hash": "13484343204966928066"}, {"file": "tests/src/utils/test/test_reporter.c", "hash": "15868353065156172044"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "sail-main/crates/sail-sql-analyzer/src/value.rs", "hash": "11899051109929445996"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link-cvtres.read.1.tlog", "hash": "14266672962152854807"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_order_by.json", "hash": "15451013693676700966"}, {"file": "sail-main/docs/development/recipes/pyspark-local.md", "hash": "14140235148526263010"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/AlertManager.md", "hash": "6970097577749661159"}, {"file": "tests/unit/distributed_training.c", "hash": "13748589215294334048"}, {"file": "sail-main/crates/sail-sql-parser/src/container/vec.rs", "hash": "4678338652483393340"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_array_min_max.rs", "hash": "8083501206383040447"}, {"file": "config/models/e2b.json", "hash": "11946067316528104980"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q92.sql", "hash": "7528936454561216979"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "sail-main/python/pysail/tests/spark/__init__.py", "hash": "3244421341483603138"}, {"file": "sail-main/crates/sail-execution/src/driver/actor/output.rs", "hash": "18368732270980200297"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchDebug.md", "hash": "1115055897695565800"}, {"file": "scripts/extract_errors.md", "hash": "2406851965891616138"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_ops.md", "hash": "6617533335334583642"}, {"file": "tests/unit/touchdebug.h", "hash": "5172689095380585051"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "sail-main/crates/sail-plan/src/function/mod.rs", "hash": "7765345436695608262"}, {"file": "tools/dependency_visualizer.py", "hash": "6834548461517481430"}, {"file": "tests/unit/driver_includes.h", "hash": "17360877510984532985"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_set_operation.json", "hash": "9171489849955548451"}, {"file": "tests/src/utils/QueueOperations.c", "hash": "8016806957506324853"}, {"file": "test_doubao_standard.py", "hash": "6848855045427525730"}, {"file": "sail-main/crates/sail-plan/src/catalog/database.rs", "hash": "15914360527861661359"}, {"file": "sail-main/crates/sail-plan/src/function/generator.rs", "hash": "15790749524266908379"}, {"file": "src/core/driver/driver_entry.c", "hash": "15358898381255813726"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_with.json", "hash": "12255423529810075634"}, {"file": "tests/test_quality_controller.py", "hash": "13643594330830895234"}, {"file": "docs/KMDF Driver1/tests/src/utils/ProcessFunctions.md", "hash": "6572274267278185400"}, {"file": "tests/src/performance/PerformanceData.c", "hash": "10364043397620603240"}, {"file": "tmp_structure/tests/unit/device_context.c", "hash": "3876629123896102954"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/CMakeFiles/TargetDirectories.txt", "hash": "5797799117669636633"}, {"file": "tests/src/utils/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/suites/mod.rs", "hash": "720654792062403319"}, {"file": "sail-main/scripts/spark-tests/spark-3.5.5.patch", "hash": "6359546373559934147"}, {"file": "docs/KMDF Driver1/backup/include/driver/driver_types.md", "hash": "6648445345879484221"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/sample_test.md", "hash": "9338080078639493927"}, {"file": "docs/KMDF Driver1/backup/backup/include/bus/kmdf_spi.md", "hash": "6188852679481726868"}, {"file": "sail-main/docs/development/index.data.ts", "hash": "6971438091880268344"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/queue.md", "hash": "2482812125865225743"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/spi_interface.md", "hash": "13233617362743644576"}, {"file": "test_huoshan_api.py", "hash": "11998932597004611737"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q75.sql", "hash": "3010440079224418514"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_manager.md", "hash": "15079418453579044879"}, {"file": "sail-main/.devcontainer/devcontainer.json", "hash": "15055127830166295336"}, {"file": "tests/src/utils/device/i2_c_interface.c", "hash": "931873009436168421"}, {"file": "header_consistency_summary.md", "hash": "6415337201004196467"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/queue_test.md", "hash": "1004855502735791525"}, {"file": "tmp_structure/tests/unit/driver_includes.h", "hash": "5942675050603689176"}, {"file": "sail-main/Cargo.toml", "hash": "2627271171072777563"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/interval.rs", "hash": "7499266413080512205"}, {"file": "config/models/wolframalpha.json", "hash": "754122350504641271"}, {"file": "tmp_structure/tools/fix/fix_build_issues.py", "hash": "12950168642469148032"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CL.command.1.tlog", "hash": "9278843941399868106"}, {"file": "tests/src/memory/DMAOptimizer.h", "hash": "17577588181507726687"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/string.rs", "hash": "4942247113328582643"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_evaluator.md", "hash": "8871547997162145722"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "tests/src/security/error_prevention.c", "hash": "6292741100613388313"}, {"file": "sail-main/crates/sail-common-datafusion/src/lib.rs", "hash": "9057018493708238846"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAManager.md", "hash": "9127980178123817019"}, {"file": "tests/src/performance/PerformanceExport.c", "hash": "7332497143218165792"}, {"file": "sail-main/crates/sail-cli/src/python/native_logging.py", "hash": "10451321795210135464"}, {"file": "tmp_structure/tools/fix/fix_code_style.ps1", "hash": "5626665489439777687"}, {"file": "tests/src/memory/MemoryManager.h", "hash": "16144556611256439050"}, {"file": "sail-main/crates/sail-plan/src/resolver/option.rs", "hash": "1078496253997889245"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_scheduler.md", "hash": "4149850547689728390"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_udtf.rs", "hash": "5081385752353838566"}, {"file": "docs/KMDF Driver1/backup/include/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "tests/src/driver/driver_manager.c", "hash": "18213335034763467499"}, {"file": "sail-main/docs/development/recipes/debugger.md", "hash": "5141050055726359097"}, {"file": "tmp_structure/tools/utils/full_verify.ps1", "hash": "9828792850310277588"}, {"file": "sail-main/crates/sail-common/src/error.rs", "hash": "8139916405960863422"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/module_manager.md", "hash": "11840949454089485922"}, {"file": "tests/src/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "docs/MCP使用方法/context7_mcp使用指南.md", "hash": "13953553922861588048"}, {"file": ".vscode/dev_environment.json", "hash": "12134811363302867853"}, {"file": "docs/project_md/ai_optimization_strategies.md", "hash": "14102316258447283852"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_power.md", "hash": "3395931921895444161"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q64.sql", "hash": "1336525504621389115"}, {"file": "tmp_structure/include/bus/kmdf_spi.h", "hash": "18044188265549547991"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "304722371227472817"}, {"file": "tmp_structure/tests/performance/touchperformance.h", "hash": "12049939923929688356"}, {"file": "tests/src/utils/simd/simd_performance.c", "hash": "9288036268856686374"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/usb_interface.md", "hash": "15608419832055058263"}, {"file": "tmp_structure/tests/unit/simd_implementation.h", "hash": "14180874393517690822"}, {"file": "tmp_structure/tools/utils/init.py", "hash": "12994821110821436525"}, {"file": "sail-main/crates/sail-plan/src/resolver/statistic.rs", "hash": "14825709165873667845"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/resource_optimizer.md", "hash": "13332246610626941843"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/power/power_test.md", "hash": "8564540950060806619"}, {"file": "build/ZERO_CHECK.vcxproj.filters", "hash": "17375854973772690826"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/event_trace.md", "hash": "12299964648108506000"}, {"file": "sail-main/.github/workflows/release.yml", "hash": "564444430300182822"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_queue.md", "hash": "7516366818546283918"}, {"file": "tmp_structure/docs/MACE-S_使用指南.md", "hash": "5479686292920693858"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/MaintenanceManager.md", "hash": "5703461401317863092"}, {"file": "build/format.vcxproj.filters", "hash": "4431466903779244657"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_test.md", "hash": "4783658079732715387"}, {"file": "sail-main/docker/release/Dockerfile", "hash": "5901002113226047486"}, {"file": "sail-main/docker/quickstart/Dockerfile", "hash": "14166992542883053815"}, {"file": "tests/src/utils/optimization/simd_optimization.c", "hash": "1016262082760471380"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery.md", "hash": "11567773110083975900"}, {"file": "sail-main/crates/sail-execution/src/worker_manager/mod.rs", "hash": "7529962681055001174"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_core.md", "hash": "2996160899354651317"}, {"file": "tmp_structure/tests/unit/registry_callback.c", "hash": "17205669410881855421"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/base.proto", "hash": "13810954975127323821"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_power.md", "hash": "3395931921895444161"}, {"file": "tmp_structure/tests/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_analyzer.md", "hash": "12681780716891350526"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeFiles/cmake.check_cache", "hash": "8396945883222416317"}, {"file": "tmp_structure/tests/performance/performancemonitor.c", "hash": "17506105073300868858"}, {"file": "project_structure_full.txt", "hash": "11809739184110832730"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverEntry.md", "hash": "16472842237000653964"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_manager.md", "hash": "968320189468067565"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/telemetry_manager.md", "hash": "247681583355927358"}, {"file": "tests/src/utils/resource_manager.c", "hash": "157428320305306828"}, {"file": "sail-main/python/pysail/__main__.py", "hash": "840855760045693408"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_resource_monitor.md", "hash": "9018393555267135129"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/test_analyzer.md", "hash": "6049406713010296516"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/manager.md", "hash": "12857642143589749316"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q3.sql", "hash": "1690281631481285044"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/memory_manager.md", "hash": "10511834971114926523"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/adaptive_scheduler.md", "hash": "321518494769210079"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmake_install.cmake", "hash": "422670481707435688"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_manager.md", "hash": "2473704941982409361"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/GlobalVariables.md", "hash": "3478961572820031987"}, {"file": "sail-main/crates/sail-common/src/spec/plan.rs", "hash": "18044194924531868797"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device.md", "hash": "7448308400255503639"}, {"file": "tests/src/device/TouchAI.c", "hash": "1972091963095643935"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/event_tracer.md", "hash": "15011940588707327893"}, {"file": "tests/src/device/TouchManager.c", "hash": "2612989593074257093"}, {"file": "sail-main/.github/workflows/gold-data-script-validation.yml", "hash": "12664744623794756229"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": ".vscode/sdv-default.xml", "hash": "10931019705986488358"}, {"file": "tests/unit/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "sail-main/crates/sail-plan/src/extension/function/functions_nested_utils.rs", "hash": "3233874211538484179"}, {"file": "sail-main/docs/reference/python/[page].md", "hash": "16360610385314748251"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84_MINSIZEREL_loc", "hash": "14350971382702270915"}, {"file": "tmp_structure/tools/utils/update_includes.py", "hash": "7148404675258928119"}, {"file": "docs/project_md/cursor_machine_id.py.teaching.md", "hash": "7810754778805062073"}, {"file": ".trae/rules/project_rules.md", "hash": "4880051458355600887"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handling_test.md", "hash": "7236286416758687736"}, {"file": "tmp_structure/tools/utils/setup_crash_dump.ps1", "hash": "10910026215077032983"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q39.sql", "hash": "10410634202096521361"}, {"file": "tests/unit/monitor.h", "hash": "7267562670448795700"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorPrevention.md", "hash": "17818079868441769260"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "docs/KMDF Driver1/tests/src/io/IOManager.md", "hash": "13313837815783856174"}, {"file": "tests/unit/logger_test.c", "hash": "15334138061466012986"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/range.rs", "hash": "7488894405386131466"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q42.sql", "hash": "4947922874527301888"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/monitor.md", "hash": "12333284298038535705"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger_trace.md", "hash": "10304453869909953023"}, {"file": "src/driver_main.c", "hash": "7591992841743508423"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CMakeCXXCompilerId.cpp", "hash": "18364213766541449008"}, {"file": "tests/unit/registry_cache.c", "hash": "18036733393600373308"}, {"file": "tmp_structure/tests/performance/device_performance_monitor.c", "hash": "11916941423559648567"}, {"file": "tmp_structure/tools/fix/fix_headers.py", "hash": "10161838224352095660"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/string.json", "hash": "1503482410603600953"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/CompilerIdC.vcxproj", "hash": "11628124092553633657"}, {"file": "config/models/huoshan.json", "hash": "8084680881529474041"}, {"file": "tests/src/performance/monitor.cpp", "hash": "7364005627501044434"}, {"file": "tests/unit/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "sail-main/scripts/hadoop/Dockerfile", "hash": "11176924176883729692"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/query.rs", "hash": "9712320843218264685"}, {"file": "tests/unit/resource_management_test.c", "hash": "81080999296386809"}, {"file": "sail-main/crates/sail-plan/src/extension/function/error_utils.rs", "hash": "15999736245953826328"}, {"file": "tests/src/utils/manager.cpp", "hash": "16568954766253776110"}, {"file": "tools/convert_hal_encoding.ps1", "hash": "12457230657918497233"}, {"file": "sail-main/crates/sail-plan/src/extension/function/struct_function.rs", "hash": "4603912761720620428"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/Predictor.md", "hash": "10386180171614306631"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_array_item_with_position.rs", "hash": "111725435070186286"}, {"file": "tmp_structure/tests/unit/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_manager.md", "hash": "3365062664957336262"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "docs/KMDF Driver1/backup/include/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "docs/KMDF Driver1/tests/src/utils/event_dispatcher.md", "hash": "467961671039835308"}, {"file": ".github/workflows/build.yml", "hash": "17535677776325500891"}, {"file": "scripts/add_date_to_files.bat", "hash": "9908643534159953284"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformancePredictor.md", "hash": "15411689379061262882"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchManager.md", "hash": "8626033256526254123"}, {"file": "tests/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "tmp_structure/tools/utils/analyze_driver.ps1", "hash": "2536145212828588932"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_group_by.json", "hash": "9579535284371476693"}, {"file": "tests/src/utils/optimization/adaptive_scheduler.c", "hash": "10385198795992790880"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CompressionPredictor.md", "hash": "14348087692511970447"}, {"file": "config/models/gemini.json", "hash": "5869872821802908452"}, {"file": "sail-main/crates/sail-sql-analyzer/src/statement.rs", "hash": "193915082934261449"}, {"file": ".editorconfig", "hash": "16188607900808665775"}, {"file": "docs/通过自然语言理解代码实施计划.md", "hash": "10895306788647699618"}, {"file": "tmp_structure/tools/utils/agents/security_agent.py", "hash": "16177457410736494698"}, {"file": "sail-main/crates/sail-plan/src/extension/source/rename.rs", "hash": "11855695332454090754"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_insert_into.json", "hash": "8055660934442802930"}, {"file": "sail-main/scripts/hadoop/core-site.xml", "hash": "5062307001140676826"}, {"file": "tmp_structure/tests/performance/performance_analyzer.h", "hash": "4806248608216213265"}, {"file": "tests/src/utils/device/device_error_monitor.c", "hash": "15272029842124575418"}, {"file": "sail-main/crates/sail-sql-macro/src/attribute.rs", "hash": "5424699596849975368"}, {"file": "sail-main/tailwind.config.js", "hash": "13647823268823553124"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "tmp_structure/tools/utils/analyze_project.ps1", "hash": "3670972854219527193"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "docs/api/glhf.chatuser-settingsapi.txt", "hash": "16963602934096152440"}, {"file": "sail-main/python/pysail/tests/spark/test_table_function.py", "hash": "5104746633172959422"}, {"file": "tmp_structure/tests/unit/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "sail-main/crates/sail-execution/src/driver/actor/handler.rs", "hash": "3423395635345304466"}, {"file": "docs/project_md/teaching_annotator.py.teaching.md", "hash": "5755903323171479734"}, {"file": "tests/unit/memorypool.c", "hash": "8309557649383373614"}, {"file": "tmp_structure/tools/analysis/run_analysis.ps1", "hash": "5967366306196400324"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/writer.rs", "hash": "14576361605297527648"}, {"file": "include/core/common/Common.h", "hash": "13124832492036846080"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/collection.rs", "hash": "18429328170210417327"}, {"file": "simple_knowledge_test.py", "hash": "2074031030093214766"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "tests/src/utils/device/device_workitem.c", "hash": "15964808878924499639"}, {"file": "tmp_structure/tools/utils/check_versions.ps1", "hash": "11390489552012229852"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/resource_manager.md", "hash": "17123311457972373377"}, {"file": "tmp_structure/tests/unit/logger_test.c", "hash": "15334138061466012986"}, {"file": "tests/unit/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "tmp_structure/tests/unit/advanced_models.c", "hash": "10802020780109055119"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMAKE_TRY_COMPILE.sln", "hash": "2107904799354737948"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_performance_monitor.md", "hash": "767285653915167176"}, {"file": "sail-main/crates/sail-sql-macro/src/tree/parser.rs", "hash": "18425928806564058606"}, {"file": "reorganizeprojectenhanced.py", "hash": "5014953226700913895"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/range.rs", "hash": "1937668281631027514"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/bitwise.rs", "hash": "11414600566058899959"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_health_monitor.md", "hash": "10750751687875650870"}, {"file": "sail-main/crates/sail-plan/src/catalog/view.rs", "hash": "1742769208005708172"}, {"file": "sail-main/crates/sail-common/src/datetime.rs", "hash": "12528327919394304201"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFSPI.md", "hash": "5079633368373070158"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger.md", "hash": "1309368071159714371"}, {"file": "tmp_structure/tests/unit/simd_core.c", "hash": "12729929013449999851"}, {"file": "include/device/power/device_power.h", "hash": "7709195725722523957"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_performance.md", "hash": "1048439768488625304"}, {"file": "tmp_structure/tests/unit/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/data_type_json.rs", "hash": "5509971097891843571"}, {"file": "tmp_structure/tests/unit/touchai.h", "hash": "7382272859188051659"}, {"file": "sail-main/crates/sail-plan/src/resolver/tree/table_input.rs", "hash": "15232760206625048882"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_control.md", "hash": "14823537430827988127"}, {"file": "tests/test_output_formatter.py", "hash": "15495299993053318027"}, {"file": "tests/unit/device_timer.c", "hash": "3835497012896294941"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.read.1.tlog", "hash": "5351354570326925566"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/ALL_BUILD.vcxproj.filters", "hash": "12965466701489245028"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_test.md", "hash": "15477382941510235392"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/numeric.json", "hash": "8364533858330714355"}, {"file": "sail-main/crates/sail-python-udf/src/cereal/pyspark_udf.rs", "hash": "6107702549188149385"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchController.md", "hash": "6274914068193184456"}, {"file": "tests/unit/touchcontroller.c", "hash": "7422034257177845882"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q30.sql", "hash": "12928107519690440495"}, {"file": "tmp_structure/tools/utils/remove_bak_files.ps1", "hash": "14369670428058771939"}, {"file": "sail-main/scripts/spark-tests/prepare-server.sh", "hash": "18317104870222453259"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceAnalyzer.md", "hash": "17860791912881724719"}, {"file": "docs/KMDF Driver1/backup/include/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model_evaluation.md", "hash": "2670150953098694415"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_test.md", "hash": "8777324941864887741"}, {"file": "tmp_structure/tools/utils/core/__pycache__/code_parser.cpython-313.pyc", "hash": "6448863451993605535"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "sail-main/crates/sail-spark-connect/src/service/artifact_manager.rs", "hash": "1530999933829158991"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/conversion.json", "hash": "6583851128483084621"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAHandler.md", "hash": "3316050523215663600"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q77.sql", "hash": "11790629440283164253"}, {"file": "sail-main/crates/sail-common/src/config/mod.rs", "hash": "16230830982023295277"}, {"file": "docs/KMDF Driver1/tmp_structure/src/log/driver_log.md", "hash": "17856606067144505067"}, {"file": "tests/src/device/GyroHwSim.c", "hash": "15003407868114686644"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/Cl.items.tlog", "hash": "7566281551702455546"}, {"file": "tmp_structure/tools/fix/fix_coding_style.py", "hash": "15649094057833155149"}, {"file": "tests/unit/memory_manager.c", "hash": "13099111058581566695"}, {"file": "sail-main/README.md", "hash": "7845841214668030770"}, {"file": "docs/KMDF Driver1/backup/backup/include/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "sail-main/crates/sail-plan/src/extension/function/kurtosis.rs", "hash": "12002951789390878367"}, {"file": "tests/src/memory/DMAManager.c", "hash": "8084048833832484340"}, {"file": "tmp_structure/output/维护文档.md", "hash": "16577422483792727038"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_drop_index.json", "hash": "3444501306637464547"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_context.md", "hash": "11117283112430833157"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/cache_optimizer.md", "hash": "16401685227458206285"}, {"file": "docs/api/gemini.txt", "hash": "14621487865774973161"}, {"file": "docs/KMDF Driver1/backup/backup/include/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchPerformance.md", "hash": "3331360388054379050"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAHandler.md", "hash": "3316050523215663600"}, {"file": "tmp_structure/tests/unit/optimization_database.c", "hash": "1382797123011915962"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q65.sql", "hash": "12109326898637764916"}, {"file": "sail-main/crates/sail-plan/src/resolver/tree/window.rs", "hash": "11237543795010971541"}, {"file": "sail-main/.editorconfig", "hash": "14476316283412046255"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/HardwareManager.md", "hash": "6930293510445000701"}, {"file": "tmp_structure/tools/utils/agents/structural_agent.py", "hash": "9375312455345966219"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/Driver.md", "hash": "3073102130935575849"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchAI.md", "hash": "939606902265643493"}, {"file": "include/hal/devices/spi_device.h", "hash": "8018070506967611507"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/link.command.1.tlog", "hash": "7467075880246793357"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/suites/parser.rs", "hash": "9627191439754351890"}, {"file": "tmp_structure/tools/fix/fix_header_guards.ps1", "hash": "3509839413937215495"}, {"file": "tmp_structure/tests/unit/dmahandler.c", "hash": "7693675145006693987"}, {"file": "tests/src/utils/ProcessFunctions.c", "hash": "15471745620740669001"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "tmp_structure/tests/unit/health_checker.c", "hash": "13081061987138608445"}, {"file": "sail-main/scripts/hadoop/hdfs-site.xml", "hash": "10274758425452301614"}, {"file": "tests/src/device/TouchPredictor.c", "hash": "10637331736903154528"}, {"file": "config/models/jina.json", "hash": "14050760086702475995"}, {"file": "tests/src/utils/device/device_test.c", "hash": "4283402855535051437"}, {"file": "tmp_structure/tests/unit/touchcontroller.c", "hash": "7422034257177845882"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_test.md", "hash": "11557057547972990945"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/statistics_manager.md", "hash": "8721089824310490847"}, {"file": "tests/src/driver/Driver.c", "hash": "5937756760430682963"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_monitor.md", "hash": "16894954371874134391"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/CMAKE_TRY_COMPILE.sln", "hash": "14111373029338577345"}, {"file": "docs/project_md/code_teacher.md", "hash": "3104141829689213043"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.Build.CppClean.log", "hash": "3244421341483603138"}, {"file": "sail-main/docs/.vitepress/theme/languages/pycon.ts", "hash": "14315779875086819556"}, {"file": "tests/unit/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "tmp_structure/tests/unit/device_state.c", "hash": "232531793412726154"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.read.1.tlog", "hash": "13129952132537788104"}, {"file": "tests/src/utils/config_group.c", "hash": "13073677126841465732"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_date.rs", "hash": "7781849482137998247"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_explain.json", "hash": "11911910660849848073"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_interrupt.md", "hash": "8991698687127595156"}, {"file": "sail-main/scripts/spark-gold-data/spark-3.5.5.patch", "hash": "17172212224483698571"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_next_day.rs", "hash": "2118031940276555132"}, {"file": "tmp_structure/tests/unit/dmamanager.h", "hash": "7624006582029009443"}, {"file": "sail-main/scripts/spark-gold-data/bootstrap.sh", "hash": "9762840548780912462"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchAI.md", "hash": "12773210007988829165"}, {"file": "scripts/fix_newlines.ps1", "hash": "16931800921766998893"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q1.sql", "hash": "10350878993337283709"}, {"file": "gitignore", "hash": "2047371395103394036"}, {"file": "scripts/simple_check.ps1", "hash": "5426566532885727715"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/map.rs", "hash": "4893992619834201579"}, {"file": "tmp_structure/tests/unit/errorhandler.c", "hash": "638403771659111855"}, {"file": "docs/MCP使用方法/everything_search_mcp使用指南.md", "hash": "17433933541212280052"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_reporter.md", "hash": "1776717960419419935"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt.md", "hash": "7122344570168608003"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_operations.md", "hash": "7572738197267690752"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "304722371227472817"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.vcxproj", "hash": "11612577334171339520"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/map_partitions.rs", "hash": "8383615096463810399"}, {"file": "tests/src/performance/performance_benchmark.c", "hash": "11801416405866941523"}, {"file": "tests/src/utils/ml/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "tests/unit/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "tmp_structure/tools/fix/fix_sources.ps1", "hash": "15787374859056701385"}, {"file": "tests/src/device/Gyro.c", "hash": "11748498765099230555"}, {"file": "sail-main/crates/sail-execution/src/worker/flight_server.rs", "hash": "5588795731009171476"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "tmp_structure/include/device/device_stat_types.h", "hash": "12470765283103911762"}, {"file": "tests/src/utils/ModuleManager.c", "hash": "12705701726995479740"}, {"file": "logs/build_error.log", "hash": "7207608942876430833"}, {"file": "tests/__pycache__/test_prompt_engine.cpython-314.pyc", "hash": "8918093362374298349"}, {"file": "sail-main/scripts/spark-tests/run-tests.sh", "hash": "4925016631996885202"}, {"file": "tests/src/performance/performance_collector.c", "hash": "13527805926885479396"}, {"file": ".vscode/performance_test_config.json", "hash": "4503311343993568100"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_test.md", "hash": "13058366218973303497"}, {"file": "docs/project_md/CMakeCache.txt", "hash": "17177311492269572653"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/analyzer.md", "hash": "8824479516875383820"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "tests/src/utils/ReliabilityManager.c", "hash": "16962898220370878971"}, {"file": "sail-main/docs/.vitepress/theme/components/SphinxPage.vue", "hash": "9616729044480920773"}, {"file": ".vs/KMDF Driver1/v17/DocumentLayout.json", "hash": "8378132551731795648"}, {"file": "test_moonshot.py", "hash": "12439654262898103573"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/array.json", "hash": "18181346631180130441"}, {"file": "tmp_structure/tests/performance/simd_performance.c", "hash": "9288036268856686374"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_optimizer.md", "hash": "473303732562322893"}, {"file": "tests/unit/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger_trace.md", "hash": "10304453869909953023"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/commands.proto", "hash": "12764002924363240529"}, {"file": "build/.cmake/api/v1/reply/cmakeFiles-v1-ee050e74fe50c40ba432.json", "hash": "15436201108022110699"}, {"file": "docs/硬件方案设计终极方案.txt", "hash": "15157568784909259289"}, {"file": "tests/unit/hardware.h", "hash": "2185692304651730343"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_map_iter_udf.rs", "hash": "15853425858353473648"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/queue_test.md", "hash": "4288726996381533338"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/CMakeFiles/cmake.check_cache", "hash": "8396945883222416317"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/show_string.rs", "hash": "18205571355820136980"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "build/CMakeFiles/generate.stamp", "hash": "9089816209918881806"}, {"file": "config/models/siliconflow.json", "hash": "9961775674181545909"}, {"file": "tests/unit/resource_optimizer.c", "hash": "2793110138288950515"}, {"file": "tmp_structure/tests/unit/iomanager.c", "hash": "6435585653620536882"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.vcxproj.FileListAbsolute.txt", "hash": "3244421341483603138"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q94.sql", "hash": "11984687439279713918"}, {"file": "sail-main/crates/sail-sql-macro/src/lib.rs", "hash": "17374405551009828042"}, {"file": "config/models/cloudflare.json", "hash": "1927853033215223763"}, {"file": "tools/utils/file_research_teaching_annotator.py", "hash": "11519010549794262179"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link-rc.read.1.tlog", "hash": "5727257940521980044"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/CommonInternal.md", "hash": "7520992645958513174"}, {"file": "sail-main/docs/guide/installation/index.md", "hash": "11791704720480568117"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_operations.md", "hash": "12450057505270295025"}, {"file": "tests/performance/perfmonitor.c", "hash": "11571150342783768226"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "sail-main/python/pysail/tests/spark/test_datetime.py", "hash": "1688580944014371847"}, {"file": "tmp_structure/tests/unit/touchpredictor.c", "hash": "15043340538585148217"}, {"file": "docs/project_md/idear2怎么把代码注释做好.txt", "hash": "15555374162579802027"}, {"file": "tmp_structure/tools/utils/ai_collaboration_process.py", "hash": "12273776964799610497"}, {"file": "sail-main/docs/.vitepress/theme/utils/link.ts", "hash": "5474510056342388428"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/agent_discussion.cpython-313.pyc", "hash": "1448609043699339899"}, {"file": "tmp_structure/tools/utils/resolve_duplicates.py", "hash": "16547623118355958279"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/device/DeviceStatTypes.md", "hash": "8956006212008637204"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q41.sql", "hash": "17180452757488044960"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/mod.rs", "hash": "6826455669814928605"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/link.read.1.tlog", "hash": "5612583295716819166"}, {"file": "test_volcengine_api.py", "hash": "4524134310110625423"}, {"file": "tests/src/core/resource_management_test.c", "hash": "81080999296386809"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/CL.write.1.tlog", "hash": "14304377311043167597"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorRecovery.md", "hash": "677055025807912378"}, {"file": "tests/unit/trace.h", "hash": "7325021250740089223"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/Debug/cmTC_f343a.pdb", "hash": "11626945863842737461"}, {"file": "tests/src/utils/event_buffer.c", "hash": "7571709781436123223"}, {"file": "docs/KMDF Driver1/tmp_structure/include/driver/driver.md", "hash": "9302614752768382714"}, {"file": "sail-main/python/pysail/data/tpch/queries/q6.sql", "hash": "18064240025747347268"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/mod.rs", "hash": "4871477666523010816"}, {"file": "tests/performance/performance_manager.c", "hash": "597698374259466063"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/AlertManager.md", "hash": "6970097577749661159"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "tests/unit/statistics_manager.c", "hash": "4531819615493049368"}, {"file": "tests/src/utils/logging/logger_test.c", "hash": "15334138061466012986"}, {"file": "sail-main/crates/sail-plan/src/resolver/state.rs", "hash": "7939309350847985671"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_manager.md", "hash": "13629910787921453760"}, {"file": "tmp_structure/tools/fix/fix_tabs.py", "hash": "16509530680484815031"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_manager.md", "hash": "9371494073877759190"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "tests/src/utils/simd/simd_operations.c", "hash": "2970634337399079429"}, {"file": "sail-main/docs/development/spark-tests/spark-setup.md", "hash": "12272358544932625314"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q25.sql", "hash": "16120114360018925237"}, {"file": "tmp_structure/tests/unit/test_main.c", "hash": "17856805622170871630"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/simd_optimizer.md", "hash": "1767557284205257898"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_test.md", "hash": "7510514405337728791"}, {"file": "tests/src/security/ErrorRecovery.c", "hash": "7122396928859668049"}, {"file": "tests/src/utils/registry_cache.c", "hash": "18036733393600373308"}, {"file": "src/core/log/driver_log.c", "hash": "16185958001074647137"}, {"file": ".vscode/alert_rules.json", "hash": "3146142592217095474"}, {"file": "tests/unit/device_monitor.c", "hash": "10802691851731843766"}, {"file": "tmp_structure/tools/utils/core/__pycache__/integration_engine.cpython-313.pyc", "hash": "16035286460932738115"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/trace_core.md", "hash": "10502311811018818231"}, {"file": "tests/unit/spi_interface.c", "hash": "13749364630813119982"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/generate.stamp.rule", "hash": "1819760324823437288"}, {"file": "sail-main/crates/sail-plan/src/extension/function/update_struct_field.rs", "hash": "1730652097402622239"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_state.md", "hash": "11490068797830905955"}, {"file": "docs/KMDF Driver1/backup/include/core/Common.md", "hash": "14593471796666110241"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/table_schema.json", "hash": "3824647128997317756"}, {"file": "tests/src/performance/performance_monitor_manager.c", "hash": "9008144578910281466"}, {"file": "sail-main/crates/sail-common/src/spec/expression.rs", "hash": "7452135290283953122"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_optimizer.md", "hash": "12099553868555302793"}, {"file": "sail-main/docs/reference/python/[page].paths.ts", "hash": "11350639286576655985"}, {"file": "tests/src/utils/simd/simd_core.c", "hash": "12729929013449999851"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/utils/logging/Logger.md", "hash": "12110289051126337780"}, {"file": "check_structure.py", "hash": "12371258786447826844"}, {"file": "tests/unit/modulemanager.c", "hash": "12705701726995479740"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceMetricsImpl.md", "hash": "13584290171069905482"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/simd_optimization.md", "hash": "17159784567835091991"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/catalog.rs", "hash": "5649639822848091551"}, {"file": "tests/unit/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "sail-main/crates/sail-cli/src/python/spark_shell.py", "hash": "11187954774840003066"}, {"file": "tmp_structure/tests/unit/simd_ops.c", "hash": "4755770406672099314"}, {"file": "sail-main/docs/development/recipes/standalone-binary.md", "hash": "11436726353073958337"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/event_trace.md", "hash": "14921740565764576536"}, {"file": "tmp_structure/tests/unit/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_alter_table.json", "hash": "15370656682480414017"}, {"file": "tests/unit/globalvariables.c", "hash": "703525128182974641"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "include/core/common/core_types.h", "hash": "11012044591064156036"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q66.sql", "hash": "7394870621772540246"}, {"file": "include/core/types/CommonTypes.h", "hash": "11814470043667503803"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFUSB.md", "hash": "2444171969419869765"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "sail-main/.gitignore", "hash": "4689232083097639637"}, {"file": "tests/unit/device_power.c", "hash": "12389752397870169629"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/date.json", "hash": "7666953467868312993"}, {"file": "tests/performance/performanceexport.c", "hash": "7332497143218165792"}, {"file": "tools/code_quality_analyzer.py", "hash": "16225249839978784825"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_codes.md", "hash": "61239640176245628"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-Debug-6adffef19a40386b1678.json", "hash": "16997379449962811400"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "tmp_structure/include/bus/kmdf_i2c.h", "hash": "13876872804077991685"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q86.sql", "hash": "4475747016247331210"}, {"file": "tests/src/memory/DMAManager.h", "hash": "18121240782498049197"}, {"file": "tmp_structure/tools/fix/fix_headers.ps1", "hash": "16935879167314502722"}, {"file": "build/ALL_BUILD.vcxproj.filters", "hash": "9995966739536615345"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_performance.md", "hash": "6815440093307604725"}, {"file": ".github/workflows/ci.yml", "hash": "12304493898047627145"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceData.md", "hash": "17754136278712063044"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q38.sql", "hash": "14474885157957815939"}, {"file": "sail-main/crates/sail-common/src/lib.rs", "hash": "8592510143158352995"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/plan.rs", "hash": "14251800827778753233"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_manager.md", "hash": "12781635121050400413"}, {"file": "tmp_structure/tools/utils/agents/knowledge_agent.py", "hash": "2538876160459838789"}, {"file": "tmp_structure/include/device/device_types.h", "hash": "2655726247897297113"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q47.sql", "hash": "1714494387432677827"}, {"file": "tmp_structure/tests/unit/error_module.c", "hash": "10513386486768326845"}, {"file": "tmp_structure/tests/unit/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "header_template.h", "hash": "2209264767001714824"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverCore.md", "hash": "11754590676982575203"}, {"file": "docs/KMDF Driver1/backup/include/error/kmdf_error_internal.md", "hash": "16508033105774443594"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_performance_monitor.md", "hash": "5433707484237699770"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q27.sql", "hash": "6566367562527824563"}, {"file": "test_huoshan_final.py", "hash": "16129081120139665508"}, {"file": "sail-main/docs/guide/tasks/udf/overview.md", "hash": "4656771696492232237"}, {"file": "docs/batch_processing.md", "hash": "3156366143293904858"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/simd_optimizer.md", "hash": "5137710775163109652"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/interactive_agent.cpython-313.pyc", "hash": "13970068067423626862"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/conversion.rs", "hash": "13067719464803534592"}, {"file": "include/hal/bus/kmdf_gpio.h", "hash": "4006174276729433783"}, {"file": "config/config.json", "hash": "6101988485205918148"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/optimizer.md", "hash": "4900046108333349195"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_timer.md", "hash": "12858369133682259518"}, {"file": "tmp_structure/tools/utils/check_line_endings.py", "hash": "12193072142883303704"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "tmp_structure/tools/utils/check_env.py", "hash": "15916084536442429794"}, {"file": "include/core/driver/driver_entry.h", "hash": "9612320755824265761"}, {"file": "tmp_structure/tests/unit/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "docs/KMDF Driver1/backup/include/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": "tmp_structure/tests/unit/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": "docs/KMDF Driver1/tests/src/core/GlobalVariables.md", "hash": "17948367196669321453"}, {"file": "tmp_structure/tools/fix/fix_source_files.py", "hash": "8518775545233258652"}, {"file": "tmp_structure/src/driver/driverentry.c", "hash": "73705776410683099"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_describe.json", "hash": "3160540429718142385"}, {"file": "tests/src/utils/optimization/optimization_database.c", "hash": "1382797123011915962"}, {"file": "docs/api/api_config_help.md", "hash": "8934429542747036263"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_model.md", "hash": "17020077241309144761"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorHandler.md", "hash": "17849145562086669299"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q14.sql", "hash": "8283087705360995894"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/struct.rs", "hash": "4305274125679174703"}, {"file": "tmp_structure/tests/performance/performanceexport.c", "hash": "7332497143218165792"}, {"file": ".vscode/driver_signing_config.json", "hash": "2007317409999427743"}, {"file": "tmp_structure/tools/test/create_test_cert.ps1", "hash": "9282573216776791126"}, {"file": "tests/unit/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/i2_c_interface.md", "hash": "7589968553775886296"}, {"file": "sail-main/crates/sail-plan/src/formatter.rs", "hash": "8275905712870454757"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/link.write.1.tlog", "hash": "186307984892038081"}, {"file": "tmp_structure/include/bus/kmdf_usb.h", "hash": "11899584837454725746"}, {"file": "config/models/openrouter.json", "hash": "11233378633730705709"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q19.sql", "hash": "17136318039239278883"}, {"file": "config/models/mistral.json", "hash": "9872388936552245613"}, {"file": "sail-main/crates/sail-execution/build.rs", "hash": "6541327548361689881"}, {"file": "tmp_structure/tools/utils/enhanced_analyzer.py", "hash": "1750511501545068280"}, {"file": "tests/unit/trace.c", "hash": "7049877013033903552"}, {"file": "tmp_structure/tests/unit/driverlog.c", "hash": "13535373348672446805"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_recovery_kmdf.md", "hash": "12638653863570197085"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_strategies.md", "hash": "1076962325564799858"}, {"file": "src/hal/devices/i2c_device.c.bak", "hash": "5742460478894627044"}, {"file": "tmp_structure/tests/unit/resource_scheduler.c", "hash": "14879402796131490726"}, {"file": "package.json", "hash": "16753833722885029064"}, {"file": "sail-main/docs/development/spark-tests/github-actions.md", "hash": "14530262708793187491"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/misc.rs", "hash": "3651476403251719408"}, {"file": "sail-main/crates/sail-sql-parser/src/container/mod.rs", "hash": "9128912945693495065"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger_trace.md", "hash": "1185709471855129467"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "build/x64/Debug/ALL_BUILD/ALL_BUILD.vcxproj.FileListAbsolute.txt", "hash": "3244421341483603138"}, {"file": "tests/src/utils/device/device_queue.c", "hash": "16684379454006678062"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/logger_core.md", "hash": "3068037791562044206"}, {"file": "tmp_structure/tools/fix/fix_header_guard_format.py", "hash": "17645422050763808232"}, {"file": "sail-main/crates/sail-common/src/tests.rs", "hash": "16031823145590698957"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "tmp_structure/tests/unit/kmdfspi.c", "hash": "1159729704795014365"}, {"file": "tmp_structure/tools/fix/fix_header_guards_improved.py", "hash": "2118443273864971945"}, {"file": "tests/src/utils/device/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "sail-main/scripts/spark-config/README.md", "hash": "1612918950883536558"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_ops.md", "hash": "16405194461971140783"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_state_manager.md", "hash": "11349264977040135580"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_types.md", "hash": "15790440358578900422"}, {"file": "docs/KMDF Driver1/tests/src/security/SecurityManager.md", "hash": "6164524214767333503"}, {"file": "docs/project_md/code_teacher.py.teaching.md", "hash": "4874293727162587059"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.command.1.tlog", "hash": "13279721637047200964"}, {"file": "sail-main/python/pysail/tests/__init__.py", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_power.md", "hash": "2872841184312516318"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/unsuccessfulbuild", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/trace.md", "hash": "12676781890278582523"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchPredictor.md", "hash": "12512413519988847469"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_power.md", "hash": "2872841184312516318"}, {"file": "sail-main/crates/sail-cli/src/lib.rs", "hash": "1264084922547357057"}, {"file": "tmp_structure/tests/unit/i2c_interface.c", "hash": "931873009436168421"}, {"file": "tests/performance/performanceanalyzer.c", "hash": "16444703254818628465"}, {"file": "sail-main/.github/workflows/docs-tag.yml", "hash": "17147309080441199454"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.tlog/CL.command.1.tlog", "hash": "17137440962915435686"}, {"file": "sail-main/crates/sail-execution/src/codec.rs", "hash": "405835099054508763"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/levenshtein.rs", "hash": "975430405959697594"}, {"file": "sail-main/crates/sail-python-udf/src/cereal/pyspark_udtf.rs", "hash": "4295810756521637093"}, {"file": "sail-main/crates/sail-sql-parser/src/container/sequence.rs", "hash": "16014830794209889663"}, {"file": "tmp_structure/tests/unit/driverentry.c", "hash": "15432365288492737233"}, {"file": "tests/unit/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "sail-main/python/pysail/tests/spark/test_column.py", "hash": "5430321482272116381"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/url.rs", "hash": "836663585358760157"}, {"file": "tmp_structure/tests/unit/errormanager.h", "hash": "11956927130609736461"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_resource_monitor.md", "hash": "10903229905072343484"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/security_agent.cpython-313.pyc", "hash": "12259403970642078874"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q73.sql", "hash": "6595289387796080450"}, {"file": "sail-main/crates/sail-spark-connect/src/service/mod.rs", "hash": "11909980483581043601"}, {"file": "tmp_structure/tools/analysis/generate_report.py", "hash": "8759885286234283788"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_data_collector.md", "hash": "6566743570803561025"}, {"file": "sail-main/crates/sail-python-udf/Cargo.toml", "hash": "9225014693812313929"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_analyzer.md", "hash": "14568771719083238013"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchPredictor.md", "hash": "12512413519988847469"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/cmTC_f343a.lastbuildstate", "hash": "18177184362031412170"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92.dir/Debug/cmTC_fba92.tlog/cmTC_fba92.lastbuildstate", "hash": "11592732137412907926"}, {"file": "rundrivertest.bat", "hash": "8778339928498419027"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q6.sql", "hash": "4832027577618553681"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ConfigManager.md", "hash": "14798362301014302758"}, {"file": "tests/src/utils/test/sample_test.c", "hash": "6872651473851085099"}, {"file": "tmp_structure/tests/unit/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "tmp_structure/tests/unit/device_power.c", "hash": "12389752397870169629"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_manager.md", "hash": "13629910787921453760"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/config_manager.md", "hash": "14863063657951608679"}, {"file": "docs/KMDF Driver1/tests/src/device/TouchProcessor.md", "hash": "5462925402941158058"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryPool.md", "hash": "10702931801727958310"}, {"file": "test_volcengine_complete.py", "hash": "17935958284127479142"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "3921821082679517869"}, {"file": "tests/src/utils/test/test_framework.c", "hash": "8175968703783348918"}, {"file": "sail-main/crates/sail-spark-connect/src/schema.rs", "hash": "13030457287234711886"}, {"file": "tmp_structure/tests/unit/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": ".vscode/code_analysis_config.json", "hash": "312228725902229289"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "tests/src/utils/telemetry_processor.c", "hash": "14561982686020510948"}, {"file": "tmp_structure/tests/unit/optimization_policy.c", "hash": "1075060633170793996"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_test.md", "hash": "5528223060007826182"}, {"file": "sail-main/python/pysail/tests/spark/test_tpcds.py", "hash": "15989779133097005921"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "tmp_structure/tools/utils/agents/interactive_agent.py", "hash": "13399413608931209626"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q96.sql", "hash": "17042573849984754324"}, {"file": "sail-main/crates/sail-plan/src/extension/function/drop_struct_field.rs", "hash": "12800793420338446245"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_make_ym_interval.rs", "hash": "6314328649980953421"}, {"file": "sail-main/crates/sail-execution/src/worker_manager/local.rs", "hash": "11436841985849319089"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_io.md", "hash": "15280929294455826365"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/spi_controller.md", "hash": "7615035126808120337"}, {"file": "docs/project_md/requirements.txt", "hash": "1345609730279446603"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_io.md", "hash": "16194404026375741960"}, {"file": "tests/src/utils/recovery_impl.c", "hash": "11673762893829083973"}, {"file": "sail-main/python/pysail/tests/spark/literal/test_date.txt", "hash": "13127126473493670592"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.tlog/CL.write.1.tlog", "hash": "12455854967440642625"}, {"file": "sail-main/crates/sail-common/src/spec/literal.rs", "hash": "14427337339547091104"}, {"file": "sail-main/crates/sail-sql-analyzer/src/query.rs", "hash": "17931822595950539198"}, {"file": "tmp_structure/driver_entry_test.py", "hash": "2253797170165017009"}, {"file": "tmp_structure/tests/unit/kmdferror.c", "hash": "3859077427525291268"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_manager.md", "hash": "14138160186724125070"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceExport.md", "hash": "17280862790576496424"}, {"file": "sail-main/docs/development/spark-tests/test-run.md", "hash": "7360158424844343808"}, {"file": "docs/project_md/collaboration_process.py.teaching.md", "hash": "2940783448297322589"}, {"file": "tmp_structure/tests/unit/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "docs/KMDF Driver1/tmp_structure/include/log/trace.md", "hash": "17217117922386663488"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/utils.rs", "hash": "6439270967831288714"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/expressions.proto", "hash": "14592699905854646933"}, {"file": ".vs/KMDF Driver1/FileContentIndex/f57f5149-efaa-499c-b70e-9e6091fa303e.vsidx", "hash": "12643329660832643390"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "tmp_structure/tests/performance/touchperformance.c", "hash": "5007490272852559090"}, {"file": "scripts/fix_encoding.ps1", "hash": "9576252314598886296"}, {"file": "include/hal/bus/kmdf_i2c.h", "hash": "14230871969883548300"}, {"file": "sail-main/python/pysail/tests/spark/test_cast_from_string.txt", "hash": "9605254453995742608"}, {"file": "tests/src/performance/performance_core.c", "hash": "5730367670395922615"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_recovery_framework.md", "hash": "4481649399000513017"}, {"file": "tmp_structure/tools/fix/fix_format.py", "hash": "14127110787080049849"}, {"file": "tmp_structure/tests/unit/driver_manager.c", "hash": "18213335034763467499"}, {"file": "docs/KMDF Driver1/tests/src/security/error_prevention.md", "hash": "1875309196829316540"}, {"file": "sail-main/crates/sail-cli/src/python.rs", "hash": "6292338445750919428"}, {"file": "sail-main/crates/sail-plan/src/lib.rs", "hash": "11450637738265062204"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.ilk", "hash": "3592903381167155268"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorCore.md", "hash": "1340757887149976797"}, {"file": "tests/unit/usb_interface.c", "hash": "15002876401871970527"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/data_type.rs", "hash": "11565157394283538359"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/mod.rs", "hash": "748641764112154074"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_engine.md", "hash": "1707343726405816748"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "runprojectoptimization.bat", "hash": "2125495458914620176"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link-rc.write.1.tlog", "hash": "2877714277780181727"}, {"file": "tmp_structure/tools/utils/core/code_parser.py", "hash": "12329907362720636894"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_module.md", "hash": "16648157775560897177"}, {"file": "docs/KMDF Driver1/tests/src/utils/CommonInternal.md", "hash": "4664956977958856282"}, {"file": "config/templates/basic.txt", "hash": "16933140311407412703"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_policy.md", "hash": "12279598450356209653"}, {"file": "tests/test_api_manager.py", "hash": "8550478795907993636"}, {"file": "sail-main/.github/workflows/gold-data.yml", "hash": "15094401856164417610"}, {"file": "docs/MCP使用方法/puppeteer_mcp使用指南.md", "hash": "11568379398149184806"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": "tmp_structure/tests/unit/spi_controller.c", "hash": "15313722384938567487"}, {"file": "sail-main/crates/sail-python-udf/src/python/spark.rs", "hash": "18299719346270210090"}, {"file": "gitattributes", "hash": "7779088979552154010"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/README.md", "hash": "13397291605712041243"}, {"file": "tmp_structure/tools/utils/quick_batch.py", "hash": "14073928928729352464"}, {"file": "cmake/CodeCoverage.cmake", "hash": "8322637480442708767"}, {"file": "tests/unit/hal.c", "hash": "13073342420439847883"}, {"file": "src/core/device/device_manager.c", "hash": "6362969198040367947"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": ".vs/KMDF Driver1/v17/Browse.VC.db", "hash": "3583566759131987680"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/knowledge_agent.cpython-313.pyc", "hash": "3595420196522840354"}, {"file": "docs/KMDF Driver1/tests/src/utils/recovery_impl.md", "hash": "2569326196912403502"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/GamepadCore.md", "hash": "17415216908876162054"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFSPI.md", "hash": "5079633368373070158"}, {"file": "tmp_structure/tools/test/test_load_driver.ps1", "hash": "4841543805499648601"}, {"file": "tests/src/security/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q97.sql", "hash": "5599175380113194039"}, {"file": "tmp_structure/tools/build/check_build_config.ps1", "hash": "6673720728417110734"}, {"file": "tmp_structure/tests/unit/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "docs/KMDF Driver1/backup/src/driver/driver_stubs.md", "hash": "11143428489304142760"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_pmod.rs", "hash": "6836188652002404070"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_handler.md", "hash": "12498787032465535448"}, {"file": "tests/src/utils/ml/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "docs/project_md/search_results.txt", "hash": "3244421341483603138"}, {"file": "cleanupprojectfixed.ps1", "hash": "1729661487922461346"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ReliabilityManager.md", "hash": "8634971326101470578"}, {"file": "tests/src/memory/DMAOptimizer.c", "hash": "12436293271881128025"}, {"file": "tmp_structure/tests/unit/device.c", "hash": "6161212960231513500"}, {"file": "tests/unit/errorprevention.c", "hash": "10299302956295713904"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q49.sql", "hash": "8188882099169760076"}, {"file": "include/core/driver/driver_core.h", "hash": "17311934845619086185"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/cache_optimizer.md", "hash": "4292989671843767456"}, {"file": "docs/KMDF Driver1/backup/include/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/logger.md", "hash": "15563485205402096322"}, {"file": "tmp_structure/tests/unit/kmdfusb.c", "hash": "3804768512383554498"}, {"file": "sail-main/python/pysail/tests/spark/literal/test_timestamp_ntz.txt", "hash": "12056461526194929518"}, {"file": "docs/structured_docs/api_reference/wdf_设备函数.md", "hash": "16787713271245699007"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/prediction_internal.md", "hash": "13825641208428294959"}, {"file": "tests/src/utils/ml/auto_optimizer.cpp", "hash": "8101721435210232709"}, {"file": "sail-main/python/pysail/tests/spark/test_group_by.py", "hash": "12841692318215850106"}, {"file": "tests/test_prompt_engine.py", "hash": "1752670602706898972"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/bus/KMDFI2C.md", "hash": "6535570456787467291"}, {"file": "tmp_structure/tools/utils/check_encoding.py", "hash": "13948401717445501161"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92_MINSIZEREL_loc", "hash": "11983781533420869330"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_handling.md", "hash": "7149914668807538345"}, {"file": "tmp_structure/tests/unit/cache_manager.c", "hash": "868028145507112649"}, {"file": "tests/unit/interrupt.c", "hash": "438091713417069452"}, {"file": "tests/src/utils/notification_manager.c", "hash": "16862955048666143712"}, {"file": "sail-main/crates/sail-execution/src/rpc.rs", "hash": "4046973950171994471"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/literal.rs", "hash": "10398966470122406094"}, {"file": "tests/unit/processfunctions.c", "hash": "15471745620740669001"}, {"file": "src/hal/devices/gpio_device.c.bak", "hash": "14781073600839470055"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "sail-main/docs/.vitepress/theme/utils/tree.ts", "hash": "5970344990534862989"}, {"file": "tests/src/utils/CompressionPredictor.c", "hash": "11817161568988905271"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/MaintenanceManager.md", "hash": "5703461401317863092"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q56.sql", "hash": "9019136866563168848"}, {"file": "docs/KMDF Driver1/tests/src/memory/MemoryCompression.md", "hash": "449121492967900735"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorRecovery.md", "hash": "677055025807912378"}, {"file": "tmp_structure/tests/unit/spi_interface.c", "hash": "13749364630813119982"}, {"file": "tmp_structure/tools/utils/tidy.ps1", "hash": "15876974776598375532"}, {"file": "sail-main/crates/sail-sql-analyzer/src/error.rs", "hash": "17282308938685116987"}, {"file": "sail-main/k8s/sail.yaml", "hash": "8583345853654776628"}, {"file": "touch.c", "hash": "6578032005835634426"}, {"file": "src/hal/bus/i2c_core.c.bak", "hash": "7021082159789302476"}, {"file": "tests/src/utils/trace/logger_trace.c", "hash": "9545559538338278246"}, {"file": "tests/src/security/ErrorHandler.c", "hash": "638403771659111855"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q40.sql", "hash": "2625173200672866233"}, {"file": "build/CMakeFiles/3.31.3/CMakeDetermineCompilerABI_C.bin", "hash": "11161833998618522851"}, {"file": "docs/KMDF Driver1/tests/src/utils/MaintenanceManager.md", "hash": "359784250778668666"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_manager.md", "hash": "17231002221847300177"}, {"file": "sail-main/.gitattributes", "hash": "4775171866631102531"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ReliabilityManager.md", "hash": "8634971326101470578"}, {"file": "tests/src/security/ErrorManager.c", "hash": "1036485733612102205"}, {"file": "tmp_structure/tools/utils/check_code_style.ps1", "hash": "2063858751040355594"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/logger.md", "hash": "14318949831029739559"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_manager.md", "hash": "968320189468067565"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/types/BaseTypes.md", "hash": "14629461928739236646"}, {"file": "tests/unit/iooptimizer.c", "hash": "11289350191801585883"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceStatTypes.md", "hash": "7255025081818893703"}, {"file": "sail-main/crates/sail-gold-test/Cargo.toml", "hash": "5937440973753975391"}, {"file": "tests/src/utils/trace/event_trace.c", "hash": "10188444021102792748"}, {"file": "docs/KMDF Driver1/backup/include/driver/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ModuleManager.md", "hash": "6642213020461104442"}, {"file": "sail-main/crates/sail-execution/src/stream/channel.rs", "hash": "12357259482000955450"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_test.md", "hash": "7510514405337728791"}, {"file": "tmp_structure/tools/utils/sync_headers_with_teaching.py", "hash": "14152856265668051425"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/QueueOperations.md", "hash": "11607574489902703500"}, {"file": "tests/src/utils/device/device_performance_monitor.c", "hash": "9414896522403079228"}, {"file": "sail-main/docs/development/build/index.data.ts", "hash": "11187376696318205656"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "tmp_structure/tests/unit/error_recovery_kmdf.c", "hash": "13515625592826245452"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-RelWithDebInfo-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/auto_optimizer.md", "hash": "11758342447071272246"}, {"file": "Cargo.toml", "hash": "7967259547056559679"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_unix_timestamp.rs", "hash": "13666186842882741154"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_context.md", "hash": "6181310458797943691"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_pandas_grouped_map_udf.txt", "hash": "6044126735688713519"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_scheduler.md", "hash": "4149850547689728390"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/literal.rs", "hash": "3910174647628892300"}, {"file": "build/PACKAGE.vcxproj.filters", "hash": "9749945271910142052"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/mod.rs", "hash": "5634764044295538348"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_state.md", "hash": "16244328757231660569"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_stubs.md", "hash": "7773882594705755902"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_analyzer.md", "hash": "9730074305712305096"}, {"file": "runannotator.bat", "hash": "5714542090824015771"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/PerformanceMonitor.md", "hash": "156328902122332910"}, {"file": "tests/src/driver/driver_log_test.c", "hash": "7173703466634916616"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q87.sql", "hash": "8645039926692790895"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_join.json", "hash": "6168973789785914769"}, {"file": "docs/project_md/fix_header_guards.py.teaching.md", "hash": "7986253460171213892"}, {"file": "sail-main/crates/sail-plan/src/extension/function/map/mod.rs", "hash": "4408322838918651559"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_init_test.md", "hash": "17333699458054262275"}, {"file": "test_volcengine_auth.py", "hash": "17643482513176861101"}, {"file": "sail-main/crates/sail-plan/src/extension/function/spark_to_string.rs", "hash": "13261929510122307205"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_ops.md", "hash": "13916165421437368796"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_handling.md", "hash": "8736094554619853824"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/optimizer.md", "hash": "14386815896161672304"}, {"file": "sail-main/crates/sail-execution/src/job/runner.rs", "hash": "3048173916366114617"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CMakeCXXCompilerId.obj", "hash": "6380061009148582960"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/spi_controller.md", "hash": "3178658698084741503"}, {"file": "tmp_structure/tests/unit/hyperparam_optimizer.c", "hash": "1961346564345982646"}, {"file": "docs/project_md/multi_ai_collaboration_plan.md", "hash": "4434216606880434162"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/auto_optimizer.md", "hash": "3883996352235318562"}, {"file": "tmp_structure/tests/unit/power_state_manager.c", "hash": "18427479698405091297"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_manager.md", "hash": "12926653278278343738"}, {"file": "sail-main/crates/sail-python-udf/src/python/spark.py", "hash": "8992861255327112354"}, {"file": "tests/src/core/GlobalVariables.c", "hash": "703525128182974641"}, {"file": "fix_hal_files.cmd", "hash": "16562127807537512199"}, {"file": "tests/unit/memorymanager.c", "hash": "13193390556443584375"}, {"file": "fix_headers.py", "hash": "15475551727779115310"}, {"file": "sail-main/eslint.config.js", "hash": "6865388652597604047"}, {"file": "tests/unit/error_recovery.c", "hash": "6818450905492757794"}, {"file": "sail-main/python/pysail/docs/_templates/autosummary/class.rst", "hash": "9969339975494998461"}, {"file": "cmake/checks.cmake", "hash": "2668118663152144359"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/spark_base64.rs", "hash": "505930427602108585"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceExport.md", "hash": "3120014411541002467"}, {"file": "sail-main/python/pysail/tests/spark/conftest.py", "hash": "14218116428876827621"}, {"file": "src/hal/devices/spi_device.c", "hash": "14387713210982713868"}, {"file": "build/.cmake/api/v1/reply/target-build_script-RelWithDebInfo-251266553d8db8bed6c5.json", "hash": "13997068141168601047"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.exe.recipe", "hash": "8578983596400573147"}, {"file": "sail-main/crates/sail-execution/src/stream/merge.rs", "hash": "12850722640262580435"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_init_test.md", "hash": "17396170189008255718"}, {"file": "sail-main/.vscode/settings.json", "hash": "5369419549107896325"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFSPI.md", "hash": "13937455001840529656"}, {"file": "docs/KMDF Driver1/tmp_structure/include/hardware/kmdf_hal.md", "hash": "13397201630394238542"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handling.md", "hash": "11368279126817252286"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/ErrorPrevention.md", "hash": "17818079868441769260"}, {"file": ".vs/slnx.sqlite", "hash": "10372569932437568579"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_strategies.md", "hash": "1076962325564799858"}, {"file": "build/.cmake/api/v1/reply/cache-v2-5028cbd92f3ecff66752.json", "hash": "6006223230731891780"}, {"file": "tests/unit/queueoperations.c", "hash": "8016806957506324853"}, {"file": ".vscode/project_settings.json", "hash": "13497777477831448018"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.tlog/CustomBuild.write.1.tlog", "hash": "14117961221003328239"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/ErrorCore.md", "hash": "1340757887149976797"}, {"file": "sail-main/python/pysail/tests/spark/test_math.py", "hash": "8724983300964407325"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceMonitor.md", "hash": "2312054369463986103"}, {"file": "fix_final_headers.py", "hash": "18202133227987375845"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_scheduler.md", "hash": "12584940170140586153"}, {"file": "sail-main/crates/sail-spark-connect/src/service/config_manager.rs", "hash": "921463135050125362"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_pandas_map_udf.txt", "hash": "17031807134575998966"}, {"file": "tests/src/utils/device/usb_interface.c", "hash": "15002876401871970527"}, {"file": "tools/scripts/enhanced_code_analyzer.py", "hash": "5763503898948417376"}, {"file": "tests/performance/performancemanager.c", "hash": "12428470120826957350"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_udaf.rs", "hash": "6565820715445169761"}, {"file": "sail-main/docs/guide/tasks/udf/index.data.ts", "hash": "6608651665366565985"}, {"file": "build/.cmake/api/v1/reply/target-format-RelWithDebInfo-81d6e7efb85862f3b20c.json", "hash": "5876608019943124009"}, {"file": "tests/src/utils/optimization/optimization_strategies.cpp", "hash": "18062982622965410321"}, {"file": "tools/scripts/project_manager.ps1", "hash": "14461698973857256077"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/Queue.md", "hash": "10162557302537918983"}, {"file": ".github/config/code_analysis_rules.json", "hash": "15843396151099728677"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchDebug.md", "hash": "1115055897695565800"}, {"file": "tests/src/security/SecurityManager.c", "hash": "10840736394663120643"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "sail-main/crates/sail-sql-parser/src/span.rs", "hash": "7719752510892553651"}, {"file": "sail-main/.github/actions/commit-baseline/action.yml", "hash": "2876755796671776895"}, {"file": "sail-main/docs/guide/tasks/index.data.ts", "hash": "8850063862520685504"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/optimization_policy.md", "hash": "9008996255866575673"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q89.sql", "hash": "2986227515649997475"}, {"file": "sail-main/python/pysail/data/tpch/queries/q17.sql", "hash": "3544906096412265662"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/test/test_analyzer.md", "hash": "6049406713010296516"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/function.rs", "hash": "9994461009938428386"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "336743115064200357"}, {"file": "tests/src/utils/CommonInternal.c", "hash": "12692290799640821342"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/device/device.md", "hash": "15950813879397350842"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.tlog/ZERO_CHECK.lastbuildstate", "hash": "7748267508661821711"}, {"file": "sail-main/crates/sail-sql-parser/build.rs", "hash": "17758302314873429899"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_monitor.md", "hash": "12909935724192969691"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_insert_overwrite.json", "hash": "6307735690951008697"}, {"file": "tmp_structure/tests/unit/gamepadcore.c", "hash": "15479958173847767625"}, {"file": "tmp_structure/tests/unit/trace.h", "hash": "1843899095343991810"}, {"file": "sail-main/docs/development/recipes/reducing-build-time.md", "hash": "5980861675836884453"}, {"file": "sail-main/crates/sail-execution/src/stream/mod.rs", "hash": "10773719165235594030"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "sail-main/crates/sail-spark-connect/src/entrypoint.rs", "hash": "382938991866829819"}, {"file": "tmp_structure/include/core/common.h", "hash": "5175167649420740486"}, {"file": "simple_knowledge_result.json", "hash": "14876908669090570886"}, {"file": "sail-main/docs/.vitepress/theme/components/PageTreeList.vue", "hash": "14228923320471984245"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_test.md", "hash": "5528223060007826182"}, {"file": ".windsurf<PERSON><PERSON>", "hash": "4880051458355600887"}, {"file": "tests/src/device/TouchDebug.c", "hash": "11957451505368650923"}, {"file": "docs/KMDF Driver1/tests/src/driver/DriverLog.md", "hash": "1410373447449831134"}, {"file": "tmp_structure/src/driver/driver.c", "hash": "4929818876129192855"}, {"file": "tools/scripts/reorganize_project.py", "hash": "12998432177407977966"}, {"file": "tmp_structure/tools/analysis/run_enhanced_analysis.ps1", "hash": "12205270835548525724"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.tlog/Cl.items.tlog", "hash": "1043060065137930654"}, {"file": "tmp_structure/include/driver/driver.h", "hash": "13846183591385912328"}, {"file": "tests/unit/device_test.c", "hash": "4283402855535051437"}, {"file": "tests/performance/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": ".vscode/documentation_config.json", "hash": "16120575310560030239"}, {"file": "docs/KMDF Driver1/backup/include/driver/driver_core.md", "hash": "2981211088971537176"}, {"file": "build/INSTALL.vcxproj.tmp4ff98", "hash": "3686658815850517832"}, {"file": "tests/src/utils/health_checker.c", "hash": "13081061987138608445"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_strategies.md", "hash": "18204326867969565026"}, {"file": "tests/src/security/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "sail-main/docs/guide/deployment/kubernetes.md", "hash": "18023714603548396158"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/device/DeviceTypes.md", "hash": "10281915747584305109"}, {"file": "docs/KMDF Driver1/backup/backup/include/performance/core/monitor.md", "hash": "245168986588772421"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_performance_monitor.md", "hash": "2693427970388783504"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/MemoryPool.md", "hash": "10702931801727958310"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/xml.rs", "hash": "1769930683719833036"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceData.md", "hash": "17754136278712063044"}, {"file": "sail-main/crates/sail-execution/src/driver/options.rs", "hash": "689241343570831183"}, {"file": "docs/KMDF Driver1/tests/src/utils/cache_manager.md", "hash": "331729552811588733"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_module.md", "hash": "16648157775560897177"}, {"file": "tests/unit/error_handler.c", "hash": "2913023791582526489"}, {"file": "sail-main/.vscode/extensions.json", "hash": "5882669939249479137"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_weekofyear.rs", "hash": "8751145036674243777"}, {"file": "sail-main/scripts/common-gold-data/report.sh", "hash": "8741656552908913422"}, {"file": "test_doubao_sdk.py", "hash": "6469760139033215625"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "docs/KMDF Driver1/tests/src/security/error_module.md", "hash": "16613471808559261902"}, {"file": "tmp_structure/tests/unit/notification_manager.c", "hash": "16862955048666143712"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_try_to_timestamp.rs", "hash": "17898279329181221285"}, {"file": "tests/unit/error_recovery_framework.c", "hash": "13699128380064941463"}, {"file": "sail-main/crates/sail-sql-parser/src/container/tuple.rs", "hash": "16358659626931210405"}, {"file": "tmp_structure/tests/unit/kmdflogger.c", "hash": "3048538446081918844"}, {"file": "build/KMDFDriver1.dir/Debug/KMDFDriver1.vcxproj.FileListAbsolute.txt", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "tests/src/security/ErrorManager.h", "hash": "1005541988791518561"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q63.sql", "hash": "17746786000620691438"}, {"file": "sail-main/crates/sail-plan/src/extension/function/collection/spark_reverse.rs", "hash": "13004889769508585722"}, {"file": "tests/unit/errormanager.c", "hash": "14480009171928608282"}, {"file": "tests/unit/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "sail-main/python/pysail/tests/spark/test_tpch.py", "hash": "15319160656043932205"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handler.md", "hash": "12359971319317369078"}, {"file": "tests/performance/simd_performance.c", "hash": "9288036268856686374"}, {"file": "tmp_structure/tools/test/standardize_tests.py", "hash": "15877158136264827518"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/operator.rs", "hash": "11802526456789362109"}, {"file": "sail-main/python/pysail/spark/__init__.py", "hash": "4958116046061707480"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/local_stream.rs", "hash": "7353252826283932554"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFError.md", "hash": "2086467841528888470"}, {"file": "README.md", "hash": "9280963088635331262"}, {"file": "docs/KMDF Driver1/backup/backup/include/device/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/include/io/io_manager.md", "hash": "15079488343970755473"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_cache.md", "hash": "10642137261929724889"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_io.md", "hash": "16194404026375741960"}, {"file": "tests/src/core/CoreManager.c", "hash": "318187113017440242"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/prediction_engine.md", "hash": "1202724730309612837"}, {"file": "sail-main/crates/sail-plan/src/resolver/expression.rs", "hash": "13036653308473480855"}, {"file": "tmp_structure/tests/unit/drivercore.c", "hash": "2661836926917990059"}, {"file": "sail-main/crates/sail-plan/src/catalog/mod.rs", "hash": "3803930052312732229"}, {"file": "build/.cmake/api/v1/reply/index-2025-05-30T15-18-13-0705.json", "hash": "3544091773352550363"}, {"file": "tests/src/driver/DriverCore.c", "hash": "2661836926917990059"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/log/LogTypes.md", "hash": "8956926128937153051"}, {"file": "docs/KMDF Driver1/tests/src/utils/AlertManager.md", "hash": "5817230148968624754"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/csv.json", "hash": "3914924647118121425"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/ml_optimizer.md", "hash": "8283783923648259421"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverLog.md", "hash": "4769579153366881971"}, {"file": "tests/src/security/error_core.c", "hash": "13244377760009713122"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "docs/KMDF Driver1/backup/src/utils/logging/Logger.md", "hash": "4387232194484138377"}, {"file": "include/device/base/device.h", "hash": "11087151227771504575"}, {"file": "build/CMakeFiles/3.31.3/CMakeRCCompiler.cmake", "hash": "7478422761629706063"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_udf.rs", "hash": "159785781726553820"}, {"file": "sail-main/crates/sail-spark-connect/proto/spark/connect/common.proto", "hash": "7143943642959953062"}, {"file": "sail-main/crates/sail-plan/src/resolver/function.rs", "hash": "5472733271985662125"}, {"file": "scripts/project_file_auto_reader.py", "hash": "11227123832079663649"}, {"file": "tmp_structure/tests/unit/touchprocessor.c", "hash": "8524256194400325748"}, {"file": "sail-main/crates/sail-spark-connect/src/service/plan_executor.rs", "hash": "12077156987185583229"}, {"file": "tests/unit/device.c", "hash": "6161212960231513500"}, {"file": "tests/src/utils/device/device_state.c", "hash": "232531793412726154"}, {"file": "docs/api/wolfarmAPI.txt", "hash": "6531473657264747639"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/planner.rs", "hash": "16949294105199694976"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/core/device/DeviceStateTypes.md", "hash": "6763864190905748396"}, {"file": "sail-main/python/pysail/data/tpch/queries/q9.sql", "hash": "9566337092348127755"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_database.md", "hash": "3898663799363477466"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchController.md", "hash": "5893240157153592323"}, {"file": "docs/KMDF Driver1/include/device/io/device_io.md", "hash": "15280929294455826365"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_reporter.md", "hash": "13138863208284489715"}, {"file": "tests/__pycache__/test_api_manager.cpython-314.pyc", "hash": "17423905573846310036"}, {"file": "tmp_structure/tests/unit/driverlog.h", "hash": "18313408282782209901"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFError.md", "hash": "9994384953078924681"}, {"file": "sail-main/docs/guide/deployment/docker-images/development.md", "hash": "1216237883389165496"}, {"file": "sail-main/scripts/spark-tests/build-pyspark.sh", "hash": "11456148664584314482"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/numeric.rs", "hash": "1489377241927778056"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_benchmark.md", "hash": "4089739322338621121"}, {"file": "tmp_structure/tools/fix/fix_includes.py", "hash": "18140236329106783058"}, {"file": "tests/unit/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_core.md", "hash": "4962745022155368642"}, {"file": "tests/src/utils/trace/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/PerfMonitor.md", "hash": "7405414634036513776"}, {"file": "tmp_structure/tools/test/test_api.py", "hash": "726597173146823267"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_create_table.json", "hash": "8439244080182575393"}, {"file": "tests/unit/iomanager.c", "hash": "6435585653620536882"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q93.sql", "hash": "11707785278503464682"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q69.sql", "hash": "13699025062936560197"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeFiles/generate.stamp.depend", "hash": "16863660142281511390"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/health_checker.md", "hash": "9850915435701639911"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/power_optimizer.md", "hash": "14225129833115120878"}, {"file": "sail-main/crates/sail-execution/src/driver/mod.rs", "hash": "13974469533727987368"}, {"file": "src/core/driver/driver_core.c", "hash": "7530350961901278368"}, {"file": "config/templates/basic_prompt.json", "hash": "9401672000993726034"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/predicate.json", "hash": "6240255529576290594"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/simd_optimizer.md", "hash": "16564215083805726102"}, {"file": "sail-main/crates/sail-plan/src/extension/function/multi_expr.rs", "hash": "14677822955780376846"}, {"file": "scripts/fix_tabs.ps1", "hash": "7234465746255906538"}, {"file": "tests/src/utils/ml/feature_engineering.c", "hash": "12955500543281247863"}, {"file": "tests/unit/touchpredictor.c", "hash": "15043340538585148217"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ModuleManager.md", "hash": "6642213020461104442"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_state_types.md", "hash": "7660836948132653389"}, {"file": "tmp_structure/tools/test/test_e2b.py", "hash": "12009727001778827673"}, {"file": "tests/performance/performance_collector.c", "hash": "9157021843784231895"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/recovery_impl.md", "hash": "13993772039987120939"}, {"file": "sail-main/python/pysail/data/tpch/queries/q11.sql", "hash": "6935715994985582645"}, {"file": "sail-main/docs/guide/tasks/data-access.md", "hash": "2996569828583847780"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_update.json", "hash": "8267902348397690317"}, {"file": "tests/src/utils/ml/prediction_core.c", "hash": "1857614276287916371"}, {"file": "tmp_structure/include/error/error_codes.h", "hash": "16517599858835515243"}, {"file": "tests/src/performance/performance_analyzer.h", "hash": "2328266082764694390"}, {"file": "sail-main/docs/development/release/index.md", "hash": "5735141379759474280"}, {"file": "tests/unit/gamepadcore.c", "hash": "15479958173847767625"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link-rc.write.1.tlog", "hash": "9508405476468811004"}, {"file": "tests/unit/registry_callback.c", "hash": "17205669410881855421"}, {"file": "tmp_structure/tools/utils/collaboration_process.py", "hash": "1343857773564930929"}, {"file": "tests/src/device/HardwareManager.h", "hash": "3697009021781778473"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_executor.md", "hash": "115535078954673227"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Touch.md", "hash": "6473562417806726533"}, {"file": "tmp_structure/tools/utils/ai_optimization_strategies.py", "hash": "7587537925346873322"}, {"file": "docs/project_md/cursor_machine_id.md", "hash": "12525391712515250278"}, {"file": "build/x64/Debug/ALL_BUILD/ALL_BUILD.Build.CppClean.log", "hash": "3244421341483603138"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_analyzer.md", "hash": "3484574314302268365"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_udtf.txt", "hash": "15348445496658073031"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/scheduler_optimizer.md", "hash": "2518041016705239047"}, {"file": "sail-main/crates/sail-plan/src/object_store/layers/runtime.rs", "hash": "907360029168220991"}, {"file": "tests/unit/kmdflogger.c", "hash": "3048538446081918844"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_benchmark.md", "hash": "4089739322338621121"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/struct.json", "hash": "17616687369914477046"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_context.md", "hash": "9967185622851685113"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "docs/KMDF Driver1/tmp_structure/include/error/error_handling.md", "hash": "8024255047764659432"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceManager.md", "hash": "4322100284458284897"}, {"file": "build/.cmake/api/v1/reply/directory-.-Release-c82fe57cc33975ab45bc.json", "hash": "7493991670780580716"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_state.md", "hash": "4327613149066213288"}, {"file": ".vscode/test_config.json", "hash": "10963637313950704576"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_dma.md", "hash": "10496417195142863382"}, {"file": "header_consistency_report.csv", "hash": "17290809390456052859"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceLogger.md", "hash": "10635370005391666013"}, {"file": "docs/KMDF Driver1/backup/include/utils/optimization/optimization_policy.md", "hash": "14959967751472875080"}, {"file": "sail-main/python/pysail/data/tpch/queries/q12.sql", "hash": "2808361597792821389"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/data_type_arrow.rs", "hash": "14829099144777337653"}, {"file": "sail-main/crates/sail-python-udf/src/python/mod.rs", "hash": "2144975880542106059"}, {"file": "tests/unit/dmahandler.c", "hash": "7693675145006693987"}, {"file": "tests/unit/simd_ops.c", "hash": "4755770406672099314"}, {"file": "tests/src/utils/PerfMonitor.c", "hash": "11571150342783768226"}, {"file": "tmp_structure/tools/utils/core/model_adapters.py", "hash": "8795703199135127069"}, {"file": "docs/KMDF Driver1/build/CMakeFiles/3.31.3/CompilerIdC/CMakeCCompilerId.md", "hash": "12087973490887621766"}, {"file": "tmp_structure/tools/utils/simple_demo.py", "hash": "15158633839973973863"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/simd_prediction.md", "hash": "15285699593433826437"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchPerformance.md", "hash": "3331360388054379050"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_scheduler.md", "hash": "16605851794068813054"}, {"file": "sail-main/python/pysail/tests/spark/catalog/test_temp_view_columns.txt", "hash": "13706528717415343911"}, {"file": "tmp_structure/tools/fix/fix_encoding.ps1", "hash": "5147143283489740337"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/ml_optimizer.md", "hash": "1811472538079544434"}, {"file": "tmp_structure/tests/unit/commoninternal.c", "hash": "12692290799640821342"}, {"file": "sail-main/python/pysail/tests/spark/test_group_by_alias.txt", "hash": "2618450209135161114"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q60.sql", "hash": "3461198560788785289"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/test/test_framework.md", "hash": "5788066271965753797"}, {"file": "docs/project_md/project_ideas.md", "hash": "1186718690124445127"}, {"file": "tests/unit/driver_core.c", "hash": "2424469765469195657"}, {"file": "docs/KMDF Driver1/tmp_structure/include/core/common.md", "hash": "16647291213906929623"}, {"file": ".vs/KMDF Driver1/v17/.wsuo", "hash": "408958271423915178"}, {"file": "tmp_structure/tools/test/test_code.py", "hash": "8644629831272372251"}, {"file": "sail-main/python/pysail/tests/spark/function/test_floor.txt", "hash": "6910856918420449401"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q72.sql", "hash": "2927270423110788491"}, {"file": "sail-main/crates/sail-cli/src/spark/shell.rs", "hash": "9138787056758444481"}, {"file": "docs/KMDF Driver1/backup/include/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "sail-main/docs/guide/deployment/docker-images/production.md", "hash": "18371426322522119707"}, {"file": "sail-main/LICENSE", "hash": "12649885980860800067"}, {"file": "docs/KMDF Driver1/backup/include/bus/kmdf_usb.md", "hash": "18152046806154247336"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model_ensemble.md", "hash": "8517005769346873133"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/auto_tuning.md", "hash": "6272516641966292299"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "tests/unit/hardwaremanager.c", "hash": "5852295366093323504"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_queue.md", "hash": "10025303424683255249"}, {"file": "docs/project_md/analysis_report.md", "hash": "1542542377250099719"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "tmp_structure/tests/unit/prediction_internal.c", "hash": "18276760294454323322"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/statistics_manager.md", "hash": "8721089824310490847"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q13.sql", "hash": "12570263856136643197"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q35.sql", "hash": "10020536664705464250"}, {"file": "sail-main/crates/sail-common/src/config/cli.rs", "hash": "1762699261477783119"}, {"file": "analysis_output/dependency_analysis.json", "hash": "9543739775522133972"}, {"file": "tests/src/driver/driver_init_test.c", "hash": "11766586708068698317"}, {"file": "sail-main/crates/sail-sql-analyzer/src/lib.rs", "hash": "8755260591076715016"}, {"file": "docs/KMDF Driver1/backup/20250506_205459/include/bus/kmdf_i2c.md", "hash": "7356725021734401791"}, {"file": "tests/unit/CMakeLists.txt", "hash": "16190250173074905128"}, {"file": "tmp_structure/tests/unit/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "build/.cmake/api/v1/query/client-vscode/query.json", "hash": "16428861418797765294"}, {"file": "docs/KMDF Driver1/tests/src/utils/health_checker.md", "hash": "1198728672636693643"}, {"file": "tmp_structure/tools/utils/verify_env.py", "hash": "15386892121415965824"}, {"file": "tests/src/utils/interrupt/interrupt_manager.c", "hash": "2882719635214248526"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/Debug/cmTC_58f84.exe", "hash": "11023376176586363246"}, {"file": "tests/unit/driver_test.c", "hash": "14081732213465014022"}, {"file": "docs/KMDF Driver1/include/device/base/device.md", "hash": "15950813879397350842"}, {"file": "sail-main/crates/sail-plan/src/extension/function/collection/spark_concat.rs", "hash": "14224022644583090179"}, {"file": "tests/src/utils/ml/scheduler_optimizer.c", "hash": "12335990698351349517"}, {"file": "tmp_structure/tests/unit/commonutils.c", "hash": "11647735861415970558"}, {"file": "sail-main/crates/sail-spark-connect/build.rs", "hash": "6834540554177857200"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_interrupt.md", "hash": "3814950842790577981"}, {"file": "tmp_structure/tools/utils/standardize_code_style.py", "hash": "4459166999164536411"}, {"file": "docs/KMDF Driver1/tests/src/core/CoreManager.md", "hash": "5931295104163142810"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_core.md", "hash": "1711428132703189616"}, {"file": "tests/src/utils/device/device_timer.c", "hash": "3835497012896294941"}, {"file": "sail-main/crates/sail-telemetry/src/lib.rs", "hash": "12903400035950218628"}, {"file": "sail-main/crates/sail-execution/src/driver/state.rs", "hash": "11837553206651317268"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_error_monitor.md", "hash": "2218156172550209497"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/window.json", "hash": "6284753695764399778"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "tmp_structure/tools/fix/quick_fix.py", "hash": "13203920727107741020"}, {"file": "tmp_structure/tests/performance/performance_data_collector.c", "hash": "4810952109990638802"}, {"file": "project_cleanup.py", "hash": "13183627259473291690"}, {"file": "docs/KMDF Driver1/backup/20250506_205501/include/core/types/kmdf_types.md", "hash": "161877053758335927"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/CompilerIdCXX.vcxproj", "hash": "8040884264789654826"}, {"file": "docs/KMDF Driver1/backup/include/driver/Driver.md", "hash": "3073102130935575849"}, {"file": "sail-main/docs/development/setup/python.md", "hash": "6386348928744575930"}, {"file": "sail-main/docs/.vitepress/theme/index.ts", "hash": "10044599009248961292"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q29.sql", "hash": "12882523049161103000"}, {"file": ".clang-format", "hash": "17813942105361887437"}, {"file": "sail-main/crates/sail-python-udf/src/accumulator.rs", "hash": "5952160151027273119"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/event_dispatcher.md", "hash": "6014464624155038316"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformancePredictor.md", "hash": "15411689379061262882"}, {"file": "tests/src/utils/config_manager.c", "hash": "18100728998672094806"}, {"file": "tmp_structure/tests/unit/errorcore.c", "hash": "8426091125501211666"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_monitor_manager.md", "hash": "16650903172073960168"}, {"file": "tmp_structure/tests/unit/logger_core.c", "hash": "5596205493550234397"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger.md", "hash": "1999416809368717412"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt.md", "hash": "1138018929248212817"}, {"file": "tests/__pycache__/test_quality_controller.cpython-314.pyc", "hash": "3992480986374148898"}, {"file": "sail-main/.github/workflows/python-build.yml", "hash": "11246426070367639260"}, {"file": "sail-main/docs/development/recipes/index.data.ts", "hash": "17458174580667590777"}, {"file": "sail-main/crates/sail-common/src/config/default.toml", "hash": "11175683767831927848"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/feature_engineering.md", "hash": "11114454749909286843"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/usb_interface.md", "hash": "1081299217814221501"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/CommonUtils.md", "hash": "13690485749395410695"}, {"file": "sail-main/crates/sail-spark-connect/src/debug.rs", "hash": "2441826900249007941"}, {"file": "docs/KMDF Driver1/backup/include/utils/test/sample_test.md", "hash": "3457782722145466830"}, {"file": "tests/unit/trace_core.c", "hash": "10541209614786999240"}, {"file": "tests/src/memory/MemoryPool.c", "hash": "16759837123446157155"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/Driver.md", "hash": "6101310240137421671"}, {"file": "tmp_structure/tests/unit/gyrohwsim.c", "hash": "9939547639384961965"}, {"file": "sail-main/docs/.vitepress/site.d.ts", "hash": "942444976959436872"}, {"file": "tests/src/utils/ml/optimizer.cpp", "hash": "8927165647459203052"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/analyzer.rs", "hash": "15570853678323596859"}, {"file": "build/x64/Debug/ZERO_CHECK/ZERO_CHECK.recipe", "hash": "367578897923677545"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/resource_optimizer_1.md", "hash": "5677242174374142631"}, {"file": "tmp_structure/tests/unit/resource_management_test.c", "hash": "81080999296386809"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-Release-e5692e6ed879150c5474.json", "hash": "6502387549155151165"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/CMakeFiles/TargetDirectories.txt", "hash": "722092573267544121"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/math.rs", "hash": "12092128596474349043"}, {"file": "scripts/extract_errors.py", "hash": "14839614070312556472"}, {"file": "sail-main/.github/workflows/docs-build.yml", "hash": "7791978067297488998"}, {"file": "tests/unit/config_manager.c", "hash": "18100728998672094806"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.dir/Debug/cmTC_f343a.ilk", "hash": "8480778318256906157"}, {"file": "tests/src/driver/DriverEntry.c", "hash": "15432365288492737233"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q83.sql", "hash": "14811567366711738818"}, {"file": "sail-main/docs/guide/deployment/_common/support.md", "hash": "4038832173316064802"}, {"file": "sail-main/docs/.vitepress/theme/components/PageList.vue", "hash": "6059411850657542609"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84_RELWITHDEBINFO_loc", "hash": "121175230543071829"}, {"file": "docs/KMDF Driver1/backup/include/core/types/DriverTypes.md", "hash": "12685288772553887132"}, {"file": "src/hal/bus/spi_core.c", "hash": "237621112639118554"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/cmTC_fba92.vcxproj", "hash": "16172983438809511386"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "sail-main/python/pysail/data/tpch/queries/q10.sql", "hash": "1941193993808825776"}, {"file": "sail-main/crates/sail-common/src/string.rs", "hash": "2753292160442930678"}, {"file": "sail-main/python/pysail/docs/.gitignore", "hash": "10752258690114484908"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/MemoryCompression.md", "hash": "11093687460873692203"}, {"file": "tests/src/utils/optimization/optimization_history.c", "hash": "11553267181717299345"}, {"file": ".vscode/sdv-default_20250108_140645.xml", "hash": "10931019705986488358"}, {"file": "docs/KMDF Driver1/tests/src/utils/manager.md", "hash": "17036075936429722929"}, {"file": "sail-main/python/pysail/tests/spark/test_udf.py", "hash": "14143936174364367146"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformancePredictor.md", "hash": "13344106447495631249"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q80.sql", "hash": "11270868335757183117"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/QueueOperations.md", "hash": "11607574489902703500"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_analyzer.md", "hash": "12003155831058412718"}, {"file": "sail-main/crates/sail-sql-analyzer/Cargo.toml", "hash": "14174108404454658432"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_group_map_udf.rs", "hash": "16864680045767560730"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_misc.json", "hash": "15580004034706074217"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceMonitor.md", "hash": "2312054369463986103"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/__init__.cpython-313.pyc", "hash": "10876873813678925159"}, {"file": "tests/src/device/TouchPerformance.c", "hash": "12536186045815100314"}, {"file": "tests/unit/dmamanager.h", "hash": "7624006582029009443"}, {"file": "tests/src/core/error_handling_test.c", "hash": "15216857325453098660"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/timestamp_now.rs", "hash": "5655802270590857622"}, {"file": "sail-main/crates/sail-sql-parser/src/tree.rs", "hash": "3212963497828882307"}, {"file": "tmp_structure/tools/utils/agents/best_practice_agent.py", "hash": "17112760407799093744"}, {"file": "tests/src/test_main.c", "hash": "17856805622170871630"}, {"file": "tmp_structure/tests/performance/performance_test.c", "hash": "9866094369922669477"}, {"file": "docs/KMDF Driver1/build/version.md", "hash": "5643954368802313811"}, {"file": "tmp_structure/tools/fix/code_fixer.py", "hash": "4486771173918266878"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/PerformanceManager.md", "hash": "1380858619688071149"}, {"file": "tmp_structure/tools/build/build.ps1", "hash": "12262130116809123457"}, {"file": "tmp_structure/tools/utils/core/__pycache__/model_adapters.cpython-313.pyc", "hash": "3926203778600644025"}, {"file": "tmp_structure/tests/unit/device_health_monitor.c", "hash": "16961352920839327710"}, {"file": "sail-main/crates/sail-plan/src/extension/physical/schema_pivot.rs", "hash": "4087854556084077241"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/usb_interface.md", "hash": "7755960637918024082"}, {"file": "tmp_structure/output/分析结果.json", "hash": "3415510036615062894"}, {"file": "analyze_headers.py", "hash": "17648001957220340279"}, {"file": "docs/KMDF Driver1/tests/src/utils/statistics_manager.md", "hash": "10687184947354352039"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/interrupt/interrupt_test.md", "hash": "15984150053883273172"}, {"file": "tmp_structure/tests/unit/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "docs/KMDF Driver1/tests/src/utils/power/power_state_manager.md", "hash": "13097609162600527176"}, {"file": "tmp_structure/output/研究文档.md", "hash": "9640913094441942790"}, {"file": "docs/KMDF Driver1/backup/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "tmp_structure/tests/unit/interrupt.c", "hash": "438091713417069452"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt.md", "hash": "7122344570168608003"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/performance_data_collector.md", "hash": "6566743570803561025"}, {"file": "docs/KMDF Driver1/tests/src/utils/PerfMonitor.md", "hash": "1661169831681127515"}, {"file": "sail-main/python/pysail/examples/spark/tpch.py", "hash": "2972885951792036083"}, {"file": "tests/unit/dmamanager.c", "hash": "16085011483513293531"}, {"file": "tmp_structure/tools/fix/fix_tabs_v2.ps1", "hash": "11887283907230863418"}, {"file": "sail-main/crates/sail-plan/src/extension/function/map/spark_element_at.rs", "hash": "6770532283395758297"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/rpc.rs", "hash": "5274541581616151262"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/registry_callback.md", "hash": "1464403380017115903"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/advanced_models.md", "hash": "2138160585059009631"}, {"file": "tests/src/utils/device/spi_interface.c", "hash": "13749364630813119982"}, {"file": "tmp_structure/tools/utils/core/adaptive_output.py", "hash": "1928394092660785968"}, {"file": "tests/src/utils/cache_manager.c", "hash": "868028145507112649"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "docs/module_guidelines.md", "hash": "10696276466022967433"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/prediction_core.md", "hash": "587477157269630570"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/ml_optimizer.md", "hash": "17924039229138340958"}, {"file": "tests/src/device/TouchController.c", "hash": "8555681626782575179"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_state.md", "hash": "4327613149066213288"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/json.rs", "hash": "6433637683619273470"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/mod.rs", "hash": "18363666773065669692"}, {"file": "build/build_script.vcxproj", "hash": "1649852021370741417"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/hash.rs", "hash": "18307841736181652632"}, {"file": "tests/src/performance/PerformancePredictor.c", "hash": "16358558668992702664"}, {"file": "tmp_structure/tests/unit/event_trace.c", "hash": "10188444021102792748"}, {"file": "tests/src/utils/power/power_test.c", "hash": "9102832245856542684"}, {"file": "tmp_structure/tests/unit/test_reporter.c", "hash": "15868353065156172044"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/optimization_evaluator.md", "hash": "9762678218674954908"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_control.md", "hash": "14823537430827988127"}, {"file": "sail-main/crates/sail-cli/Cargo.toml", "hash": "7578983491512577588"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_executor.md", "hash": "8670951633232470291"}, {"file": "docs/KMDF Driver1/backup/include/device/device.md", "hash": "15950813879397350842"}, {"file": "tmp_structure/tools/fix/auto_fix_declarations.py", "hash": "2374951955509788451"}, {"file": "rustup-init.exe", "hash": "6615076961598520463"}, {"file": "sail-main/python/pysail/tests/test_package.py", "hash": "6615584211801613582"}, {"file": "tests/src/utils/device.tmh", "hash": "5558032582126480111"}, {"file": "tests/src/utils/optimization/optimization_executor.c", "hash": "5284552834346901902"}, {"file": "tmp_structure/include/log/trace.h", "hash": "15279218248740019239"}, {"file": "build/.cmake/api/v1/reply/target-build_script-Debug-251266553d8db8bed6c5.json", "hash": "13997068141168601047"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/CMakeFiles/cmake.check_cache", "hash": "8396945883222416317"}, {"file": "sail-main/python/pysail/data/tpch/queries/q18.sql", "hash": "11987898900157400648"}, {"file": "sail-main/crates/sail-sql-parser/src/location.rs", "hash": "4398577124306798549"}, {"file": "tests/unit/touchmanager.h", "hash": "18031675363219744266"}, {"file": "sail-main/scripts/spark-tests/show-passed-tests.jq", "hash": "8466162096886910797"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/mod.rs", "hash": "6915412533197675465"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/GlobalPerformanceManager.md", "hash": "8455061991712322545"}, {"file": "docs/KMDF Driver1/tests/src/utils/hal.md", "hash": "798073682164515556"}, {"file": "docs/KMDF Driver1/backup/include/core/types/kmdf_types.md", "hash": "11820292990252072646"}, {"file": "tests/src/io/IOManager.c", "hash": "6435585653620536882"}, {"file": "certs/kmdf_driver_test_cert.cer", "hash": "6270925379150306323"}, {"file": "build/format.vcxproj", "hash": "16969557356432151454"}, {"file": "docs/KMDF Driver1/backup/include/hardware/kmdf_hal.md", "hash": "7178785057074432366"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_performance_monitor.md", "hash": "2458298706816984463"}, {"file": "tmp_structure/tests/unit/predictor.c", "hash": "15488274478411287487"}, {"file": "sail-main/crates/sail-execution/src/id.rs", "hash": "11207280949363260473"}, {"file": "sail-main/crates/sail-cli/src/worker/entrypoint.rs", "hash": "8818354762090065693"}, {"file": "docs/KMDF Driver1/tmp_structure/include/log/driver_log.md", "hash": "12751813489477682259"}, {"file": "tmp_structure/tests/unit/prediction_model.c", "hash": "10565836354052141209"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q98.sql", "hash": "13026109219783178560"}, {"file": "build/.cmake/api/v1/reply/directory-.-RelWithDebInfo-db295b480dbd9a08bd60.json", "hash": "1733733511912741264"}, {"file": "include/hal/bus/kmdf_bus_common.h", "hash": "8533679384540431468"}, {"file": "tests/unit/kmdfhal.c", "hash": "13443276317294174339"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/spi_interface.md", "hash": "17056524494875492501"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q71.sql", "hash": "14742571857829139598"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/Debug/cmTC_fba92.pdb", "hash": "10213736011911894601"}, {"file": ".vs/ProjectSettings.json", "hash": "3239194559763511211"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/schema_pivot.rs", "hash": "13625348486883126553"}, {"file": "sail-main/crates/sail-python-udf/src/config.rs", "hash": "9607732740747717003"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/cmTC_f343a.vcxproj.filters", "hash": "16424281735504224369"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "sail-main/crates/sail-plan/src/extension/function/spark_xxhash64.rs", "hash": "6242815201167157412"}, {"file": "tests/src/utils/interrupt/interrupt_test.c", "hash": "10091785769281496381"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/error/ErrorTypes.md", "hash": "8245966024912691833"}, {"file": "tests/src/utils/GamepadCore.c", "hash": "15479958173847767625"}, {"file": "tmp_structure/tools/fix/fix_tabs.ps1", "hash": "15431393325432432609"}, {"file": "tests/unit/errorhandler.c", "hash": "638403771659111855"}, {"file": "docs/KMDF Driver1/tests/src/utils/interrupt/interrupt_handler.md", "hash": "8221316859104640318"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_load_data.json", "hash": "17591761252902732006"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q88.sql", "hash": "14537475311440919374"}, {"file": "tmp_structure/tests/unit/memorypool.c", "hash": "8309557649383373614"}, {"file": "sail-main/scripts/common-gold-data/count_missing_functions.py", "hash": "1986676766030921934"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_ops.md", "hash": "6617533335334583642"}, {"file": "sail-main/crates/sail-sql-parser/src/lib.rs", "hash": "16359352303042093319"}, {"file": "docs/project_structure.md", "hash": "2037958482711414603"}, {"file": "sail-main/crates/sail-plan/src/extension/function/max_min_by.rs", "hash": "13215227352496526384"}, {"file": "sail-main/scripts/spark-tests/generate-test-report.sh", "hash": "14328652669309560923"}, {"file": "tests/src/driver/driver_includes.h", "hash": "12262707748624835517"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "docs/KMDF Driver1/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": ".vs/VSWorkspaceState.json", "hash": "12966325862263698138"}, {"file": "docs/project_md/how_to_run_as_admin.txt", "hash": "6816337553749047106"}, {"file": "tmp_structure/tests/unit/device_queue.c", "hash": "16684379454006678062"}, {"file": "sail-main/docs/development/spark-tests/spark-patch.md", "hash": "17078284312931306073"}, {"file": "tests/src/utils/simd/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": "tests/unit/errorrecovery.c", "hash": "7122396928859668049"}, {"file": ".github/workflows/code-style-check.yml", "hash": "9198763704460426098"}, {"file": "tmp_structure/tools/fix/fix_header_guards_improved.ps1", "hash": "7993290607892531405"}, {"file": "tests/src/utils/device/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "build/.cmake/api/v1/reply/codemodel-v2-4cd3f1becf3d9e80f03c.json", "hash": "11828297610868367021"}, {"file": "generateteachingnotes.bat", "hash": "13489632455467238609"}, {"file": "tools/scripts/run_project_optimization.bat", "hash": "8415366508469273981"}, {"file": "sail-main/crates/sail-plan/src/resolver/url.rs", "hash": "11624079465244683336"}, {"file": "tmp_structure/tools/build/build_manager.py", "hash": "15492535410479556845"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/url.json", "hash": "14906240713976957471"}, {"file": "tests/src/performance/performance_predictor.c", "hash": "13212490056959431768"}, {"file": "tests/src/utils/Queue.c", "hash": "4795497370184670599"}, {"file": "tmp_structure/tests/performance/performance_monitor_manager.c", "hash": "6036622391152221299"}, {"file": "tmp_structure/tests/unit/errorrecovery.c", "hash": "7122396928859668049"}, {"file": "sail-main/crates/sail-execution/Cargo.toml", "hash": "17998857403561937004"}, {"file": "example_project/module2/visualization.py", "hash": "15986574477792699771"}, {"file": "tests/unit/prediction_core.c", "hash": "1857614276287916371"}, {"file": "tests/src/driver/KMDFSPI.c", "hash": "17213720173027797590"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchManager.md", "hash": "67499987134114106"}, {"file": "tmp_structure/tools/build/build_driver.ps1", "hash": "17906769147179237377"}, {"file": "tmp_structure/tests/unit/simd_operations.c", "hash": "2970634337399079429"}, {"file": "sail-main/crates/sail-execution/src/worker/mod.rs", "hash": "6718748100402445315"}, {"file": "docs/KMDF Driver1/backup/include/public/driver/driver_manager.md", "hash": "9671447558169829830"}, {"file": "sail-main/crates/sail-sql-parser/src/common.rs", "hash": "15926717565533138012"}, {"file": "tests/unit/driverlog.c", "hash": "13535373348672446805"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_manager_1.md", "hash": "11358954628081867466"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_resource.md", "hash": "474047599558141070"}, {"file": "tests/unit/touch.c", "hash": "5834024937245580957"}, {"file": "docs/KMDF Driver1/include/driver/log/DriverLog.md", "hash": "1410373447449831134"}, {"file": "tmp_structure/tools/utils/teaching_annotator.py", "hash": "3056533638353222275"}, {"file": "tmp_structure/tools/utils/simple_driver_sign.ps1", "hash": "9714128146809078086"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_manager_1.md", "hash": "11358954628081867466"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CMakeCCompilerId.obj", "hash": "17924779487164352067"}, {"file": "tmp_structure/tests/unit/driver.c", "hash": "5937756760430682963"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q50.sql", "hash": "7419397474068928100"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q33.sql", "hash": "45182612794789087"}, {"file": "tests/src/utils/device/device_queue_1.c", "hash": "330133164890642531"}, {"file": "sail-main/docs/development/spark-tests/test-log-analysis.md", "hash": "5166526314025180907"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/test_framework_test.md", "hash": "5313371865062439033"}, {"file": "tests/unit/event_tracer.c", "hash": "7710005633789833902"}, {"file": "tmp_structure/tests/unit/memorymanager.h", "hash": "10010922765026236252"}, {"file": "sail-main/crates/sail-execution/src/worker_manager/kubernetes.rs", "hash": "9198011815455218189"}, {"file": "sail-main/python/pysail/docs/index.rst", "hash": "6946560330809569173"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/config_group.md", "hash": "13037418512325033205"}, {"file": "tmp_structure/tools/utils/cursor_machine_id.py", "hash": "11784640157050564506"}, {"file": "tests/unit/device_interrupt.c", "hash": "1203837636593473621"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/resource_manager.md", "hash": "17123311457972373377"}, {"file": "fix_test_filenames.bat", "hash": "14819378410324417697"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_operations.md", "hash": "12450057505270295025"}, {"file": "sail-main/crates/sail-gold-test/src/lib.rs", "hash": "856900682241694432"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_optimizer.md", "hash": "473303732562322893"}, {"file": "tmp_structure/tests/unit/processfunctions.c", "hash": "15471745620740669001"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeCache.txt", "hash": "12543916234924644661"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/power_optimizer.md", "hash": "8344076760809889555"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/Debug/cmTC_58f84.pdb", "hash": "6905416279087703616"}, {"file": "tests/unit/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "docs/KMDF Driver1/tests/src/driver/Driver.md", "hash": "8867171595501583707"}, {"file": "src/core/error/error_handling.c", "hash": "14423419909351606483"}, {"file": "tmp_structure/tools/utils/ai_roles.py", "hash": "1926999291726298739"}, {"file": "tests/unit/error_core.c", "hash": "13244377760009713122"}, {"file": "tests/src/utils/trace/trace.c", "hash": "7049877013033903552"}, {"file": "tests/unit/sample_test.c", "hash": "6872651473851085099"}, {"file": "tmp_structure/output/教学文档.md", "hash": "16979563766180323480"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/datetime.rs", "hash": "784266542693658793"}, {"file": "docs/KMDF Driver1/backup/include/error/error_handler.md", "hash": "11014780612490094847"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/memory/memory_pool.md", "hash": "1168139887190025561"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/io/IOManager.md", "hash": "12962392107439165305"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger_core.md", "hash": "17698932983924238851"}, {"file": "tmp_structure/tests/unit/config_group.c", "hash": "13073677126841465732"}, {"file": "sail-main/docs/guide/tasks/udf/index.md", "hash": "13239709296536726412"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/CMakeCache.txt", "hash": "3857472814008649546"}, {"file": "tests/unit/logger.c", "hash": "2692251408270688909"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/hyperparam_optimizer.md", "hash": "12122682215687726617"}, {"file": "sail-main/crates/sail-spark-connect/src/executor.rs", "hash": "665811273623727658"}, {"file": "sail-main/python/pysail/data/tpch/queries/q22.sql", "hash": "2766237084482014706"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_select.json", "hash": "17058861694858893481"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/interrupt/interrupt_test.md", "hash": "15984150053883273172"}, {"file": "tmp_structure/tools/fix/fix_code.ps1", "hash": "2433744338792008947"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/CL.write.1.tlog", "hash": "13088518092928041168"}, {"file": "tmp_structure/tests/unit/driver_test.c", "hash": "14081732213465014022"}, {"file": "sail-main/python/pysail/cli.py", "hash": "18222799987051407997"}, {"file": "sail-main/crates/sail-common/src/config/application.rs", "hash": "3365554595169131644"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "sail-main/crates/sail-sql-parser/src/options.rs", "hash": "11404392613132044711"}, {"file": "sail-main/.github/actions/pull-request-report/action.yml", "hash": "8151008992862532740"}, {"file": "sail-main/pnpm-lock.yaml", "hash": "10269566117349771710"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/spi_interface.md", "hash": "13233617362743644576"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/expression/like.json", "hash": "15094039182179087034"}, {"file": "tests/src/device/TouchPerformance.h", "hash": "3643163510723135612"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/event_buffer.md", "hash": "11762678649378503503"}, {"file": "src/hal/devices/gpio_device.c", "hash": "3015179492402158855"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "tmp_structure/tools/build/analyze_build_log.py", "hash": "15023787929842826386"}, {"file": "docs/KMDF Driver1/tmp_structure/include/driver/driver_core.md", "hash": "1145859136680343544"}, {"file": "tmp_structure/tools/utils/core/integration_engine.py", "hash": "28873787710426007"}, {"file": "docs/KMDF Driver1/include/device/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_io.md", "hash": "16633121732728631638"}, {"file": "tests/src/driver/KMDFDriver1.inf", "hash": "17027072167203267679"}, {"file": "tests/src/utils/ml/distributed_training.c", "hash": "13748589215294334048"}, {"file": "sail-main/python/pysail/data/tpch/queries/q21.sql", "hash": "2258497526355208899"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q10.sql", "hash": "17503159169343734705"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver_core.md", "hash": "15214766627783842292"}, {"file": "tests/unit/touchai.c", "hash": "6072030623807491314"}, {"file": "sail-main/docs/guide/deployment/docker-images/index.data.ts", "hash": "1922104014002154446"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/optimization/optimization_history.md", "hash": "10843695273737027041"}, {"file": "tmp_structure/tests/unit/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "tmp_structure/tests/unit/auto_tuning.h", "hash": "5228767904912538066"}, {"file": "docs/KMDF Driver1/tests/src/security/error_recovery.md", "hash": "5511226053469075265"}, {"file": "docs/api/火山方舟API.txt", "hash": "12834483488995527018"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-9mhkke/cmTC_4f03d.dir/Debug/cmTC_4f03d.ilk", "hash": "17745460184794301169"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q51.sql", "hash": "6052587455718861825"}, {"file": "build/.cmake/api/v1/reply/target-format-Debug-81d6e7efb85862f3b20c.json", "hash": "5876608019943124009"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/Driver.md", "hash": "13509455873758057648"}, {"file": "tests/src/device/TouchManager.h", "hash": "7272426768181106230"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_hex_unhex.rs", "hash": "10785823486482187348"}, {"file": "tmp_structure/tests/unit/device_test.c", "hash": "4283402855535051437"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/resource_optimizer.md", "hash": "14022510383458335517"}, {"file": "tests/unit/device_state.c", "hash": "232531793412726154"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFHAL.md", "hash": "14106586863704401393"}, {"file": "tmp_structure/tests/unit/touchdebug.h", "hash": "7306178602982181008"}, {"file": "tests/src/utils/optimization/auto_tuning.c", "hash": "4493664318922361716"}, {"file": "build/.cmake/api/v1/reply/toolchains-v1-06b4e756c8bd416e0e57.json", "hash": "13008094141361006255"}, {"file": "tmp_structure/tests/unit/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/least_greatest.rs", "hash": "3994708551332490899"}, {"file": "tmp_structure/tests/unit/errorprevention.c", "hash": "10299302956295713904"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_monitor.md", "hash": "17046761877009662786"}, {"file": "tests/unit/kmdferror.c", "hash": "3859077427525291268"}, {"file": "docs/整改计划.md", "hash": "2132364354320091350"}, {"file": "sail-main/crates/sail-plan/src/utils.rs", "hash": "5162645816895057343"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/simd_prediction.md", "hash": "680214203800970958"}, {"file": "tmp_structure/tests/performance/performanceanalyzer.c", "hash": "16444703254818628465"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q7.sql", "hash": "7128345000043240871"}, {"file": "sail-main/crates/sail-execution/proto/sail/worker/service.proto", "hash": "12668811943508468265"}, {"file": "tests/src/utils/trace/log_manager.h", "hash": "15729901125985167955"}, {"file": "tests/unit/error_handling.c", "hash": "15024041996413401458"}, {"file": "tests/src/utils/AlertManager.cpp", "hash": "9142306627596829532"}, {"file": "tmp_structure/tools/utils/print_env.py", "hash": "15462824488675427187"}, {"file": "sail-main/crates/sail-spark-connect/src/config.rs", "hash": "14264960124481874327"}, {"file": "tests/unit/module_manager.c", "hash": "6226471683647205323"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CompilerIdCXX.lastbuildstate", "hash": "12833379477460082831"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_pandas_agg_udf.txt", "hash": "6734954635397080964"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceAnalyzer.md", "hash": "17860791912881724719"}, {"file": "docs/refactor_plan_20240101_driver_optimization.md", "hash": "11339095362012304468"}, {"file": "tests/performance/device_performance_monitor.c", "hash": "9414896522403079228"}, {"file": "sail-main/python/pysail/data/tpch/queries/q2.sql", "hash": "17667936087616483180"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/feature_engineering.md", "hash": "8263595592922849097"}, {"file": "tests/unit/hardwaremanager.h", "hash": "1603174420775838200"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_error_monitor.md", "hash": "3841573886328726193"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-nsh998/cmTC_35600_DEBUG_loc", "hash": "3543777267981702088"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/prediction_internal.md", "hash": "17346287682889573781"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/link.write.1.tlog", "hash": "15817256484340894647"}, {"file": "sail-main/rustfmt.toml", "hash": "11002475896267431581"}, {"file": "test_ark_sdk.py", "hash": "18356357060012908025"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFError.md", "hash": "2086467841528888470"}, {"file": "docs/KMDF Driver1/tests/src/utils/ml/model_evaluation.md", "hash": "8588449621368013720"}, {"file": "run_build_and_log.bat", "hash": "16386400324458232912"}, {"file": "docs/修改步骤.md", "hash": "10364683472406199775"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/generator.json", "hash": "2587344914148328621"}, {"file": "sail-main/docs/public/banner.png", "hash": "3250293694238695832"}, {"file": "sail-main/crates/sail-common-datafusion/src/datetime.rs", "hash": "16834507030017313509"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_core.md", "hash": "12158579485392829125"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/core/types.md", "hash": "13035358849170729291"}, {"file": "scripts/fix_header_guards.ps1", "hash": "12174742135892345499"}, {"file": "docs/KMDF Driver1/tests/src/performance/Performance.md", "hash": "2031739876215854096"}, {"file": "sail-main/python/pysail/data/tpch/queries/q19.sql", "hash": "14913542615177813682"}, {"file": "tests/unit/telemetry_manager.c", "hash": "15920047234830324928"}, {"file": "docs/MCP使用方法/memory_mcp使用指南.md", "hash": "12655324536445253388"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_timer.md", "hash": "704186043498800617"}, {"file": "tests/src/performance/Performance.c", "hash": "542837662377689141"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/analyzer.md", "hash": "8824479516875383820"}, {"file": "tmp_structure/tools/utils/__pycache__/api_adapter.cpython-313.pyc", "hash": "2956084882020527952"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q4.sql", "hash": "17672609773646770737"}, {"file": "sail-main/crates/sail-plan/src/resolver/data_type.rs", "hash": "17958322607898581405"}, {"file": "sail-main/crates/sail-server/src/lib.rs", "hash": "1547945453562629757"}, {"file": "cleanupproject.ps1", "hash": "12508153147597765990"}, {"file": "src/hal/bus/gpio_core.c", "hash": "15184170150572455756"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q8.sql", "hash": "7467416880605540281"}, {"file": "tmp_structure/tools/fix/fix_coding_issues.ps1", "hash": "2051506399048036844"}, {"file": "tests/unit/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "sail-main/docs/.vitepress/theme/app.css", "hash": "8072674454493207025"}, {"file": "tests/unit/touchdebug.c", "hash": "3448456818563192507"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_performance_monitor.md", "hash": "1279374117974282270"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_error_monitor.md", "hash": "15987310965375036974"}, {"file": "sail-main/crates/sail-execution/src/driver/client.rs", "hash": "4991480455477307816"}, {"file": "tests/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/simd/simd_performance.md", "hash": "10392560569300028256"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/logging/LogConfig.md", "hash": "12050376208257490738"}, {"file": "fix_hal_encoding.ps1", "hash": "10991348314127528048"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "tmp_structure/tests/unit/registry_cache.c", "hash": "18036733393600373308"}, {"file": "tests/unit/commonutils.c", "hash": "11647735861415970558"}, {"file": "sail-main/crates/sail-plan/src/catalog/catalog.rs", "hash": "8192028245628247694"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/conditional.json", "hash": "15095020771291331153"}, {"file": "vs_info.json", "hash": "5146447665631701869"}, {"file": "tests/unit/test_reporter.c", "hash": "15868353065156172044"}, {"file": "sail-main/.github/actions/mount-target-directory/action.yml", "hash": "11977431549780773764"}, {"file": "sail-main/crates/sail-common/src/spec/data_type.rs", "hash": "11658543539514694190"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_performance_monitor.md", "hash": "5561470136532144482"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/stream_monitor.rs", "hash": "9858290221543015914"}, {"file": "tests/unit/optimization_database.c", "hash": "1382797123011915962"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/semantic_agent.cpython-313.pyc", "hash": "8694611905034078421"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAOptimizer.md", "hash": "5790309278613804471"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "tests/src/driver/DriverLog.c", "hash": "13535373348672446805"}, {"file": "tmp_structure/tools/utils/agents/__init__.py", "hash": "7742675949575766707"}, {"file": "tests/src/utils/ml/power_optimizer.c", "hash": "14755199465299351821"}, {"file": "sail-main/crates/sail-python-udf/src/udf/pyspark_unresolved_udf.rs", "hash": "1128490765505444898"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/lambda.json", "hash": "8821858738252330161"}, {"file": "docs/KMDF Driver1/tests/src/utils/registry_cache.md", "hash": "10201547265023507712"}, {"file": "sail-main/crates/sail-plan/src/resolver/literal.rs", "hash": "18372592875218966716"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/adaptive_scheduler.md", "hash": "6201384970667833372"}, {"file": "tests/src/utils/ml/cache_optimizer.c", "hash": "11608085625558857373"}, {"file": "sail-main/crates/sail-plan/src/object_store/layers/lazy.rs", "hash": "5465072889438915603"}, {"file": "tmp_structure/tools/utils/format_code.ps1", "hash": "8664614111954803457"}, {"file": "tmp_structure/tools/utils/check_includes.py", "hash": "5368652997572253015"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "config/models/xunfei.json", "hash": "6867580776858784280"}, {"file": "tmp_structure/tools/utils/header_repair.py", "hash": "3244421341483603138"}, {"file": "tests/unit/queue.c", "hash": "4795497370184670599"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceAnalyzer.md", "hash": "4689131099208303134"}, {"file": "build/.cmake/api/v1/reply/target-format-MinSizeRel-81d6e7efb85862f3b20c.json", "hash": "5876608019943124009"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ConfigManager.md", "hash": "14798362301014302758"}, {"file": "tests/src/driver/driver_test.c", "hash": "14081732213465014022"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/INSTALL_force.rule", "hash": "1819760324823437288"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/model_evaluation.md", "hash": "11583937441064285777"}, {"file": "tests/src/utils/interrupt/interrupt_optimizer.c", "hash": "2533103627182738699"}, {"file": "tmp_structure/tests/unit/resource_monitor.c", "hash": "6229842563307933005"}, {"file": "tests/unit/device_io.c", "hash": "9774509186909211676"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_array_empty_to_null.rs", "hash": "17038256331184351424"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/kmdf_core.md", "hash": "14537529512716125567"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_delete_from.json", "hash": "17744515004467142186"}, {"file": "include/core/error/error_codes.h", "hash": "13134488197283627121"}, {"file": "sail-main/crates/sail-sql-macro/src/utils.rs", "hash": "8165665724137784272"}, {"file": "tests/src/utils/interrupt/interrupt.c", "hash": "438091713417069452"}, {"file": "tests/unit/device_queue.c", "hash": "16684379454006678062"}, {"file": "sail-main/docs/development/recipes/object-store.md", "hash": "5998092782281437184"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "docs/project_md/test_e2b.md", "hash": "9062257711975633675"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/trace/event_tracer.md", "hash": "2817761038166392675"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_interrupt.md", "hash": "17377046062159831964"}, {"file": "tmp_structure/docs/project_structure.md", "hash": "3281558196775761998"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/Debug/CompilerIdC.tlog/link.write.1.tlog", "hash": "4971635830808493534"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/string.json", "hash": "14917610069334144072"}, {"file": "sail-main/crates/sail-plan/src/resolver/ddl.rs", "hash": "8199489388289895986"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/hash.json", "hash": "14392829946598336696"}, {"file": "sail-main/crates/sail-cli/src/python/spark_mcp_server.py", "hash": "8197131840766276666"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/prediction_core.md", "hash": "16511274734961665551"}, {"file": "docs/KMDF Driver1/include/core/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "tests/unit/simd_prediction.c", "hash": "12613885636365332720"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/KMDFI2C.md", "hash": "2401467074656005273"}, {"file": "sail-main/crates/sail-plan/src/object_store/hugging_face.rs", "hash": "7740628567407986120"}, {"file": "tmp_structure/tests/unit/error_handling_test.c", "hash": "1379311432594092980"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/trace/trace_core.md", "hash": "14024586508680330554"}, {"file": "tmp_structure/tests/unit/registry_manager.c", "hash": "7212363665723535550"}, {"file": "tmp_structure/tools/fix/fix_source_standards.py", "hash": "16802809975601174919"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/simd/simd_ops.md", "hash": "3205151156127908198"}, {"file": "docs/KMDF Driver1/backup/include/error/error_codes.md", "hash": "11757503738850153410"}, {"file": "docs/KMDF Driver1/backup/include/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "docs/KMDF Driver1/backup/tests/src/io/IOOptimizer.md", "hash": "2425979290851834818"}, {"file": "tmp_structure/tools/utils/core/__pycache__/__init__.cpython-313.pyc", "hash": "2201897863955216378"}, {"file": "tmp_structure/tools/test/test_encoding.py", "hash": "12351899566809374434"}, {"file": "tmp_structure/tests/unit/kmdf_core.c", "hash": "11085129429806464239"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_alter_view.json", "hash": "12744323970996104160"}, {"file": "tests/src/utils/device/device_context.c", "hash": "3876629123896102954"}, {"file": "tests/src/utils/test/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "docs/KMDF Driver1/tests/src/security/error_handling_test.md", "hash": "12833519669823771262"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_test.md", "hash": "17457826267075368302"}, {"file": "tests/unit/touchmanager.c", "hash": "2612989593074257093"}, {"file": "docs/KMDF Driver1/backup/tests/src/core/CoreManager.md", "hash": "3302314418596781527"}, {"file": "tests/src/utils/simd/simd_implementation.c", "hash": "10472737744387686396"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/queue.md", "hash": "17411770077844460848"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-ff8aef/Debug/cmTC_f343a.exe", "hash": "11161833998618522851"}, {"file": "sail-main/crates/sail-plan/src/extension/function/datetime/spark_make_timestamp.rs", "hash": "3844863609033736551"}, {"file": "sail-main/crates/sail-execution/src/worker/client.rs", "hash": "9680715682575518313"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/Hardware.md", "hash": "12499629258003032487"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/link.write.1.tlog", "hash": "3812677343372885969"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": ".vscode/file_rules.json", "hash": "15179874288697686795"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/usb_interface.md", "hash": "15608419832055058263"}, {"file": "docs/KMDF Driver1/tmp_structure/src/error/error_handling.md", "hash": "12651407806898763387"}, {"file": "tmp_structure/tests/unit/device_control.c", "hash": "1983799697968931608"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q21.sql", "hash": "16700710249278548920"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_workitem.md", "hash": "3984672476542598313"}, {"file": "docs/KMDF Driver1/tests/src/device/Gyro.md", "hash": "139370696856845314"}, {"file": "sail-main/scripts/spark-tests/plugins/spark.py", "hash": "484233986140980790"}, {"file": "tests/unit/logger_core.c", "hash": "5596205493550234397"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/trace.md", "hash": "938798632008336272"}, {"file": "tmp_structure/tools/utils/restructure_project.py", "hash": "10519243202187776696"}, {"file": "tests/unit/touchai.h", "hash": "12290558413403050005"}, {"file": "sail-main/crates/sail-sql-analyzer/src/literal/datetime.rs", "hash": "5337397223503482373"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/test_framework.md", "hash": "4745469584451480626"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/ddl_replace_table.json", "hash": "8020106192877354830"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/mod.rs", "hash": "4835897139267406524"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFUSB.md", "hash": "2444171969419869765"}, {"file": "sail-main/crates/sail-cli/src/main.rs", "hash": "13894889199565408442"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/driver/DriverEntry.md", "hash": "17003589181975058397"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/model.md", "hash": "6914076471633022777"}, {"file": "tests/unit/interrupt_handler.c", "hash": "3565575960434681796"}, {"file": ".vs/KMDF Driver1/FileContentIndex/3af8275c-b9d5-4d99-a938-c606361ca9cf.vsidx", "hash": "12815895691180002701"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_with.json", "hash": "16926217331082006540"}, {"file": "docs/KMDF Driver1/include/device/io/device_queue.md", "hash": "10025303424683255249"}, {"file": "sail-main/crates/sail-spark-connect/src/session_manager.rs", "hash": "9828597936153852903"}, {"file": "tmp_structure/tools/utils/kmdf_controller.py", "hash": "10576471519790572159"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt_optimizer.md", "hash": "6880610618742573433"}, {"file": "tmp_structure/tools/utils/ai_collaboration_roles.py", "hash": "15025797022093181680"}, {"file": "docs/KMDF Driver1/tests/src/utils/resource_scheduler.md", "hash": "9259475315249995297"}, {"file": "docs/KMDF Driver1/tests/src/utils/test/queue_test.md", "hash": "18427168565910843215"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_collector.md", "hash": "354623443312585585"}, {"file": "docs/project_md/test_annotator.md", "hash": "11268776145223411133"}, {"file": "tests/src/performance/performance_optimizer.c", "hash": "18322676524363042540"}, {"file": "docs/KMDF Driver1/backup/src/core/error_handling.md", "hash": "8736094554619853824"}, {"file": "sail-main/python/pysail/data/tpch/queries/q15.sql", "hash": "12496705792721828431"}, {"file": "docs/api/jinaAPI.txt", "hash": "6331602777864225042"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/cache_manager.md", "hash": "14041659744676161652"}, {"file": "tests/src/utils/trace/log_manager.c", "hash": "1851491385887858320"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_context.md", "hash": "5783058402535230175"}, {"file": "sail-main/crates/sail-execution/src/worker/actor/core.rs", "hash": "2654279876409569673"}, {"file": "sail-main/python/pysail/tests/spark/literal/test_timestamp.txt", "hash": "12006797349833694768"}, {"file": ".github/workflows/ci_tests.yml", "hash": "6228437175284659019"}, {"file": "docs/KMDF Driver1/tests/src/utils/optimization/optimization_evaluator.md", "hash": "11857399457039219799"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q55.sql", "hash": "6876216984713303093"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/cache_manager.md", "hash": "14041659744676161652"}, {"file": "tmp_structure/tools/utils/core/__pycache__/mace_config.cpython-313.pyc", "hash": "4929717637443591073"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_core.md", "hash": "2996160899354651317"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q52.sql", "hash": "5988018438893418864"}, {"file": "tmp_structure/tests/unit/event_buffer.c", "hash": "7571709781436123223"}, {"file": "sail-main/crates/sail-plan/src/extension/function/mode.rs", "hash": "14951745868043715316"}, {"file": "tmp_structure/tools/fix/fix_newlines.ps1", "hash": "3484662595788553404"}, {"file": "tmp_structure/tools/utils/verify_dev_env.ps1", "hash": "10167228439969573549"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/optimization/simd_optimization.md", "hash": "10173780754813314530"}, {"file": "tests/src/utils/hal.c", "hash": "13073342420439847883"}, {"file": "tests/unit/error_manager.c", "hash": "17523935371877631884"}, {"file": "tmp_structure/tests/unit/sample_test.c", "hash": "6872651473851085099"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceManager.md", "hash": "4322100284458284897"}, {"file": "tmp_structure/tests/unit/monitor.h", "hash": "7267562670448795700"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "build/.cmake/api/v1/reply/target-ALL_BUILD-MinSizeRel-e5692e6ed879150c5474.json", "hash": "6502387549155151165"}, {"file": "docs/KMDF Driver1/backup/include/utils/interrupt/interrupt.md", "hash": "1953429579170954632"}, {"file": "package-lock.json", "hash": "11496171963891232631"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/test/test_reporter.md", "hash": "13242994936096055600"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath.txt", "hash": "11217971592272304290"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q46.sql", "hash": "5908547835843098507"}, {"file": "docs/api/api2_key.txt", "hash": "299278678968192926"}, {"file": "sail-main/docs/guide/deployment/docker-images/index.md", "hash": "13519923033068911860"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/Common.md", "hash": "14593471796666110241"}, {"file": "sail-main/python/pysail/data/tpch/queries/q8.sql", "hash": "30177232023284360"}, {"file": "sail-main/scripts/spark-tests/run-server.sh", "hash": "8005911659906440133"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/trace_core.md", "hash": "14024586508680330554"}, {"file": "tests/src/driver/KMDFHAL.c", "hash": "13443276317294174339"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath/x64/Debug/VCTargetsPath.recipe", "hash": "14189227093951361660"}, {"file": "build/ZERO_CHECK.vcxproj", "hash": "13643743675429131443"}, {"file": "build/.cmake/api/v1/reply/target-build_script-Release-251266553d8db8bed6c5.json", "hash": "13997068141168601047"}, {"file": "sail-main/docs/development/recipes/benchmark.md", "hash": "15425257279279313410"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/test/sample_test.md", "hash": "13965977043636224817"}, {"file": "sail-main/crates/sail-spark-connect/src/proto/expression.rs", "hash": "193873332081327491"}, {"file": "tmp_structure/tools/utils/simple_demo_no_emoji.py", "hash": "10037256737053458005"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/optimization/optimization_history.md", "hash": "3898006990554381939"}, {"file": "build/.cmake/api/v1/reply/target-KMDFDriver1-MinSizeRel-faf1ee0ac63ae83b0f5d.json", "hash": "13707405207235672800"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q22.sql", "hash": "7277368029062753040"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceData.md", "hash": "3407684398554571116"}, {"file": "sail-main/crates/sail-python-udf/src/name.rs", "hash": "8429676087048104605"}, {"file": "tmp_structure/tools/utils/run_wpp.ps1", "hash": "16363139438204069178"}, {"file": "tests/unit/test_main.c", "hash": "17856805622170871630"}, {"file": "tmp_structure/tests/unit/prediction_core.c", "hash": "1857614276287916371"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q85.sql", "hash": "5537758792641651555"}, {"file": "tests/unit/gyrohwsim.c", "hash": "9939547639384961965"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_resource_monitor.md", "hash": "13613074638300174529"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "tests/src/utils/device/device_resource.c", "hash": "2198688305377271422"}, {"file": "tests/integration/CMakeLists.txt", "hash": "14784254389476627328"}, {"file": "tests/src/device/Touch.c", "hash": "2902667088611246260"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/mod.rs", "hash": "13857963259409222069"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/feature_engineering.md", "hash": "15700191621865806204"}, {"file": "tests/unit/simd_implementation.h", "hash": "1488013038368583593"}, {"file": "tests/src/utils/ml/prediction_engine.c", "hash": "12387832078328906327"}, {"file": "tmp_structure/tools/fix/fix_all_formatting.ps1", "hash": "7231079512419148032"}, {"file": "sail-main/scripts/spark-tests/count-errors.jq", "hash": "4076003959711906793"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-a20bke/CMakeLists.txt", "hash": "6971656360767436945"}, {"file": "tests/src/utils/device/device_dma.c", "hash": "7826498135188615291"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_pandas_scalar_udf.txt", "hash": "12555587546061198882"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/SecurityManager.md", "hash": "3884452379323183165"}, {"file": "tests/unit/maintenancemanager.c", "hash": "13897789596428706927"}, {"file": "docs/KMDF Driver1/backup/20250506_205501/include/core/types/DriverTypes.md", "hash": "5378835974814819684"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/test/sample_test.md", "hash": "9338080078639493927"}, {"file": "signdrivergui.bat", "hash": "17320788386012970020"}, {"file": "tmp_structure/tests/performance/simd_performance_monitor.c", "hash": "1886857658452867684"}, {"file": "sail-main/python/pysail/tests/spark/function/test_timestamp_add.txt", "hash": "9737282516318102579"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/security/error_prevention.md", "hash": "14119463069258361082"}, {"file": "tmp_structure/tests/unit/kmdfhal.c", "hash": "13443276317294174339"}, {"file": "sail-main/crates/sail-plan/src/function/window.rs", "hash": "13385263896755372928"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q57.sql", "hash": "13293240027990120305"}, {"file": "tmp_structure/tests/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/registry_cache.md", "hash": "10642137261929724889"}, {"file": "docs/KMDF Driver1/tests/src/device/Touch.md", "hash": "5297632455794381816"}, {"file": "tmp_structure/tests/unit/hardware.c", "hash": "2307175637427553353"}, {"file": "sail-main/crates/sail-plan/src/object_store/layers/mod.rs", "hash": "12429460323935996788"}, {"file": "sail-main/crates/sail-execution/src/lib.rs", "hash": "8651681600776283523"}, {"file": "tmp_structure/tests/unit/error_manager.c", "hash": "17523935371877631884"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q15.sql", "hash": "15821637120095199056"}, {"file": "tests/unit/simd_optimizer.c", "hash": "9530376551164781333"}, {"file": "sail-main/python/pysail/tests/spark/test_dml.py", "hash": "13207075934418982141"}, {"file": "docs/project_md/ai_roles.py.teaching.md", "hash": "17146544006075112183"}, {"file": "sail-main/crates/sail-execution/src/driver/server.rs", "hash": "10596598314031458697"}, {"file": "docs/KMDF Driver1/tmp_structure/src/driver/driver_entry.md", "hash": "17773269773045267719"}, {"file": "example_project/module1/data_processor.py", "hash": "5596420748158219335"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/public/core/kmdf_core.md", "hash": "10206234875345744062"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath/x64/Debug/VCTargetsPath.tlog/VCTargetsPath.lastbuildstate", "hash": "17226218581842892828"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/TouchController.md", "hash": "5893240157153592323"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_scalar_udf.txt", "hash": "12882874900887057933"}, {"file": "sail-main/.github/actions/commit-message-match/action.yml", "hash": "3175003500493471771"}, {"file": "docs/KMDF Driver1/tests/src/security/error_manager.md", "hash": "13578911875909071015"}, {"file": "sail-main/crates/sail-plan/src/resolver/plan.rs", "hash": "18136773776977244403"}, {"file": "cmake/driver_tests.cmake", "hash": "18206696834570218411"}, {"file": "tmp_structure/tools/utils/check_dev_env.ps1", "hash": "2785216015131417667"}, {"file": "build/CMakeFiles/3.31.3/VCTargetsPath.vcxproj", "hash": "6389611057365642248"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/device/device_workitem.md", "hash": "3984672476542598313"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger_trace.md", "hash": "11328660283933113950"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/KMDFI2C.md", "hash": "2401467074656005273"}, {"file": "docs/KMDF Driver1/backup/tests/src/device/GyroHwSim.md", "hash": "1957359040119195426"}, {"file": "tests/unit/hardware.c", "hash": "2307175637427553353"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/resource_optimizer_1.md", "hash": "16202012913445874045"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/suites/function.rs", "hash": "1881769099267103032"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdC/CMakeCCompilerId.c", "hash": "10232577801309739533"}, {"file": "tmp_structure/tests/unit/device_manager.c", "hash": "1823201296405534462"}, {"file": "tests/unit/kmdfi2c.c", "hash": "13687270665700358473"}, {"file": "sail-main/docs/development/build/index.md", "hash": "12252635296027530467"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_workitem.md", "hash": "14928245208347617217"}, {"file": "Cmakelists.txt", "hash": "12163760953672255618"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/conditional.rs", "hash": "13563817341857580164"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/data_type.json", "hash": "9457277527036263182"}, {"file": "tmp_structure/tests/unit/queueoperations.c", "hash": "8016806957506324853"}, {"file": "docs/KMDF Driver1/tests/src/utils/CommonUtils.md", "hash": "1783789029930977614"}, {"file": "tests/performance/globalperformancemanager.c", "hash": "13783737954691746004"}, {"file": "docs/project_md/smart_annotator.md", "hash": "10058532935138847851"}, {"file": "tmp_structure/tests/unit/prediction_context.c", "hash": "8771136116938717181"}, {"file": "test_doubao_api.py", "hash": "6072182743631665945"}, {"file": "build/build_script.vcxproj.filters", "hash": "4945757689210894939"}, {"file": "tests/__pycache__/test_output_formatter.cpython-314.pyc", "hash": "12340273229223992362"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/ml/resource_optimizer.md", "hash": "13332246610626941843"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q31.sql", "hash": "13654006755048728685"}, {"file": "tmp_structure/tests/unit/touchdebug.c", "hash": "3448456818563192507"}, {"file": "tests/unit/driverlog.h", "hash": "10155223240312540344"}, {"file": "tests/unit/ml_optimizer.c", "hash": "18371782018547849103"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_test.md", "hash": "8777324941864887741"}, {"file": "tmp_structure/tools/utils/agents/__pycache__/structural_agent.cpython-313.pyc", "hash": "3684322942785208257"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_data_collector.md", "hash": "2064764203318285168"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/feature_engineering.md", "hash": "15700191621865806204"}, {"file": "sail-main/crates/sail-sql-parser/src/ast/keywords.rs", "hash": "5242656039782428664"}, {"file": "docs/KMDF Driver1/tests/src/utils/notification_manager.md", "hash": "774001524088173601"}, {"file": "tmp_structure/tests/unit/touchmanager.h", "hash": "6380166057180092411"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_health_monitor.md", "hash": "12940986551069438218"}, {"file": "tmp_structure/tests/unit/driver_stubs.c", "hash": "770506472769498247"}, {"file": "tmp_structure/tools/fix/fix_header_guards_complete.py", "hash": "15335801640125736637"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/ml/distributed_training.md", "hash": "9032081913617886487"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/power/power_state_manager.md", "hash": "1635534176611093163"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_includes.md", "hash": "12294332545210200785"}, {"file": "tests/unit/registry_manager.c", "hash": "7212363665723535550"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_manager.md", "hash": "8524736742959489422"}, {"file": "docs/KMDF Driver1/tests/src/performance/performance_optimizer.md", "hash": "15191805584477784060"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_implementation.md", "hash": "4972665001930048087"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/core/Common.md", "hash": "14593471796666110241"}, {"file": "src/precomp.c", "hash": "17336256805518999052"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/log/LogTypes.md", "hash": "15844539242907667008"}, {"file": "sail-main/crates/sail-spark-connect/src/lib.rs", "hash": "8805312419729416131"}, {"file": "tmp_structure/tests/unit/touchprocessor.h", "hash": "11281579014250633771"}, {"file": "build/CMakeFiles/22998ac6a11d9d17c8e72de751f3f652/PACKAGE_force.rule", "hash": "1819760324823437288"}, {"file": "tests/src/utils/ml/model_ensemble.c", "hash": "2318274484452547554"}, {"file": "build/CMakeFiles/cmake.check_cache", "hash": "8396945883222416317"}, {"file": "docs/KMDF Driver1/tests/src/utils/trace/logger_core.md", "hash": "9949325019420752238"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/hyperparam_optimizer.md", "hash": "3921821082679517869"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/Queue.md", "hash": "10162557302537918983"}, {"file": "build/CMakeFiles/3.31.3/CompilerIdCXX/Debug/CompilerIdCXX.tlog/CL.command.1.tlog", "hash": "16509436315269999865"}, {"file": "docs/KMDF Driver1/backup/tests/src/memory/DMAManager.md", "hash": "9127980178123817019"}, {"file": "sail-main/crates/sail-gold-test/src/bootstrap/spark/common.rs", "hash": "8564219715230770642"}, {"file": "runtest.bat", "hash": "14285696330554629230"}, {"file": "sail-main/crates/sail-plan/src/extension/function/math/spark_expm1.rs", "hash": "9618575294147399445"}, {"file": "tmp_structure/tests/unit/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "docs/KMDF Driver1/backup/backup/include/utils/device/device_resource.md", "hash": "7227988027766159387"}, {"file": "nx.json", "hash": "14443875240832808589"}, {"file": "docs/KMDF Driver1/tests/src/utils/telemetry_processor.md", "hash": "546651824264917243"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_model.md", "hash": "13492235446936850245"}, {"file": "sail-main/.prettierignore", "hash": "14190845186459127550"}, {"file": "version.h.in", "hash": "13397157829776257934"}, {"file": "tmp_structure/tests/unit/device_dma.c", "hash": "7826498135188615291"}, {"file": "sail-main/crates/sail-common-datafusion/src/formatter.rs", "hash": "11927348016732507746"}, {"file": "docs/KMDF Driver1/backup/backup/src/driver/DriverCore.md", "hash": "9688012867055754603"}, {"file": "tests/src/io/IOOptimizer.c", "hash": "9949378264387320340"}, {"file": "tests/unit/errormanager.h", "hash": "18142214723588776258"}, {"file": "config/models/chatanywhere.json", "hash": "14294184777396776436"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/CommonTypes.md", "hash": "6349873971314642483"}, {"file": "scripts/add_date_to_files.py", "hash": "17867098762369502932"}, {"file": "docs/KMDF Driver1/backup/20250506_205452/tests/src/utils/simd/simd_performance_monitor.md", "hash": "4004437680223110827"}, {"file": ".env", "hash": "103273168256038291"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_queue.md", "hash": "10025303424683255249"}, {"file": "sail-main/python/pysail/tests/spark/test_cast_to_string.txt", "hash": "13295780287304446258"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/performance/monitoring/performance_monitor_manager.md", "hash": "3469875960050886027"}, {"file": "build/INSTALL.vcxproj", "hash": "3686658815850517832"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/power/power_manager.md", "hash": "6803732204210283668"}, {"file": "sail-main/crates/sail-spark-connect/src/session.rs", "hash": "3230756194630799529"}, {"file": "sail-main/python/pysail/data/tpch/queries/q7.sql", "hash": "709757365021169049"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/scheduler_optimizer.md", "hash": "3965136730573772049"}, {"file": "docs/KMDF Driver1/backup/include/utils/power/power_manager.md", "hash": "16271096146856150907"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/TouchAI.md", "hash": "939606902265643493"}, {"file": "build/KMDFDriver1.dir/Debug/vc143.pdb", "hash": "16905423085045812928"}, {"file": "tests/performance/performance_monitor_manager.c", "hash": "9008144578910281466"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/log_manager.md", "hash": "14895641069151458651"}, {"file": "docs/project_md/driver_testing_guide.md", "hash": "14028189855036050473"}, {"file": "tmp_structure/src/driver/driverlog.c", "hash": "16668896646093452722"}, {"file": "sail-main/crates/sail-plan/src/catalog/column.rs", "hash": "14311141747780804567"}, {"file": "tmp_structure/tests/unit/error_prevention.c", "hash": "6292741100613388313"}, {"file": "docs/KMDF Driver1/tests/src/driver/KMDFUSB.md", "hash": "3480578702525265059"}, {"file": "tests/src/utils/analyzer.cpp", "hash": "13899095304220185495"}, {"file": "sail-main/docs/reference/index.md", "hash": "14961896858272742055"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q78.sql", "hash": "13161510014772179895"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_join.json", "hash": "5829936492499048969"}, {"file": "sail-main/crates/sail-python-udf/src/conversion.rs", "hash": "4864853689570977989"}, {"file": "docs/KMDF Driver1/tests/src/driver/driver_manager.md", "hash": "4482833301212000020"}, {"file": "docs/KMDF Driver1/tests/src/security/ErrorPrevention.md", "hash": "731783132979183964"}, {"file": "tests/unit/test_analyzer.c", "hash": "4831041201999830041"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/CMAKE_TRY_COMPILE.sln", "hash": "3144379529874141167"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/logger_core.md", "hash": "7887487036814598355"}, {"file": "tests/performance/touchperformance.h", "hash": "6747028698210755022"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/simd/simd_performance.md", "hash": "1048439768488625304"}, {"file": "sail-main/python/pysail/docs/spark.rst", "hash": "11170402368160044008"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/trace/event_trace.md", "hash": "12299964648108506000"}, {"file": "docs/KMDF Driver1/backup/include/utils/trace/log_manager.md", "hash": "6903768120734889768"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/device/HardwareManager.md", "hash": "6930293510445000701"}, {"file": "docs/KMDF Driver1/tests/src/performance/PerformanceManager.md", "hash": "2592179390791234152"}, {"file": "tmp_structure/tests/unit/test_framework_test.c", "hash": "8515757519434258310"}, {"file": "tests/src/security/error_recovery.c", "hash": "3264091201689155259"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/trace/trace.md", "hash": "938798632008336272"}, {"file": "docs/KMDF Driver1/backup/include/utils/ml/prediction_internal.md", "hash": "3032529117616248408"}, {"file": "docs/KMDF Driver1/tests/src/memory/DMAHandler.md", "hash": "4752476239246391491"}, {"file": "docs/KMDF Driver1/backup/20250506_205500/include/core/error/ErrorTypes.md", "hash": "1324368017208072176"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/telemetry_manager.md", "hash": "247681583355927358"}, {"file": "docs/KMDF Driver1/src_backup_20250503_224937/error/error_handler.md", "hash": "130901189025584605"}, {"file": "env", "hash": "1346634933353421132"}, {"file": "include/device/power/power_manager.h", "hash": "5873311443046064208"}, {"file": "sail-main/crates/sail-plan/src/extension/function/array/spark_sequence.rs", "hash": "10685471411153594808"}, {"file": "sail-main/python/pysail/data/tpcds/queries/q84.sql", "hash": "15037001020568898898"}, {"file": "sail-main/crates/sail-telemetry/Cargo.toml", "hash": "6345586241538806590"}, {"file": "tmp_structure/tools/utils/sign_driver_ps.ps1", "hash": "3768153872774026849"}, {"file": "docs/KMDF Driver1/backup/include/utils/device/device_context.md", "hash": "11117283112430833157"}, {"file": "tests/src/performance/performance_analyzer.c", "hash": "6727025678980931635"}, {"file": "docs/project_md/ai_deployment_setup.md", "hash": "12038449922546488687"}, {"file": "sail-main/crates/sail-plan/src/object_store/registry.rs", "hash": "14359226857422391159"}, {"file": "tmp_structure/tools/utils/core/__pycache__/model_interface.cpython-313.pyc", "hash": "2786865539013472691"}, {"file": "sail-main/crates/sail-plan/src/extension/function/string/spark_encode_decode.rs", "hash": "7976014975631400312"}, {"file": "sail-main/python/pysail/tests/spark/test_shuffle.py", "hash": "18278774426750155448"}, {"file": ".vscode/deployment_config.json", "hash": "1768465812626934688"}, {"file": "sail-main/crates/sail-plan/src/resolver/mod.rs", "hash": "16409364774495233524"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/driver/driver_core.md", "hash": "1723612259800373307"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/interrupt/interrupt_test.md", "hash": "8961378442058678609"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/error_handling.md", "hash": "7149914668807538345"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/optimization/simd_optimization.md", "hash": "3740213908017695348"}, {"file": "tests/src/utils/device/spi_controller.c", "hash": "9569065947463762991"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/driver/device.md", "hash": "15950813879397350842"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/distributed_training.md", "hash": "5668726731270982163"}, {"file": "sail-main/docs/development/index.md", "hash": "2405578521968782420"}, {"file": "build/.cmake/api/v1/reply/target-ZERO_CHECK-MinSizeRel-d96e50ebfb5facdebe2d.json", "hash": "1822656417171922218"}, {"file": "sail-main/docs/.vitepress/config.mts", "hash": "990051612790900936"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/performance_monitor_manager.md", "hash": "16650903172073960168"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/plan_hint.json", "hash": "8516593847769554996"}, {"file": "build/.cmake/api/v1/reply/target-build_script-MinSizeRel-251266553d8db8bed6c5.json", "hash": "13997068141168601047"}, {"file": "docs/KMDF Driver1/backup/include/core/device/DeviceStateTypes.md", "hash": "6696260495724237518"}, {"file": "tests/src/memory/MemoryCompression.c", "hash": "10542558592008849227"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceMetricsImpl.md", "hash": "6718369574091188526"}, {"file": "tests/src/driver/KMDFLogger.c", "hash": "11681378835085244101"}, {"file": "tests/unit/device_resource_monitor.c", "hash": "15417105486048566191"}, {"file": "sail-main/python/pysail/tests/spark/test_cast_from_numeric.txt", "hash": "3653734595087927888"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/module_manager.md", "hash": "11840949454089485922"}, {"file": "docs/KMDF Driver1/backup/backup/include/hardware/kmdf_hal.md", "hash": "7178785057074432366"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/function/agg.json", "hash": "13320905018597610954"}, {"file": "sail-main/crates/sail-execution/src/stream/reader.rs", "hash": "9922515862060350694"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_timer.md", "hash": "13275692677651774437"}, {"file": "docs/KMDF Driver1/backup/tests/src/performance/PerformanceMetricsImpl.md", "hash": "6718369574091188526"}, {"file": "docs/structured_docs/tutorials/kmdf_入门指南.md", "hash": "11948446666877054333"}, {"file": "docs/module_dependencies_20240101.md", "hash": "18292169858001390161"}, {"file": "tests/unit/configmanager.c", "hash": "12407172891497760359"}, {"file": "tests/unit/event_trace.c", "hash": "10188444021102792748"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_core.md", "hash": "1723612259800373307"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/ml/advanced_models.md", "hash": "16221757585958477850"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/ml/simd_optimizer.md", "hash": "16564215083805726102"}, {"file": "docs/KMDF Driver1/backup/tests/src/utils/device/device_dma.md", "hash": "15203853900872757579"}, {"file": "tmp_structure/include/hardware/kmdf_hal.h", "hash": "2499818489678582216"}, {"file": "source_template.c", "hash": "13440679973508946342"}, {"file": "tests/src/utils/simd/simd_ops.c", "hash": "4755770406672099314"}, {"file": "tests/unit/power_test.c", "hash": "9102832245856542684"}, {"file": "gitmessage", "hash": "8748897939388348871"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/kmdf_types.md", "hash": "11820292990252072646"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/PerformanceExport.md", "hash": "17280862790576496424"}, {"file": "sail-main/crates/sail-plan/src/function/scalar/csv.rs", "hash": "1817253322945617075"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/performance/monitor.md", "hash": "12333284298038535705"}, {"file": "docs/project_md/idear1.txt", "hash": "9456835543446444211"}, {"file": "docs/KMDF Driver1/backup/tests/src/driver/driver_manager.md", "hash": "12926653278278343738"}, {"file": "docs/KMDF Driver1/backup/20250506_205453/tests/src/utils/trace/logger_trace.md", "hash": "18408039824941962990"}, {"file": "tests/unit/device_manager1.c", "hash": "9178587879886038827"}, {"file": "docs/KMDF Driver1/include_backup_20250503_224915/utils/device/device_control.md", "hash": "14448704410520776442"}, {"file": "templates/expert_prompt.json", "hash": "15278861292494340274"}, {"file": "tmp_structure/tools/fix/verify_fixes.ps1", "hash": "10675361795171464049"}, {"file": "build/CMakeFiles/CMakeScratch/TryCompile-mbn200/cmTC_58f84.dir/Debug/cmTC_58f84.tlog/link.secondary.1.tlog", "hash": "9385706112243369218"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/types/BaseTypes.md", "hash": "13028179470507206042"}, {"file": "sail-main/crates/sail-spark-connect/tests/gold_data/plan/error_select.json", "hash": "12093082772345206985"}, {"file": "tests/unit/driver_stubs.c", "hash": "770506472769498247"}, {"file": "tests/src/security/error_manager.c", "hash": "11703476280763806582"}, {"file": "tmp_structure/tests/unit/optimization_evaluator.c", "hash": "15637208431290006549"}, {"file": "docs/KMDF Driver1/tests/src/utils/device/device_queue.md", "hash": "4268410109923421812"}, {"file": "sail-main/crates/sail-plan/src/extension/function/table_input.rs", "hash": "10205454743434931223"}, {"file": "sail-main/python/pysail/tests/spark/udf/test_register_scalar_udf.txt", "hash": "15659251811844795434"}, {"file": "tmp_structure/driver_analysis.py", "hash": "6874825107015864783"}, {"file": "docs/KMDF Driver1/backup/backup/include/core/device/DeviceTypes.md", "hash": "1883459930781203031"}, {"file": "docs/KMDF Driver1/backup/tests/src/security/error_handling.md", "hash": "8736094554619853824"}, {"file": "docs/KMDF Driver1/tmp_structure/include/device/device_stat_types.md", "hash": "15997768796965679315"}, {"file": "docs/KMDF Driver1/backup/backup/include/driver/driver_types.md", "hash": "6648445345879484221"}, {"file": "docs/KMDF Driver1/backup/backup/tests/src/utils/config_manager.md", "hash": "14863063657951608679"}, {"file": "tmp_structure/tests/performance/globalperformancemanager.c", "hash": "13783737954691746004"}, {"file": "sail-main/crates/sail-plan/src/extension/logical/sort.rs", "hash": "4889695340310084615"}], "projectFileMap": {}}}