{"name": "火山方舟AI", "provider": "huo<PERSON>", "api_key": "58775fd4-eff6-490c-919e-2f01836fd314", "access_key_id": "AKLTNDY3N2U4NTZiMmIxNGQ0ZGIwMTAwNjk0MWQzZjhmMmM", "secret_access_key": "TkdSa05UVTRPVFZrTUdKbE5HRXpNR0UyTldFMU9XSmxaVGMwTWprME5XRQ==", "base_url": "https://open.volcengineapi.com/api/v1/ml_platform/chatglm/openapi/deployments/moonshot/chat/completions", "api_base": "https://api.moonshot.cn/v1/chat/completions", "models": [{"id": "moonshot-v1-8k", "name": "moonshot 8K", "max_tokens": 8192}, {"id": "moonshot-v1-32k", "name": "moonshot 32K", "max_tokens": 32768, "default": true}, {"id": "deepseek-v1", "name": "DeepSeek", "max_tokens": 8192}, {"id": "doubao-1.5-ui-tars-250328", "name": "doubao 1.5 ui tars", "max_tokens": 32768}], "default_model": "doubao-1.5-ui-tars-250328", "max_tokens": 8000, "temperature": 0.7, "timeout": 120, "retry_count": 3, "retry_delay": 5, "system_prompt": "你是一个火山方舟平台上的专业代码分析助手，特别擅长驱动程序的解析和注释。请为Windows驱动程序提供清晰、精确的中文注释，重点解释复杂的逻辑和关键的安全性考量。", "authentication": {"bearer_token": {"method": "Header", "header": "Authorization: Bearer {api_key}"}, "ak_sk": {"method": "Signature", "description": "生成复杂签名并在请求头中添加", "headers": {"Authorization": "AWS4-HMAC-SHA256 Credential={access_key_id}/{date}/volcengine1/request, SignedHeaders=host;x-amz-date, Signature={signature}", "X-Amz-Date": "{date_time}"}}}, "features": {"cloud_assistant": {"commands": {"create": "CreateCommand", "modify": "ModifyCommand", "delete": "DeleteCommand", "describe": "DescribeCommands", "invoke": "InvokeCommand", "run": "RunCommand"}, "invocation": {"delete": "DeleteInvocation", "describe": "DescribeInvocations", "describe_instances": "DescribeInvocationInstances", "describe_results": "DescribeInvocationResults", "stop": "StopInvocation"}, "client": {"describe_status": "DescribeCloudAssistantStatus", "install": "InstallCloudAssistant", "uninstall": "UninstallCloudAssistants", "upgrade": "UpgradeCloudAssistants"}}, "hpc_cluster": {"describe": "DescribeHpcClusters", "create": "CreateHpcCluster", "delete": "DeleteHpcCluster"}, "instance": {"start": "StartInstance", "stop": "StopInstance", "reboot": "RebootInstance", "delete": "DeleteInstance", "describe": "DescribeInstances", "modify_spec": "ModifyInstanceSpec", "modify_charge_type": "ModifyInstanceChargeType", "describe_resource": "DescribeAvailableResource", "renew": "RenewInstance", "describe_user_data": "DescribeUserData", "describe_terminal_url": "DescribeInstanceECSTerminalUrl", "describe_vnc_url": "DescribeInstanceVncUrl", "describe_iam_roles": "DescribeInstancesIamRoles"}}, "api_versions": {"cloud_assistant": "2021-01-01", "hpc_cluster": "2021-01-01", "instance": "2021-01-01"}, "request_format": {"example": {"chat_completion": {"Action": "RunCommand", "Version": "2021-01-01", "model": "moonshot-v1-32k", "messages": [{"role": "system", "content": "你是一个专业的代码分析助手。"}, {"role": "user", "content": "请分析这段驱动代码"}], "temperature": 0.7, "top_p": 0.9, "stream": false, "max_tokens": 4096}}}, "response_format": {"example": {"id": "chatcmpl-abc123", "object": "chat.completion", "created": 1677858242, "model": "moonshot-v1-32k", "choices": [{"message": {"role": "assistant", "content": "这是对驱动代码的分析..."}, "finish_reason": "stop", "index": 0}], "usage": {"prompt_tokens": 150, "completion_tokens": 500, "total_tokens": 650}}}}