<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/devices/i2c_device.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('i2c__device_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">i2c_device.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="kmdf__i2c_8h_source.html">../bus/kmdf_i2c.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for i2c_device.h:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2devices_2i2c__device_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2i2c__device_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2devices_2i2c__device_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" title=" " alt="" coords="89,336,154,363"/>
<area shape="poly" title=" " alt="" coords="83,50,66,94,51,155,46,188,46,222,50,255,60,287,73,308,91,325,87,328,69,311,55,289,45,256,40,222,41,188,45,154,61,93,78,48"/>
<area shape="rect" title=" " alt="" coords="70,253,123,280"/>
<area shape="poly" title=" " alt="" coords="93,49,98,238,93,238,87,49"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="132,96,255,123"/>
<area shape="poly" title=" " alt="" coords="118,46,167,84,164,88,115,51"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="127,171,273,197"/>
<area shape="poly" title=" " alt="" coords="197,123,200,155,195,155,192,123"/>
<area shape="poly" title=" " alt="" coords="186,200,126,246,122,242,182,196"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="147,245,277,288"/>
<area shape="poly" title=" " alt="" coords="205,197,210,230,204,231,199,198"/>
<area shape="poly" title=" " alt="" coords="191,291,149,328,145,324,187,287"/>
</map>
</div>
</div>
<p><a href="i2c__device_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FI2C_5FSTATISTICS_5Fstruct_5F_5FI2C_5F_5FSTATISTICS" id="r__5FI2C_5FSTATISTICS_5Fstruct_5F_5FI2C_5F_5FSTATISTICS"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__I2C__STATISTICS">_I2C_STATISTICS</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a64738b9f6c1ba2f1f919ef7a61ad1e35" id="r_a64738b9f6c1ba2f1f919ef7a61ad1e35"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a64738b9f6c1ba2f1f919ef7a61ad1e35">I2C_ADDRESS_10BIT</a>&#160;&#160;&#160;1</td></tr>
<tr class="memitem:ab65b9830c26b77346e0f420c643d63b9" id="r_ab65b9830c26b77346e0f420c643d63b9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab65b9830c26b77346e0f420c643d63b9">I2C_ADDRESS_7BIT</a>&#160;&#160;&#160;0</td></tr>
<tr class="memitem:ad6922f686b3fc13f8365467975aba1d7" id="r_ad6922f686b3fc13f8365467975aba1d7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad6922f686b3fc13f8365467975aba1d7">I2C_TRANSFER_READ</a>&#160;&#160;&#160;0x00000001</td></tr>
<tr class="memitem:a23164ebc8fc22800438176c588caa941" id="r_a23164ebc8fc22800438176c588caa941"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a23164ebc8fc22800438176c588caa941">IOCTL_I2C_GET_STATISTICS</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a69ebe19cf050058019d84f005052cc00" id="r_a69ebe19cf050058019d84f005052cc00"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a69ebe19cf050058019d84f005052cc00">IOCTL_I2C_RESET</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a9c3881592ae1c10fbd5ba1b9ae7e85ec" id="r_a9c3881592ae1c10fbd5ba1b9ae7e85ec"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9c3881592ae1c10fbd5ba1b9ae7e85ec">IOCTL_I2C_SET_BUS_SPEED</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a15204e5c2582622fc3ef40f01fe93322" id="r_a15204e5c2582622fc3ef40f01fe93322"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a15204e5c2582622fc3ef40f01fe93322">IOCTL_I2C_TRANSFER</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a478905e3b3f7f700dd9e0975f42fe1a3" id="r_a478905e3b3f7f700dd9e0975f42fe1a3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a478905e3b3f7f700dd9e0975f42fe1a3">IOCTL_I2C_TRANSFER_SEQUENCE</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a7d8f35e89fbf4832545b3dfab3bd1ae1" id="r_a7d8f35e89fbf4832545b3dfab3bd1ae1"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__STATISTICS">_I2C_STATISTICS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7d8f35e89fbf4832545b3dfab3bd1ae1">I2C_STATISTICS</a></td></tr>
<tr class="memitem:aeb652cfe1149dff5ee42abab74d96813" id="r_aeb652cfe1149dff5ee42abab74d96813"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__STATISTICS">_I2C_STATISTICS</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a5da67a960d3cf99caa6874438a84629b" id="r_a5da67a960d3cf99caa6874438a84629b"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5da67a960d3cf99caa6874438a84629b">I2cDeviceCleanup</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a709aca0009ccfb39adebbdd9ce97e252" id="r_a709aca0009ccfb39adebbdd9ce97e252"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a709aca0009ccfb39adebbdd9ce97e252">I2cDeviceGetStatistics</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_ <a class="el" href="#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a> Statistics)</td></tr>
<tr class="memitem:ab0c3b778b5a363d418c3d768cdb1e2d4" id="r_ab0c3b778b5a363d418c3d768cdb1e2d4"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab0c3b778b5a363d418c3d768cdb1e2d4">I2cDeviceInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="el" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>)</td></tr>
<tr class="memitem:a6576f1e3485d12c22c444244044c1d30" id="r_a6576f1e3485d12c22c444244044c1d30"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6576f1e3485d12c22c444244044c1d30">I2cDeviceRead</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR <a class="el" href="#a862821561008426245a34e458d02a093">DeviceAddress</a>, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</td></tr>
<tr class="memitem:ad84f26684684313ff193803d1d9c7c32" id="r_ad84f26684684313ff193803d1d9c7c32"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad84f26684684313ff193803d1d9c7c32">I2cDeviceTransfer</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_reads_(TransferCount) <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> Transfers, _In_ ULONG TransferCount)</td></tr>
<tr class="memitem:a580f2434082501937a3d8bc4d5591866" id="r_a580f2434082501937a3d8bc4d5591866"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580f2434082501937a3d8bc4d5591866">I2cDeviceWrite</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR <a class="el" href="#a862821561008426245a34e458d02a093">DeviceAddress</a>, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a40f62c35b73ac8d898f982eba3295ed5" id="r_a40f62c35b73ac8d898f982eba3295ed5"><td class="memItemLeft" align="right" valign="top">PVOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a40f62c35b73ac8d898f982eba3295ed5">Data</a></td></tr>
<tr class="memitem:a2c5c92bbcc636db6bd909df12a1802da" id="r_a2c5c92bbcc636db6bd909df12a1802da"><td class="memItemLeft" align="right" valign="top">UCHAR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2c5c92bbcc636db6bd909df12a1802da">DataAddress</a></td></tr>
<tr class="memitem:a306a7e4c1d020919c3002e3b8bd37c27" id="r_a306a7e4c1d020919c3002e3b8bd37c27"><td class="memItemLeft" align="right" valign="top">ULONG&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a306a7e4c1d020919c3002e3b8bd37c27">DelayInMicroseconds</a></td></tr>
<tr class="memitem:a862821561008426245a34e458d02a093" id="r_a862821561008426245a34e458d02a093"><td class="memItemLeft" align="right" valign="top">UCHAR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a862821561008426245a34e458d02a093">DeviceAddress</a></td></tr>
<tr class="memitem:a385668d045c2844f0f13d8613b3e0459" id="r_a385668d045c2844f0f13d8613b3e0459"><td class="memItemLeft" align="right" valign="top">ULONG&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a385668d045c2844f0f13d8613b3e0459">Flags</a></td></tr>
<tr class="memitem:af8486d1fd9d3904ca8eb4408ec81d9c4" id="r_af8486d1fd9d3904ca8eb4408ec81d9c4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af8486d1fd9d3904ca8eb4408ec81d9c4">I2C_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:a95996125e1f73ecc8e0ecf222dd14372" id="r_a95996125e1f73ecc8e0ecf222dd14372"><td class="memItemLeft" align="right" valign="top">*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a95996125e1f73ecc8e0ecf222dd14372">PI2C_TRANSFER_PACKET</a></td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__I2C__STATISTICS" id="struct__I2C__STATISTICS"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__I2C__STATISTICS">&#9670;&#160;</a></span>_I2C_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _I2C_STATISTICS</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ac6637ce7aa131fb27cf2b60ce31f35db" name="ac6637ce7aa131fb27cf2b60ce31f35db"></a>ULONG</td>
<td class="fieldname">
BusClockFrequency</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a0d581d279416ac434f26896560418188" name="a0d581d279416ac434f26896560418188"></a>ULONG</td>
<td class="fieldname">
BusNumber</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a1ac16411f8ad21fe7d60868251438978" name="a1ac16411f8ad21fe7d60868251438978"></a>BOOLEAN</td>
<td class="fieldname">
DeviceInitialized</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a717ee36112176580f0ec5152cd12e0a2" name="a717ee36112176580f0ec5152cd12e0a2"></a>ULONG</td>
<td class="fieldname">
ErrorCount</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="add2f1e1c89d79eee2641ef44c12f29d3" name="add2f1e1c89d79eee2641ef44c12f29d3"></a>ULONG</td>
<td class="fieldname">
TransactionCount</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="a64738b9f6c1ba2f1f919ef7a61ad1e35" name="a64738b9f6c1ba2f1f919ef7a61ad1e35"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64738b9f6c1ba2f1f919ef7a61ad1e35">&#9670;&#160;</a></span>I2C_ADDRESS_10BIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define I2C_ADDRESS_10BIT&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab65b9830c26b77346e0f420c643d63b9" name="ab65b9830c26b77346e0f420c643d63b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab65b9830c26b77346e0f420c643d63b9">&#9670;&#160;</a></span>I2C_ADDRESS_7BIT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define I2C_ADDRESS_7BIT&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad6922f686b3fc13f8365467975aba1d7" name="ad6922f686b3fc13f8365467975aba1d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6922f686b3fc13f8365467975aba1d7">&#9670;&#160;</a></span>I2C_TRANSFER_READ</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define I2C_TRANSFER_READ&#160;&#160;&#160;0x00000001</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a23164ebc8fc22800438176c588caa941" name="a23164ebc8fc22800438176c588caa941"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a23164ebc8fc22800438176c588caa941">&#9670;&#160;</a></span>IOCTL_I2C_GET_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_I2C_GET_STATISTICS&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a69ebe19cf050058019d84f005052cc00" name="a69ebe19cf050058019d84f005052cc00"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69ebe19cf050058019d84f005052cc00">&#9670;&#160;</a></span>IOCTL_I2C_RESET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_I2C_RESET&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9c3881592ae1c10fbd5ba1b9ae7e85ec" name="a9c3881592ae1c10fbd5ba1b9ae7e85ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9c3881592ae1c10fbd5ba1b9ae7e85ec">&#9670;&#160;</a></span>IOCTL_I2C_SET_BUS_SPEED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_I2C_SET_BUS_SPEED&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a15204e5c2582622fc3ef40f01fe93322" name="a15204e5c2582622fc3ef40f01fe93322"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a15204e5c2582622fc3ef40f01fe93322">&#9670;&#160;</a></span>IOCTL_I2C_TRANSFER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_I2C_TRANSFER&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a478905e3b3f7f700dd9e0975f42fe1a3" name="a478905e3b3f7f700dd9e0975f42fe1a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a478905e3b3f7f700dd9e0975f42fe1a3">&#9670;&#160;</a></span>IOCTL_I2C_TRANSFER_SEQUENCE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_I2C_TRANSFER_SEQUENCE&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_I2C_BASE + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a7d8f35e89fbf4832545b3dfab3bd1ae1" name="a7d8f35e89fbf4832545b3dfab3bd1ae1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d8f35e89fbf4832545b3dfab3bd1ae1">&#9670;&#160;</a></span>I2C_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__STATISTICS">_I2C_STATISTICS</a> <a class="el" href="#a7d8f35e89fbf4832545b3dfab3bd1ae1">I2C_STATISTICS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aeb652cfe1149dff5ee42abab74d96813" name="aeb652cfe1149dff5ee42abab74d96813"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb652cfe1149dff5ee42abab74d96813">&#9670;&#160;</a></span>PI2C_STATISTICS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__STATISTICS">_I2C_STATISTICS</a> * <a class="el" href="#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a5da67a960d3cf99caa6874438a84629b" name="a5da67a960d3cf99caa6874438a84629b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5da67a960d3cf99caa6874438a84629b">&#9670;&#160;</a></span>I2cDeviceCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID I2cDeviceCleanup </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceCleanup - 娓呯悊I2C璁惧璧勬簮</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_a5da67a960d3cf99caa6874438a84629b_cgraph.png" border="0" usemap="#ai2c__device_8h_a5da67a960d3cf99caa6874438a84629b_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_a5da67a960d3cf99caa6874438a84629b_cgraph" id="ai2c__device_8h_a5da67a960d3cf99caa6874438a84629b_cgraph">
<area shape="rect" title=" " alt="" coords="5,80,134,107"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="187,29,292,56"/>
<area shape="poly" title=" " alt="" coords="116,77,177,58,179,63,118,82"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="182,80,298,107"/>
<area shape="poly" title=" " alt="" coords="134,91,166,91,166,96,134,96"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="205,131,275,157"/>
<area shape="poly" title=" " alt="" coords="118,105,191,127,189,132,116,110"/>
<area shape="poly" title=" " alt="" coords="210,30,205,21,209,11,221,5,240,3,260,5,271,12,269,16,259,10,240,8,222,10,213,15,211,20,215,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="346,105,474,132"/>
<area shape="poly" title=" " alt="" coords="275,136,330,128,331,133,276,141"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="362,156,458,183"/>
<area shape="poly" title=" " alt="" coords="276,147,348,157,347,163,275,152"/>
</map>
</div>

</div>
</div>
<a id="a709aca0009ccfb39adebbdd9ce97e252" name="a709aca0009ccfb39adebbdd9ce97e252"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a709aca0009ccfb39adebbdd9ce97e252">&#9670;&#160;</a></span>I2cDeviceGetStatistics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceGetStatistics </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ <a class="el" href="#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a></td>          <td class="paramname"><span class="paramname"><em>Statistics</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceGetStatistics - 鑾峰彇I2C璁惧缁熻淇℃伅</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Statistics</td><td>缁熻淇℃伅缁撴瀯</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_a709aca0009ccfb39adebbdd9ce97e252_cgraph.png" border="0" usemap="#ai2c__device_8h_a709aca0009ccfb39adebbdd9ce97e252_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_a709aca0009ccfb39adebbdd9ce97e252_cgraph" id="ai2c__device_8h_a709aca0009ccfb39adebbdd9ce97e252_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,162,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="210,29,315,56"/>
<area shape="poly" title=" " alt="" coords="162,54,194,50,195,55,162,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="227,80,298,107"/>
<area shape="poly" title=" " alt="" coords="162,76,212,84,211,89,162,82"/>
<area shape="poly" title=" " alt="" coords="234,30,229,21,232,11,244,5,262,3,282,5,293,12,290,16,280,10,262,8,245,10,236,15,234,20,238,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="363,55,491,81"/>
<area shape="poly" title=" " alt="" coords="298,85,347,78,348,83,298,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="380,105,475,132"/>
<area shape="poly" title=" " alt="" coords="298,96,365,106,364,112,298,101"/>
</map>
</div>

</div>
</div>
<a id="ab0c3b778b5a363d418c3d768cdb1e2d4" name="ab0c3b778b5a363d418c3d768cdb1e2d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0c3b778b5a363d418c3d768cdb1e2d4">&#9670;&#160;</a></span>I2cDeviceInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>I2cConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceInitialize - 鍒濆鍖朓2C璁惧</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">I2cConfig</td><td>I2C閰嶇疆</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph.png" border="0" usemap="#ai2c__device_8h_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph" id="ai2c__device_8h_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,134,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="182,29,287,56"/>
<area shape="poly" title=" " alt="" coords="134,55,166,50,167,56,134,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="199,80,270,107"/>
<area shape="poly" title=" " alt="" coords="134,75,184,83,183,88,134,81"/>
<area shape="poly" title=" " alt="" coords="206,30,201,21,204,11,216,5,234,3,254,5,265,12,262,16,252,10,234,8,217,10,208,15,206,20,210,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="335,55,463,81"/>
<area shape="poly" title=" " alt="" coords="270,85,319,78,320,83,270,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,447,132"/>
<area shape="poly" title=" " alt="" coords="270,96,337,106,336,112,270,101"/>
</map>
</div>

</div>
</div>
<a id="a6576f1e3485d12c22c444244044c1d30" name="a6576f1e3485d12c22c444244044c1d30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6576f1e3485d12c22c444244044c1d30">&#9670;&#160;</a></span>I2cDeviceRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceRead </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>DeviceAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesRead</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceRead - 浠嶪2C璁惧璇诲彇鏁版嵁</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">DeviceAddress</td><td>I2C璁惧鍦板潃 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>瀵勫瓨鍣ㄥ湴鍧€ </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Buffer</td><td>鏁版嵁缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>缂撳啿鍖洪暱搴? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesRead</td><td>瀹為檯璇诲彇鐨勫瓧鑺傛暟</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_a6576f1e3485d12c22c444244044c1d30_cgraph.png" border="0" usemap="#ai2c__device_8h_a6576f1e3485d12c22c444244044c1d30_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_a6576f1e3485d12c22c444244044c1d30_cgraph" id="ai2c__device_8h_a6576f1e3485d12c22c444244044c1d30_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,117,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="165,29,270,56"/>
<area shape="poly" title=" " alt="" coords="117,56,149,51,150,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="182,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,167,83,166,88,117,80"/>
<area shape="poly" title=" " alt="" coords="190,30,185,21,189,11,200,5,217,3,236,5,246,12,243,16,234,10,217,8,201,10,193,15,191,20,195,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="318,55,446,81"/>
<area shape="poly" title=" " alt="" coords="253,85,302,78,303,83,253,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,430,132"/>
<area shape="poly" title=" " alt="" coords="253,96,320,106,319,112,253,101"/>
</map>
</div>

</div>
</div>
<a id="ad84f26684684313ff193803d1d9c7c32" name="ad84f26684684313ff193803d1d9c7c32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad84f26684684313ff193803d1d9c7c32">&#9670;&#160;</a></span>I2cDeviceTransfer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceTransfer </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_(TransferCount) <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>Transfers</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TransferCount</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceTransfer - 鎵ц澶嶆潅I2C浼犺緭</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Transfers</td><td>浼犺緭鏁版嵁鍖呮暟缁? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TransferCount</td><td>浼犺緭鏁版嵁鍖呮暟閲? * </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_ad84f26684684313ff193803d1d9c7c32_cgraph.png" border="0" usemap="#ai2c__device_8h_ad84f26684684313ff193803d1d9c7c32_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_ad84f26684684313ff193803d1d9c7c32_cgraph" id="ai2c__device_8h_ad84f26684684313ff193803d1d9c7c32_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,134,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="182,29,287,56"/>
<area shape="poly" title=" " alt="" coords="134,55,166,50,167,56,134,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="199,80,270,107"/>
<area shape="poly" title=" " alt="" coords="134,75,184,83,183,88,134,81"/>
<area shape="poly" title=" " alt="" coords="206,30,201,21,204,11,216,5,234,3,254,5,265,12,262,16,252,10,234,8,217,10,208,15,206,20,210,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="335,55,463,81"/>
<area shape="poly" title=" " alt="" coords="270,85,319,78,320,83,270,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,447,132"/>
<area shape="poly" title=" " alt="" coords="270,96,337,106,336,112,270,101"/>
</map>
</div>

</div>
</div>
<a id="a580f2434082501937a3d8bc4d5591866" name="a580f2434082501937a3d8bc4d5591866"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580f2434082501937a3d8bc4d5591866">&#9670;&#160;</a></span>I2cDeviceWrite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceWrite </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>DeviceAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesWritten</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceWrite - 鍚慖2C璁惧鍐欏叆鏁版嵁</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">DeviceAddress</td><td>I2C璁惧鍦板潃 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>瀵勫瓨鍣ㄥ湴鍧€ </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Buffer</td><td>鏁版嵁缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>缂撳啿鍖洪暱搴? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesWritten</td><td>瀹為檯鍐欏叆鐨勫瓧鑺傛暟</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8h_a580f2434082501937a3d8bc4d5591866_cgraph.png" border="0" usemap="#ai2c__device_8h_a580f2434082501937a3d8bc4d5591866_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8h_a580f2434082501937a3d8bc4d5591866_cgraph" id="ai2c__device_8h_a580f2434082501937a3d8bc4d5591866_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,116,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="164,29,269,56"/>
<area shape="poly" title=" " alt="" coords="116,56,148,51,149,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="181,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,166,83,165,88,116,80"/>
<area shape="poly" title=" " alt="" coords="189,30,185,21,188,11,199,5,216,3,235,5,245,12,242,16,233,10,216,8,200,10,192,15,190,20,194,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="317,55,445,81"/>
<area shape="poly" title=" " alt="" coords="252,85,301,78,302,83,252,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,429,132"/>
<area shape="poly" title=" " alt="" coords="252,96,319,106,318,112,252,101"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="a40f62c35b73ac8d898f982eba3295ed5" name="a40f62c35b73ac8d898f982eba3295ed5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40f62c35b73ac8d898f982eba3295ed5">&#9670;&#160;</a></span>Data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PVOID Data</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2c5c92bbcc636db6bd909df12a1802da" name="a2c5c92bbcc636db6bd909df12a1802da"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2c5c92bbcc636db6bd909df12a1802da">&#9670;&#160;</a></span>DataAddress</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">UCHAR DataAddress</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a306a7e4c1d020919c3002e3b8bd37c27" name="a306a7e4c1d020919c3002e3b8bd37c27"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a306a7e4c1d020919c3002e3b8bd37c27">&#9670;&#160;</a></span>DelayInMicroseconds</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ULONG DelayInMicroseconds</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a862821561008426245a34e458d02a093" name="a862821561008426245a34e458d02a093"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a862821561008426245a34e458d02a093">&#9670;&#160;</a></span>DeviceAddress</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">UCHAR DeviceAddress</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a385668d045c2844f0f13d8613b3e0459" name="a385668d045c2844f0f13d8613b3e0459"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a385668d045c2844f0f13d8613b3e0459">&#9670;&#160;</a></span>Flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ULONG Flags</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af8486d1fd9d3904ca8eb4408ec81d9c4" name="af8486d1fd9d3904ca8eb4408ec81d9c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8486d1fd9d3904ca8eb4408ec81d9c4">&#9670;&#160;</a></span>I2C_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a95996125e1f73ecc8e0ecf222dd14372" name="a95996125e1f73ecc8e0ecf222dd14372"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95996125e1f73ecc8e0ecf222dd14372">&#9670;&#160;</a></span>PI2C_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">* <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_f51f2e86ea53a1a257ee2ea690474c95.html">devices</a></li><li class="navelem"><a href="i2c__device_8h.html">i2c_device.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
