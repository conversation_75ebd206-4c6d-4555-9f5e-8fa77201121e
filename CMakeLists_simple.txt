# 简化的CMakeLists.txt用于测试KMDF驱动构建
cmake_minimum_required(VERSION 3.15)

# 项目信息
project(KMDFDriver1 VERSION 1.0.0 LANGUAGES C CXX)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the build type." FORCE)
endif()

# 设置WDK路径
set(WDK_ROOT "C:/Program Files (x86)/Windows Kits/10")
set(WDK_VERSION "10.0.26100.0")
set(WDF_VERSION "1.35")

# 验证WDK路径
if(NOT EXISTS "${WDK_ROOT}/Include/${WDK_VERSION}")
    message(FATAL_ERROR "WDK not found at: ${WDK_ROOT}")
endif()

# 设置包含目录
set(WDK_INCLUDE_DIRS
    "${WDK_ROOT}/Include/${WDK_VERSION}/km"
    "${WDK_ROOT}/Include/${WDK_VERSION}/shared"
    "${WDK_ROOT}/Include/${WDK_VERSION}/um"
    "${WDK_ROOT}/Include/wdf/kmdf/${WDF_VERSION}"
)

# 设置库目录
set(WDK_LIB_DIRS
    "${WDK_ROOT}/Lib/${WDK_VERSION}/km/x64"
    "${WDK_ROOT}/Lib/wdf/kmdf/x64/${WDF_VERSION}"
)

# 源文件
set(DRIVER_SOURCES
    src/precomp.c
    src/driver_main.c
    src/core/log/driver_log.c
    src/core/driver/driver_core.c
    src/core/device/device_manager.c
)

# 创建驱动目标
add_library(${PROJECT_NAME} SHARED ${DRIVER_SOURCES})

# 设置包含目录
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
    ${WDK_INCLUDE_DIRS}
)

# 设置编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    _WIN64
    _AMD64_
    _KERNEL_MODE
    WIN32_LEAN_AND_MEAN
    NTDDI_VERSION=0x0A00000A
    WINVER=0x0A00
    _WIN32_WINNT=0x0A00
)

# 设置编译选项
target_compile_options(${PROJECT_NAME} PRIVATE
    /W4
    /WX
    /kernel
    /GS
)

# 设置链接目录
target_link_directories(${PROJECT_NAME} PRIVATE ${WDK_LIB_DIRS})

# 设置链接库
target_link_libraries(${PROJECT_NAME} PRIVATE
    ntoskrnl.lib
    hal.lib
    wdfldr.lib
    wdfdriverentry.lib
)

# 设置链接选项
set_target_properties(${PROJECT_NAME} PROPERTIES
    LINK_FLAGS "/DRIVER /SUBSYSTEM:NATIVE /ENTRY:FxDriverEntry"
    SUFFIX ".sys"
)
