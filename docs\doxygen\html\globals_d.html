<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li class="current"><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_d.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>Data&#160;:&#160;<a class="el" href="i2c__device_8c.html#a7cd334185ddc76afeacf2cd57615dc81">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a40f62c35b73ac8d898f982eba3295ed5">i2c_device.h</a></li>
<li>DataAddress&#160;:&#160;<a class="el" href="i2c__device_8c.html#a63268b1e9e5ee12309a44d8d6c9fc652">i2c_device.c</a>, <a class="el" href="i2c__device_8h.html#a2c5c92bbcc636db6bd909df12a1802da">i2c_device.h</a></li>
<li>DataLength&#160;:&#160;<a class="el" href="i2c__device_8c.html#adb5ea4ad84a87209e6f98b3e012adbbf">i2c_device.c</a></li>
<li>DEBUG_PRINT&#160;:&#160;<a class="el" href="precomp_8h.html#a7ba5cb5a943f312e7b50de9cd8ffaf37">precomp.h</a></li>
<li>DelayInMicroseconds&#160;:&#160;<a class="el" href="i2c__device_8c.html#af9a881dabb7ea1e15ee2808cca09fd6a">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#af9a881dabb7ea1e15ee2808cca09fd6a">spi_device.c</a>, <a class="el" href="i2c__device_8h.html#a306a7e4c1d020919c3002e3b8bd37c27">i2c_device.h</a></li>
<li>DEVICE_CONTEXT&#160;:&#160;<a class="el" href="device__manager_8h.html#a5fe592117cc2296f7e78acab9055f526">device_manager.h</a></li>
<li>DEVICE_INFO&#160;:&#160;<a class="el" href="device__manager_8h.html#a6c959d0e0181f5f4b6a8b6b3f8e16760">device_manager.h</a></li>
<li>DEVICE_INIT_CONFIG&#160;:&#160;<a class="el" href="device__manager_8h.html#a2e1a690ef27ca2498b8893d6bf8ab597">device_manager.h</a></li>
<li>DeviceAddress&#160;:&#160;<a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">i2c_device.h</a></li>
<li>DeviceConfigureIoQueue()&#160;:&#160;<a class="el" href="device__manager_8c.html#ae4d976e80d1c2e2961eed2dc2ff6318c">device_manager.c</a></li>
<li>deviceContext()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">gpio_device.c</a></li>
<li>DeviceCreate()&#160;:&#160;<a class="el" href="device__manager_8c.html#a85f48e60bea1385e67ec52def6e57442">device_manager.c</a>, <a class="el" href="device__manager_8h.html#a5c125d43e51d9453782573ff3083bd3c">device_manager.h</a></li>
<li>DeviceD0Entry&#160;:&#160;<a class="el" href="device__manager_8h.html#a51a0fa7bdbb22680e79efaa2cacbb729">device_manager.h</a>, <a class="el" href="device__manager_8c.html#acb3d9726752bc4014673e7d6999b4e4b">device_manager.c</a></li>
<li>DeviceD0Exit&#160;:&#160;<a class="el" href="device__manager_8h.html#a1625e40d469a21ece1cad244736fbe81">device_manager.h</a>, <a class="el" href="device__manager_8c.html#ad82beaffc976103892cb92d64dd6e2de">device_manager.c</a></li>
<li>DeviceInitContext()&#160;:&#160;<a class="el" href="device__manager_8c.html#aebab0b9bc330432c9faaf78df6cfb6b2">device_manager.c</a>, <a class="el" href="device__manager_8h.html#a6e86b6c1162a93b64aa151c03f63890f">device_manager.h</a></li>
<li>DeviceInitialized&#160;:&#160;<a class="el" href="i2c__device_8c.html#ae49d231a428d107c888f925e845daf62">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#ae49d231a428d107c888f925e845daf62">spi_device.c</a></li>
<li>DeviceInterruptDisable&#160;:&#160;<a class="el" href="device__manager_8c.html#a7346c529a00f42617b55dfed2c4a8c6b">device_manager.c</a></li>
<li>DeviceInterruptDpc&#160;:&#160;<a class="el" href="device__manager_8c.html#a27966856080fa6cd1a9b7692140774b6">device_manager.c</a></li>
<li>DeviceInterruptEnable&#160;:&#160;<a class="el" href="device__manager_8c.html#a7c6d2ffd6c40693fcc51198997b8220c">device_manager.c</a></li>
<li>DeviceInterruptIsr&#160;:&#160;<a class="el" href="device__manager_8c.html#a66bb4353dba21c361d66c286dc941155">device_manager.c</a></li>
<li>DeviceIoControl&#160;:&#160;<a class="el" href="device__manager_8c.html#aa3daed4e70ed14cc04cbd390a34a3e90">device_manager.c</a>, <a class="el" href="device__manager_8h.html#aa3daed4e70ed14cc04cbd390a34a3e90">device_manager.h</a>, <a class="el" href="device__manager_8c.html#ad0a38f6ee5ec061af8f147cb6f9850aa">device_manager.c</a></li>
<li>DeviceIoRead&#160;:&#160;<a class="el" href="device__manager_8c.html#adbd8ce862fd891104b08816d03110316">device_manager.c</a>, <a class="el" href="device__manager_8h.html#adbd8ce862fd891104b08816d03110316">device_manager.h</a>, <a class="el" href="device__manager_8c.html#af9753d89f71d81c5fc2038491eb88932">device_manager.c</a></li>
<li>DeviceIoWrite&#160;:&#160;<a class="el" href="device__manager_8c.html#aafc7ca77e7f4cc11bf35ad04cff9a631">device_manager.c</a>, <a class="el" href="device__manager_8h.html#aafc7ca77e7f4cc11bf35ad04cff9a631">device_manager.h</a>, <a class="el" href="device__manager_8c.html#a47b96d5bfdb1f42b07cc85978325d77a">device_manager.c</a></li>
<li>DeviceManager_EvtDeviceAdd()&#160;:&#160;<a class="el" href="device__manager_8c.html#ae45323f3c2e302bf5f913275b84c7ce2">device_manager.c</a></li>
<li>DeviceManager_EvtDeviceContextCleanup()&#160;:&#160;<a class="el" href="device__manager_8c.html#ae47038053b18948b8fc9579f5a785cf4">device_manager.c</a></li>
<li>DeviceMapResources()&#160;:&#160;<a class="el" href="device__manager_8c.html#afa743e52d2410ed36cf2f06a30c07fcd">device_manager.c</a></li>
<li>DevicePrepareHardware&#160;:&#160;<a class="el" href="device__manager_8h.html#a4fda7ae13ae040932bf046e74f5bb8a6">device_manager.h</a>, <a class="el" href="device__manager_8c.html#abadb1053ad035a1858c6f71af0f00d56">device_manager.c</a></li>
<li>DeviceReleaseHardware&#160;:&#160;<a class="el" href="device__manager_8h.html#a20074db76287e7b7a18bc2f2c83c2c61">device_manager.h</a>, <a class="el" href="device__manager_8c.html#a214c96b10358f61af2f6a9ef3752ebc3">device_manager.c</a></li>
<li>DeviceResetHardware()&#160;:&#160;<a class="el" href="device__manager_8c.html#a059e0debbb6a3741ada3018f40b25b79">device_manager.c</a></li>
<li>DeviceSetupInterrupt()&#160;:&#160;<a class="el" href="device__manager_8c.html#ac506ca5136446bf4cc6f68c0747f56e0">device_manager.c</a></li>
<li>DeviceTimerFunc&#160;:&#160;<a class="el" href="device__manager_8c.html#ab5c458c6825a16167b4f7baef381f352">device_manager.c</a></li>
<li>DeviceType&#160;:&#160;<a class="el" href="i2c__device_8c.html#abf852046373359fb294f66a784b38263">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#abf852046373359fb294f66a784b38263">spi_device.c</a></li>
<li>DeviceUnmapResources()&#160;:&#160;<a class="el" href="device__manager_8c.html#a55323ce5aacec6667669f95f8abf7e22">device_manager.c</a></li>
<li>Direction&#160;:&#160;<a class="el" href="gpio__device_8c.html#a130124259198fee8d71747e31a529e96">gpio_device.c</a></li>
<li>DirectISR()&#160;:&#160;<a class="el" href="driver__core_8c.html#aa0699c5861e30ca66c92343c6fe22011">driver_core.c</a></li>
<li>DRIVER_ASSERT&#160;:&#160;<a class="el" href="error__handling_8h.html#a3a8ba39810c16fad65af2271c2dd6a6c">error_handling.h</a></li>
<li>DRIVER_CONFIG&#160;:&#160;<a class="el" href="driver__core_8h.html#ab3f3478bc4dcb804303c86d537979d13">driver_core.h</a></li>
<li>DRIVER_CONTEXT&#160;:&#160;<a class="el" href="driver__core_8c.html#a3171f2b55fc099beecef32f0d09d5d5b">driver_core.c</a></li>
<li>DRIVER_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="driver__core_8h.html#ad13345f7fccb1948813e53c9b2127947">driver_core.h</a></li>
<li>DRIVER_NAME&#160;:&#160;<a class="el" href="driver__main_8c.html#a25634d21648ca7fb7a2aca614bafaaeb">driver_main.c</a></li>
<li>DRIVER_STATISTICS&#160;:&#160;<a class="el" href="driver__core_8h.html#a0a4763a4c06b82137b4f23d6d2a62dd3">driver_core.h</a></li>
<li>DRIVER_TYPE&#160;:&#160;<a class="el" href="driver__core_8h.html#a21f9f5c777c8b513e7ed8e5e2621622a">driver_core.h</a></li>
<li>DRIVER_VERSION&#160;:&#160;<a class="el" href="driver__core_8h.html#aea65724e2a4566373216b88a7aa3fe62">driver_core.h</a></li>
<li>DRIVER_VERSION_BUILD&#160;:&#160;<a class="el" href="driver__main_8c.html#afe74bc852cf46e301606e5ac20720e34">driver_main.c</a></li>
<li>DRIVER_VERSION_MAJOR&#160;:&#160;<a class="el" href="driver__main_8c.html#a90d323e79537750a33d74eeab4a66837">driver_main.c</a></li>
<li>DRIVER_VERSION_MINOR&#160;:&#160;<a class="el" href="driver__main_8c.html#a31ba5103952e666f059389c86d76b640">driver_main.c</a></li>
<li>DRIVER_VERSION_REV&#160;:&#160;<a class="el" href="driver__main_8c.html#a3b369f69dba713375e6704ac172ecceb">driver_main.c</a></li>
<li>DriverCoreAddDevice()&#160;:&#160;<a class="el" href="driver__core_8c.html#abc51f0e6ed5304a27afddf92da4720e6">driver_core.c</a>, <a class="el" href="driver__core_8h.html#abc51f0e6ed5304a27afddf92da4720e6">driver_core.h</a></li>
<li>DriverCoreCleanup()&#160;:&#160;<a class="el" href="driver__core_8c.html#aac97f3e68a787ac88617369283c60b79">driver_core.c</a>, <a class="el" href="driver__core_8h.html#aac97f3e68a787ac88617369283c60b79">driver_core.h</a></li>
<li>DriverCoreGetConfiguration()&#160;:&#160;<a class="el" href="driver__core_8c.html#a7eca1538f567ab5a5add60bb6d2d03b5">driver_core.c</a>, <a class="el" href="driver__core_8h.html#a7eca1538f567ab5a5add60bb6d2d03b5">driver_core.h</a></li>
<li>DriverCoreGetDevice()&#160;:&#160;<a class="el" href="driver__core_8c.html#a24d4768bc415635465e00a5f5a3b4187">driver_core.c</a>, <a class="el" href="driver__core_8h.html#a24d4768bc415635465e00a5f5a3b4187">driver_core.h</a></li>
<li>DriverCoreGetDeviceCount()&#160;:&#160;<a class="el" href="driver__core_8c.html#a222752d15e46b43b3a4ae0c787d4018d">driver_core.c</a>, <a class="el" href="driver__core_8h.html#a222752d15e46b43b3a4ae0c787d4018d">driver_core.h</a></li>
<li>DriverCoreInitialize()&#160;:&#160;<a class="el" href="driver__core_8c.html#acdb452dbcae039af8967376463c758b9">driver_core.c</a>, <a class="el" href="driver__core_8h.html#acdb452dbcae039af8967376463c758b9">driver_core.h</a></li>
<li>DriverCoreProcessIoControl()&#160;:&#160;<a class="el" href="driver__core_8c.html#ae610e53c6df75743831a2e87ef9e746b">driver_core.c</a>, <a class="el" href="driver__core_8h.html#ae610e53c6df75743831a2e87ef9e746b">driver_core.h</a></li>
<li>DriverCoreRemoveDevice()&#160;:&#160;<a class="el" href="driver__core_8c.html#a89627789114e389118ee51bda8684ab6">driver_core.c</a>, <a class="el" href="driver__core_8h.html#a89627789114e389118ee51bda8684ab6">driver_core.h</a></li>
<li>DriverEntry()&#160;:&#160;<a class="el" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d">driver_entry.c</a>, <a class="el" href="driver__main_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d">driver_main.c</a></li>
<li>DriverTypeGeneric&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b">driver_core.h</a></li>
<li>DriverTypeHID&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5ae00ccbc82ec45bcc3369d612a2a99084">driver_core.h</a></li>
<li>DriverTypeNetwork&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6">driver_core.h</a></li>
<li>DriverTypeSerial&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5aeff4b54327f5a8d39d1d7f6cf199d3ca">driver_core.h</a></li>
<li>DriverTypeStorage&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56">driver_core.h</a></li>
<li>DriverTypeUSB&#160;:&#160;<a class="el" href="driver__core_8h.html#a04ec4f75c5ed103e258282d8e27e4ea5abd4c8ebdc07c8c948ada9572ab1c24cf">driver_core.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
