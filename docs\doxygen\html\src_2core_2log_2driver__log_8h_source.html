<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/core/log/driver_log.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('src_2core_2log_2driver__log_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">driver_log.h</div></div>
</div><!--header-->
<div class="contents">
<a href="src_2core_2log_2driver__log_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="preprocessor">#ifndef DRIVER_LOG_H</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="preprocessor">#define DRIVER_LOG_H</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span> </div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span> <span class="comment">// Required for NTSTATUS, etc.</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment">// Disable warning C4324 (structure was padded due to alignment specifier)</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment">// specifically for WDF headers, as this warning is often triggered by them</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment">// and can be treated as an error (C2220) in some build configurations.</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#pragma warning(push)</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#pragma warning(disable: 4324)</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="preprocessor">#include &lt;wdf.h&gt;</span>   <span class="comment">// Required for WDF_OBJECT_ATTRIBUTES, WDFSPINLOCK, etc.</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#pragma warning(pop)</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span> </div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment">//</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment">// Log Level Enum</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">// Defines the severity of the log message.</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00017" data-start="{" data-end="};">
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">   17</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a> {</div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">   18</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a> = 0, <span class="comment">// System is unusable.</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">   19</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a>,         <span class="comment">// Action must be taken immediately.</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">   20</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a>,      <span class="comment">// Critical conditions.</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">   21</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a>,         <span class="comment">// Error conditions.</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">   22</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a>,       <span class="comment">// Warning conditions.</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">   23</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a>,        <span class="comment">// Normal but significant condition.</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">   24</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a>,          <span class="comment">// Informational messages.</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">   25</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a>,         <span class="comment">// Debug-level messages.</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">   26</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a>,         <span class="comment">// Trace-level messages (more verbose than debug).</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">   27</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a>            <span class="comment">// Max log level, used for array sizing and validation.</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">   28</a></span>} <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a>;</div>
</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span> </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="comment">//</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="comment">// Log Types Enum (Bitmask)</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="comment">// Defines where the log message should be sent.</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00034" data-start="{" data-end="};">
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5">   34</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a> {</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1">   35</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1">LogTypeNone</a>       = 0x00, <span class="comment">// No logging output.</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127">   36</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127">LogTypeDebugger</a>   = 0x01, <span class="comment">// Output to kernel debugger (e.g., KdPrint).</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">   37</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a>       = 0x02, <span class="comment">// Output to a log file.</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">   38</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a>        = 0x04, <span class="comment">// Output to ETW (Event Tracing for Windows).</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">   39</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a>        = 0x08, <span class="comment">// Output to WPP Software Tracing.</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span>    <span class="comment">// Add more types as needed, e.g., LogTypeSyslog = 0x10;</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">   41</a></span>    <a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a>        = 0xFF  <span class="comment">// Log to all supported types (convenience).</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">   42</a></span>} <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a>;</div>
</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span> </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="comment">//</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="comment">// Log Configuration Structure</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="comment">// Holds the current configuration for the logging system.</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00048" data-start="{" data-end="};">
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html">   48</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a> {</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#ac0ad1887a3c94cf0998d9c5359900a4f">   49</a></span>    <a class="code hl_typedef" href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#ac0ad1887a3c94cf0998d9c5359900a4f">MinLevel</a>;      <span class="comment">// Minimum log level to record. Messages below this level will be ignored.</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="include_2core_2log_2driver__log_8h.html#aa4471d5ed80ebce68218e751798de8dc">   50</a></span>    <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a> <a class="code hl_variable" href="include_2core_2log_2driver__log_8h.html#aa4471d5ed80ebce68218e751798de8dc">LogTargets</a>;    <span class="comment">// Bitmask of log targets (Debugger, File, ETW, WPP).</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>    <span class="comment">// Future extensions:</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span>    <span class="comment">// WCHAR LogFilePath[256]; // Path for LogTypeFile, if configurable at runtime.</span></div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>    <span class="comment">// ULONG MaxLogFileSize;   // Max size for LogTypeFile before rotation.</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>    <span class="comment">// INT MaxBackupFiles;     // Number of backup files for LogTypeFile.</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">   55</a></span>} <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>;</div>
</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span> </div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">   57</a></span><span class="keyword">typedef</span> <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>* <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a>;</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span> </div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span><span class="comment">//</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="comment">// Extern declarations for global variables defined in driver_log.c.</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><span class="comment">// These allow other .c files that include this header to access these global settings.</span></div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span><span class="comment">//</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span><span class="keyword">extern</span> <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a> <a class="code hl_variable" href="driver__log_8c.html#af114289be71fc27e5ce43d55d4d6622c">g_LogConfig</a>;         <span class="comment">// Global logging configuration settings.</span></div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span><span class="keyword">extern</span> BOOLEAN <a class="code hl_variable" href="driver__log_8c.html#acd7f22e672d8bbb5ac97c70459b869fb">g_LogInitialized</a>;       <span class="comment">// Flag indicating if the logging system has been initialized.</span></div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span><span class="keyword">extern</span> <span class="keyword">const</span> PCSTR <a class="code hl_variable" href="src_2core_2log_2driver__log_8h.html#ad2099051d14962ced91f03017c7021f3">g_LogLevelNames</a>[<a class="code hl_enumvalue" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a>]; <span class="comment">// Array of human-readable names for log levels.</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span> </div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span><span class="comment">//</span></div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><span class="comment">// Public Logging Function Prototypes</span></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span><span class="comment">// These are the functions that other parts of the driver can call.</span></div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span><span class="comment">//</span></div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span> </div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span><span class="comment">// Initializes the logging system.</span></div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span><span class="comment">// This function must be called (e.g., in DriverEntry) before any logging can occur.</span></div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span><span class="comment">// Parameters:</span></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span><span class="comment">//   DriverObject:  Pointer to the WDFDRIVER object. This can be used to associate</span></div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span><span class="comment">//                  WDF resources like spinlocks with the driver&#39;s lifetime.</span></div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span><span class="comment">//   InitialConfig: Optional pointer to a LOG_CONFIG structure to override default settings.</span></div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><span class="comment">//                  If NULL, default logging settings will be used.</span></div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span><span class="comment">// Return Value:</span></div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span><span class="comment">//   STATUS_SUCCESS if initialization was successful, otherwise an NTSTATUS error code.</span></div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>NTSTATUS</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9">LogInitialize</a>(</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a> DriverObject,</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>    _In_opt_ CONST <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a>* InitialConfig</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>);</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span> </div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span><span class="comment">// Initializes the logging configuration structure with default values.</span></div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>VOID</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span><a class="code hl_function" href="include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a">LogConfigInit</a>(</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    _Out_ <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a> LogConfig</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>);</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span> </div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span><span class="comment">// Uninitializes the logging system and releases any allocated resources.</span></div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="comment">// This function should be called when the driver is unloading (e.g., in EvtDriverUnload).</span></div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>VOID</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span><a class="code hl_function" href="src_2core_2log_2driver__log_8h.html#aab8bcb7121136bc236fe5d55778fbaf2">LogUninitialize</a>(</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    VOID</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>);</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span> </div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span><span class="comment">// Core logging function that takes a va_list for variable arguments.</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><span class="comment">// This is typically called by user-friendly macros rather than directly.</span></div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span><span class="comment">// Parameters:</span></div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><span class="comment">//   Level:    The severity level of the message.</span></div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span><span class="comment">//   Function: The name of the function where the log message originated.</span></div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span><span class="comment">//   Line:     The line number in the source file where the log message originated.</span></div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><span class="comment">//   Format:   The printf-style format string for the message.</span></div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><span class="comment">//   Args:     A va_list containing the arguments for the format string.</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>VOID</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span><a class="code hl_function" href="src_2core_2log_2driver__log_8h.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a>(</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    _In_ <a class="code hl_typedef" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a> Level,</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    _In_ PCSTR Function,</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    _In_ ULONG Line,</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>    _In_ PCSTR Format,</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    _In_ va_list Args</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>);</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span> </div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span><span class="comment">//</span></div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="comment">// User-friendly Logging Macros (Recommended for use in driver code)</span></div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span><span class="comment">// These macros simplify logging by automatically providing the function name (__FUNCTION__)</span></div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span><span class="comment">// and line number (__LINE__), and handling the variable arguments (...).</span></div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span><span class="comment">//</span></div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span><span class="comment">// Usage Example:</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span><span class="comment">//   LOG_INFO(&quot;Device initialized successfully, status: 0x%X&quot;, status);</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span><span class="comment">//   LOG_ERROR(&quot;Failed to allocate memory of size %zu bytes.&quot;, requiredSize);</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span><span class="comment">//</span></div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span> </div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#ad706db1253940848e01bbc71ede868ef">  127</a></span><span class="preprocessor">#define LOG_EMERGENCY(Format, ...) LogMessageVA(LogLevelEmergency, __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#aa1911455782e83f3b06fab600be0e43e">  128</a></span><span class="preprocessor">#define LOG_ALERT(Format, ...)     LogMessageVA(LogLevelAlert,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#abc03884460a6987df33fea0d5cae8302">  129</a></span><span class="preprocessor">#define LOG_CRITICAL(Format, ...)  LogMessageVA(LogLevelCritical,  __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a29e75b488d8e8ef5641c5bd16709faec">  130</a></span><span class="preprocessor">#define LOG_ERROR(Format, ...)     LogMessageVA(LogLevelError,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a1dd05e1ef2b66fc68251edacaa75e9f7">  131</a></span><span class="preprocessor">#define LOG_WARNING(Format, ...)   LogMessageVA(LogLevelWarning,   __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a05bf2404451e701f51d18409e72321fd">  132</a></span><span class="preprocessor">#define LOG_NOTICE(Format, ...)    LogMessageVA(LogLevelNotice,    __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a7748b322eafa9e058c518fef49b110cb">  133</a></span><span class="preprocessor">#define LOG_INFO(Format, ...)      LogMessageVA(LogLevelInfo,      __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#acfe39a25e08737b535dc881071ebf149">  134</a></span><span class="preprocessor">#define LOG_DEBUG(Format, ...)     LogMessageVA(LogLevelDebug,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a0972af62c9ad7b688924604669d7d762">  135</a></span><span class="preprocessor">#define LOG_TRACE(Format, ...)     LogMessageVA(LogLevelTrace,     __FUNCTION__, __LINE__, Format, __VA_ARGS__)</span></div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span> </div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span><span class="comment">//</span></div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span><span class="comment">// Conditional Logging Macros (Log only if a specified condition is true)</span></div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span><span class="comment">// These macros help reduce log spam by only logging when a particular condition is met.</span></div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span><span class="comment">//</span></div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span><span class="comment">// Usage Example:</span></div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span><span class="comment">//   NTSTATUS status = SomeFunction();</span></div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span><span class="comment">//   LOG_ERROR_IF(status != STATUS_SUCCESS, &quot;SomeFunction failed with status: 0x%X&quot;, status);</span></div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span><span class="comment">//</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span> </div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a570937723f42dd301b24b631ec455b58">  146</a></span><span class="preprocessor">#define LOG_EMERGENCY_IF(Condition, Format, ...) do { if (Condition) { LogMessageVA(LogLevelEmergency, __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a4c42b3fa94110619ab8458eb672d189d">  147</a></span><span class="preprocessor">#define LOG_ALERT_IF(Condition, Format, ...)     do { if (Condition) { LogMessageVA(LogLevelAlert,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#ab48ce4a2ee7f0b5f74153fedf6ad7c25">  148</a></span><span class="preprocessor">#define LOG_CRITICAL_IF(Condition, Format, ...)  do { if (Condition) { LogMessageVA(LogLevelCritical,  __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#aff0a0cc082f6b2fad9ed0979da6e8a9b">  149</a></span><span class="preprocessor">#define LOG_ERROR_IF(Condition, Format, ...)     do { if (Condition) { LogMessageVA(LogLevelError,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a238f142a1b0fcbd8378c38d99b233baa">  150</a></span><span class="preprocessor">#define LOG_WARNING_IF(Condition, Format, ...)   do { if (Condition) { LogMessageVA(LogLevelWarning,   __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a6cf4240fc51cea71e901acf9df797b98">  151</a></span><span class="preprocessor">#define LOG_NOTICE_IF(Condition, Format, ...)    do { if (Condition) { LogMessageVA(LogLevelNotice,    __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a94d7f96857344352ffbc6ee65e9f2390">  152</a></span><span class="preprocessor">#define LOG_INFO_IF(Condition, Format, ...)      do { if (Condition) { LogMessageVA(LogLevelInfo,      __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#ae930e4b3ae4e59dc6a7b6a4feefb116f">  153</a></span><span class="preprocessor">#define LOG_DEBUG_IF(Condition, Format, ...)     do { if (Condition) { LogMessageVA(LogLevelDebug,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno"><a class="line" href="src_2core_2log_2driver__log_8h.html#a8793c218a97ef927e72271b80a872495">  154</a></span><span class="preprocessor">#define LOG_TRACE_IF(Condition, Format, ...)     do { if (Condition) { LogMessageVA(LogLevelTrace,     __FUNCTION__, __LINE__, Format, __VA_ARGS__); } } while(0)</span></div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span> </div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span><span class="preprocessor">#endif </span><span class="comment">// DRIVER_LOG_H</span></div>
<div class="ttc" id="acore__types_8h_html_acd2f53446ede16834cc0bd30335e71cb"><div class="ttname"><a href="core__types_8h.html#acd2f53446ede16834cc0bd30335e71cb">WDFDRIVER</a></div><div class="ttdeci">struct WDFDRIVER__ * WDFDRIVER</div><div class="ttdef"><b>Definition</b> core_types.h:31</div></div>
<div class="ttc" id="adriver__log_8c_html_acd7f22e672d8bbb5ac97c70459b869fb"><div class="ttname"><a href="driver__log_8c.html#acd7f22e672d8bbb5ac97c70459b869fb">g_LogInitialized</a></div><div class="ttdeci">BOOLEAN g_LogInitialized</div><div class="ttdef"><b>Definition</b> driver_log.c:17</div></div>
<div class="ttc" id="adriver__log_8c_html_af114289be71fc27e5ce43d55d4d6622c"><div class="ttname"><a href="driver__log_8c.html#af114289be71fc27e5ce43d55d4d6622c">g_LogConfig</a></div><div class="ttdeci">LOG_CONFIG g_LogConfig</div><div class="ttdef"><b>Definition</b> driver_log.c:16</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_aa4471d5ed80ebce68218e751798de8dc"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#aa4471d5ed80ebce68218e751798de8dc">_LOG_CONFIG::LogTargets</a></div><div class="ttdeci">LOG_TYPES LogTargets</div><div class="ttdef"><b>Definition</b> driver_log.h:50</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_aa90925833aff044f4ba03f43f8084bf7"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></div><div class="ttdeci">enum _LOG_LEVEL LOG_LEVEL</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ab4caba1729c833f0c7cce2e72c20e30a"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a">LogConfigInit</a></div><div class="ttdeci">VOID LogConfigInit(PLOG_CONFIG LogConfig)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ac0ad1887a3c94cf0998d9c5359900a4f"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ac0ad1887a3c94cf0998d9c5359900a4f">_LOG_CONFIG::MinLevel</a></div><div class="ttdeci">LOG_LEVEL MinLevel</div><div class="ttdef"><b>Definition</b> driver_log.h:49</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_ae2910293c9c672800cca68427812b7c9"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9">LogInitialize</a></div><div class="ttdeci">VOID LogInitialize(PDRIVER_OBJECT DriverObject, PLOG_CONFIG LogConfig)</div></div>
<div class="ttc" id="ainclude_2core_2log_2driver__log_8h_html_struct__LOG__CONFIG"><div class="ttname"><a href="include_2core_2log_2driver__log_8h.html#struct__LOG__CONFIG">_LOG_CONFIG</a></div><div class="ttdef"><b>Definition</b> driver_log.h:48</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a565ca8d986ea85865e5e0e69c0fccc9d"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">LOG_CONFIG</a></div><div class="ttdeci">struct _LOG_CONFIG LOG_CONFIG</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a5c3fab47ae6bd7de107b55a48ff20591"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">LOG_TYPES</a></div><div class="ttdeci">enum _LOG_TYPES LOG_TYPES</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843f"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843f">_LOG_LEVEL</a></div><div class="ttdeci">_LOG_LEVEL</div><div class="ttdef"><b>Definition</b> driver_log.h:17</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">LogLevelAlert</a></div><div class="ttdeci">@ LogLevelAlert</div><div class="ttdef"><b>Definition</b> driver_log.h:19</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">LogLevelWarning</a></div><div class="ttdeci">@ LogLevelWarning</div><div class="ttdef"><b>Definition</b> driver_log.h:22</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">LogLevelError</a></div><div class="ttdeci">@ LogLevelError</div><div class="ttdef"><b>Definition</b> driver_log.h:21</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">LogLevelTrace</a></div><div class="ttdeci">@ LogLevelTrace</div><div class="ttdef"><b>Definition</b> driver_log.h:26</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">LogLevelNotice</a></div><div class="ttdeci">@ LogLevelNotice</div><div class="ttdef"><b>Definition</b> driver_log.h:23</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">LogLevelEmergency</a></div><div class="ttdeci">@ LogLevelEmergency</div><div class="ttdef"><b>Definition</b> driver_log.h:18</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">LogLevelMax</a></div><div class="ttdeci">@ LogLevelMax</div><div class="ttdef"><b>Definition</b> driver_log.h:27</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">LogLevelDebug</a></div><div class="ttdeci">@ LogLevelDebug</div><div class="ttdef"><b>Definition</b> driver_log.h:25</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">LogLevelInfo</a></div><div class="ttdeci">@ LogLevelInfo</div><div class="ttdef"><b>Definition</b> driver_log.h:24</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">LogLevelCritical</a></div><div class="ttdeci">@ LogLevelCritical</div><div class="ttdef"><b>Definition</b> driver_log.h:20</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5">_LOG_TYPES</a></div><div class="ttdeci">_LOG_TYPES</div><div class="ttdef"><b>Definition</b> driver_log.h:34</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">LogTypeFile</a></div><div class="ttdeci">@ LogTypeFile</div><div class="ttdef"><b>Definition</b> driver_log.h:37</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">LogTypeWPP</a></div><div class="ttdeci">@ LogTypeWPP</div><div class="ttdef"><b>Definition</b> driver_log.h:39</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127">LogTypeDebugger</a></div><div class="ttdeci">@ LogTypeDebugger</div><div class="ttdef"><b>Definition</b> driver_log.h:36</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">LogTypeETW</a></div><div class="ttdeci">@ LogTypeETW</div><div class="ttdef"><b>Definition</b> driver_log.h:38</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1">LogTypeNone</a></div><div class="ttdeci">@ LogTypeNone</div><div class="ttdef"><b>Definition</b> driver_log.h:35</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">LogTypeAll</a></div><div class="ttdeci">@ LogTypeAll</div><div class="ttdef"><b>Definition</b> driver_log.h:41</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_a8e8711da6408af7b3b313f892121215e"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#a8e8711da6408af7b3b313f892121215e">LogMessageVA</a></div><div class="ttdeci">VOID LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args)</div><div class="ttdef"><b>Definition</b> driver_log.c:103</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_aa90925833aff044f4ba03f43f8084bf7"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">LOG_LEVEL</a></div><div class="ttdeci">enum _LOG_LEVEL LOG_LEVEL</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_aab8bcb7121136bc236fe5d55778fbaf2"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#aab8bcb7121136bc236fe5d55778fbaf2">LogUninitialize</a></div><div class="ttdeci">VOID LogUninitialize(VOID)</div><div class="ttdef"><b>Definition</b> driver_log.c:45</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_ab99d8d17b06b190b7fecbbadd3d6b7df"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">PLOG_CONFIG</a></div><div class="ttdeci">LOG_CONFIG * PLOG_CONFIG</div><div class="ttdef"><b>Definition</b> driver_log.h:57</div></div>
<div class="ttc" id="asrc_2core_2log_2driver__log_8h_html_ad2099051d14962ced91f03017c7021f3"><div class="ttname"><a href="src_2core_2log_2driver__log_8h.html#ad2099051d14962ced91f03017c7021f3">g_LogLevelNames</a></div><div class="ttdeci">const PCSTR g_LogLevelNames[LogLevelMax]</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_aebb8dcc11953d78e620bbef0b9e2183.html">core</a></li><li class="navelem"><a href="dir_2aa9c0e397d40306fad4535cf762fffd.html">log</a></li><li class="navelem"><a href="src_2core_2log_2driver__log_8h.html">driver_log.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
