<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/devices/spi_device.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('spi__device_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">spi_device.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="hal__interface_8h_source.html">../../../include/hal/hal_interface.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="kmdf__spi_8h_source.html">../../../include/hal/bus/kmdf_spi.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="spi__device_8h_source.html">../../../include/hal/devices/spi_device.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for spi_device.c:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2devices_2spi__device_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2spi__device_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2spi__device_8c">
<area shape="rect" title=" " alt="" coords="259,5,427,48"/>
<area shape="rect" title=" " alt="" coords="320,459,385,485"/>
<area shape="poly" title=" " alt="" coords="429,37,504,56,542,70,578,88,610,110,636,137,653,169,659,206,659,300,657,321,651,341,630,375,599,403,561,425,519,441,477,454,400,468,399,463,475,449,518,436,559,420,596,398,626,372,646,339,652,320,654,300,654,207,648,171,632,140,607,114,576,92,540,75,502,61,427,42"/>
<area shape="rect" title=" " alt="" coords="390,376,443,403"/>
<area shape="poly" title=" " alt="" coords="384,46,410,67,431,95,444,130,451,167,453,205,452,242,442,310,428,362,423,361,436,309,447,242,448,205,446,168,439,131,426,97,406,71,381,51"/>
<area shape="rect" href="hal__interface_8h.html" title=" " alt="" coords="25,277,144,320"/>
<area shape="poly" title=" " alt="" coords="307,51,273,72,241,98,200,140,163,186,107,266,102,263,159,183,196,137,237,94,270,68,304,46"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="152,368,270,411"/>
<area shape="poly" title=" " alt="" coords="289,51,220,84,141,132,102,163,67,197,38,236,15,278,8,300,9,308,15,318,39,343,70,361,103,373,137,381,136,386,102,378,67,366,36,348,10,322,4,310,3,299,10,276,33,233,64,194,99,159,138,128,217,79,287,46"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="182,187,301,229"/>
<area shape="poly" title=" " alt="" coords="308,51,281,71,260,97,247,134,243,171,238,171,242,133,255,95,278,67,304,46"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="269,96,417,139"/>
<area shape="poly" title=" " alt="" coords="346,48,346,81,340,81,340,48"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="491,277,619,320"/>
<area shape="poly" title=" " alt="" coords="403,46,441,66,474,94,504,135,526,180,541,224,551,262,546,263,536,226,521,182,499,138,470,98,438,71,400,51"/>
<area shape="poly" title=" " alt="" coords="92,319,109,365,124,389,142,409,181,433,224,450,267,460,305,465,304,471,266,465,222,455,178,438,139,413,119,392,104,367,87,321"/>
<area shape="poly" title=" " alt="" coords="145,314,157,317,375,375,374,380,156,323,144,319"/>
<area shape="poly" title=" " alt="" coords="115,318,170,356,167,361,112,323"/>
<area shape="poly" title=" " alt="" coords="249,409,319,448,316,453,246,414"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="169,285,315,312"/>
<area shape="poly" title=" " alt="" coords="244,230,244,270,239,270,239,230"/>
<area shape="poly" title=" " alt="" coords="268,310,379,366,377,371,266,315"/>
<area shape="poly" title=" " alt="" coords="240,313,226,354,221,353,235,312"/>
<area shape="poly" title=" " alt="" coords="346,139,354,443,349,443,341,139"/>
<area shape="poly" title=" " alt="" coords="355,138,374,186,399,281,414,360,409,361,393,282,369,187,350,140"/>
<area shape="poly" title=" " alt="" coords="321,141,279,178,275,174,318,137"/>
<area shape="poly" title=" " alt="" coords="542,322,505,366,457,413,425,435,394,453,391,449,423,430,453,409,501,362,538,319"/>
<area shape="poly" title=" " alt="" coords="524,323,451,370,448,365,521,318"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ae42ccb14fff6c8b1c06d1ff178b6c146" id="r_ae42ccb14fff6c8b1c06d1ff178b6c146"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</a> (<a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a>, 'SPIW')</td></tr>
<tr class="memitem:a1a243a15dd793b6d0f7b7011461a8641" id="r_a1a243a15dd793b6d0f7b7011461a8641"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a243a15dd793b6d0f7b7011461a8641">if</a> (!<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>))</td></tr>
<tr class="memitem:ab29d05a3528131be0d35fe785e85590f" id="r_ab29d05a3528131be0d35fe785e85590f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab29d05a3528131be0d35fe785e85590f">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="i2c__device_8c.html#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a> !=NULL)</td></tr>
<tr class="memitem:ac40f83943701ccbf4235e0c238583dfb" id="r_ac40f83943701ccbf4235e0c238583dfb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac40f83943701ccbf4235e0c238583dfb">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a> !=NULL)</td></tr>
<tr class="memitem:a9d2d77fd6fa0d75751b40049e614b00b" id="r_a9d2d77fd6fa0d75751b40049e614b00b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9d2d77fd6fa0d75751b40049e614b00b">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>==NULL)</td></tr>
<tr class="memitem:a4b5c92f0859e4be1ead5d71edc903427" id="r_a4b5c92f0859e4be1ead5d71edc903427"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4b5c92f0859e4be1ead5d71edc903427">if</a> (<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>) &amp;&amp;BytesRead !=NULL)</td></tr>
<tr class="memitem:a164e77dd43f69d29ea926ae0ec42969b" id="r_a164e77dd43f69d29ea926ae0ec42969b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a164e77dd43f69d29ea926ae0ec42969b">if</a> (<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>) &amp;&amp;BytesWritten !=NULL)</td></tr>
<tr class="memitem:afff80b1a0000ef578da0277667a994ff" id="r_afff80b1a0000ef578da0277667a994ff"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#afff80b1a0000ef578da0277667a994ff">if</a> (<a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a>==NULL)</td></tr>
<tr class="memitem:a92b63e772873a034bea01d26f382ed57" id="r_a92b63e772873a034bea01d26f382ed57"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a92b63e772873a034bea01d26f382ed57">LogInfo</a> (__FUNCTION__, __LINE__, &quot;SPI device initialized successfully&quot;)</td></tr>
<tr class="memitem:aef91cc299dacb19ea73324acf8c8ff2c" id="r_aef91cc299dacb19ea73324acf8c8ff2c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aef91cc299dacb19ea73324acf8c8ff2c">LogInfo</a> (__FUNCTION__, __LINE__, &quot;SPI transfer succeeded&quot;)</td></tr>
<tr class="memitem:a06c5e5172ac494575aa45dd42bfc32f5" id="r_a06c5e5172ac494575aa45dd42bfc32f5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a06c5e5172ac494575aa45dd42bfc32f5">RtlCopyMemory</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>, <a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>, sizeof(<a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>))</td></tr>
<tr class="memitem:a052b57a96b994325a574bcb9f3db837a" id="r_a052b57a96b994325a574bcb9f3db837a"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a052b57a96b994325a574bcb9f3db837a">SpiDeviceCleanup</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:ae2be7c6b48ddf5b08876e1115879469d" id="r_ae2be7c6b48ddf5b08876e1115879469d"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae2be7c6b48ddf5b08876e1115879469d">SpiDeviceGetStatistics</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_ <a class="el" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a> Statistics)</td></tr>
<tr class="memitem:a6939e12311ec72f975bcd03a4250a3e2" id="r_a6939e12311ec72f975bcd03a4250a3e2"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6939e12311ec72f975bcd03a4250a3e2">SpiDeviceInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>)</td></tr>
<tr class="memitem:a3bc98267d67ee8988179bde952efaa87" id="r_a3bc98267d67ee8988179bde952efaa87"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3bc98267d67ee8988179bde952efaa87">SpiDeviceRead</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</td></tr>
<tr class="memitem:a2428921b9d71ab9d24f34e0a7b23487c" id="r_a2428921b9d71ab9d24f34e0a7b23487c"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2428921b9d71ab9d24f34e0a7b23487c">SpiDeviceTransfer</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:ae90ccf3d865bebb54c2c76e10fcbcaa8" id="r_ae90ccf3d865bebb54c2c76e10fcbcaa8"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae90ccf3d865bebb54c2c76e10fcbcaa8">SpiDeviceWrite</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aee3e2abd9dd27ddfc3e158a9ffce1746" id="r_aee3e2abd9dd27ddfc3e158a9ffce1746"><td class="memItemLeft" align="right" valign="top">Exit&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aee3e2abd9dd27ddfc3e158a9ffce1746">__pad0__</a></td></tr>
<tr class="memitem:aff83b2530e0944d77d4ee0965b41ad89" id="r_aff83b2530e0944d77d4ee0965b41ad89"><td class="memItemLeft" align="right" valign="top">WDFMEMORY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aff83b2530e0944d77d4ee0965b41ad89">ConfigurationMemory</a> = NULL</td></tr>
<tr class="memitem:af9a881dabb7ea1e15ee2808cca09fd6a" id="r_af9a881dabb7ea1e15ee2808cca09fd6a"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af9a881dabb7ea1e15ee2808cca09fd6a">DelayInMicroseconds</a> = 0</td></tr>
<tr class="memitem:ae49d231a428d107c888f925e845daf62" id="r_ae49d231a428d107c888f925e845daf62"><td class="memItemLeft" align="right" valign="top">BOOLEAN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae49d231a428d107c888f925e845daf62">DeviceInitialized</a> = FALSE</td></tr>
<tr class="memitem:abf852046373359fb294f66a784b38263" id="r_abf852046373359fb294f66a784b38263"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abf852046373359fb294f66a784b38263">DeviceType</a> = <a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab">HalDeviceSPI</a></td></tr>
<tr class="memitem:a0544c3fe466e421738dae463968b70ba" id="r_a0544c3fe466e421738dae463968b70ba"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0544c3fe466e421738dae463968b70ba">else</a></td></tr>
<tr class="memitem:ab9707a002fb8033fdc202e8c8b8f8569" id="r_ab9707a002fb8033fdc202e8c8b8f8569"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab9707a002fb8033fdc202e8c8b8f8569">ErrorCount</a> = 0</td></tr>
<tr class="memitem:aaea9f9b32650901ecb0d31cb5066cd7f" id="r_aaea9f9b32650901ecb0d31cb5066cd7f"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaea9f9b32650901ecb0d31cb5066cd7f">Flags</a> = 0</td></tr>
<tr class="memitem:a96ba6885a1d23da9ee577cfc9b91ae60" id="r_a96ba6885a1d23da9ee577cfc9b91ae60"><td class="memItemLeft" align="right" valign="top"><a class="el" href="hal__interface_8h.html#a2f4ba870132c1fd57e2d74ba94e39805">HAL_DEVICE_HANDLE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a></td></tr>
<tr class="memitem:a40d2c447ac37fcd86673f2a11b2ca094" id="r_a40d2c447ac37fcd86673f2a11b2ca094"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a40d2c447ac37fcd86673f2a11b2ca094">PrivateData</a> = <a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></td></tr>
<tr class="memitem:a0470f3b47bad91bd5e08004c87a8d98a" id="r_a0470f3b47bad91bd5e08004c87a8d98a"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0470f3b47bad91bd5e08004c87a8d98a">PrivateDataSize</a> = sizeof(<a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>)</td></tr>
<tr class="memitem:a2d4e25dc12a54c28261d5ba390e3aa19" id="r_a2d4e25dc12a54c28261d5ba390e3aa19"><td class="memItemLeft" align="right" valign="top">*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d4e25dc12a54c28261d5ba390e3aa19">PSPI_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:ac2677e024009c29e2bcee99e0c32c735" id="r_ac2677e024009c29e2bcee99e0c32c735"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac2677e024009c29e2bcee99e0c32c735">ReadBuffer</a> = Buffer</td></tr>
<tr class="memitem:a1d0f6e5a27e5a31ee40de06efe3ba233" id="r_a1d0f6e5a27e5a31ee40de06efe3ba233"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1d0f6e5a27e5a31ee40de06efe3ba233">ReadLength</a> = Length</td></tr>
<tr class="memitem:aa226b0d93154d552caefa2ca1550155c" id="r_aa226b0d93154d552caefa2ca1550155c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa226b0d93154d552caefa2ca1550155c">SPI_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:addbc5753ca32543e25382ea5a386d59b" id="r_addbc5753ca32543e25382ea5a386d59b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a> = NULL</td></tr>
<tr class="memitem:a9611b3a00430a86619b5923de30f9fdb" id="r_a9611b3a00430a86619b5923de30f9fdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9611b3a00430a86619b5923de30f9fdb">status</a></td></tr>
<tr class="memitem:a77b4762318f24dff847f94f382cfeea6" id="r_a77b4762318f24dff847f94f382cfeea6"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77b4762318f24dff847f94f382cfeea6">STATUS_SUCCESS</a></td></tr>
<tr class="memitem:a63212990a463669c2face6cfbfd28d26" id="r_a63212990a463669c2face6cfbfd28d26"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a63212990a463669c2face6cfbfd28d26">TransactionCount</a> = 0</td></tr>
<tr class="memitem:a11ec07dcb5c1cea421134a0b149443a5" id="r_a11ec07dcb5c1cea421134a0b149443a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11ec07dcb5c1cea421134a0b149443a5">WdfDevice</a> = Device</td></tr>
<tr class="memitem:ad26ded9b73e8b14b4117614b39440d86" id="r_ad26ded9b73e8b14b4117614b39440d86"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad26ded9b73e8b14b4117614b39440d86">WriteBuffer</a> = <a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a></td></tr>
<tr class="memitem:a7a33fa49b57196f5722a55916cff0a52" id="r_a7a33fa49b57196f5722a55916cff0a52"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a> [0] = RegisterAddress</td></tr>
<tr class="memitem:a530eca3d7e36c5dde60c5e49dd7b2b34" id="r_a530eca3d7e36c5dde60c5e49dd7b2b34"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a530eca3d7e36c5dde60c5e49dd7b2b34">WriteLength</a> = 1</td></tr>
</table>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="ae42ccb14fff6c8b1c06d1ff178b6c146" name="ae42ccb14fff6c8b1c06d1ff178b6c146"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae42ccb14fff6c8b1c06d1ff178b6c146">&#9670;&#160;</a></span>ExFreePoolWithTag()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ExFreePoolWithTag </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">'SPIW'</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_ae42ccb14fff6c8b1c06d1ff178b6c146_icgraph.png" border="0" usemap="#aspi__device_8c_ae42ccb14fff6c8b1c06d1ff178b6c146_icgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_ae42ccb14fff6c8b1c06d1ff178b6c146_icgraph" id="aspi__device_8c_ae42ccb14fff6c8b1c06d1ff178b6c146_icgraph">
<area shape="rect" title=" " alt="" coords="172,31,309,57"/>
<area shape="rect" href="gpio__core_8c.html#a785f00e9c0879fb478077d2cdce99906" title=" " alt="" coords="5,5,124,32"/>
<area shape="poly" title=" " alt="" coords="156,34,124,30,124,25,156,29"/>
<area shape="rect" href="gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641" title=" " alt="" coords="45,56,84,83"/>
<area shape="poly" title=" " alt="" coords="157,59,84,69,84,64,156,53"/>
</map>
</div>

</div>
</div>
<a id="a1a243a15dd793b6d0f7b7011461a8641" name="a1a243a15dd793b6d0f7b7011461a8641"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a243a15dd793b6d0f7b7011461a8641">&#9670;&#160;</a></span>if() <span class="overload">[1/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>NT_SUCCESS</em></span>status</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph.png" border="0" usemap="#aspi__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" id="aspi__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,44,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="92,31,162,57"/>
<area shape="poly" title=" " alt="" coords="44,41,76,41,76,47,44,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="210,5,338,32"/>
<area shape="poly" title=" " alt="" coords="162,35,195,30,195,35,163,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="227,56,322,83"/>
<area shape="poly" title=" " alt="" coords="163,47,212,56,211,61,162,53"/>
</map>
</div>

</div>
</div>
<a id="ab29d05a3528131be0d35fe785e85590f" name="ab29d05a3528131be0d35fe785e85590f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab29d05a3528131be0d35fe785e85590f">&#9670;&#160;</a></span>if() <span class="overload">[2/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="i2c__device_8c.html#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a> !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph.png" border="0" usemap="#aspi__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph" id="aspi__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="97,29,203,56"/>
<area shape="poly" title=" " alt="" coords="43,62,82,54,83,59,45,67"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="92,80,208,107"/>
<area shape="poly" title=" " alt="" coords="45,69,77,76,76,81,43,74"/>
<area shape="poly" title=" " alt="" coords="128,30,124,21,126,11,135,5,150,3,165,5,174,12,170,16,163,10,150,8,137,10,131,14,129,20,132,28"/>
</map>
</div>

</div>
</div>
<a id="ac40f83943701ccbf4235e0c238583dfb" name="ac40f83943701ccbf4235e0c238583dfb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac40f83943701ccbf4235e0c238583dfb">&#9670;&#160;</a></span>if() <span class="overload">[3/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a> !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_ac40f83943701ccbf4235e0c238583dfb_cgraph.png" border="0" usemap="#aspi__device_8c_ac40f83943701ccbf4235e0c238583dfb_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_ac40f83943701ccbf4235e0c238583dfb_cgraph" id="aspi__device_8c_ac40f83943701ccbf4235e0c238583dfb_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,44,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,40,76,40,76,45,44,45"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
</map>
</div>

</div>
</div>
<a id="a9d2d77fd6fa0d75751b40049e614b00b" name="a9d2d77fd6fa0d75751b40049e614b00b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d2d77fd6fa0d75751b40049e614b00b">&#9670;&#160;</a></span>if() <span class="overload">[4/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph.png" border="0" usemap="#aspi__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph" id="aspi__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,61,76,54,77,60,45,67"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="109,80,180,107"/>
<area shape="poly" title=" " alt="" coords="45,69,94,80,93,85,44,75"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="245,55,373,81"/>
<area shape="poly" title=" " alt="" coords="180,85,229,78,230,83,181,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="262,105,357,132"/>
<area shape="poly" title=" " alt="" coords="181,96,247,106,246,112,180,101"/>
</map>
</div>

</div>
</div>
<a id="a4b5c92f0859e4be1ead5d71edc903427" name="a4b5c92f0859e4be1ead5d71edc903427"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4b5c92f0859e4be1ead5d71edc903427">&#9670;&#160;</a></span>if() <span class="overload">[5/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>) &amp;&amp;BytesRead !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a164e77dd43f69d29ea926ae0ec42969b" name="a164e77dd43f69d29ea926ae0ec42969b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a164e77dd43f69d29ea926ae0ec42969b">&#9670;&#160;</a></span>if() <span class="overload">[6/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>) &amp;&amp;BytesWritten !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="afff80b1a0000ef578da0277667a994ff" name="afff80b1a0000ef578da0277667a994ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afff80b1a0000ef578da0277667a994ff">&#9670;&#160;</a></span>if() <span class="overload">[7/7]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a></td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_afff80b1a0000ef578da0277667a994ff_cgraph.png" border="0" usemap="#aspi__device_8c_afff80b1a0000ef578da0277667a994ff_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_afff80b1a0000ef578da0277667a994ff_cgraph" id="aspi__device_8c_afff80b1a0000ef578da0277667a994ff_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,44,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="92,31,162,57"/>
<area shape="poly" title=" " alt="" coords="44,41,76,41,76,47,44,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="210,5,338,32"/>
<area shape="poly" title=" " alt="" coords="162,35,195,30,195,35,163,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="227,56,322,83"/>
<area shape="poly" title=" " alt="" coords="163,47,212,56,211,61,162,53"/>
</map>
</div>

</div>
</div>
<a id="a92b63e772873a034bea01d26f382ed57" name="a92b63e772873a034bea01d26f382ed57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a92b63e772873a034bea01d26f382ed57">&#9670;&#160;</a></span>LogInfo() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;SPI device initialized successfully&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aef91cc299dacb19ea73324acf8c8ff2c" name="aef91cc299dacb19ea73324acf8c8ff2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef91cc299dacb19ea73324acf8c8ff2c">&#9670;&#160;</a></span>LogInfo() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;SPI transfer succeeded&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a06c5e5172ac494575aa45dd42bfc32f5" name="a06c5e5172ac494575aa45dd42bfc32f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06c5e5172ac494575aa45dd42bfc32f5">&#9670;&#160;</a></span>RtlCopyMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RtlCopyMemory </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;</td>          <td class="paramname"><span class="paramname"><em>SpiConfig</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">sizeof(<a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>)</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a06c5e5172ac494575aa45dd42bfc32f5_cgraph.png" border="0" usemap="#aspi__device_8c_a06c5e5172ac494575aa45dd42bfc32f5_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a06c5e5172ac494575aa45dd42bfc32f5_cgraph" id="aspi__device_8c_a06c5e5172ac494575aa45dd42bfc32f5_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,122,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="170,29,275,56"/>
<area shape="poly" title=" " alt="" coords="122,40,154,40,154,45,122,45"/>
<area shape="poly" title=" " alt="" coords="195,30,190,21,193,11,204,5,222,3,242,5,252,12,249,16,240,10,222,8,206,10,197,15,195,20,199,28"/>
</map>
</div>

</div>
</div>
<a id="a052b57a96b994325a574bcb9f3db837a" name="a052b57a96b994325a574bcb9f3db837a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a052b57a96b994325a574bcb9f3db837a">&#9670;&#160;</a></span>SpiDeviceCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID SpiDeviceCleanup </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceCleanup - u6e05u7406SPIu8bbeu5907u8d44u6e90</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a052b57a96b994325a574bcb9f3db837a_cgraph.png" border="0" usemap="#aspi__device_8c_a052b57a96b994325a574bcb9f3db837a_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a052b57a96b994325a574bcb9f3db837a_cgraph" id="aspi__device_8c_a052b57a96b994325a574bcb9f3db837a_cgraph">
<area shape="rect" title=" " alt="" coords="5,80,135,107"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="188,29,294,56"/>
<area shape="poly" title=" " alt="" coords="117,77,178,58,180,63,119,82"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="183,80,299,107"/>
<area shape="poly" title=" " alt="" coords="135,91,167,91,167,96,135,96"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="206,131,276,157"/>
<area shape="poly" title=" " alt="" coords="119,105,192,127,190,132,117,110"/>
<area shape="poly" title=" " alt="" coords="211,30,206,21,210,11,222,5,241,3,261,5,272,12,270,16,260,10,241,8,223,10,214,15,212,20,216,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="347,105,475,132"/>
<area shape="poly" title=" " alt="" coords="276,136,331,128,332,133,277,141"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="364,156,459,183"/>
<area shape="poly" title=" " alt="" coords="277,147,349,157,348,163,276,152"/>
</map>
</div>

</div>
</div>
<a id="ae2be7c6b48ddf5b08876e1115879469d" name="ae2be7c6b48ddf5b08876e1115879469d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2be7c6b48ddf5b08876e1115879469d">&#9670;&#160;</a></span>SpiDeviceGetStatistics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceGetStatistics </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ <a class="el" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a></td>          <td class="paramname"><span class="paramname"><em>Statistics</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceGetStatistics - u83b7u53d6SPIu8bbeu5907u7edfu8ba1u4fe1u606f</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Statistics</td><td>u7edfu8ba1u4fe1u606fu7ed3u6784</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_ae2be7c6b48ddf5b08876e1115879469d_cgraph.png" border="0" usemap="#aspi__device_8c_ae2be7c6b48ddf5b08876e1115879469d_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_ae2be7c6b48ddf5b08876e1115879469d_cgraph" id="aspi__device_8c_ae2be7c6b48ddf5b08876e1115879469d_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,163,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="211,29,316,56"/>
<area shape="poly" title=" " alt="" coords="162,54,195,50,196,55,163,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="228,80,298,107"/>
<area shape="poly" title=" " alt="" coords="163,76,213,84,212,89,162,82"/>
<area shape="poly" title=" " alt="" coords="235,30,230,21,233,11,245,5,263,3,283,5,294,12,291,16,281,10,263,8,246,10,237,15,235,20,239,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="364,55,492,81"/>
<area shape="poly" title=" " alt="" coords="299,85,348,78,349,83,299,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="380,105,476,132"/>
<area shape="poly" title=" " alt="" coords="299,96,366,106,365,112,299,101"/>
</map>
</div>

</div>
</div>
<a id="a6939e12311ec72f975bcd03a4250a3e2" name="a6939e12311ec72f975bcd03a4250a3e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6939e12311ec72f975bcd03a4250a3e2">&#9670;&#160;</a></span>SpiDeviceInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>SpiConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceInitialize - u521du59cbu5316SPIu8bbeu5907</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">SpiConfig</td><td>SPIu914du7f6e</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a6939e12311ec72f975bcd03a4250a3e2_cgraph.png" border="0" usemap="#aspi__device_8c_a6939e12311ec72f975bcd03a4250a3e2_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a6939e12311ec72f975bcd03a4250a3e2_cgraph" id="aspi__device_8c_a6939e12311ec72f975bcd03a4250a3e2_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,135,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="183,29,288,56"/>
<area shape="poly" title=" " alt="" coords="135,55,167,50,168,56,136,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="200,80,270,107"/>
<area shape="poly" title=" " alt="" coords="136,75,185,83,185,88,135,81"/>
<area shape="poly" title=" " alt="" coords="207,30,202,21,205,11,217,5,235,3,255,5,266,12,263,16,253,10,235,8,218,10,209,15,207,20,211,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="336,55,464,81"/>
<area shape="poly" title=" " alt="" coords="271,85,320,78,321,83,271,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,448,132"/>
<area shape="poly" title=" " alt="" coords="271,96,338,106,337,112,271,101"/>
</map>
</div>

</div>
</div>
<a id="a3bc98267d67ee8988179bde952efaa87" name="a3bc98267d67ee8988179bde952efaa87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3bc98267d67ee8988179bde952efaa87">&#9670;&#160;</a></span>SpiDeviceRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceRead </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesRead</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceRead - u4eceSu5907u8bfbu53d6u6570u636e</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>u5bc4u5b58u5668u5730u5740 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Buffer</td><td>u6570u636eu7f13u51b2u533a </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>u7f13u51b2u533au957fu5ea6 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesRead</td><td>u5b9eu9645u8bfbu53d6u7684u5b57u8282u6570</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a3bc98267d67ee8988179bde952efaa87_cgraph.png" border="0" usemap="#aspi__device_8c_a3bc98267d67ee8988179bde952efaa87_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a3bc98267d67ee8988179bde952efaa87_cgraph" id="aspi__device_8c_a3bc98267d67ee8988179bde952efaa87_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,118,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="166,29,271,56"/>
<area shape="poly" title=" " alt="" coords="118,56,150,51,151,56,118,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="183,80,254,107"/>
<area shape="poly" title=" " alt="" coords="118,74,168,83,167,88,118,80"/>
<area shape="poly" title=" " alt="" coords="191,30,186,21,190,11,201,5,218,3,237,5,248,12,245,16,236,10,218,8,202,10,194,15,192,20,196,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="319,55,447,81"/>
<area shape="poly" title=" " alt="" coords="254,85,303,78,304,83,254,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="336,105,431,132"/>
<area shape="poly" title=" " alt="" coords="254,96,321,106,320,112,254,101"/>
</map>
</div>

</div>
</div>
<a id="a2428921b9d71ab9d24f34e0a7b23487c" name="a2428921b9d71ab9d24f34e0a7b23487c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2428921b9d71ab9d24f34e0a7b23487c">&#9670;&#160;</a></span>SpiDeviceTransfer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceTransfer </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceTransfer - u6267u884cSPIu4f20u8f93</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TransferPacket</td><td>u4f20u8f93u6570u636eu5305 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TimeoutMs</td><td>u8d85u65f6u65f6u95f4(u6bebu79d2)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_a2428921b9d71ab9d24f34e0a7b23487c_cgraph.png" border="0" usemap="#aspi__device_8c_a2428921b9d71ab9d24f34e0a7b23487c_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_a2428921b9d71ab9d24f34e0a7b23487c_cgraph" id="aspi__device_8c_a2428921b9d71ab9d24f34e0a7b23487c_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,135,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="183,29,288,56"/>
<area shape="poly" title=" " alt="" coords="135,55,167,50,168,56,136,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="200,80,270,107"/>
<area shape="poly" title=" " alt="" coords="136,75,185,83,185,88,135,81"/>
<area shape="poly" title=" " alt="" coords="207,30,202,21,205,11,217,5,235,3,255,5,266,12,263,16,253,10,235,8,218,10,209,15,207,20,211,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="336,55,464,81"/>
<area shape="poly" title=" " alt="" coords="271,85,320,78,321,83,271,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,448,132"/>
<area shape="poly" title=" " alt="" coords="271,96,338,106,337,112,271,101"/>
</map>
</div>

</div>
</div>
<a id="ae90ccf3d865bebb54c2c76e10fcbcaa8" name="ae90ccf3d865bebb54c2c76e10fcbcaa8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae90ccf3d865bebb54c2c76e10fcbcaa8">&#9670;&#160;</a></span>SpiDeviceWrite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS SpiDeviceWrite </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesWritten</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>SpiDeviceWrite - u5411SPIu8bbeu5907u5199u5165u6570u636e</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDFu8bbeu5907u5bf9u8c61 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>u5bc4u5b58u5668u5730u5740 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Buffer</td><td>u6570u636eu7f13u51b2u533a </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>u7f13u51b2u533au957fu5ea6 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesWritten</td><td>u5b9eu9645u5199u5165u7684u5b57u8282u6570</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS u72b6u6001u7801 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="spi__device_8c_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph.png" border="0" usemap="#aspi__device_8c_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph" loading="lazy" alt=""/></div>
<map name="aspi__device_8c_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph" id="aspi__device_8c_ae90ccf3d865bebb54c2c76e10fcbcaa8_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,117,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="165,29,270,56"/>
<area shape="poly" title=" " alt="" coords="117,56,149,51,150,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="182,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,167,83,166,88,117,80"/>
<area shape="poly" title=" " alt="" coords="190,30,185,21,189,11,200,5,217,3,236,5,246,12,243,16,234,10,217,8,201,10,193,15,191,20,195,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="318,55,446,81"/>
<area shape="poly" title=" " alt="" coords="253,85,302,78,303,83,253,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,430,132"/>
<area shape="poly" title=" " alt="" coords="253,96,320,106,319,112,253,101"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="aee3e2abd9dd27ddfc3e158a9ffce1746" name="aee3e2abd9dd27ddfc3e158a9ffce1746"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee3e2abd9dd27ddfc3e158a9ffce1746">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Exit __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aff83b2530e0944d77d4ee0965b41ad89" name="aff83b2530e0944d77d4ee0965b41ad89"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff83b2530e0944d77d4ee0965b41ad89">&#9670;&#160;</a></span>ConfigurationMemory</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> ConfigurationMemory = NULL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af9a881dabb7ea1e15ee2808cca09fd6a" name="af9a881dabb7ea1e15ee2808cca09fd6a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9a881dabb7ea1e15ee2808cca09fd6a">&#9670;&#160;</a></span>DelayInMicroseconds</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet DelayInMicroseconds = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae49d231a428d107c888f925e845daf62" name="ae49d231a428d107c888f925e845daf62"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae49d231a428d107c888f925e845daf62">&#9670;&#160;</a></span>DeviceInitialized</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics DeviceInitialized = FALSE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abf852046373359fb294f66a784b38263" name="abf852046373359fb294f66a784b38263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf852046373359fb294f66a784b38263">&#9670;&#160;</a></span>DeviceType</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig DeviceType = <a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab">HalDeviceSPI</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0544c3fe466e421738dae463968b70ba" name="a0544c3fe466e421738dae463968b70ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0544c3fe466e421738dae463968b70ba">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        InterlockedIncrement(&amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;TransactionCount)</div>
<div class="ttc" id="agpio__device_8c_html_a7b6a29716fe6f8117de54edaedc57974"><div class="ttname"><a href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></div><div class="ttdeci">RtlCopyMemory &amp; deviceContext(GPIO_DEVICE_CONFIG)</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="ab9707a002fb8033fdc202e8c8b8f8569" name="ab9707a002fb8033fdc202e8c8b8f8569"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab9707a002fb8033fdc202e8c8b8f8569">&#9670;&#160;</a></span>ErrorCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics ErrorCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaea9f9b32650901ecb0d31cb5066cd7f" name="aaea9f9b32650901ecb0d31cb5066cd7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaea9f9b32650901ecb0d31cb5066cd7f">&#9670;&#160;</a></span>Flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet Flags = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a96ba6885a1d23da9ee577cfc9b91ae60" name="a96ba6885a1d23da9ee577cfc9b91ae60"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96ba6885a1d23da9ee577cfc9b91ae60">&#9670;&#160;</a></span>HalHandle</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="hal__interface_8h.html#a2f4ba870132c1fd57e2d74ba94e39805">HAL_DEVICE_HANDLE</a> HalHandle</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a40d2c447ac37fcd86673f2a11b2ca094" name="a40d2c447ac37fcd86673f2a11b2ca094"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40d2c447ac37fcd86673f2a11b2ca094">&#9670;&#160;</a></span>PrivateData</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig PrivateData = <a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0470f3b47bad91bd5e08004c87a8d98a" name="a0470f3b47bad91bd5e08004c87a8d98a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0470f3b47bad91bd5e08004c87a8d98a">&#9670;&#160;</a></span>PrivateDataSize</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig PrivateDataSize = sizeof(<a class="el" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2d4e25dc12a54c28261d5ba390e3aa19" name="a2d4e25dc12a54c28261d5ba390e3aa19"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d4e25dc12a54c28261d5ba390e3aa19">&#9670;&#160;</a></span>PSPI_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">* <a class="el" href="spi__core_8c.html#a5e8b4813a61b999753aee353d4944c23">PSPI_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac2677e024009c29e2bcee99e0c32c735" name="ac2677e024009c29e2bcee99e0c32c735"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac2677e024009c29e2bcee99e0c32c735">&#9670;&#160;</a></span>ReadBuffer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet ReadBuffer = Buffer</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a1d0f6e5a27e5a31ee40de06efe3ba233" name="a1d0f6e5a27e5a31ee40de06efe3ba233"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d0f6e5a27e5a31ee40de06efe3ba233">&#9670;&#160;</a></span>ReadLength</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet ReadLength = Length</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa226b0d93154d552caefa2ca1550155c" name="aa226b0d93154d552caefa2ca1550155c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa226b0d93154d552caefa2ca1550155c">&#9670;&#160;</a></span>SPI_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="spi__core_8c.html#a2a4a689dbe0ef33045635ddfa5db3194">SPI_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="addbc5753ca32543e25382ea5a386d59b" name="addbc5753ca32543e25382ea5a386d59b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#addbc5753ca32543e25382ea5a386d59b">&#9670;&#160;</a></span>SpiConfig</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> SpiConfig = NULL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9611b3a00430a86619b5923de30f9fdb" name="a9611b3a00430a86619b5923de30f9fdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9611b3a00430a86619b5923de30f9fdb">&#9670;&#160;</a></span>status</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return status</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= WdfMemoryCreate(</div>
<div class="line">        WDF_NO_OBJECT_ATTRIBUTES,</div>
<div class="line">        NonPagedPoolNx,</div>
<div class="line">        0,</div>
<div class="line">        <span class="keyword">sizeof</span>(<a class="code hl_typedef" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>),</div>
<div class="line">        &amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;ConfigurationMemory,</div>
<div class="line">        (PVOID*)&amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;SpiConfig</div>
<div class="line">    )</div>
<div class="ttc" id="akmdf__spi_8h_html_aa750b6896a759b95054bedea9ad132d9"><div class="ttname"><a href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a></div><div class="ttdeci">struct _SPI_CONFIG SPI_CONFIG</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a77b4762318f24dff847f94f382cfeea6" name="a77b4762318f24dff847f94f382cfeea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77b4762318f24dff847f94f382cfeea6">&#9670;&#160;</a></span>STATUS_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return STATUS_SUCCESS</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a63212990a463669c2face6cfbfd28d26" name="a63212990a463669c2face6cfbfd28d26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63212990a463669c2face6cfbfd28d26">&#9670;&#160;</a></span>TransactionCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics TransactionCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a11ec07dcb5c1cea421134a0b149443a5" name="a11ec07dcb5c1cea421134a0b149443a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11ec07dcb5c1cea421134a0b149443a5">&#9670;&#160;</a></span>WdfDevice</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> WdfDevice = Device</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad26ded9b73e8b14b4117614b39440d86" name="ad26ded9b73e8b14b4117614b39440d86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad26ded9b73e8b14b4117614b39440d86">&#9670;&#160;</a></span>WriteBuffer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet WriteBuffer = <a class="el" href="#a7a33fa49b57196f5722a55916cff0a52">writeBuffer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7a33fa49b57196f5722a55916cff0a52" name="a7a33fa49b57196f5722a55916cff0a52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a33fa49b57196f5722a55916cff0a52">&#9670;&#160;</a></span>writeBuffer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a>&amp;[1] writeBuffer = RegisterAddress</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a530eca3d7e36c5dde60c5e49dd7b2b34" name="a530eca3d7e36c5dde60c5e49dd7b2b34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a530eca3d7e36c5dde60c5e49dd7b2b34">&#9670;&#160;</a></span>WriteLength</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet WriteLength = 1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_340a9c6f51eab01289f9b188a5d35565.html">devices</a></li><li class="navelem"><a href="spi__device_8c.html">spi_device.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
