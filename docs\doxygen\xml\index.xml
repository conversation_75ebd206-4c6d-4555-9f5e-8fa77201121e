<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygenindex xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="index.xsd" version="1.14.0" xml:lang="en-US">
  <compound refid="struct__BUS__CONFIG" kind="struct"><name>_BUS_CONFIG</name>
    <member refid="struct__BUS__CONFIG_1ae19319720b3c056dc60261c26e1dd43e" kind="variable"><name>BusSpecificConfig</name></member>
    <member refid="struct__BUS__CONFIG_1ab3667ea857ae85e39b62c3a39b8d6761" kind="variable"><name>BusType</name></member>
    <member refid="struct__BUS__CONFIG_1a76efcd63df259ff464d8b6e9be040134" kind="variable"><name>Flags</name></member>
    <member refid="struct__BUS__CONFIG_1a4dc4b392465c874dc7b75256fa2ac00a" kind="variable"><name>Speed</name></member>
  </compound>
  <compound refid="struct__BUS__TRANSFER__PACKET" kind="struct"><name>_BUS_TRANSFER_PACKET</name>
    <member refid="struct__BUS__TRANSFER__PACKET_1ab2d70f7d13f1b96499d1ed9fa11b881b" kind="variable"><name>Buffer</name></member>
    <member refid="struct__BUS__TRANSFER__PACKET_1acd85870608d805b91015bd3e0c4302c4" kind="variable"><name>BufferLength</name></member>
    <member refid="struct__BUS__TRANSFER__PACKET_1ab3b945cbfe042fe6d4cf96ab9517ae6a" kind="variable"><name>Context</name></member>
    <member refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" kind="variable"><name>Status</name></member>
  </compound>
  <compound refid="struct__CORE__CONFIG" kind="struct"><name>_CORE_CONFIG</name>
    <member refid="struct__CORE__CONFIG_1aa39d274049e18efc4f5e946b8567e36b" kind="variable"><name>DefaultTimeoutMs</name></member>
    <member refid="struct__CORE__CONFIG_1a6416f3952ddddc1f822393b7daab87a1" kind="variable"><name>EnableVerboseLogging</name></member>
    <member refid="struct__CORE__CONFIG_1a045726e6262e09620c456b27c0c93189" kind="variable"><name>MaxDevices</name></member>
  </compound>
  <compound refid="struct__CORE__DEVICE__CONTEXT" kind="struct"><name>_CORE_DEVICE_CONTEXT</name>
    <member refid="struct__CORE__DEVICE__CONTEXT_1aaad78692f657bc883fec1dcb3d55bf75" kind="variable"><name>DefaultQueue</name></member>
    <member refid="struct__CORE__DEVICE__CONTEXT_1ac7d1bbe75efb04ad822e9dec95c9e1b1" kind="variable"><name>Device</name></member>
    <member refid="struct__CORE__DEVICE__CONTEXT_1af91bee2f060625339121b21f385b5756" kind="variable"><name>HalContext</name></member>
    <member refid="struct__CORE__DEVICE__CONTEXT_1a9530788176323d8e99978ff3879a58dd" kind="variable"><name>Status</name></member>
  </compound>
  <compound refid="struct__CORE__DEVICE__STATUS" kind="struct"><name>_CORE_DEVICE_STATUS</name>
    <member refid="struct__CORE__DEVICE__STATUS_1aa10d05e79e202f43369c77ef7cb7d984" kind="variable"><name>IsRemoved</name></member>
    <member refid="struct__CORE__DEVICE__STATUS_1ada892e967656ee111fb070b845eea190" kind="variable"><name>OutstandingIoCount</name></member>
    <member refid="struct__CORE__DEVICE__STATUS_1a29681c789642fba294bddef8ee47e8de" kind="variable"><name>PnpState</name></member>
    <member refid="struct__CORE__DEVICE__STATUS_1ade9c430654dce161c95c507c130bba13" kind="variable"><name>Statistics</name></member>
  </compound>
  <compound refid="struct__CORE__REQUEST__CONTEXT" kind="struct"><name>_CORE_REQUEST_CONTEXT</name>
    <member refid="struct__CORE__REQUEST__CONTEXT_1a1d93ca72fec998e4b91d9ce39029717c" kind="variable"><name>DeviceContext</name></member>
    <member refid="struct__CORE__REQUEST__CONTEXT_1a559291b7d96c7f687cc512a50cf451e2" kind="variable"><name>ListEntry</name></member>
    <member refid="struct__CORE__REQUEST__CONTEXT_1acb3c72892f6fb6655c15c8af18642259" kind="variable"><name>Request</name></member>
    <member refid="struct__CORE__REQUEST__CONTEXT_1a1bcaa1182dbb6013ddbcb1ed690d1a60" kind="variable"><name>StartTime</name></member>
  </compound>
  <compound refid="struct__CORE__STATISTICS" kind="struct"><name>_CORE_STATISTICS</name>
    <member refid="struct__CORE__STATISTICS_1aad92a7c93dae88c688f4ffbb0b6fb98f" kind="variable"><name>ActiveRequests</name></member>
    <member refid="struct__CORE__STATISTICS_1a9723c956a32512edfdfa4c5c1aca1b2d" kind="variable"><name>ErrorCount</name></member>
    <member refid="struct__CORE__STATISTICS_1ad0ac4cfc20318f0d788f149c2876e3d2" kind="variable"><name>FailedRequests</name></member>
    <member refid="struct__CORE__STATISTICS_1aa76c1124eab25e55a5851d8ef7676947" kind="variable"><name>SuccessfulRequests</name></member>
    <member refid="struct__CORE__STATISTICS_1a7e5aceedcb2ad4ea827f2c3c2272a4d6" kind="variable"><name>TotalRequests</name></member>
  </compound>
  <compound refid="struct__CORE__STATUS" kind="struct"><name>_CORE_STATUS</name>
    <member refid="struct__CORE__STATUS_1ac77e8bd906c72c507fe814cd96cd8a4f" kind="variable"><name>ActiveDeviceCount</name></member>
    <member refid="struct__CORE__STATUS_1a9a45291b6d443d9ed668c3da8cb5715e" kind="variable"><name>GlobalStatistics</name></member>
    <member refid="struct__CORE__STATUS_1a0882ddefacbd218a4ac5c51db2f1f44f" kind="variable"><name>IsAcceptingNewRequests</name></member>
    <member refid="struct__CORE__STATUS_1aab7110a3de57c3c1d2ea459987fb47e7" kind="variable"><name>StatusLock</name></member>
  </compound>
  <compound refid="struct__DEVICE__CONTEXT" kind="struct"><name>_DEVICE_CONTEXT</name>
    <member refid="struct__DEVICE__CONTEXT_1a9fa4b3d648523f487271092bb9c8d439" kind="variable"><name>BusContext</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a006a8f4a8cfb5bd18c0c7c06ffa3f594" kind="variable"><name>BusType</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a653432eea87ead945fb98ca4ab7ae91a" kind="variable"><name>CurrentTouchPoint</name></member>
    <member refid="struct__DEVICE__CONTEXT_1add6c5298dc182d42a41aa7c2aa5f6692" kind="variable"><name>DeviceId</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a5a08d5bb4eebbb38afe08d3316d4d18e" kind="variable"><name>DeviceInterfaceGuid</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a9f7b447529327e68b98065bd102e52ab" kind="variable"><name>DevicePowered</name></member>
    <member refid="struct__DEVICE__CONTEXT_1afc54ebe2e3da6887c48fa5e0d91fbcf1" kind="variable"><name>DeviceStarted</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a6bf5b69c0bc2a73a57b9179c1cff182a" kind="variable"><name>HidCaps</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a7cdeae347c2e97c47da592cd8f9cf659" kind="variable"><name>HidPreparsedData</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a13a8e81167539b908635abc3085f5c74" kind="variable"><name>HidReportDescriptor</name></member>
    <member refid="struct__DEVICE__CONTEXT_1aa5e78183081bdad3dabf8e82d5e1503a" kind="variable"><name>HidReportDescriptorLength</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a43621987690ebe2010be900cf3a78207" kind="variable"><name>Interrupt</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a9c5d6e826cc40727b53a6986279e8ab1" kind="variable"><name>InterruptInPipe</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a0ba2405294828f82bb2e4d967492fffc" kind="variable"><name>InterruptInPipeMaxPacketSize</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a0f2f683dcf52ec000fcb5dbef24806cb" kind="variable"><name>InterruptMode</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a30de6520f49b6aca93db8edc20ef678b" kind="variable"><name>InterruptVector</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a446ce7337655abb4746643c1e67e7e5f" kind="variable"><name>MemoryBasePA</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a24015f961586d153ecb9dbc79bb5349d" kind="variable"><name>MemoryBaseVA</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a4c70db8492a405e4a7fa5165be1fa996" kind="variable"><name>MemoryLength</name></member>
    <member refid="struct__DEVICE__CONTEXT_1adb2f242d93cc41089e8f11cfdf97020d" kind="variable"><name>PowerState</name></member>
    <member refid="struct__DEVICE__CONTEXT_1ae3c61fe5c97375b267608461fa8e675b" kind="variable"><name>TouchDataLock</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a593a8782541d01f19b83b27a01b58182" kind="variable"><name>UsbDevice</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a910de0fe5eeb34429b4a1f4163f79e6a" kind="variable"><name>UsbInterface</name></member>
    <member refid="struct__DEVICE__CONTEXT_1ad148829b906d0372071349682ae26196" kind="variable"><name>VendorId</name></member>
    <member refid="struct__DEVICE__CONTEXT_1a7da84a71479cecd3ea601a3500d2273c" kind="variable"><name>WdfDevice</name></member>
  </compound>
  <compound refid="struct__DEVICE__INFO" kind="struct"><name>_DEVICE_INFO</name>
    <member refid="struct__DEVICE__INFO_1aecb40da598eb07be27b2fa762a56cb7c" kind="variable"><name>BusType</name></member>
    <member refid="struct__DEVICE__INFO_1ad1b5079ed73fc6009607d35543e275ff" kind="variable"><name>DeviceId</name></member>
    <member refid="struct__DEVICE__INFO_1a7a411dffe28d739e918801b1a3a60517" kind="variable"><name>FirmwareVersion</name></member>
    <member refid="struct__DEVICE__INFO_1affc12ad434fe559e681e77ee32b1e3da" kind="variable"><name>PowerState</name></member>
    <member refid="struct__DEVICE__INFO_1a7f74be1556d3a954c0540a166ca6221f" kind="variable"><name>VendorId</name></member>
  </compound>
  <compound refid="struct__DEVICE__INIT__CONFIG" kind="struct"><name>_DEVICE_INIT_CONFIG</name>
    <member refid="struct__DEVICE__INIT__CONFIG_1a55239223743c68f1900157add76e756b" kind="variable"><name>BusType</name></member>
    <member refid="struct__DEVICE__INIT__CONFIG_1a7ebe4b67b3f9303e10ddace98e7f69ff" kind="variable"><name>DeviceId</name></member>
    <member refid="struct__DEVICE__INIT__CONFIG_1a4bb7090072ead7f949e84d6de069fe54" kind="variable"><name>DeviceInterfaceGuid</name></member>
    <member refid="struct__DEVICE__INIT__CONFIG_1a354a892269e5bc5d7859d3c99785d087" kind="variable"><name>EnableIdlePowerManagement</name></member>
    <member refid="struct__DEVICE__INIT__CONFIG_1aca71f0560489dd375ac4baa0e0b35dd7" kind="variable"><name>SymbolicLinkName</name></member>
    <member refid="struct__DEVICE__INIT__CONFIG_1accbb4ad53561dc015a600f3684e552e6" kind="variable"><name>VendorId</name></member>
  </compound>
  <compound refid="struct__DRIVER__CONFIG" kind="struct"><name>_DRIVER_CONFIG</name>
    <member refid="struct__DRIVER__CONFIG_1a000ada599f1b619fbded0ff23963c1f4" kind="variable"><name>DeviceSymLinkName</name></member>
    <member refid="struct__DRIVER__CONFIG_1ad02e8885eeb5542df2e816c56a3c89c5" kind="variable"><name>DriverFlags</name></member>
    <member refid="struct__DRIVER__CONFIG_1a0ad249f48409a76f0a5818a71cb02f97" kind="variable"><name>DriverType</name></member>
    <member refid="struct__DRIVER__CONFIG_1a46d28956be671bbbda04a5e43175b210" kind="variable"><name>EnableEventLogging</name></member>
    <member refid="struct__DRIVER__CONFIG_1aee9c4a267f9198351079c9287c248ff5" kind="variable"><name>EnableWPP</name></member>
    <member refid="struct__DRIVER__CONFIG_1a462a9835cac3e90e89501fd667ee0dff" kind="variable"><name>LogLevel</name></member>
    <member refid="struct__DRIVER__CONFIG_1a390b2848a9d77722f7dd36b28d87b712" kind="variable"><name>PowerFlags</name></member>
    <member refid="struct__DRIVER__CONFIG_1a28c1020f27519d547738476aee8ae8d2" kind="variable"><name>Version</name></member>
  </compound>
  <compound refid="struct__DRIVER__CONTEXT" kind="struct"><name>_DRIVER_CONTEXT</name>
    <member refid="struct__DRIVER__CONTEXT_1a47ece9e7bf05f77968bf674a4342396a" kind="variable"><name>DeviceCount</name></member>
    <member refid="struct__DRIVER__CONTEXT_1a008388fc65c89b1ae3992fc6a4aa27f3" kind="variable"><name>Devices</name></member>
    <member refid="struct__DRIVER__CONTEXT_1a36a8e8b359a2c287561b83b1fee106f8" kind="variable"><name>Driver</name></member>
    <member refid="struct__DRIVER__CONTEXT_1aa7ddcfc01ffa413221702e66f502fc1d" kind="variable"><name>DriverConfig</name></member>
  </compound>
  <compound refid="struct__DRIVER__DEVICE__CONTEXT" kind="struct"><name>_DRIVER_DEVICE_CONTEXT</name>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1a0f4f257c9c6dd29fede7952b753aa5de" kind="variable"><name>DefaultQueue</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1a1c0d47fc1537af41807ac5ad7f202e71" kind="variable"><name>DeviceInitialized</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1a16494bafe3313ea28a5aa9f2f435bc56" kind="variable"><name>DeviceState</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1a3a1f5cbb1b49a99ce046bf77438f1152" kind="variable"><name>DeviceSymLink</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1aee0bdebc6bfac5c4a1320abc8b7611a4" kind="variable"><name>Interrupt</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1aa1f623bacb4440ecf53c66d85d6d0a22" kind="variable"><name>PrivateData</name></member>
    <member refid="struct__DRIVER__DEVICE__CONTEXT_1adfee791f2d1c3a54d4dfeb6cdea8add8" kind="variable"><name>WdfDevice</name></member>
  </compound>
  <compound refid="struct__DRIVER__STATISTICS" kind="struct"><name>_DRIVER_STATISTICS</name>
    <member refid="struct__DRIVER__STATISTICS_1a752819e9a3d5e0c0807f1ad785cf8b95" kind="variable"><name>DeviceCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1a7b537923e8a94ee50fad5096b12cf604" kind="variable"><name>ErrorCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1a350abc66d99d5fdffa1ecf214dd2520d" kind="variable"><name>FailedIoCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1a20af12b36a08e1504e7c98eba79e990c" kind="variable"><name>InterruptCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1a0be3bf05b72dc271420c5e4b508cc497" kind="variable"><name>SuccessfulIoCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1ac3e1ea47bae6fb1bc0f70721fb1a6555" kind="variable"><name>TotalIoCount</name></member>
    <member refid="struct__DRIVER__STATISTICS_1a5719b06dd8f5b9ccd3239f5de2c0c60c" kind="variable"><name>UptimeSeconds</name></member>
  </compound>
  <compound refid="struct__DRIVER__VERSION" kind="struct"><name>_DRIVER_VERSION</name>
    <member refid="struct__DRIVER__VERSION_1a0f5edd5b90ea7d0767d56293b15a36f5" kind="variable"><name>Build</name></member>
    <member refid="struct__DRIVER__VERSION_1a94c8bf9509d348f5cd15c358a92eb0ce" kind="variable"><name>Major</name></member>
    <member refid="struct__DRIVER__VERSION_1aaa3047faacbaba04237817cbf6c9f0e9" kind="variable"><name>Minor</name></member>
    <member refid="struct__DRIVER__VERSION_1a7869e35e89c7ca48a53d1e555a4123cc" kind="variable"><name>Revision</name></member>
  </compound>
  <compound refid="struct__ERROR__CODE__ENTRY" kind="struct"><name>_ERROR_CODE_ENTRY</name>
    <member refid="struct__ERROR__CODE__ENTRY_1a93ed5d60e5be34b389dbc2e34ee5c0a1" kind="variable"><name>Code</name></member>
    <member refid="struct__ERROR__CODE__ENTRY_1a1a31bfac0862609ac44bfee61400152e" kind="variable"><name>Description</name></member>
  </compound>
  <compound refid="struct__GPIO__DEVICE__CONFIG" kind="struct"><name>_GPIO_DEVICE_CONFIG</name>
    <member refid="struct__GPIO__DEVICE__CONFIG_1aede019215cfc30d3e4610984d3903eb5" kind="variable"><name>DebounceTime</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1a664ba2cda132fa14533710f1c3f822b4" kind="variable"><name>DeviceContext</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1a6a8cbfb763fb3333f78514eb57a6f9d1" kind="variable"><name>DeviceType</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1afd346bb1662b8fd0787e95a58459801b" kind="variable"><name>Direction</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1aa569540357376717aca1aa0d6c94a6c4" kind="variable"><name>EnableInterrupt</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1aed18d39f9d22f9f8fb5c65dc0c1887d8" kind="variable"><name>InterruptType</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1a8070a58e69d1707fa6295dbe6709ea58" kind="variable"><name>PinNumber</name></member>
    <member refid="struct__GPIO__DEVICE__CONFIG_1a612f8dcb2e2376c49891b7f8f80460ce" kind="variable"><name>Polarity</name></member>
  </compound>
  <compound refid="struct__GPIO__PIN__CONFIG" kind="struct"><name>_GPIO_PIN_CONFIG</name>
    <member refid="struct__GPIO__PIN__CONFIG_1a8d011afe85a8ab79a1f0ab8a0aeeee9f" kind="variable"><name>DebounceTime</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1aa3d8c873130efce02f77b07f742a05ff" kind="variable"><name>Direction</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1a16e25974fff79d31225fbb4402059d42" kind="variable"><name>InterruptType</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1abd8ea283a387003ee58595a12fb2b5ec" kind="variable"><name>OpenDrain</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1acc85fb800144aa8336d910544113014c" kind="variable"><name>PinNumber</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1a3298588b14792c3fdd790313352c673e" kind="variable"><name>Polarity</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1afd66b730c3f6bec3b15b28ccd0832f85" kind="variable"><name>PullDown</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1a0de26b1a7ce28b0b9aa018fe961fdf60" kind="variable"><name>PullUp</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1a6065b7e884557238880cacf2002110d3" kind="variable"><name>SpbConnectionId</name></member>
    <member refid="struct__GPIO__PIN__CONFIG_1aec4597ae991a215e755336600b15e60a" kind="variable"><name>SpbDeviceObject</name></member>
  </compound>
  <compound refid="struct__GPIO__PIN__CONTEXT" kind="struct"><name>_GPIO_PIN_CONTEXT</name>
    <member refid="struct__GPIO__PIN__CONTEXT_1a44db5b39e5c14ce83ac0637da253fb47" kind="variable"><name>EvtGpioInterruptDpc</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a4b4c364430ee4f37c01413a6552df5d0" kind="variable"><name>EvtGpioInterruptIsr</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1ae1602e3f0f02ccfe7b30fdaa89b017a7" kind="variable"><name>Lock</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a4dd51420a9c389d77c721c25d4349927" kind="variable"><name>Pins</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a81f98266b74a3a8d80a632eead026240" kind="variable"><name>SpbIoTarget</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" kind="variable"><name>STATUS_INVALID_PARAMETER</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a9e971cde7006142d4b5ac56689228e0b" kind="variable"><name>WdfDevice</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" kind="function"><name>GPIOInitialize</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" kind="function"><name>if</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" kind="function"><name>LogError</name></member>
    <member refid="struct__GPIO__PIN__CONTEXT_1a846739f679fc9ddea85cb263aa72dc9f" kind="function"><name>WdfSpinLockRelease</name></member>
  </compound>
  <compound refid="struct__HAL__DEVICE__CONFIG" kind="struct"><name>_HAL_DEVICE_CONFIG</name>
    <member refid="struct__HAL__DEVICE__CONFIG_1a093ec6f202f2fcee9e19b91ab1b43bb8" kind="variable"><name>DeviceType</name></member>
    <member refid="struct__HAL__DEVICE__CONFIG_1a4d27003c8ea318d8a2254d7f91e7a9c9" kind="variable"><name>Flags</name></member>
    <member refid="struct__HAL__DEVICE__CONFIG_1aa55a8f607da92a0193ec09a87beb68ee" kind="variable"><name>PrivateData</name></member>
    <member refid="struct__HAL__DEVICE__CONFIG_1a8e95d0419e53dcdbffbea78f59ba208e" kind="variable"><name>PrivateDataSize</name></member>
    <member refid="struct__HAL__DEVICE__CONFIG_1a0aada8eaa1ab32c1ce95904fe980b69a" kind="variable"><name>ResourceCount</name></member>
    <member refid="struct__HAL__DEVICE__CONFIG_1a5d89313a9b4bdf1ddb5a95273142cbe0" kind="variable"><name>Resources</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE" kind="struct"><name>_HAL_RESOURCE</name>
    <member refid="struct__HAL__RESOURCE_1ab23513080e2ce560fea6c709d32f5a8d" kind="variable"><name>Type</name></member>
    <member refid="struct__HAL__RESOURCE_1a8965cb875f114a21a9cc0fa8495a7e4e" kind="variable"><name>u</name></member>
  </compound>
  <compound refid="union__HAL__RESOURCE_8u" kind="union"><name>_HAL_RESOURCE.u</name>
    <member refid="union__HAL__RESOURCE_8u_1a6e4b38bfd57741ac1597c440a1c98074" kind="variable"><name>Bus</name></member>
    <member refid="union__HAL__RESOURCE_8u_1a33fd5f6391f2f0cb4c91179d7f521949" kind="variable"><name>DMA</name></member>
    <member refid="union__HAL__RESOURCE_8u_1a1ce92afa20b1c7f6a4e5d0dc73e5c92a" kind="variable"><name>Interrupt</name></member>
    <member refid="union__HAL__RESOURCE_8u_1a4789f23283b3a61f858b641a1bef19a3" kind="variable"><name>Memory</name></member>
    <member refid="union__HAL__RESOURCE_8u_1a60aaf44d4b562252c04db7f98497e9aa" kind="variable"><name>Port</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE_8u_8Bus" kind="struct"><name>_HAL_RESOURCE.u.Bus</name>
    <member refid="struct__HAL__RESOURCE_8u_8Bus_1a2459f8102e6c51d4351846fcade46c9b" kind="variable"><name>BusNumber</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Bus_1a73c1ec640a656cef0b068947d7dd378f" kind="variable"><name>SlotNumber</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE_8u_8DMA" kind="struct"><name>_HAL_RESOURCE.u.DMA</name>
    <member refid="struct__HAL__RESOURCE_8u_8DMA_1a781dc97dc62331eec3ea9ec4373a3ca8" kind="variable"><name>Channel</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8DMA_1a60aaf44d4b562252c04db7f98497e9aa" kind="variable"><name>Port</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE_8u_8Interrupt" kind="struct"><name>_HAL_RESOURCE.u.Interrupt</name>
    <member refid="struct__HAL__RESOURCE_8u_8Interrupt_1a936031363ea9c7f977507af852fdc666" kind="variable"><name>Irql</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Interrupt_1a650be61892bf690026089544abbd9d26" kind="variable"><name>Mode</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Interrupt_1a6a70f510750c9979a381ba3916c5c393" kind="variable"><name>ShareVector</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Interrupt_1a57dea6f5039281b7fee517fc43bf3110" kind="variable"><name>Vector</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE_8u_8Memory" kind="struct"><name>_HAL_RESOURCE.u.Memory</name>
    <member refid="struct__HAL__RESOURCE_8u_8Memory_1a424c77aefa1f2068caca737b5239183f" kind="variable"><name>IsMapped</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Memory_1aba2a9c6c8c77e03f83ef8bf543612275" kind="variable"><name>Length</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Memory_1ae33279234f8572f4fadb245e1cb26942" kind="variable"><name>MappedAddress</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Memory_1aa6122a65eaa676f700ae68d393054a37" kind="variable"><name>Start</name></member>
  </compound>
  <compound refid="struct__HAL__RESOURCE_8u_8Port" kind="struct"><name>_HAL_RESOURCE.u.Port</name>
    <member refid="struct__HAL__RESOURCE_8u_8Port_1aba2a9c6c8c77e03f83ef8bf543612275" kind="variable"><name>Length</name></member>
    <member refid="struct__HAL__RESOURCE_8u_8Port_1aa6122a65eaa676f700ae68d393054a37" kind="variable"><name>Start</name></member>
  </compound>
  <compound refid="struct__I2C__CONFIG" kind="struct"><name>_I2C_CONFIG</name>
    <member refid="struct__I2C__CONFIG_1ae6dda89972493740920ba937c8d27897" kind="variable"><name>ClockFrequency</name></member>
    <member refid="struct__I2C__CONFIG_1a4f627b1a2607231e3dcb321f5d728c22" kind="variable"><name>Is10BitAddress</name></member>
    <member refid="struct__I2C__CONFIG_1a2b8ec7e6589589f70cb5d4bbb55db565" kind="variable"><name>RetryCount</name></member>
    <member refid="struct__I2C__CONFIG_1a9942dbdaebaf13e2e51cef985bcc4d0d" kind="variable"><name>TimeoutMs</name></member>
  </compound>
  <compound refid="struct__I2C__DEVICE__CONTEXT" kind="struct"><name>_I2C_DEVICE_CONTEXT</name>
    <member refid="struct__I2C__DEVICE__CONTEXT_1a4096eb1e4fb3e430840ed07109bec38b" kind="variable"><name>Config</name></member>
    <member refid="struct__I2C__DEVICE__CONTEXT_1a3897e76d5d5a45297f03bdaa74c2280c" kind="variable"><name>TransferLock</name></member>
    <member refid="struct__I2C__DEVICE__CONTEXT_1acf0fff6904f00f7611172d1e8b9c2bcd" kind="variable"><name>WdfDevice</name></member>
  </compound>
  <compound refid="struct__I2C__STATISTICS" kind="struct"><name>_I2C_STATISTICS</name>
    <member refid="struct__I2C__STATISTICS_1ac6637ce7aa131fb27cf2b60ce31f35db" kind="variable"><name>BusClockFrequency</name></member>
    <member refid="struct__I2C__STATISTICS_1a0d581d279416ac434f26896560418188" kind="variable"><name>BusNumber</name></member>
    <member refid="struct__I2C__STATISTICS_1a1ac16411f8ad21fe7d60868251438978" kind="variable"><name>DeviceInitialized</name></member>
    <member refid="struct__I2C__STATISTICS_1a717ee36112176580f0ec5152cd12e0a2" kind="variable"><name>ErrorCount</name></member>
    <member refid="struct__I2C__STATISTICS_1add2f1e1c89d79eee2641ef44c12f29d3" kind="variable"><name>TransactionCount</name></member>
  </compound>
  <compound refid="struct__I2C__TRANSFER__PACKET" kind="struct"><name>_I2C_TRANSFER_PACKET</name>
    <member refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" kind="variable"><name>Common</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" kind="variable"><name>ReadBuffer</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" kind="variable"><name>ReadBufferLength</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" kind="variable"><name>SlaveAddress</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" kind="variable"><name>Type</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" kind="variable"><name>WriteBuffer</name></member>
    <member refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" kind="variable"><name>WriteBufferLength</name></member>
  </compound>
  <compound refid="struct__LIST__ENTRY" kind="struct"><name>_LIST_ENTRY</name>
    <member refid="struct__LIST__ENTRY_1a3a60644cad17d7dd53f6b7b378b191f3" kind="variable"><name>Blink</name></member>
    <member refid="struct__LIST__ENTRY_1a4d67c5f973e82b7aa25c74f3a3ca4e30" kind="variable"><name>Flink</name></member>
  </compound>
  <compound refid="struct__LOG__CONFIG" kind="struct"><name>_LOG_CONFIG</name>
    <member refid="struct__LOG__CONFIG_1ac82d96af8adf8a1862d02ac0be15939d" kind="variable"><name>IncludeComponentName</name></member>
    <member refid="struct__LOG__CONFIG_1a860b2d89bf1aff272da84d5029dcb0b1" kind="variable"><name>IncludeTimestamp</name></member>
    <member refid="struct__LOG__CONFIG_1a21ca685eeee730a0d8a179892b840d5f" kind="variable"><name>LogFilePath</name></member>
    <member refid="struct__LOG__CONFIG_1ab67683f62a123026ec78fe405cdbb177" kind="variable"><name>LogLevel</name></member>
    <member refid="struct__LOG__CONFIG_1aa4471d5ed80ebce68218e751798de8dc" kind="variable"><name>LogTargets</name></member>
    <member refid="struct__LOG__CONFIG_1a4b99237eff4b17c1d4fbfa9697528f70" kind="variable"><name>LogTypes</name></member>
    <member refid="struct__LOG__CONFIG_1a7aa67c4af6a6176801a2b2af65ac3cbb" kind="variable"><name>MaxLogFileSize</name></member>
    <member refid="struct__LOG__CONFIG_1ac0ad1887a3c94cf0998d9c5359900a4f" kind="variable"><name>MinLevel</name></member>
  </compound>
  <compound refid="struct__SPI__CONFIG" kind="struct"><name>_SPI_CONFIG</name>
    <member refid="struct__SPI__CONFIG_1a5568dcea126ea711e8d0f6b40dc951cc" kind="variable"><name>ChipSelectLine</name></member>
    <member refid="struct__SPI__CONFIG_1a23bf21cffc603724543545d3c677093a" kind="variable"><name>ChipSelectPolarity</name></member>
    <member refid="struct__SPI__CONFIG_1ad132bb074410f64505375ed71b0173f8" kind="variable"><name>IsLsbFirst</name></member>
    <member refid="struct__SPI__CONFIG_1a6cf9c836c9e4e1a5b0dd2ffc18e218e6" kind="variable"><name>MaxClockFrequency</name></member>
    <member refid="struct__SPI__CONFIG_1a5995a0bc6695e2e61567693ec8e494ca" kind="variable"><name>Mode</name></member>
    <member refid="struct__SPI__CONFIG_1a3eecf9d65abadb2106c4930606d31c7a" kind="variable"><name>RetryCount</name></member>
    <member refid="struct__SPI__CONFIG_1aed13983e08690b185df2a6c06f44ffd3" kind="variable"><name>SpbConnectionId</name></member>
    <member refid="struct__SPI__CONFIG_1a065d24ae185eeafe1fc5ce74cf823454" kind="variable"><name>SpbDeviceObject</name></member>
    <member refid="struct__SPI__CONFIG_1a85af30c409a08baa8e9c3e31567c07c2" kind="variable"><name>TimeoutMs</name></member>
    <member refid="struct__SPI__CONFIG_1a2fb798d2a102a3bc148918a6528aa329" kind="variable"><name>WordLength</name></member>
  </compound>
  <compound refid="struct__SPI__DEVICE__CONTEXT" kind="struct"><name>_SPI_DEVICE_CONTEXT</name>
    <member refid="struct__SPI__DEVICE__CONTEXT_1ad94201a4a15b3f381e7016679cfb5231" kind="variable"><name>Config</name></member>
    <member refid="struct__SPI__DEVICE__CONTEXT_1a764c0d4c344ec67dc804a438695d17bd" kind="variable"><name>SpbIoTarget</name></member>
    <member refid="struct__SPI__DEVICE__CONTEXT_1a081bab7f80eae29e5a23517be063bbdc" kind="variable"><name>TransferLock</name></member>
    <member refid="struct__SPI__DEVICE__CONTEXT_1a1d960500b2572de5188fa339fba643cb" kind="variable"><name>WdfDevice</name></member>
  </compound>
  <compound refid="struct__SPI__DEVICE__TRANSFER__PACKET" kind="struct"><name>_SPI_DEVICE_TRANSFER_PACKET</name>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a744606c6d58de9e0a6e0fe4e4acfbec6" kind="variable"><name>DelayInMicroseconds</name></member>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a5ce41e65ae89fce3ef5107acc4fb11d2" kind="variable"><name>Flags</name></member>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a182672e24da62e3c9fcbc4baf9b285a1" kind="variable"><name>ReadBuffer</name></member>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ab93e815b31b5919f672691f7f01b06af" kind="variable"><name>ReadLength</name></member>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1ace00b8a3ea9807f1b300e84fd6a8d7be" kind="variable"><name>WriteBuffer</name></member>
    <member refid="struct__SPI__DEVICE__TRANSFER__PACKET_1a8575b3351fe18405d3eb99caf11e503b" kind="variable"><name>WriteLength</name></member>
  </compound>
  <compound refid="struct__SPI__STATISTICS" kind="struct"><name>_SPI_STATISTICS</name>
    <member refid="struct__SPI__STATISTICS_1aea910d0fe28ff0978ec2ecfec60b8c52" kind="variable"><name>ClockFrequency</name></member>
    <member refid="struct__SPI__STATISTICS_1a254521d63fa6f195accebd432ad9a2c9" kind="variable"><name>DeviceInitialized</name></member>
    <member refid="struct__SPI__STATISTICS_1af7be253804f923ed13df884c7c93cdeb" kind="variable"><name>ErrorCount</name></member>
    <member refid="struct__SPI__STATISTICS_1aaddc6c47a910b922a46c02072a72b0ef" kind="variable"><name>Mode</name></member>
    <member refid="struct__SPI__STATISTICS_1aed0da9f11638910222e8bcf387a1d38f" kind="variable"><name>TransactionCount</name></member>
  </compound>
  <compound refid="struct__SPI__TRANSFER__PACKET" kind="struct"><name>_SPI_TRANSFER_PACKET</name>
    <member refid="struct__SPI__TRANSFER__PACKET_1ab7012a8e826ed6c62ef8b4e77d5dac24" kind="variable"><name>AssertChipSelect</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1a00931840236e9b48912a56116bd0d109" kind="variable"><name>Common</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1a2c4bee3baf8bbe9d12ea2b02a12deb1c" kind="variable"><name>DeassertChipSelect</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1a1bd99745fd3afc8bf6567a4460b85f28" kind="variable"><name>ReadBuffer</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1afc8f8284a74ea4a78d2531d7b09838ed" kind="variable"><name>ReadBufferLength</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1ae3ca53594a2138cf5aea46199d1f00fc" kind="variable"><name>Type</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1adfd0120a5d0f6554506d7a8c4454609b" kind="variable"><name>WriteBuffer</name></member>
    <member refid="struct__SPI__TRANSFER__PACKET_1a60c1c9ddb35c8b5aa7ef2d65b6a278a7" kind="variable"><name>WriteBufferLength</name></member>
  </compound>
  <compound refid="struct__TOUCH__POINT" kind="struct"><name>_TOUCH_POINT</name>
    <member refid="struct__TOUCH__POINT_1a80ff6717c5b2724381d8aa3d6be00695" kind="variable"><name>IsValid</name></member>
    <member refid="struct__TOUCH__POINT_1a2e7c2f66ac7d84eae585c1bc409a79b8" kind="variable"><name>Pressure</name></member>
    <member refid="struct__TOUCH__POINT_1a67f74072be670f999df19133313e9347" kind="variable"><name>Timestamp</name></member>
    <member refid="struct__TOUCH__POINT_1a06116397f1685142513723207c799807" kind="variable"><name>TouchId</name></member>
    <member refid="struct__TOUCH__POINT_1aacdb49dfac683510c0c3eeae45f5f8f8" kind="variable"><name>X</name></member>
    <member refid="struct__TOUCH__POINT_1a82bf07c3929eea9e0235dfcd631669d8" kind="variable"><name>Y</name></member>
  </compound>
  <compound refid="ioctl_8h" kind="file"><name>ioctl.h</name>
    <member refid="ioctl_8h_1ad40a8f5f93a2d0fdabdc3b13510850b6" kind="define"><name>IOCTL_TOUCH_GET_DATA</name></member>
  </compound>
  <compound refid="Common_8h" kind="file"><name>Common.h</name>
    <member refid="Common_8h_1ab47772eb758c3b2a1a990360eaf8ad5c" kind="define"><name>HANDLE_ERROR</name></member>
    <member refid="Common_8h_1a9ee9b45a31a1e12103d9ea23cc983d0c" kind="define"><name>KMDF_POOL_TAG</name></member>
    <member refid="Common_8h_1acfe39a25e08737b535dc881071ebf149" kind="define"><name>LOG_DEBUG</name></member>
    <member refid="Common_8h_1ab90a3a515b5c728d627bf24ac17c3702" kind="define"><name>SAFE_FREE</name></member>
    <member refid="Common_8h_1a6038f7bdb274c2e159988a57dedbf93d" kind="define"><name>SAFE_RELEASE</name></member>
  </compound>
  <compound refid="core__types_8h" kind="file"><name>core_types.h</name>
    <member refid="core__types_8h_1adf3938c38fa151a4752926adc328705f" kind="define"><name>_KSPIN_LOCK_</name></member>
    <member refid="core__types_8h_1af62a7f2c165fbe4d707330731083ebf9" kind="define"><name>_LIST_ENTRY_DEFINED</name></member>
    <member refid="core__types_8h_1a977bf1d43521f47048b6d555ef981398" kind="define"><name>_WDF_PNP_DEVICE_STATE_TYPE</name></member>
    <member refid="core__types_8h_1acec93a755836f578590339c921773e21" kind="define"><name>InitializeListHead</name></member>
    <member refid="core__types_8h_1a14f3c1fe642e4927959b4beed2852e2a" kind="define"><name>InsertHeadList</name></member>
    <member refid="core__types_8h_1a0bf4f8c1a40d587fc04e48b16db5a8c3" kind="define"><name>InsertTailList</name></member>
    <member refid="core__types_8h_1a1f65a0c67211ca77b491405e7c0d539e" kind="define"><name>IsListEmpty</name></member>
    <member refid="core__types_8h_1a653f024ec628312a6a0d7943ddfc0b04" kind="define"><name>KeAcquireSpinLock</name></member>
    <member refid="core__types_8h_1a280f0a35b2869a5fb76a31d3d430834f" kind="define"><name>KeInitializeSpinLock</name></member>
    <member refid="core__types_8h_1aebf33c329588245abd814d788d1b23a3" kind="define"><name>KeReleaseSpinLock</name></member>
    <member refid="core__types_8h_1a0780a52679f3fd4a75aec1f901b881ff" kind="define"><name>RemoveEntryList</name></member>
    <member refid="core__types_8h_1aa4e1fcf1f71277fe7bbb792e4bc4f1f8" kind="define"><name>STATUS_ALREADY_INITIALIZED</name></member>
    <member refid="core__types_8h_1ac336d6e1c8601404a514a7a1630aa804" kind="define"><name>STATUS_NOT_INITIALIZED</name></member>
    <member refid="core__types_8h_1aa5171e0d8c548a75e9b42333beb6390e" kind="define"><name>WDF_NO_HANDLE</name></member>
    <member refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kind="define"><name>WDFAPI</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4" kind="enum"><name>_WDF_PNP_DEVICE_STATE</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4a32e2c31db9c18bee67b905ee7790fce9" kind="enumvalue"><name>WdfPnpDeviceStateInvalid</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4a8d4da388b3e90c23c1439b64af155f46" kind="enumvalue"><name>WdfPnpDeviceStateDisabled</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4a4ec4b8f4e5fb775bb55c33aea51210f9" kind="enumvalue"><name>WdfPnpDeviceStateSurpriseRemoval</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4a57e65bf1809d0b1ce961289483e25673" kind="enumvalue"><name>WdfPnpDeviceStateRemoved</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4aa810832bd1c384c1b65dc16cefb6d746" kind="enumvalue"><name>WdfPnpDeviceStateWorking</name></member>
    <member refid="core__types_8h_1abc45cb0e4da10d450f8b522e479c1ea4a2f3d900775841ff5752d051840df67f9" kind="enumvalue"><name>WdfPnpDeviceStateDeleted</name></member>
    <member refid="core__types_8h_1a87cde496e586af832ac8cd9897ee9197" kind="typedef"><name>CORE_CONFIG</name></member>
    <member refid="core__types_8h_1a6c3de50719f00d174c32bab4750ec712" kind="typedef"><name>CORE_DEVICE_CONTEXT</name></member>
    <member refid="core__types_8h_1afea8ea357b75e588745d32feff9f0a78" kind="typedef"><name>CORE_DEVICE_STATUS</name></member>
    <member refid="core__types_8h_1ac55b20222f4500db849f020c28e22922" kind="typedef"><name>CORE_REQUEST_CONTEXT</name></member>
    <member refid="core__types_8h_1a181d21ad9f4ed9573f73613950ff1e57" kind="typedef"><name>CORE_STATISTICS</name></member>
    <member refid="core__types_8h_1a2d8b5745731e293616acee4bc7c56d44" kind="typedef"><name>CORE_STATUS</name></member>
    <member refid="core__types_8h_1a34649dd83e77771e19b14cefe45ee25d" kind="typedef"><name>KIRQL</name></member>
    <member refid="core__types_8h_1a89adb6dc0ec6cc3fcce2441adcf97013" kind="typedef"><name>KSPIN_LOCK</name></member>
    <member refid="core__types_8h_1afe2819542d515b3631539098349c6d52" kind="typedef"><name>LIST_ENTRY</name></member>
    <member refid="core__types_8h_1a874bd40bcd8acb69be8bc3133d852950" kind="typedef"><name>PCORE_CONFIG</name></member>
    <member refid="core__types_8h_1a585fa428acb755e88140079b19416f9a" kind="typedef"><name>PCORE_DEVICE_CONTEXT</name></member>
    <member refid="core__types_8h_1a567ab06ce289a42b52237fe208e5bcee" kind="typedef"><name>PCORE_DEVICE_STATUS</name></member>
    <member refid="core__types_8h_1ac6db23f2a14df51d8d00dac706f3756b" kind="typedef"><name>PCORE_REQUEST_CONTEXT</name></member>
    <member refid="core__types_8h_1aac5f1274e6c8f44dd15babfb28d8d92e" kind="typedef"><name>PCORE_STATISTICS</name></member>
    <member refid="core__types_8h_1a0cfe7c2eb9c5b617c4492b4d8253be39" kind="typedef"><name>PCORE_STATUS</name></member>
    <member refid="core__types_8h_1a13ebe748cef10522645232bace79364b" kind="typedef"><name>PFN_WDF_DEVICE_CALLBACK</name></member>
    <member refid="core__types_8h_1a377b615ce256badc15383ccb9216ac21" kind="typedef"><name>PLIST_ENTRY</name></member>
    <member refid="core__types_8h_1a221c7751a97d3a74ef06fca4c21df7be" kind="typedef"><name>WDF_PNP_DEVICE_STATE</name></member>
    <member refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kind="typedef"><name>WDFDEVICE</name></member>
    <member refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kind="typedef"><name>WDFDRIVER</name></member>
    <member refid="core__types_8h_1a3921100f8f21c9e6503c02e51eb84644" kind="typedef"><name>WDFOBJECT</name></member>
    <member refid="core__types_8h_1afd7c139f363e4f6879a9f8d21938be91" kind="typedef"><name>WDFQUEUE</name></member>
    <member refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kind="typedef"><name>WDFREQUEST</name></member>
    <member refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kind="typedef"><name>WDFSPINLOCK</name></member>
  </compound>
  <compound refid="CoreManager_8h" kind="file"><name>CoreManager.h</name>
    <member refid="CoreManager_8h_1a62afb7081a8227cdd45b31bc0f4a29bf" kind="function"><name>CoreManagerGetDeviceStatistics</name></member>
    <member refid="CoreManager_8h_1ab30fe932990f8d1244364662da1011d4" kind="function"><name>CoreManagerInitialize</name></member>
    <member refid="CoreManager_8h_1a3946620ba635d952a5f8b4fcb10c565a" kind="function"><name>CoreManagerProcessRequest</name></member>
    <member refid="CoreManager_8h_1a4a6465946715026f03a5d7d24727166c" kind="function"><name>CoreManagerRegisterCallback</name></member>
    <member refid="CoreManager_8h_1a4d8a0547e9b41f1c0da4acfa1b4c81c3" kind="function"><name>CoreManagerRegisterDevice</name></member>
    <member refid="CoreManager_8h_1a1a77e92013ea9206b995f17c9b8d52ec" kind="function"><name>CoreManagerSetDevicePnPState</name></member>
    <member refid="CoreManager_8h_1a438db4d6ac84cc12dc907619b1f02189" kind="function"><name>CoreManagerUninitialize</name></member>
    <member refid="CoreManager_8h_1a25011824e6d6aae1bf197d4e7c1259d8" kind="function"><name>CoreManagerUnregisterCallback</name></member>
    <member refid="CoreManager_8h_1a6a00c4e7db194d151210817e52953d37" kind="function"><name>CoreManagerUnregisterDevice</name></member>
  </compound>
  <compound refid="device__manager_8h" kind="file"><name>device_manager.h</name>
    <member refid="device__manager_8h_1a06bac98d6f690509acee10555e212f41" kind="define"><name>IOCTL_DEVICE_SPECIFIC_COMMAND</name></member>
    <member refid="device__manager_8h_1a5d922ac9f0d09258cd06cb7d8d7160af" kind="define"><name>IOCTL_GET_DEVICE_INFO</name></member>
    <member refid="device__manager_8h_1a2f0ab8f1ea9c5b1dbd09bf73f736a7b6" kind="define"><name>IOCTL_RESET_DEVICE</name></member>
    <member refid="device__manager_8h_1a5fe592117cc2296f7e78acab9055f526" kind="typedef"><name>DEVICE_CONTEXT</name></member>
    <member refid="device__manager_8h_1a6c959d0e0181f5f4b6a8b6b3f8e16760" kind="typedef"><name>DEVICE_INFO</name></member>
    <member refid="device__manager_8h_1a2e1a690ef27ca2498b8893d6bf8ab597" kind="typedef"><name>DEVICE_INIT_CONFIG</name></member>
    <member refid="device__manager_8h_1ab05107b30d8429daed8e1e2165bafc32" kind="typedef"><name>PDEVICE_CONTEXT</name></member>
    <member refid="device__manager_8h_1ac8affe61bb901b8aa1b863ada6ac87bc" kind="typedef"><name>PDEVICE_INFO</name></member>
    <member refid="device__manager_8h_1a57aff078ae347e96d32d9f9f76b00fb2" kind="typedef"><name>PDEVICE_INIT_CONFIG</name></member>
    <member refid="device__manager_8h_1a28f8698ae45fe8be0e44c195bea1e3ee" kind="typedef"><name>PTOUCH_POINT</name></member>
    <member refid="device__manager_8h_1afb9531ad0911544a1250e36fd58cefdf" kind="typedef"><name>TOUCH_POINT</name></member>
    <member refid="device__manager_8h_1a51a0fa7bdbb22680e79efaa2cacbb729" kind="variable"><name>DeviceD0Entry</name></member>
    <member refid="device__manager_8h_1a1625e40d469a21ece1cad244736fbe81" kind="variable"><name>DeviceD0Exit</name></member>
    <member refid="device__manager_8h_1aa3daed4e70ed14cc04cbd390a34a3e90" kind="variable"><name>DeviceIoControl</name></member>
    <member refid="device__manager_8h_1adbd8ce862fd891104b08816d03110316" kind="variable"><name>DeviceIoRead</name></member>
    <member refid="device__manager_8h_1aafc7ca77e7f4cc11bf35ad04cff9a631" kind="variable"><name>DeviceIoWrite</name></member>
    <member refid="device__manager_8h_1a4fda7ae13ae040932bf046e74f5bb8a6" kind="variable"><name>DevicePrepareHardware</name></member>
    <member refid="device__manager_8h_1a20074db76287e7b7a18bc2f2c83c2c61" kind="variable"><name>DeviceReleaseHardware</name></member>
    <member refid="device__manager_8h_1a07afcd8a4d15d856ac651241642ebcd8" kind="variable"><name>EvtUsbInterruptPipeReadComplete</name></member>
    <member refid="device__manager_8h_1a5c125d43e51d9453782573ff3083bd3c" kind="function"><name>DeviceCreate</name></member>
    <member refid="device__manager_8h_1a6e86b6c1162a93b64aa151c03f63890f" kind="function"><name>DeviceInitContext</name></member>
  </compound>
  <compound refid="driver__core_8h" kind="file"><name>driver_core.h</name>
    <member refid="driver__core_8h_1a0785b75b63fb30d328b99d809b6ef8d9" kind="define"><name>IOCTL_DRIVER_BASE</name></member>
    <member refid="driver__core_8h_1a6d4bdae2f23ab96032a14098fb28b1f2" kind="define"><name>IOCTL_DRIVER_GET_STATISTICS</name></member>
    <member refid="driver__core_8h_1a07c561db6f9baf7d9f5452605546aed9" kind="define"><name>IOCTL_DRIVER_GET_VERSION</name></member>
    <member refid="driver__core_8h_1a9cc7d1b6ef52a7692138d85aa247f989" kind="define"><name>IOCTL_DRIVER_RESET</name></member>
    <member refid="driver__core_8h_1a3cfaff7b63b2bbdd9583818a4f090d3b" kind="define"><name>IOCTL_DRIVER_SET_LOGGING</name></member>
    <member refid="driver__core_8h_1adb9000833a6dada8f3fd1e267c320b2b" kind="define"><name>MAX_DRIVER_DEVICES</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5" kind="enum"><name>_DRIVER_TYPE</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ad8cc57484cf080a209430b86da7b782b" kind="enumvalue"><name>DriverTypeGeneric</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aa21374bc46d7383df768ae35437757a6" kind="enumvalue"><name>DriverTypeNetwork</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5a9bd3be6d60cd57a4bf190b61e2deef56" kind="enumvalue"><name>DriverTypeStorage</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5ae00ccbc82ec45bcc3369d612a2a99084" kind="enumvalue"><name>DriverTypeHID</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5abd4c8ebdc07c8c948ada9572ab1c24cf" kind="enumvalue"><name>DriverTypeUSB</name></member>
    <member refid="driver__core_8h_1a04ec4f75c5ed103e258282d8e27e4ea5aeff4b54327f5a8d39d1d7f6cf199d3ca" kind="enumvalue"><name>DriverTypeSerial</name></member>
    <member refid="driver__core_8h_1ab3f3478bc4dcb804303c86d537979d13" kind="typedef"><name>DRIVER_CONFIG</name></member>
    <member refid="driver__core_8h_1ad13345f7fccb1948813e53c9b2127947" kind="typedef"><name>DRIVER_DEVICE_CONTEXT</name></member>
    <member refid="driver__core_8h_1a0a4763a4c06b82137b4f23d6d2a62dd3" kind="typedef"><name>DRIVER_STATISTICS</name></member>
    <member refid="driver__core_8h_1a21f9f5c777c8b513e7ed8e5e2621622a" kind="typedef"><name>DRIVER_TYPE</name></member>
    <member refid="driver__core_8h_1aea65724e2a4566373216b88a7aa3fe62" kind="typedef"><name>DRIVER_VERSION</name></member>
    <member refid="driver__core_8h_1af3d65acd24f1c5a387f01ac14c86f337" kind="typedef"><name>PDRIVER_CONFIG</name></member>
    <member refid="driver__core_8h_1abadaa97cf5a9145a7c73bebeccd17181" kind="typedef"><name>PDRIVER_DEVICE_CONTEXT</name></member>
    <member refid="driver__core_8h_1a2c85f1797924669249bdab6c64fc6f07" kind="typedef"><name>PDRIVER_STATISTICS</name></member>
    <member refid="driver__core_8h_1af034d45237796be01f25611c95f992b4" kind="typedef"><name>PDRIVER_VERSION</name></member>
    <member refid="driver__core_8h_1a27946e799a0fbc6a45703d48bb3fddba" kind="function"><name>CheckDriverStatus</name></member>
    <member refid="driver__core_8h_1a84dc8b7aa97027c9d340f5fccc1d0f77" kind="function"><name>CleanupDriverResources</name></member>
    <member refid="driver__core_8h_1abc51f0e6ed5304a27afddf92da4720e6" kind="function"><name>DriverCoreAddDevice</name></member>
    <member refid="driver__core_8h_1aac97f3e68a787ac88617369283c60b79" kind="function"><name>DriverCoreCleanup</name></member>
    <member refid="driver__core_8h_1a7eca1538f567ab5a5add60bb6d2d03b5" kind="function"><name>DriverCoreGetConfiguration</name></member>
    <member refid="driver__core_8h_1a24d4768bc415635465e00a5f5a3b4187" kind="function"><name>DriverCoreGetDevice</name></member>
    <member refid="driver__core_8h_1a222752d15e46b43b3a4ae0c787d4018d" kind="function"><name>DriverCoreGetDeviceCount</name></member>
    <member refid="driver__core_8h_1acdb452dbcae039af8967376463c758b9" kind="function"><name>DriverCoreInitialize</name></member>
    <member refid="driver__core_8h_1ae610e53c6df75743831a2e87ef9e746b" kind="function"><name>DriverCoreProcessIoControl</name></member>
    <member refid="driver__core_8h_1a89627789114e389118ee51bda8684ab6" kind="function"><name>DriverCoreRemoveDevice</name></member>
    <member refid="driver__core_8h_1a6c0d04af706f5b9f31c9698344dc6104" kind="function"><name>GetDriverVersion</name></member>
    <member refid="driver__core_8h_1afc516515541c17e0ba39b5ac97a01636" kind="function"><name>InitializeDevice</name></member>
  </compound>
  <compound refid="driver__entry_8h" kind="file"><name>driver_entry.h</name>
  </compound>
  <compound refid="error__codes_8h" kind="file"><name>error_codes.h</name>
    <member refid="error__codes_8h_1affd17c80938eed361b46758b3ab696cd" kind="define"><name>ERROR_BUS_PROTOCOL_ERROR</name></member>
    <member refid="error__codes_8h_1a254cfc13872e4cf4fe0f89067d00f86a" kind="define"><name>ERROR_DEVICE_HARDWARE_ERROR</name></member>
    <member refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" kind="define"><name>ERROR_DEVICE_INIT_FAILED</name></member>
    <member refid="error__codes_8h_1a869e1fe828b9b51d971bcfe6595101bd" kind="define"><name>ERROR_DEVICE_NOT_AVAILABLE</name></member>
    <member refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" kind="define"><name>ERROR_DEVICE_NOT_READY</name></member>
    <member refid="error__codes_8h_1a2db6445dc95ce4a34ce59d355566545c" kind="define"><name>ERROR_DRIVER_BASE</name></member>
    <member refid="error__codes_8h_1af5020ced0fe4cabcbd18b06de7b4fd7f" kind="define"><name>ERROR_DRIVER_INIT_FAILED</name></member>
    <member refid="error__codes_8h_1a1ee37513091f1fa4fd23ad7a31815169" kind="define"><name>ERROR_HARDWARE_INIT_FAILED</name></member>
    <member refid="error__codes_8h_1abf05011deed9a7688487446dcedb7734" kind="define"><name>ERROR_I2C_ARBITRATION_LOST</name></member>
    <member refid="error__codes_8h_1a37b24e0918ff0d7e358234acfd7514bf" kind="define"><name>ERROR_I2C_BASE</name></member>
    <member refid="error__codes_8h_1a361a624ddf35d38e3673f8fd648bc533" kind="define"><name>ERROR_I2C_BUS_BUSY</name></member>
    <member refid="error__codes_8h_1a8977c5e15064527b2c7e501feb3915de" kind="define"><name>ERROR_I2C_DEVICE_NOT_RESPONDING</name></member>
    <member refid="error__codes_8h_1a117a0e9b2ae10c0775f947eb6aaf2e43" kind="define"><name>ERROR_INSTALL_FAILURE</name></member>
    <member refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kind="define"><name>ERROR_INVALID_PARAMETER</name></member>
    <member refid="error__codes_8h_1a1d5275aca16a6e82736bccadf6d54f51" kind="define"><name>ERROR_IO_DEVICE</name></member>
    <member refid="error__codes_8h_1ab62592f2d0fe2a25aceff8a2aea7120d" kind="define"><name>ERROR_IO_OPERATION_FAILED</name></member>
    <member refid="error__codes_8h_1a8c7e35c51169754be6ad9a33b4d0ede3" kind="define"><name>ERROR_MEMORY_ALLOCATION_FAILED</name></member>
    <member refid="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" kind="define"><name>ERROR_NOT_ENOUGH_MEMORY</name></member>
    <member refid="error__codes_8h_1a61a95049738405a02053f09a5707e1c9" kind="define"><name>ERROR_NOT_READY</name></member>
    <member refid="error__codes_8h_1a1dc03de0e9017ea32c0bab1658627cab" kind="define"><name>ERROR_QUEUE_INIT_FAILED</name></member>
    <member refid="error__codes_8h_1add601dcfffde2047b7051c4990d109e6" kind="define"><name>ERROR_RESOURCE_NOT_FOUND</name></member>
    <member refid="error__codes_8h_1a224c1c26e72afd5d9b1eb990a3808ba0" kind="define"><name>ERROR_SPI_BASE</name></member>
    <member refid="error__codes_8h_1a8a14c6072318a642cb973e5e1b3749cf" kind="define"><name>ERROR_SPI_TRANSFER_FAILED</name></member>
    <member refid="error__codes_8h_1aea0ae801b7d25c979655a7eb20d034af" kind="define"><name>ERROR_SUCCESS</name></member>
    <member refid="error__codes_8h_1ad58fd51c3e56b5d9555ce48be5cc53bd" kind="define"><name>ERROR_TIMEOUT</name></member>
    <member refid="error__codes_8h_1a3b6ff704bf43afbc0cd69efc9d429f64" kind="define"><name>ERROR_USB_BASE</name></member>
    <member refid="error__codes_8h_1a23a63dc8ca4262eacbd1b165c5c9de52" kind="define"><name>ERROR_USB_ENDPOINT_NOT_FOUND</name></member>
    <member refid="error__codes_8h_1a14083fcce33766b91f8d08998cde8487" kind="function"><name>ErrorCodeToString</name></member>
  </compound>
  <compound refid="error__handling_8h" kind="file"><name>error_handling.h</name>
    <member refid="error__handling_8h_1a3a8ba39810c16fad65af2271c2dd6a6c" kind="define"><name>DRIVER_ASSERT</name></member>
    <member refid="error__handling_8h_1a98899d26e6d8086d4ae7268a1a953c01" kind="define"><name>RETURN_ON_FAILURE</name></member>
    <member refid="error__handling_8h_1a2929f142620a357d4bb33852c676e822" kind="define"><name>RETURN_ON_FALSE</name></member>
    <member refid="error__handling_8h_1a726809026748fc6eba00fc55e5a26ff0" kind="define"><name>RETURN_ON_NULL</name></member>
  </compound>
  <compound refid="CommonTypes_8h" kind="file"><name>CommonTypes.h</name>
  </compound>
  <compound refid="device_8h" kind="file"><name>device.h</name>
  </compound>
  <compound refid="device__context_8h" kind="file"><name>device_context.h</name>
  </compound>
  <compound refid="device__io_8h" kind="file"><name>device_io.h</name>
  </compound>
  <compound refid="device__queue_8h" kind="file"><name>device_queue.h</name>
  </compound>
  <compound refid="device__power_8h" kind="file"><name>device_power.h</name>
  </compound>
  <compound refid="power__manager_8h" kind="file"><name>power_manager.h</name>
  </compound>
  <compound refid="DriverLog_8h" kind="file"><name>DriverLog.h</name>
  </compound>
  <compound refid="kmdf__bus__common_8h" kind="file"><name>kmdf_bus_common.h</name>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52f" kind="enum"><name>_BUS_TRANSFER_STATUS</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" kind="enumvalue"><name>BusTransferSuccess</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" kind="enumvalue"><name>BusTransferFailed</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726" kind="enumvalue"><name>BusTransferCancelled</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c" kind="enumvalue"><name>BusTransferInvalidParameter</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b" kind="enumvalue"><name>BusTransferDeviceNotReady</name></member>
    <member refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519" kind="enumvalue"><name>BusTransferTimeout</name></member>
    <member refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123" kind="enum"><name>_BUS_TYPE</name></member>
    <member refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" kind="enumvalue"><name>BusTypeI2C</name></member>
    <member refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" kind="enumvalue"><name>BusTypeSPI</name></member>
    <member refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" kind="enumvalue"><name>BusTypeUSB</name></member>
    <member refid="kmdf__bus__common_8h_1a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38" kind="enumvalue"><name>BusTypeMax</name></member>
    <member refid="kmdf__bus__common_8h_1a90f573bacff80fd27b824b98e3e4fb9a" kind="typedef"><name>BUS_CONFIG</name></member>
    <member refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kind="typedef"><name>BUS_OPERATION_CALLBACK</name></member>
    <member refid="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" kind="typedef"><name>BUS_TRANSFER_PACKET</name></member>
    <member refid="kmdf__bus__common_8h_1ab61a790fb09aa3a337c89ea002b5a76f" kind="typedef"><name>BUS_TRANSFER_STATUS</name></member>
    <member refid="kmdf__bus__common_8h_1a070973cba55289cdfb72a260dc529d59" kind="typedef"><name>BUS_TYPE</name></member>
    <member refid="kmdf__bus__common_8h_1aeca4f45b7bb946c601f53bb52b08b170" kind="typedef"><name>PBUS_CONFIG</name></member>
    <member refid="kmdf__bus__common_8h_1a41f78cc35bd82acd5a7e7fd5fc51d00d" kind="typedef"><name>PBUS_TRANSFER_PACKET</name></member>
    <member refid="kmdf__bus__common_8h_1a4f54d258241aeda6b5d01ee12110870f" kind="function"><name>BusCancelTransfer</name></member>
    <member refid="kmdf__bus__common_8h_1ad5aa0e171a9d72e28ab08d06935ab2f5" kind="function"><name>BusInitialize</name></member>
    <member refid="kmdf__bus__common_8h_1af23a4d40f37f1cf45dd8b2fc40cf6dff" kind="function"><name>BusTransferAsynchronous</name></member>
    <member refid="kmdf__bus__common_8h_1a79b8f6be307f48971e34bb6cabfba958" kind="function"><name>BusTransferSynchronous</name></member>
    <member refid="kmdf__bus__common_8h_1a2878b61cfb78224301b7d25175aa243e" kind="function"><name>BusUninitialize</name></member>
  </compound>
  <compound refid="kmdf__gpio_8h" kind="file"><name>kmdf_gpio.h</name>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33" kind="enum"><name>_GPIO_INTERRUPT_TYPE</name></member>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76" kind="enumvalue"><name>GpioInterruptNone</name></member>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560" kind="enumvalue"><name>GpioInterruptRising</name></member>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84" kind="enumvalue"><name>GpioInterruptFalling</name></member>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64" kind="enumvalue"><name>GpioInterruptBoth</name></member>
    <member refid="kmdf__gpio_8h_1a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42" kind="enumvalue"><name>GpioInterruptLevel</name></member>
    <member refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4b" kind="enum"><name>_GPIO_PIN_DIRECTION</name></member>
    <member refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2" kind="enumvalue"><name>GpioDirectionIn</name></member>
    <member refid="kmdf__gpio_8h_1a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c" kind="enumvalue"><name>GpioDirectionOut</name></member>
    <member refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070" kind="enum"><name>_GPIO_PIN_POLARITY</name></member>
    <member refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78" kind="enumvalue"><name>GpioPolarityActiveLow</name></member>
    <member refid="kmdf__gpio_8h_1a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d" kind="enumvalue"><name>GpioPolarityActiveHigh</name></member>
    <member refid="kmdf__gpio_8h_1a9643691e5b435f14c69e6016c2fae45a" kind="typedef"><name>GPIO_INTERRUPT_CALLBACK</name></member>
    <member refid="kmdf__gpio_8h_1a941806f1dcee3e7fc53c46d5b60b362b" kind="typedef"><name>GPIO_INTERRUPT_TYPE</name></member>
    <member refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kind="typedef"><name>GPIO_PIN_CONFIG</name></member>
    <member refid="kmdf__gpio_8h_1a1ca9004cc1371bfb961fb7b894e2cd0a" kind="typedef"><name>GPIO_PIN_DIRECTION</name></member>
    <member refid="kmdf__gpio_8h_1a96127f2b5d39ef12184a23f9ad42999b" kind="typedef"><name>GPIO_PIN_POLARITY</name></member>
    <member refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kind="typedef"><name>PGPIO_PIN_CONFIG</name></member>
    <member refid="kmdf__gpio_8h_1a32c9f2fdbf98b7e59c2b494d61f465a4" kind="function"><name>GPIODisableInterrupt</name></member>
    <member refid="kmdf__gpio_8h_1a818408e823499cbc8bf3a09f74062a48" kind="function"><name>GPIOEnableInterrupt</name></member>
    <member refid="kmdf__gpio_8h_1adcddf2e62a93fe5cc3fa7f46b67845bb" kind="function"><name>GPIOGetValue</name></member>
    <member refid="kmdf__gpio_8h_1a0a73c23c89291af0e81cef7098d10e29" kind="function"><name>GPIOInitialize</name></member>
    <member refid="kmdf__gpio_8h_1ade1cb652014fc4a3984567ff49900d81" kind="function"><name>GPIOPulse</name></member>
    <member refid="kmdf__gpio_8h_1aab159dfcef06f528d7ebdb9fa3ce8be4" kind="function"><name>GPIOSetDirection</name></member>
    <member refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" kind="function"><name>GPIOSetValue</name></member>
    <member refid="kmdf__gpio_8h_1a785f00e9c0879fb478077d2cdce99906" kind="function"><name>GPIOUninitialize</name></member>
  </compound>
  <compound refid="kmdf__i2c_8h" kind="file"><name>kmdf_i2c.h</name>
    <member refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3" kind="enum"><name>_I2C_TRANSFER_TYPE</name></member>
    <member refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" kind="enumvalue"><name>I2CWrite</name></member>
    <member refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" kind="enumvalue"><name>I2CRead</name></member>
    <member refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" kind="enumvalue"><name>I2CWriteRead</name></member>
    <member refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kind="typedef"><name>I2C_ADDRESS</name></member>
    <member refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kind="typedef"><name>I2C_CONFIG</name></member>
    <member refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kind="typedef"><name>I2C_TRANSFER_PACKET</name></member>
    <member refid="kmdf__i2c_8h_1a70b5b0e5b59f4301d02402a14c4ecb0b" kind="typedef"><name>I2C_TRANSFER_TYPE</name></member>
    <member refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kind="typedef"><name>PI2C_ADDRESS</name></member>
    <member refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kind="typedef"><name>PI2C_CONFIG</name></member>
    <member refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kind="typedef"><name>PI2C_TRANSFER_PACKET</name></member>
    <member refid="kmdf__i2c_8h_1a5467da0184a8f514f9ff43ab28f7d2d0" kind="function"><name>I2CInitialize</name></member>
    <member refid="kmdf__i2c_8h_1aad5c9145daea9c25554b814bfed47756" kind="function"><name>I2CReadRegister</name></member>
    <member refid="kmdf__i2c_8h_1a1b937c9865418ca9d50b16766c8ceb66" kind="function"><name>I2CScanBus</name></member>
    <member refid="kmdf__i2c_8h_1a363c4a8b2ee1e16d8a6aaf35b0e67722" kind="function"><name>I2CTransferAsynchronous</name></member>
    <member refid="kmdf__i2c_8h_1ae74bb3af98d6a79ba5774f9c3a480ca7" kind="function"><name>I2CTransferSynchronous</name></member>
    <member refid="kmdf__i2c_8h_1aa8f6531c5b52bc6d04ca38fbaab3c223" kind="function"><name>I2CUninitialize</name></member>
    <member refid="kmdf__i2c_8h_1aa4838a1894b94b950fc4a7e73624d7ed" kind="function"><name>I2CWriteRegister</name></member>
  </compound>
  <compound refid="kmdf__spi_8h" kind="file"><name>kmdf_spi.h</name>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2" kind="enum"><name>_SPI_BUS_SPEED</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2" kind="enumvalue"><name>SpiBusSpeed1MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab" kind="enumvalue"><name>SpiBusSpeed2MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94" kind="enumvalue"><name>SpiBusSpeed4MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928" kind="enumvalue"><name>SpiBusSpeed8MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997" kind="enumvalue"><name>SpiBusSpeed10MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90" kind="enumvalue"><name>SpiBusSpeed20MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef" kind="enumvalue"><name>SpiBusSpeed25MHz</name></member>
    <member refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc" kind="enumvalue"><name>SpiBusSpeed50MHz</name></member>
    <member refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374" kind="enum"><name>_SPI_MODE</name></member>
    <member refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249" kind="enumvalue"><name>SpiMode0</name></member>
    <member refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972" kind="enumvalue"><name>SpiMode1</name></member>
    <member refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e" kind="enumvalue"><name>SpiMode2</name></member>
    <member refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470" kind="enumvalue"><name>SpiMode3</name></member>
    <member refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21" kind="enum"><name>_SPI_TRANSFER_TYPE</name></member>
    <member refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a" kind="enumvalue"><name>SpiWrite</name></member>
    <member refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d" kind="enumvalue"><name>SpiRead</name></member>
    <member refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36" kind="enumvalue"><name>SpiWriteRead</name></member>
    <member refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kind="typedef"><name>PSPI_CONFIG</name></member>
    <member refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kind="typedef"><name>PSPI_TRANSFER_PACKET</name></member>
    <member refid="kmdf__spi_8h_1a02075f39766ac5419ad37fbd5e96de57" kind="typedef"><name>SPI_BUS_SPEED</name></member>
    <member refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kind="typedef"><name>SPI_CONFIG</name></member>
    <member refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kind="typedef"><name>SPI_MODE</name></member>
    <member refid="kmdf__spi_8h_1ae53b781a84db92ccef86085a53448289" kind="typedef"><name>SPI_TRANSFER_PACKET</name></member>
    <member refid="kmdf__spi_8h_1a9920771d941aa8c6e1b7b97ce21e77ca" kind="typedef"><name>SPI_TRANSFER_TYPE</name></member>
    <member refid="kmdf__spi_8h_1a685d8d7731e750c1512b975df16cc030" kind="function"><name>SPIInitialize</name></member>
    <member refid="kmdf__spi_8h_1adb5a94e2dc80b87a505aea6c78f3b885" kind="function"><name>SPIReadRegister</name></member>
    <member refid="kmdf__spi_8h_1a571fb3ea7eed247b3c46c57f506fa033" kind="function"><name>SPITransferAsynchronous</name></member>
    <member refid="kmdf__spi_8h_1a682c974659ab89363d0baa22470a386c" kind="function"><name>SPITransferSynchronous</name></member>
    <member refid="kmdf__spi_8h_1ad756f8e3b06fdfa545a7048661038513" kind="function"><name>SPIUninitialize</name></member>
    <member refid="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f" kind="function"><name>SPIWriteRead</name></member>
    <member refid="kmdf__spi_8h_1a261c6752bd8e05e7e4d7eb1e60ed64f8" kind="function"><name>SPIWriteRegister</name></member>
  </compound>
  <compound refid="gpio__device_8h" kind="file"><name>gpio_device.h</name>
    <member refid="gpio__device_8h_1a0ab51b588e38783851968f26ed81d41e" kind="define"><name>IOCTL_GPIO_DEVICE_BASE</name></member>
    <member refid="gpio__device_8h_1a531a7dc21007b6e72d524a9fa29de43d" kind="define"><name>IOCTL_GPIO_DEVICE_GET_STATE</name></member>
    <member refid="gpio__device_8h_1ada8c56039bf34983b27bd0338982f351" kind="define"><name>IOCTL_GPIO_DEVICE_REGISTER_CALLBACK</name></member>
    <member refid="gpio__device_8h_1a15338fabc6809209e45575e75f4867e4" kind="define"><name>IOCTL_GPIO_DEVICE_SET_STATE</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0f" kind="enum"><name>_GPIO_DEVICE_EVENT</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fade992ff1fe1b3d791821a97aaafd5789" kind="enumvalue"><name>GpioEventPress</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa3b928ecf3b3a5198bb6a552f62814d3e" kind="enumvalue"><name>GpioEventRelease</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa5ce836b7f6d23041a7885b1518f31e0d" kind="enumvalue"><name>GpioEventOn</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa7792f7b76e37d1b5f2de982a8afe5652" kind="enumvalue"><name>GpioEventOff</name></member>
    <member refid="gpio__device_8h_1ad2f2cb92e3331b0a63b482671ae9ff0fa71136dfd97b017cb0b6288ef5889cf71" kind="enumvalue"><name>GpioEventChange</name></member>
    <member refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286" kind="enum"><name>_GPIO_DEVICE_STATE</name></member>
    <member refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ad732b0f1245f49a41431b68f0f942b5f" kind="enumvalue"><name>GpioStateUnknown</name></member>
    <member refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286ac7ad85277766be36732453d2404332e6" kind="enumvalue"><name>GpioStateOn</name></member>
    <member refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a84059d392bf267bcdcdff7eee56b1834" kind="enumvalue"><name>GpioStateOff</name></member>
    <member refid="gpio__device_8h_1af2ae1701b4a54058dd0988b07d38e286a3d5240e72d41c66eb312fbfe1eadc813" kind="enumvalue"><name>GpioStateError</name></member>
    <member refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6" kind="enum"><name>_GPIO_DEVICE_TYPE</name></member>
    <member refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a84bb6b2770444837f277383fea9033fe" kind="enumvalue"><name>GpioTypeGeneric</name></member>
    <member refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a9f11eddd6db1aa56c00b47541c54802f" kind="enumvalue"><name>GpioTypeButton</name></member>
    <member refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6a68467fe729fc33e2ae067a1377b85a60" kind="enumvalue"><name>GpioTypeLed</name></member>
    <member refid="gpio__device_8h_1a6f2f394001e2dd84a10864922d1672c6af84e71ef287fe86c36e80f06a7826d7f" kind="enumvalue"><name>GpioTypeSensor</name></member>
    <member refid="gpio__device_8h_1a062e6f1e503256e897b3f6642fdce349" kind="typedef"><name>GPIO_DEVICE_CONFIG</name></member>
    <member refid="gpio__device_8h_1aa63f80dfdfff636a324aac08eab189a0" kind="typedef"><name>GPIO_DEVICE_EVENT</name></member>
    <member refid="gpio__device_8h_1a7f38f134552248bad184ebb15479ad70" kind="typedef"><name>GPIO_DEVICE_EVENT_CALLBACK</name></member>
    <member refid="gpio__device_8h_1a8be6b3f1a7d49373c162209cfdfb0bc5" kind="typedef"><name>GPIO_DEVICE_STATE</name></member>
    <member refid="gpio__device_8h_1a2ddd51479fb5937e5f4ba2afa3e1504b" kind="typedef"><name>GPIO_DEVICE_TYPE</name></member>
    <member refid="gpio__device_8h_1a3225b6d40b644661d13ce4d49580cc08" kind="typedef"><name>PGPIO_DEVICE_CONFIG</name></member>
    <member refid="gpio__device_8h_1ac3d3347067d7be6497e71b6ba4b389c2" kind="function"><name>GpioDeviceCleanup</name></member>
    <member refid="gpio__device_8h_1a0bd26e3410adfbb26bba326602f8fec6" kind="function"><name>GpioDeviceGetState</name></member>
    <member refid="gpio__device_8h_1a21428e126bf8c8996ea3405e6a8be2f2" kind="function"><name>GpioDeviceInitialize</name></member>
    <member refid="gpio__device_8h_1a1e860d4292f8df84e5d3101f8a415d6c" kind="function"><name>GpioDevicePulse</name></member>
    <member refid="gpio__device_8h_1a6448a7e48d735f67501f3273b75485fd" kind="function"><name>GpioDeviceRegisterCallback</name></member>
    <member refid="gpio__device_8h_1a731812dec996a670e7d557a282535d3d" kind="function"><name>GpioDeviceSetState</name></member>
    <member refid="gpio__device_8h_1afbd2bd91a99c594504ca275fd8b45825" kind="function"><name>GpioDeviceUnregisterCallback</name></member>
  </compound>
  <compound refid="i2c__device_8h" kind="file"><name>i2c_device.h</name>
    <member refid="i2c__device_8h_1a64738b9f6c1ba2f1f919ef7a61ad1e35" kind="define"><name>I2C_ADDRESS_10BIT</name></member>
    <member refid="i2c__device_8h_1ab65b9830c26b77346e0f420c643d63b9" kind="define"><name>I2C_ADDRESS_7BIT</name></member>
    <member refid="i2c__device_8h_1ad6922f686b3fc13f8365467975aba1d7" kind="define"><name>I2C_TRANSFER_READ</name></member>
    <member refid="i2c__device_8h_1a23164ebc8fc22800438176c588caa941" kind="define"><name>IOCTL_I2C_GET_STATISTICS</name></member>
    <member refid="i2c__device_8h_1a69ebe19cf050058019d84f005052cc00" kind="define"><name>IOCTL_I2C_RESET</name></member>
    <member refid="i2c__device_8h_1a9c3881592ae1c10fbd5ba1b9ae7e85ec" kind="define"><name>IOCTL_I2C_SET_BUS_SPEED</name></member>
    <member refid="i2c__device_8h_1a15204e5c2582622fc3ef40f01fe93322" kind="define"><name>IOCTL_I2C_TRANSFER</name></member>
    <member refid="i2c__device_8h_1a478905e3b3f7f700dd9e0975f42fe1a3" kind="define"><name>IOCTL_I2C_TRANSFER_SEQUENCE</name></member>
    <member refid="i2c__device_8h_1a7d8f35e89fbf4832545b3dfab3bd1ae1" kind="typedef"><name>I2C_STATISTICS</name></member>
    <member refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kind="typedef"><name>PI2C_STATISTICS</name></member>
    <member refid="i2c__device_8h_1a40f62c35b73ac8d898f982eba3295ed5" kind="variable"><name>Data</name></member>
    <member refid="i2c__device_8h_1a2c5c92bbcc636db6bd909df12a1802da" kind="variable"><name>DataAddress</name></member>
    <member refid="i2c__device_8h_1a306a7e4c1d020919c3002e3b8bd37c27" kind="variable"><name>DelayInMicroseconds</name></member>
    <member refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kind="variable"><name>DeviceAddress</name></member>
    <member refid="i2c__device_8h_1a385668d045c2844f0f13d8613b3e0459" kind="variable"><name>Flags</name></member>
    <member refid="i2c__device_8h_1af8486d1fd9d3904ca8eb4408ec81d9c4" kind="variable"><name>I2C_TRANSFER_PACKET</name></member>
    <member refid="i2c__device_8h_1a95996125e1f73ecc8e0ecf222dd14372" kind="variable"><name>PI2C_TRANSFER_PACKET</name></member>
    <member refid="i2c__device_8h_1a5da67a960d3cf99caa6874438a84629b" kind="function"><name>I2cDeviceCleanup</name></member>
    <member refid="i2c__device_8h_1a709aca0009ccfb39adebbdd9ce97e252" kind="function"><name>I2cDeviceGetStatistics</name></member>
    <member refid="i2c__device_8h_1ab0c3b778b5a363d418c3d768cdb1e2d4" kind="function"><name>I2cDeviceInitialize</name></member>
    <member refid="i2c__device_8h_1a6576f1e3485d12c22c444244044c1d30" kind="function"><name>I2cDeviceRead</name></member>
    <member refid="i2c__device_8h_1ad84f26684684313ff193803d1d9c7c32" kind="function"><name>I2cDeviceTransfer</name></member>
    <member refid="i2c__device_8h_1a580f2434082501937a3d8bc4d5591866" kind="function"><name>I2cDeviceWrite</name></member>
  </compound>
  <compound refid="spi__device_8h" kind="file"><name>spi_device.h</name>
    <member refid="spi__device_8h_1aad9cdee9a56a867985b5110add53ed94" kind="define"><name>IOCTL_SPI_BASE</name></member>
    <member refid="spi__device_8h_1a97fe5a41276df38e46922527c4b9baf9" kind="define"><name>IOCTL_SPI_GET_STATISTICS</name></member>
    <member refid="spi__device_8h_1a8442695a715b85f6516ff535bc5d1409" kind="define"><name>IOCTL_SPI_RESET</name></member>
    <member refid="spi__device_8h_1aaba9d20f35713a3c0dd088bfdb433a0b" kind="define"><name>IOCTL_SPI_SET_BUS_SPEED</name></member>
    <member refid="spi__device_8h_1a471b24a3583fd2212e30e4619ae701be" kind="define"><name>IOCTL_SPI_SET_MODE</name></member>
    <member refid="spi__device_8h_1a72feec97101aca3be161a59ffe40cc2c" kind="define"><name>IOCTL_SPI_TRANSFER</name></member>
    <member refid="spi__device_8h_1a21382b2df65b9cb8a11da17114ab9491" kind="define"><name>IOCTL_SPI_TRANSFER_FULL_DUPLEX</name></member>
    <member refid="spi__device_8h_1aa7a1a825b415e6aa12c47463eefc0bb7" kind="define"><name>SPI_TRANSFER_FULL_DUPLEX</name></member>
    <member refid="spi__device_8h_1a8595e49b5f8ddb021462587455bd2ff5" kind="define"><name>SPI_TRANSFER_NO_CHIPSEL</name></member>
    <member refid="spi__device_8h_1a935c3756801c209968fb16a7be795396" kind="define"><name>SPI_TRANSFER_READ</name></member>
    <member refid="spi__device_8h_1af27a6537c3222c6796876ff953298b42" kind="define"><name>SPI_TRANSFER_WRITE</name></member>
    <member refid="spi__device_8h_1a8d5f40e6c769c7d8420d120a84cd711a" kind="typedef"><name>PSPI_DEVICE_TRANSFER_PACKET</name></member>
    <member refid="spi__device_8h_1ad0637463ce63cba4d22faa4adab1949d" kind="typedef"><name>PSPI_STATISTICS</name></member>
    <member refid="spi__device_8h_1a22213828588d3110e34d053c7f191fc0" kind="typedef"><name>SPI_DEVICE_TRANSFER_PACKET</name></member>
    <member refid="spi__device_8h_1aa83effe237db8be37288dceaeeab8d57" kind="typedef"><name>SPI_STATISTICS</name></member>
    <member refid="spi__device_8h_1a052b57a96b994325a574bcb9f3db837a" kind="function"><name>SpiDeviceCleanup</name></member>
    <member refid="spi__device_8h_1ae2be7c6b48ddf5b08876e1115879469d" kind="function"><name>SpiDeviceGetStatistics</name></member>
    <member refid="spi__device_8h_1a6939e12311ec72f975bcd03a4250a3e2" kind="function"><name>SpiDeviceInitialize</name></member>
    <member refid="spi__device_8h_1a3bc98267d67ee8988179bde952efaa87" kind="function"><name>SpiDeviceRead</name></member>
    <member refid="spi__device_8h_1a30b9d7f482d2a1343e50a60ea8d4135a" kind="function"><name>SpiDeviceSetClockFrequency</name></member>
    <member refid="spi__device_8h_1a3c91b33450d309fa46affa22959f8607" kind="function"><name>SpiDeviceSetMode</name></member>
    <member refid="spi__device_8h_1a2428921b9d71ab9d24f34e0a7b23487c" kind="function"><name>SpiDeviceTransfer</name></member>
    <member refid="spi__device_8h_1ae90ccf3d865bebb54c2c76e10fcbcaa8" kind="function"><name>SpiDeviceWrite</name></member>
  </compound>
  <compound refid="hal__interface_8h" kind="file"><name>hal_interface.h</name>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5" kind="enum"><name>_HAL_DEVICE_TYPE</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a4780c3839c83cca387af037262132c96" kind="enumvalue"><name>HalDeviceGeneric</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d" kind="enumvalue"><name>HalDeviceI2C</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a1ad2cfddb5aa189af5f3128ef0fa42ab" kind="enumvalue"><name>HalDeviceSPI</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a92d06b3f91454ca875df06efdbfbbc2a" kind="enumvalue"><name>HalDeviceGPIO</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a0c8d0e211b11297f195283b8bab8f1e4" kind="enumvalue"><name>HalDeviceUART</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5abfe18c22646c59a962b974f757552536" kind="enumvalue"><name>HalDeviceUSB</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5af20883315e8d4369dd6d1e09991730d2" kind="enumvalue"><name>HalDevicePCI</name></member>
    <member refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5acfd2dca79da10a5294a8cce839f047ce" kind="enumvalue"><name>HalDeviceMax</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8df" kind="enum"><name>_HAL_RESOURCE_TYPE</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfa4205b6598b2886f8862862c92f4809fc" kind="enumvalue"><name>HalResourceMemory</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfa72db3265ff4cc83ce6897d9ca4377445" kind="enumvalue"><name>HalResourcePort</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfa092133df88694c792610b1ae82ec7b65" kind="enumvalue"><name>HalResourceInterrupt</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfa7b4c15222686b48c4d896822c310ec81" kind="enumvalue"><name>HalResourceDMA</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfaabdad3aabbcf2fc65e87805cb57de9ba" kind="enumvalue"><name>HalResourceBus</name></member>
    <member refid="hal__interface_8h_1a2001a4956cfc97e42093954f9867d8dfa2397695c434dce8fe4329bf99cb0fbc3" kind="enumvalue"><name>HalResourceMax</name></member>
    <member refid="hal__interface_8h_1a9de221d82717e7709cdfc025378bc222" kind="typedef"><name>HAL_DEVICE_CONFIG</name></member>
    <member refid="hal__interface_8h_1a2f4ba870132c1fd57e2d74ba94e39805" kind="typedef"><name>HAL_DEVICE_HANDLE</name></member>
    <member refid="hal__interface_8h_1a4fb1c59de2a49258bb670fc1716cb5f3" kind="typedef"><name>HAL_DEVICE_TYPE</name></member>
    <member refid="hal__interface_8h_1ac75ea43249513ed2ca45aed1e8415d6d" kind="typedef"><name>HAL_RESOURCE</name></member>
    <member refid="hal__interface_8h_1ae5253bf6d647ca59aa8ee595a35dc0de" kind="typedef"><name>HAL_RESOURCE_TYPE</name></member>
    <member refid="hal__interface_8h_1a932781c14d58b9264941f4b01d6d0598" kind="typedef"><name>PHAL_DEVICE_CONFIG</name></member>
    <member refid="hal__interface_8h_1a3ecd10532c8a96e3ab750ead2b0c941d" kind="typedef"><name>PHAL_RESOURCE</name></member>
    <member refid="hal__interface_8h_1a55e44eef19627b9eadd548b034caaf44" kind="function"><name>HalCleanup</name></member>
    <member refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189" kind="function"><name>HalDeviceClose</name></member>
    <member refid="hal__interface_8h_1ab3ade1341d2d05db754ab4a9e3ee7198" kind="function"><name>HalDeviceIoControl</name></member>
    <member refid="hal__interface_8h_1a77d5908ab2a098166969c80028859e28" kind="function"><name>HalDeviceOpen</name></member>
    <member refid="hal__interface_8h_1afb35be12c0383de71b24a9eb633a74c8" kind="function"><name>HalDeviceRead</name></member>
    <member refid="hal__interface_8h_1aca328a12c9937b54319d93eeaa258fab" kind="function"><name>HalDeviceWrite</name></member>
    <member refid="hal__interface_8h_1aaecd38d42dd4177629b9d3318e61fd61" kind="function"><name>HalInitialize</name></member>
    <member refid="hal__interface_8h_1a64f394cd78ae9c84f7519ea4e7be36fc" kind="function"><name>HalMapDeviceMemory</name></member>
    <member refid="hal__interface_8h_1a30a54ad84d36a2c154d0c0161e3585b2" kind="function"><name>HalRegisterInterruptHandler</name></member>
    <member refid="hal__interface_8h_1a5b5334cd6270916df7647c684fe192dd" kind="function"><name>HalUnmapDeviceMemory</name></member>
  </compound>
  <compound refid="device__manager_8c" kind="file"><name>device_manager.c</name>
    <member refid="device__manager_8c_1af11aade3f3741fb554915d10d3f514eb" kind="define"><name>INITGUID</name></member>
    <member refid="device__manager_8c_1a7346c529a00f42617b55dfed2c4a8c6b" kind="variable"><name>DeviceInterruptDisable</name></member>
    <member refid="device__manager_8c_1a27966856080fa6cd1a9b7692140774b6" kind="variable"><name>DeviceInterruptDpc</name></member>
    <member refid="device__manager_8c_1a7c6d2ffd6c40693fcc51198997b8220c" kind="variable"><name>DeviceInterruptEnable</name></member>
    <member refid="device__manager_8c_1a66bb4353dba21c361d66c286dc941155" kind="variable"><name>DeviceInterruptIsr</name></member>
    <member refid="device__manager_8c_1aa3daed4e70ed14cc04cbd390a34a3e90" kind="variable"><name>DeviceIoControl</name></member>
    <member refid="device__manager_8c_1adbd8ce862fd891104b08816d03110316" kind="variable"><name>DeviceIoRead</name></member>
    <member refid="device__manager_8c_1aafc7ca77e7f4cc11bf35ad04cff9a631" kind="variable"><name>DeviceIoWrite</name></member>
    <member refid="device__manager_8c_1ab5c458c6825a16167b4f7baef381f352" kind="variable"><name>DeviceTimerFunc</name></member>
    <member refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" kind="function"><name>DeviceConfigureIoQueue</name></member>
    <member refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" kind="function"><name>DeviceCreate</name></member>
    <member refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" kind="function"><name>DeviceD0Entry</name></member>
    <member refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" kind="function"><name>DeviceD0Exit</name></member>
    <member refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" kind="function"><name>DeviceInitContext</name></member>
    <member refid="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" kind="function"><name>DeviceInterruptDisable</name></member>
    <member refid="device__manager_8c_1a7a7512003b2efe8ec1d3412af1b7c0b3" kind="function"><name>DeviceInterruptDpc</name></member>
    <member refid="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" kind="function"><name>DeviceInterruptEnable</name></member>
    <member refid="device__manager_8c_1a27d4fa34ed290d1ce4e0cba9b619de2b" kind="function"><name>DeviceInterruptIsr</name></member>
    <member refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" kind="function"><name>DeviceIoControl</name></member>
    <member refid="device__manager_8c_1af9753d89f71d81c5fc2038491eb88932" kind="function"><name>DeviceIoRead</name></member>
    <member refid="device__manager_8c_1a47b96d5bfdb1f42b07cc85978325d77a" kind="function"><name>DeviceIoWrite</name></member>
    <member refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" kind="function"><name>DeviceManager_EvtDeviceAdd</name></member>
    <member refid="device__manager_8c_1ae47038053b18948b8fc9579f5a785cf4" kind="function"><name>DeviceManager_EvtDeviceContextCleanup</name></member>
    <member refid="device__manager_8c_1afa743e52d2410ed36cf2f06a30c07fcd" kind="function"><name>DeviceMapResources</name></member>
    <member refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" kind="function"><name>DevicePrepareHardware</name></member>
    <member refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" kind="function"><name>DeviceReleaseHardware</name></member>
    <member refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" kind="function"><name>DeviceResetHardware</name></member>
    <member refid="device__manager_8c_1ac506ca5136446bf4cc6f68c0747f56e0" kind="function"><name>DeviceSetupInterrupt</name></member>
    <member refid="device__manager_8c_1a55323ce5aacec6667669f95f8abf7e22" kind="function"><name>DeviceUnmapResources</name></member>
    <member refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" kind="function"><name>EvtUsbInterruptPipeReadComplete</name></member>
  </compound>
  <compound refid="driver__core_8c" kind="file"><name>driver_core.c</name>
    <member refid="driver__core_8c_1af11aade3f3741fb554915d10d3f514eb" kind="define"><name>INITGUID</name></member>
    <member refid="driver__core_8c_1a3171f2b55fc099beecef32f0d09d5d5b" kind="typedef"><name>DRIVER_CONTEXT</name></member>
    <member refid="driver__core_8c_1a703dc89b9de7ff2f89e1467ae811be52" kind="typedef"><name>PDRIVER_CONTEXT</name></member>
    <member refid="driver__core_8c_1abd136e6aae106e58c949c867cbb92a41" kind="variable"><name>g_DriverContext</name></member>
    <member refid="driver__core_8c_1a7ca8aeb3b88c194b24cfb4066bcc60e7" kind="variable"><name>gLock</name></member>
    <member refid="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" kind="function"><name>DirectISR</name></member>
    <member refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" kind="function"><name>DriverCoreAddDevice</name></member>
    <member refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" kind="function"><name>DriverCoreCleanup</name></member>
    <member refid="driver__core_8c_1a7eca1538f567ab5a5add60bb6d2d03b5" kind="function"><name>DriverCoreGetConfiguration</name></member>
    <member refid="driver__core_8c_1a24d4768bc415635465e00a5f5a3b4187" kind="function"><name>DriverCoreGetDevice</name></member>
    <member refid="driver__core_8c_1a222752d15e46b43b3a4ae0c787d4018d" kind="function"><name>DriverCoreGetDeviceCount</name></member>
    <member refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" kind="function"><name>DriverCoreInitialize</name></member>
    <member refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" kind="function"><name>DriverCoreProcessIoControl</name></member>
    <member refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" kind="function"><name>DriverCoreRemoveDevice</name></member>
  </compound>
  <compound refid="driver__entry_8c" kind="file"><name>driver_entry.c</name>
    <member refid="driver__entry_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" kind="variable"><name>SampleDriverInterfaceGuid</name></member>
    <member refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" kind="function"><name>DriverEntry</name></member>
    <member refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" kind="function"><name>EvtDriverDeviceAdd</name></member>
    <member refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" kind="function"><name>EvtDriverUnload</name></member>
  </compound>
  <compound refid="error__handling_8c" kind="file"><name>error_handling.c</name>
    <member refid="error__handling_8c_1a94fd8be88deeace96d579af0616d0d13" kind="typedef"><name>ERROR_CODE_ENTRY</name></member>
    <member refid="error__handling_8c_1af6f1bd5b74ba972fc8a79164541e7af2" kind="typedef"><name>PERROR_CODE_ENTRY</name></member>
    <member refid="error__handling_8c_1aecb63565dd13e1edddcd31120d17a148" kind="variable"><name>ErrorTable</name></member>
    <member refid="error__handling_8c_1a14083fcce33766b91f8d08998cde8487" kind="function"><name>ErrorCodeToString</name></member>
    <member refid="error__handling_8c_1adbc9d5ccc2721eecfe34ae5dfcc62bed" kind="function"><name>FormatErrorMessage</name></member>
    <member refid="error__handling_8c_1afd059b655e91e68d030b34f16ee84647" kind="function"><name>GetLastErrorFromNTStatus</name></member>
    <member refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kind="function"><name>LogError</name></member>
  </compound>
  <compound refid="driver__log_8c" kind="file"><name>driver_log.c</name>
    <member refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kind="variable"><name>g_LogConfig</name></member>
    <member refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kind="variable"><name>g_LogFileHandle</name></member>
    <member refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kind="variable"><name>g_LogFilePath</name></member>
    <member refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" kind="variable"><name>g_LogFilePathBuffer</name></member>
    <member refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kind="variable"><name>g_LogInitialized</name></member>
    <member refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kind="variable"><name>g_LogLock</name></member>
    <member refid="driver__log_8c_1aa7f5f3b01615029c1cb54753c0b03175" kind="function"><name>LogConfigInit</name></member>
    <member refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" kind="function"><name>LogInitialize</name></member>
    <member refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kind="function"><name>LogMessageVA</name></member>
    <member refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" kind="function"><name>LogUninitialize</name></member>
  </compound>
  <compound refid="include_2core_2log_2driver__log_8h" kind="file"><name>driver_log.h</name>
    <member refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" kind="define"><name>FUNCTION_ENTRY</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" kind="define"><name>FUNCTION_EXIT</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1abd0b0523397fb05f0ed46fc217fb630f" kind="define"><name>LOG_DEBUG</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1abffaf9cecb61026cac6db71a16ace9c5" kind="define"><name>LOG_ERROR</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a89681da4efde0b54dc7f2839665082c8" kind="define"><name>LOG_INFO</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" kind="define"><name>LOG_VERBOSE</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a1c60134b1702d179d9b86bc618f416fe" kind="define"><name>LOG_WARNING</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kind="define"><name>LogInfo</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kind="define"><name>LogWarning</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a3adf7dc8e9dcfe1da6e33aa8043a80c3" kind="define"><name>MAX_LOG_MESSAGE_LENGTH</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kind="enum"><name>_LOG_LEVEL</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16" kind="enumvalue"><name>LogLevelDisabled</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kind="enumvalue"><name>LogLevelError</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kind="enumvalue"><name>LogLevelWarning</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kind="enumvalue"><name>LogLevelInfo</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" kind="enumvalue"><name>LogLevelVerbose</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kind="enumvalue"><name>LogLevelDebug</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867" kind="enum"><name>_LOG_TYPE</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" kind="enumvalue"><name>LogTypeKdPrint</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7" kind="enumvalue"><name>LogTypeWPP</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3" kind="enumvalue"><name>LogTypeETW</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d" kind="enumvalue"><name>LogTypeFile</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee" kind="enumvalue"><name>LogTypeAll</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kind="typedef"><name>LOG_CONFIG</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kind="typedef"><name>LOG_LEVEL</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a00e4548dd1db35b54cbeb1ee0fe45f66" kind="typedef"><name>LOG_TYPE</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a1736725db1db6ce0d7662411d9fbf587" kind="typedef"><name>PLOG_CONFIG</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" kind="function"><name>LogCleanup</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1ab4caba1729c833f0c7cce2e72c20e30a" kind="function"><name>LogConfigInit</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a8b05bbaba9e1fe6f53057c15e4a53a81" kind="function"><name>LogFunctionEntry</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1a2ebea8a6c7cbdde9ba74e856c73a2740" kind="function"><name>LogFunctionExit</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1ae2910293c9c672800cca68427812b7c9" kind="function"><name>LogInitialize</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kind="function"><name>LogMessage</name></member>
    <member refid="include_2core_2log_2driver__log_8h_1abac3042c22899daa9d6987d7f15e0185" kind="function"><name>LogSetLevel</name></member>
  </compound>
  <compound refid="src_2core_2log_2driver__log_8h" kind="file"><name>driver_log.h</name>
    <member refid="src_2core_2log_2driver__log_8h_1aa1911455782e83f3b06fab600be0e43e" kind="define"><name>LOG_ALERT</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a4c42b3fa94110619ab8458eb672d189d" kind="define"><name>LOG_ALERT_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1abc03884460a6987df33fea0d5cae8302" kind="define"><name>LOG_CRITICAL</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1ab48ce4a2ee7f0b5f74153fedf6ad7c25" kind="define"><name>LOG_CRITICAL_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1acfe39a25e08737b535dc881071ebf149" kind="define"><name>LOG_DEBUG</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1ae930e4b3ae4e59dc6a7b6a4feefb116f" kind="define"><name>LOG_DEBUG_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1ad706db1253940848e01bbc71ede868ef" kind="define"><name>LOG_EMERGENCY</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a570937723f42dd301b24b631ec455b58" kind="define"><name>LOG_EMERGENCY_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kind="define"><name>LOG_ERROR</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1aff0a0cc082f6b2fad9ed0979da6e8a9b" kind="define"><name>LOG_ERROR_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" kind="define"><name>LOG_INFO</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a94d7f96857344352ffbc6ee65e9f2390" kind="define"><name>LOG_INFO_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a05bf2404451e701f51d18409e72321fd" kind="define"><name>LOG_NOTICE</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a6cf4240fc51cea71e901acf9df797b98" kind="define"><name>LOG_NOTICE_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a0972af62c9ad7b688924604669d7d762" kind="define"><name>LOG_TRACE</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a8793c218a97ef927e72271b80a872495" kind="define"><name>LOG_TRACE_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" kind="define"><name>LOG_WARNING</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a238f142a1b0fcbd8378c38d99b233baa" kind="define"><name>LOG_WARNING_IF</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kind="enum"><name>_LOG_LEVEL</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131" kind="enumvalue"><name>LogLevelEmergency</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c" kind="enumvalue"><name>LogLevelAlert</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408" kind="enumvalue"><name>LogLevelCritical</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kind="enumvalue"><name>LogLevelError</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kind="enumvalue"><name>LogLevelWarning</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0" kind="enumvalue"><name>LogLevelNotice</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kind="enumvalue"><name>LogLevelInfo</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kind="enumvalue"><name>LogLevelDebug</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816" kind="enumvalue"><name>LogLevelTrace</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" kind="enumvalue"><name>LogLevelMax</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5" kind="enum"><name>_LOG_TYPES</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1" kind="enumvalue"><name>LogTypeNone</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kind="enumvalue"><name>LogTypeDebugger</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kind="enumvalue"><name>LogTypeFile</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" kind="enumvalue"><name>LogTypeETW</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" kind="enumvalue"><name>LogTypeWPP</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" kind="enumvalue"><name>LogTypeAll</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kind="typedef"><name>LOG_CONFIG</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kind="typedef"><name>LOG_LEVEL</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a5c3fab47ae6bd7de107b55a48ff20591" kind="typedef"><name>LOG_TYPES</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kind="typedef"><name>PLOG_CONFIG</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1af114289be71fc27e5ce43d55d4d6622c" kind="variable"><name>g_LogConfig</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1acd7f22e672d8bbb5ac97c70459b869fb" kind="variable"><name>g_LogInitialized</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3" kind="variable"><name>g_LogLevelNames</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1aa7f5f3b01615029c1cb54753c0b03175" kind="function"><name>LogConfigInit</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1aa2e9424857371175fc265253fbabcc5d" kind="function"><name>LogInitialize</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1a8e8711da6408af7b3b313f892121215e" kind="function"><name>LogMessageVA</name></member>
    <member refid="src_2core_2log_2driver__log_8h_1aab8bcb7121136bc236fe5d55778fbaf2" kind="function"><name>LogUninitialize</name></member>
  </compound>
  <compound refid="driver__main_8c" kind="file"><name>driver_main.c</name>
    <member refid="driver__main_8c_1a25634d21648ca7fb7a2aca614bafaaeb" kind="define"><name>DRIVER_NAME</name></member>
    <member refid="driver__main_8c_1afe74bc852cf46e301606e5ac20720e34" kind="define"><name>DRIVER_VERSION_BUILD</name></member>
    <member refid="driver__main_8c_1a90d323e79537750a33d74eeab4a66837" kind="define"><name>DRIVER_VERSION_MAJOR</name></member>
    <member refid="driver__main_8c_1a31ba5103952e666f059389c86d76b640" kind="define"><name>DRIVER_VERSION_MINOR</name></member>
    <member refid="driver__main_8c_1a3b369f69dba713375e6704ac172ecceb" kind="define"><name>DRIVER_VERSION_REV</name></member>
    <member refid="driver__main_8c_1a0cbf21a68b80f2cd62f3a028c051dd53" kind="variable"><name>SampleDriverInterfaceGuid</name></member>
    <member refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" kind="function"><name>DriverEntry</name></member>
    <member refid="driver__main_8c_1a0776c179fdcbdd09df07ee264e7e78e6" kind="function"><name>EvtDriverDeviceAdd</name></member>
    <member refid="driver__main_8c_1a075700d7117ddde115f3bb0db54b619e" kind="function"><name>EvtDriverUnload</name></member>
  </compound>
  <compound refid="gpio__core_8c" kind="file"><name>gpio_core.c</name>
    <member refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kind="define"><name>GPIO_POOL_TAG</name></member>
    <member refid="gpio__core_8c_1a09573d341b2d8f94a213241de2444b0b" kind="define"><name>IOCTL_GPIO_GET_VALUE</name></member>
    <member refid="gpio__core_8c_1aa5235f4dd44bf922bf5befb2ef0b3b4b" kind="define"><name>IOCTL_GPIO_SET_DIRECTION</name></member>
    <member refid="gpio__core_8c_1a921358974fe0b0cbe1288fd8bdc34196" kind="define"><name>IOCTL_GPIO_SET_VALUE</name></member>
    <member refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kind="variable"><name>attributes</name></member>
    <member refid="gpio__core_8c_1af0a4fb8c0dd33529dad81e93a0b0661f" kind="variable"><name>EvtGpioInterruptDpc</name></member>
    <member refid="gpio__core_8c_1a4d9377385f26f2d958a494c7ffbcc04f" kind="variable"><name>EvtGpioInterruptIsr</name></member>
    <member refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" kind="variable"><name>GENERIC_WRITE</name></member>
    <member refid="gpio__core_8c_1a33c00fdea3f12acb400049b8ef710ea9" kind="variable"><name>Initialized</name></member>
    <member refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kind="variable"><name>interruptConfig</name></member>
    <member refid="gpio__core_8c_1a4983b2b08534e2a12b6e0c30d87594f0" kind="variable"><name>InterruptTranslated</name></member>
    <member refid="gpio__core_8c_1ac91080baa5062b15cd46c8e08028fd51" kind="variable"><name>Lock</name></member>
    <member refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kind="variable"><name>openParams</name></member>
    <member refid="gpio__core_8c_1a9bfd47e9b367e692d16fa7a38e3944cc" kind="variable"><name>ParentObject</name></member>
    <member refid="gpio__core_8c_1a41283b4328aa08e8f095578a73755e08" kind="variable"><name>PassiveHandling</name></member>
    <member refid="gpio__core_8c_1a3a285e05689c30bbe096ff555f7a5b68" kind="variable"><name>PinCount</name></member>
    <member refid="gpio__core_8c_1aa601b044abcd0035f84077010771020b" kind="variable"><name>Pins</name></member>
    <member refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kind="variable"><name>spbDevicePath</name></member>
    <member refid="gpio__core_8c_1af781006198c718a3a6e46d55cdb1e74c" kind="variable"><name>SpbIoTarget</name></member>
    <member refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kind="variable"><name>status</name></member>
    <member refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kind="variable"><name>STATUS_INVALID_PARAMETER</name></member>
    <member refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kind="variable"><name>STATUS_SUCCESS</name></member>
    <member refid="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" kind="variable"><name>WdfDevice</name></member>
    <member refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" kind="function"><name>GPIOInitialize</name></member>
    <member refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" kind="function"><name>GPIOUninitialize</name></member>
    <member refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" kind="function"><name>if</name></member>
    <member refid="gpio__core_8c_1a495a75edfacf86c27c05ce15ada3509e" kind="function"><name>if</name></member>
    <member refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kind="function"><name>if</name></member>
    <member refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kind="function"><name>LogError</name></member>
    <member refid="gpio__core_8c_1a8f2aeb9f5f8525345d3f33df4340da13" kind="function"><name>LogInfo</name></member>
    <member refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kind="function"><name>pinContext</name></member>
    <member refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kind="function"><name>RtlZeroMemory</name></member>
    <member refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kind="function"><name>WdfSpinLockRelease</name></member>
  </compound>
  <compound refid="i2c__core_8c" kind="file"><name>i2c_core.c</name>
    <member refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" kind="typedef"><name>I2C_DEVICE_CONTEXT</name></member>
    <member refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kind="typedef"><name>PI2C_DEVICE_CONTEXT</name></member>
    <member refid="i2c__core_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" kind="variable"><name>__pad0__</name></member>
    <member refid="i2c__core_8c_1a465308d666d8357287980a516e216910" kind="variable"><name>I2CTransferTimerExpired</name></member>
    <member refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" kind="function"><name>I2CInitialize</name></member>
    <member refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" kind="function"><name>I2CReadRegister</name></member>
    <member refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" kind="function"><name>I2CScanBus</name></member>
    <member refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" kind="function"><name>I2CTransferAsynchronous</name></member>
    <member refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" kind="function"><name>I2CTransferSynchronous</name></member>
    <member refid="i2c__core_8c_1aefdc06b9d942e6b102424a8a81c0be8a" kind="function"><name>I2CTransferTimerExpired</name></member>
    <member refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" kind="function"><name>I2CUninitialize</name></member>
    <member refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" kind="function"><name>I2CWriteRegister</name></member>
  </compound>
  <compound refid="spi__core_8c" kind="file"><name>spi_core.c</name>
    <member refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kind="typedef"><name>PSPI_DEVICE_CONTEXT</name></member>
    <member refid="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" kind="typedef"><name>SPI_DEVICE_CONTEXT</name></member>
    <member refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" kind="variable"><name>SPITransferTimerExpired</name></member>
    <member refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" kind="function"><name>SPIInitialize</name></member>
    <member refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" kind="function"><name>SPIUninitialize</name></member>
  </compound>
  <compound refid="gpio__device_8c" kind="file"><name>gpio_device.c</name>
    <member refid="gpio__device_8c_1a6acc285bc10925e95287d1821e20f2f7" kind="define"><name>GPIO_DEVICE_POOL_TAG</name></member>
    <member refid="gpio__device_8c_1af02ced4aae3ad21bbe67ffd9742cda5b" kind="variable"><name>CallbackContext</name></member>
    <member refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" kind="variable"><name>Config</name></member>
    <member refid="gpio__device_8c_1a351f54e1c120ac29191cd55267d05e00" kind="variable"><name>CurrentState</name></member>
    <member refid="gpio__device_8c_1a130124259198fee8d71747e31a529e96" kind="variable"><name>Direction</name></member>
    <member refid="gpio__device_8c_1a0544c3fe466e421738dae463968b70ba" kind="variable"><name>else</name></member>
    <member refid="gpio__device_8c_1addddad69ceb0e8964c3018cd853b60d6" kind="variable"><name>EvtDebounceTimerFunc</name></member>
    <member refid="gpio__device_8c_1af4f2a770562d41e27d370140ab383393" kind="variable"><name>LastPinValue</name></member>
    <member refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kind="variable"><name>PinNumber</name></member>
    <member refid="gpio__device_8c_1a8704f4f2bf5b602d6b300432f561fe4b" kind="variable"><name>pinValue</name></member>
    <member refid="gpio__device_8c_1ad6e10a2dc0aebdabca7a9c76612727a3" kind="variable"><name>Polarity</name></member>
    <member refid="gpio__device_8c_1a9611b3a00430a86619b5923de30f9fdb" kind="variable"><name>status</name></member>
    <member refid="gpio__device_8c_1a77b4762318f24dff847f94f382cfeea6" kind="variable"><name>STATUS_SUCCESS</name></member>
    <member refid="gpio__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" kind="variable"><name>WdfDevice</name></member>
    <member refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kind="function"><name>deviceContext</name></member>
    <member refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" kind="function"><name>EvtDebounceTimerFunc</name></member>
    <member refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" kind="function"><name>GpioDeviceCleanup</name></member>
    <member refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" kind="function"><name>GpioDeviceGetState</name></member>
    <member refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" kind="function"><name>GpioDeviceInitialize</name></member>
    <member refid="gpio__device_8c_1a3d13bafdbad06cbdfb7f43c4c1b098d4" kind="function"><name>GpioDeviceInterruptCallback</name></member>
    <member refid="gpio__device_8c_1a9b4681b0c2d7f8009a387b9104735752" kind="function"><name>GpioDeviceInterruptCallback</name></member>
    <member refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" kind="function"><name>GpioDevicePulse</name></member>
    <member refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" kind="function"><name>GpioDeviceRegisterCallback</name></member>
    <member refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" kind="function"><name>GpioDeviceSetState</name></member>
    <member refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" kind="function"><name>GpioDeviceUnregisterCallback</name></member>
    <member refid="gpio__device_8c_1a6184bcc868fec6d16949da5c95315be4" kind="function"><name>GPIOUninitialize</name></member>
    <member refid="gpio__device_8c_1ab3fc244423e0b04d6b75007d82be3b1b" kind="function"><name>HandleStateChange</name></member>
    <member refid="gpio__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" kind="function"><name>if</name></member>
    <member refid="gpio__device_8c_1aac20ced732c198d7484287c9eb39e413" kind="function"><name>if</name></member>
    <member refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" kind="function"><name>if</name></member>
    <member refid="gpio__device_8c_1a9eef4336d2e5308042aaa49f8966c7fa" kind="function"><name>if</name></member>
    <member refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" kind="function"><name>if</name></member>
    <member refid="gpio__device_8c_1ac410021632e38928f5eb4b3bb6939ab9" kind="function"><name>LogInfo</name></member>
    <member refid="gpio__device_8c_1a3156531cfce23bfe597c3aaad2f23aad" kind="function"><name>LogInfo</name></member>
    <member refid="gpio__device_8c_1a1fbae9652e19914386dbb4fc4848b63d" kind="function"><name>LogWarning</name></member>
    <member refid="gpio__device_8c_1a546ebaed609861f7aa12740071033d54" kind="function"><name>pinConfig</name></member>
    <member refid="gpio__device_8c_1a714d770abd4d38214bae1604c752369a" kind="function"><name>RtlZeroMemory</name></member>
  </compound>
  <compound refid="i2c__device_8c" kind="file"><name>i2c_device.c</name>
    <member refid="i2c__device_8c_1a4bbdac9bba21cb3959a7355001ea590f" kind="define"><name>I2C_DEFAULT_TIMEOUT</name></member>
    <member refid="i2c__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" kind="variable"><name>__pad0__</name></member>
    <member refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kind="variable"><name>ConfigurationMemory</name></member>
    <member refid="i2c__device_8c_1a7cd334185ddc76afeacf2cd57615dc81" kind="variable"><name>Data</name></member>
    <member refid="i2c__device_8c_1a63268b1e9e5ee12309a44d8d6c9fc652" kind="variable"><name>DataAddress</name></member>
    <member refid="i2c__device_8c_1adb5ea4ad84a87209e6f98b3e012adbbf" kind="variable"><name>DataLength</name></member>
    <member refid="i2c__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" kind="variable"><name>DelayInMicroseconds</name></member>
    <member refid="i2c__device_8c_1ae49d231a428d107c888f925e845daf62" kind="variable"><name>DeviceInitialized</name></member>
    <member refid="i2c__device_8c_1abf852046373359fb294f66a784b38263" kind="variable"><name>DeviceType</name></member>
    <member refid="i2c__device_8c_1a0544c3fe466e421738dae463968b70ba" kind="variable"><name>else</name></member>
    <member refid="i2c__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" kind="variable"><name>ErrorCount</name></member>
    <member refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" kind="variable"><name>EvtI2cInterruptDpc</name></member>
    <member refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" kind="variable"><name>EvtI2cInterruptIsr</name></member>
    <member refid="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" kind="variable"><name>EvtI2cRequestCompletion</name></member>
    <member refid="i2c__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" kind="variable"><name>Flags</name></member>
    <member refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kind="variable"><name>HalHandle</name></member>
    <member refid="i2c__device_8c_1aa89f192944d335c9a60e5858325ed89a" kind="variable"><name>I2C_DEVICE_CONTEXT</name></member>
    <member refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kind="variable"><name>I2cConfig</name></member>
    <member refid="i2c__device_8c_1a89c965cbcba7aedff1b61ea4c0498d3e" kind="variable"><name>PI2C_DEVICE_CONTEXT</name></member>
    <member refid="i2c__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" kind="variable"><name>PrivateData</name></member>
    <member refid="i2c__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" kind="variable"><name>PrivateDataSize</name></member>
    <member refid="i2c__device_8c_1a9611b3a00430a86619b5923de30f9fdb" kind="variable"><name>status</name></member>
    <member refid="i2c__device_8c_1a77b4762318f24dff847f94f382cfeea6" kind="variable"><name>STATUS_SUCCESS</name></member>
    <member refid="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" kind="variable"><name>TransactionCount</name></member>
    <member refid="i2c__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" kind="variable"><name>WdfDevice</name></member>
    <member refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" kind="function"><name>EvtI2cInterruptIsr</name></member>
    <member refid="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" kind="function"><name>I2cDeviceCleanup</name></member>
    <member refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" kind="function"><name>I2cDeviceGetStatistics</name></member>
    <member refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" kind="function"><name>I2cDeviceInitialize</name></member>
    <member refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" kind="function"><name>I2cDeviceRead</name></member>
    <member refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" kind="function"><name>I2cDeviceTransfer</name></member>
    <member refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" kind="function"><name>I2cDeviceWrite</name></member>
    <member refid="i2c__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1ab29d05a3528131be0d35fe785e85590f" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1a6957e0e0f326c7986a222f431530dc94" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1a161904443c5f73d8654306b3fa8d29bb" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" kind="function"><name>if</name></member>
    <member refid="i2c__device_8c_1a90ec17a9895e508ccdb9077fed539682" kind="function"><name>LogInfo</name></member>
    <member refid="i2c__device_8c_1ae957556f2bac1175f6f4b37cd8f268f9" kind="function"><name>LogInfo</name></member>
    <member refid="i2c__device_8c_1aabdf6e2791329ae7f1d903b2c7b47add" kind="function"><name>LogInfo</name></member>
    <member refid="i2c__device_8c_1a6e8f3cbefed6c462cd6392131f5f0a29" kind="function"><name>LogInfo</name></member>
    <member refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kind="function"><name>RtlCopyMemory</name></member>
  </compound>
  <compound refid="spi__device_8c" kind="file"><name>spi_device.c</name>
    <member refid="spi__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" kind="variable"><name>__pad0__</name></member>
    <member refid="spi__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kind="variable"><name>ConfigurationMemory</name></member>
    <member refid="spi__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" kind="variable"><name>DelayInMicroseconds</name></member>
    <member refid="spi__device_8c_1ae49d231a428d107c888f925e845daf62" kind="variable"><name>DeviceInitialized</name></member>
    <member refid="spi__device_8c_1abf852046373359fb294f66a784b38263" kind="variable"><name>DeviceType</name></member>
    <member refid="spi__device_8c_1a0544c3fe466e421738dae463968b70ba" kind="variable"><name>else</name></member>
    <member refid="spi__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" kind="variable"><name>ErrorCount</name></member>
    <member refid="spi__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" kind="variable"><name>Flags</name></member>
    <member refid="spi__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kind="variable"><name>HalHandle</name></member>
    <member refid="spi__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" kind="variable"><name>PrivateData</name></member>
    <member refid="spi__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" kind="variable"><name>PrivateDataSize</name></member>
    <member refid="spi__device_8c_1a2d4e25dc12a54c28261d5ba390e3aa19" kind="variable"><name>PSPI_DEVICE_CONTEXT</name></member>
    <member refid="spi__device_8c_1ac2677e024009c29e2bcee99e0c32c735" kind="variable"><name>ReadBuffer</name></member>
    <member refid="spi__device_8c_1a1d0f6e5a27e5a31ee40de06efe3ba233" kind="variable"><name>ReadLength</name></member>
    <member refid="spi__device_8c_1aa226b0d93154d552caefa2ca1550155c" kind="variable"><name>SPI_DEVICE_CONTEXT</name></member>
    <member refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kind="variable"><name>SpiConfig</name></member>
    <member refid="spi__device_8c_1a9611b3a00430a86619b5923de30f9fdb" kind="variable"><name>status</name></member>
    <member refid="spi__device_8c_1a77b4762318f24dff847f94f382cfeea6" kind="variable"><name>STATUS_SUCCESS</name></member>
    <member refid="spi__device_8c_1a63212990a463669c2face6cfbfd28d26" kind="variable"><name>TransactionCount</name></member>
    <member refid="spi__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" kind="variable"><name>WdfDevice</name></member>
    <member refid="spi__device_8c_1ad26ded9b73e8b14b4117614b39440d86" kind="variable"><name>WriteBuffer</name></member>
    <member refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" kind="variable"><name>writeBuffer</name></member>
    <member refid="spi__device_8c_1a530eca3d7e36c5dde60c5e49dd7b2b34" kind="variable"><name>WriteLength</name></member>
    <member refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kind="function"><name>ExFreePoolWithTag</name></member>
    <member refid="spi__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1ab29d05a3528131be0d35fe785e85590f" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1ac40f83943701ccbf4235e0c238583dfb" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1a4b5c92f0859e4be1ead5d71edc903427" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1a164e77dd43f69d29ea926ae0ec42969b" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1afff80b1a0000ef578da0277667a994ff" kind="function"><name>if</name></member>
    <member refid="spi__device_8c_1a92b63e772873a034bea01d26f382ed57" kind="function"><name>LogInfo</name></member>
    <member refid="spi__device_8c_1aef91cc299dacb19ea73324acf8c8ff2c" kind="function"><name>LogInfo</name></member>
    <member refid="spi__device_8c_1a06c5e5172ac494575aa45dd42bfc32f5" kind="function"><name>RtlCopyMemory</name></member>
    <member refid="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" kind="function"><name>SpiDeviceCleanup</name></member>
    <member refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" kind="function"><name>SpiDeviceGetStatistics</name></member>
    <member refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" kind="function"><name>SpiDeviceInitialize</name></member>
    <member refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" kind="function"><name>SpiDeviceRead</name></member>
    <member refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" kind="function"><name>SpiDeviceTransfer</name></member>
    <member refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" kind="function"><name>SpiDeviceWrite</name></member>
  </compound>
  <compound refid="precomp_8c" kind="file"><name>precomp.c</name>
  </compound>
  <compound refid="precomp_8h" kind="file"><name>precomp.h</name>
    <member refid="precomp_8h_1ac50762666aa00bd3a4308158510f1748" kind="define"><name>_WIN32_WINNT</name></member>
    <member refid="precomp_8h_1abf0924766241e8f46e68e2dcbca9ac5b" kind="define"><name>ARRAY_SIZE</name></member>
    <member refid="precomp_8h_1a7ba5cb5a943f312e7b50de9cd8ffaf37" kind="define"><name>DEBUG_PRINT</name></member>
    <member refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" kind="define"><name>IN</name></member>
    <member refid="precomp_8h_1afa99ec4acc4ecb2dc3c2d05da15d0e3f" kind="define"><name>MAX</name></member>
    <member refid="precomp_8h_1a3acffbd305ee72dcd4593c0d8af64a4f" kind="define"><name>MIN</name></member>
    <member refid="precomp_8h_1abc91c0b65b29dc8fb94e770e4066e07c" kind="define"><name>NT_ERROR</name></member>
    <member refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kind="define"><name>NT_SUCCESS</name></member>
    <member refid="precomp_8h_1afdff552467cc2d2d5815831e9656cffc" kind="define"><name>OPTIONAL</name></member>
    <member refid="precomp_8h_1aec78e7a9e90a406a56f859ee456e8eae" kind="define"><name>OUT</name></member>
    <member refid="precomp_8h_1a966cd377b9f3fdeb1432460c33352af1" kind="define"><name>WINVER</name></member>
  </compound>
  <compound refid="dir_2f3dd6422cd26a3c624d951a6957c4e2" kind="dir"><name>C:/KMDF Driver1/include/device/base</name>
  </compound>
  <compound refid="dir_c5d1a81f9f5aef5a9f7467903b289108" kind="dir"><name>C:/KMDF Driver1/include/hal/bus</name>
  </compound>
  <compound refid="dir_43b43b2f79854d1934869d5a4aaeb79e" kind="dir"><name>C:/KMDF Driver1/src/hal/bus</name>
  </compound>
  <compound refid="dir_0966d06610f72609fd9aa4979c2b5a92" kind="dir"><name>C:/KMDF Driver1/include/common</name>
  </compound>
  <compound refid="dir_fedb270071c245cf6cf50dce60f14a28" kind="dir"><name>C:/KMDF Driver1/include/core/common</name>
  </compound>
  <compound refid="dir_3d69f64eaf81436fe2b22361382717e5" kind="dir"><name>C:/KMDF Driver1/include/core</name>
  </compound>
  <compound refid="dir_aebb8dcc11953d78e620bbef0b9e2183" kind="dir"><name>C:/KMDF Driver1/src/core</name>
  </compound>
  <compound refid="dir_b8ce80c44281ced4eeefc2db384aced2" kind="dir"><name>C:/KMDF Driver1/include/core/device</name>
  </compound>
  <compound refid="dir_00639e50a5e06ba932c41b2ae621276d" kind="dir"><name>C:/KMDF Driver1/include/device</name>
  </compound>
  <compound refid="dir_82037213e05c7748ea2cdf8fdb5c5467" kind="dir"><name>C:/KMDF Driver1/src/core/device</name>
  </compound>
  <compound refid="dir_f51f2e86ea53a1a257ee2ea690474c95" kind="dir"><name>C:/KMDF Driver1/include/hal/devices</name>
  </compound>
  <compound refid="dir_340a9c6f51eab01289f9b188a5d35565" kind="dir"><name>C:/KMDF Driver1/src/hal/devices</name>
  </compound>
  <compound refid="dir_d34f543cf4124e7ff2d676a2fc55266c" kind="dir"><name>C:/KMDF Driver1/include/core/driver</name>
  </compound>
  <compound refid="dir_d0f205a5f109744caf4fff09fa44f913" kind="dir"><name>C:/KMDF Driver1/include/driver</name>
  </compound>
  <compound refid="dir_67b4da34d574a8dd96cb9765195127fb" kind="dir"><name>C:/KMDF Driver1/src/core/driver</name>
  </compound>
  <compound refid="dir_aa60e2c50bb7a16bcf7ccb58d97021cb" kind="dir"><name>C:/KMDF Driver1/include/core/error</name>
  </compound>
  <compound refid="dir_7f6c38d6e6d20109ccf1dd9d772dac3d" kind="dir"><name>C:/KMDF Driver1/src/core/error</name>
  </compound>
  <compound refid="dir_a413b7f902cba5167b433a6fe834d5bd" kind="dir"><name>C:/KMDF Driver1/include/hal</name>
  </compound>
  <compound refid="dir_4ce6a7f885e2866a554ba9e7335035f1" kind="dir"><name>C:/KMDF Driver1/src/hal</name>
  </compound>
  <compound refid="dir_d44c64559bbebec7f509842c48db8b23" kind="dir"><name>C:/KMDF Driver1/include</name>
  </compound>
  <compound refid="dir_3cad9685a36aa226c5df106279a80923" kind="dir"><name>C:/KMDF Driver1/include/device/io</name>
  </compound>
  <compound refid="dir_ebc4183974da10596d83fbd9697e69e1" kind="dir"><name>C:/KMDF Driver1/include/core/log</name>
  </compound>
  <compound refid="dir_72f352bdb71451f8551298b1522c81b0" kind="dir"><name>C:/KMDF Driver1/include/driver/log</name>
  </compound>
  <compound refid="dir_2aa9c0e397d40306fad4535cf762fffd" kind="dir"><name>C:/KMDF Driver1/src/core/log</name>
  </compound>
  <compound refid="dir_79a3ac15060bf6afc446c9119448fdd8" kind="dir"><name>C:/KMDF Driver1/include/device/power</name>
  </compound>
  <compound refid="dir_68267d1309a1af8e8297ef4c3efbcdba" kind="dir"><name>C:/KMDF Driver1/src</name>
  </compound>
  <compound refid="dir_eab1a74f26378627c41f7a8c397431b0" kind="dir"><name>C:/KMDF Driver1/include/core/types</name>
  </compound>
</doxygenindex>
