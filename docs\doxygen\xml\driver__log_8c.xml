<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="driver__log_8c" kind="file" language="C++">
    <compoundname>driver_log.c</compoundname>
    <includes refid="precomp_8h" local="yes">../../precomp.h</includes>
    <includes refid="src_2core_2log_2driver__log_8h" local="yes">driver_log.h</includes>
    <includes local="no">ntstrsafe.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="19">
        <label>driver_log.h</label>
        <link refid="src_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="var">
      <memberdef kind="variable" id="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" prot="public" static="no" mutable="no">
        <type><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref></type>
        <definition>LOG_CONFIG g_LogConfig</definition>
        <argsstring></argsstring>
        <name>g_LogConfig</name>
        <initializer>= { <ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>, <ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kindref="member">LogTypeDebugger</ref> }</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="16" column="12" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="16" bodyend="16"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" compoundref="driver__log_8c" startline="103" endline="129">LogMessageVA</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" prot="public" static="no" mutable="no">
        <type>HANDLE</type>
        <definition>HANDLE g_LogFileHandle</definition>
        <argsstring></argsstring>
        <name>g_LogFileHandle</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="33" column="8" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="33" bodyend="-1"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" prot="public" static="no" mutable="no">
        <type>UNICODE_STRING</type>
        <definition>UNICODE_STRING g_LogFilePath</definition>
        <argsstring></argsstring>
        <name>g_LogFilePath</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="34" column="16" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="34" bodyend="-1"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" prot="public" static="no" mutable="no">
        <type>WCHAR</type>
        <definition>WCHAR g_LogFilePathBuffer[260]</definition>
        <argsstring>[260]</argsstring>
        <name>g_LogFilePathBuffer</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="35" column="7" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="35" bodyend="-1"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" prot="public" static="no" mutable="no">
        <type>BOOLEAN</type>
        <definition>BOOLEAN g_LogInitialized</definition>
        <argsstring></argsstring>
        <name>g_LogInitialized</name>
        <initializer>= FALSE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="17" column="9" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="17" bodyend="-1"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" compoundref="driver__log_8c" startline="103" endline="129">LogMessageVA</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref></type>
        <definition>WDFSPINLOCK g_LogLock</definition>
        <argsstring></argsstring>
        <name>g_LogLock</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="18" column="13" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="18" bodyend="-1"/>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="driver__log_8c_1aa7f5f3b01615029c1cb54753c0b03175" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogConfigInit</definition>
        <argsstring>(_Out_ PLOG_CONFIG LogConfig)</argsstring>
        <name>LogConfigInit</name>
        <param>
          <type>_Out_ <ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref></type>
          <declname>LogConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="90" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="90" bodyend="100"/>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" compoundref="src_2core_2log_2driver__log_8h" startline="24">LogLevelInfo</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" compoundref="src_2core_2log_2driver__log_8h" startline="36">LogTypeDebugger</references>
        <references refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</references>
      </memberdef>
      <memberdef kind="function" id="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS LogInitialize</definition>
        <argsstring>(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig)</argsstring>
        <name>LogInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref></type>
          <declname>DriverObject</declname>
        </param>
        <param>
          <type>_In_opt_ CONST <ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref> *</type>
          <declname>InitialConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="135" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="135" bodyend="241"/>
        <references refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" compoundref="driver__log_8c" startline="16" endline="16">g_LogConfig</references>
        <references refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" compoundref="driver__log_8c" startline="33">g_LogFileHandle</references>
        <references refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" compoundref="driver__log_8c" startline="34">g_LogFilePath</references>
        <references refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" compoundref="driver__log_8c" startline="35">g_LogFilePathBuffer</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3">g_LogLevelNames</references>
        <references refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" compoundref="driver__log_8c" startline="18">g_LogLock</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" compoundref="src_2core_2log_2driver__log_8h" startline="24">LogLevelInfo</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" compoundref="src_2core_2log_2driver__log_8h" startline="27">LogLevelMax</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" compoundref="src_2core_2log_2driver__log_8h" startline="37">LogTypeFile</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="core__types_8h_1aa4e1fcf1f71277fe7bbb792e4bc4f1f8" compoundref="core__types_8h" startline="168">STATUS_ALREADY_INITIALIZED</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
      </memberdef>
      <memberdef kind="function" id="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogMessageVA</definition>
        <argsstring>(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args)</argsstring>
        <name>LogMessageVA</name>
        <param>
          <type>_In_ <ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref></type>
          <declname>Level</declname>
        </param>
        <param>
          <type>_In_ PCSTR</type>
          <declname>Function</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Line</declname>
        </param>
        <param>
          <type>_In_ PCSTR</type>
          <declname>Format</declname>
        </param>
        <param>
          <type>_In_ va_list</type>
          <declname>Args</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="103" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="103" bodyend="129"/>
        <references refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" compoundref="driver__log_8c" startline="16" endline="16">g_LogConfig</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" compoundref="src_2core_2log_2driver__log_8h" startline="36">LogTypeDebugger</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogUninitialize</definition>
        <argsstring>(VOID)</argsstring>
        <name>LogUninitialize</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/core/log/driver_log.c" line="45" column="1" bodyfile="C:/KMDF Driver1/src/core/log/driver_log.c" bodystart="45" bodyend="87"/>
        <references refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" compoundref="driver__log_8c" startline="33">g_LogFileHandle</references>
        <references refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" compoundref="driver__log_8c" startline="34">g_LogFilePath</references>
        <references refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" compoundref="driver__log_8c" startline="17">g_LogInitialized</references>
        <references refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" compoundref="driver__log_8c" startline="18">g_LogLock</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">../../precomp.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="src_2core_2log_2driver__log_8h" kindref="compound">driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntstrsafe.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight><highlight class="comment">//<sp/>IMPORTANT:<sp/>The<sp/>following<sp/>are<sp/>placeholders<sp/>or<sp/>assumptions.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight><highlight class="comment">//<sp/>You<sp/>MUST<sp/>ensure<sp/>these<sp/>types<sp/>and<sp/>variables<sp/>are<sp/>correctly<sp/>defined<sp/>and<sp/>available,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight><highlight class="comment">//<sp/>potentially<sp/>via<sp/>a<sp/>driver_log.h<sp/>or<sp/>other<sp/>included<sp/>files.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="comment">//<sp/>---<sp/>BEGIN<sp/>REQUIRED<sp/>DEFINITIONS<sp/>(Ensure<sp/>these<sp/>are<sp/>properly<sp/>sourced)<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>MAX_LOG_MESSAGE_LENGTH</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>MAX_LOG_MESSAGE_LENGTH<sp/>256<sp/></highlight><highlight class="comment">//<sp/>Example:<sp/>Max<sp/>length<sp/>of<sp/>a<sp/>single<sp/>log<sp/>message</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>LOG_LEVEL,<sp/>LOG_TYPES,<sp/>and<sp/>LOG_CONFIG<sp/>are<sp/>now<sp/>defined<sp/>in<sp/>driver_log.h</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Global<sp/>variables<sp/>for<sp/>logging<sp/>state<sp/>and<sp/>configuration</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" refkind="member"><highlight class="normal"><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref><sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref><sp/>=<sp/>{<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>,<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kindref="member">LogTypeDebugger</ref><sp/>};<sp/></highlight><highlight class="comment">//<sp/>Default<sp/>configuration</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" refkind="member"><highlight class="normal">BOOLEAN<sp/><ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref><sp/>=<sp/>FALSE;<sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Flag<sp/>to<sp/>indicate<sp/>if<sp/>logging<sp/>is<sp/>initialized</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" refkind="member"><highlight class="normal"><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref><sp/><ref refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kindref="member">g_LogLock</ref><sp/>=<sp/>NULL;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Spinlock<sp/>for<sp/>thread-safe<sp/>logging<sp/>operations</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Array<sp/>of<sp/>log<sp/>level<sp/>names<sp/>for<sp/>display<sp/>purposes</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="22"><highlight class="comment">const<sp/>char*<sp/>const<sp/>g_LogLevelNames[]<sp/>=<sp/>{</highlight></codeline>
<codeline lineno="23"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;DISABLED&quot;,<sp/><sp/>//<sp/>LogLevelDisabled<sp/>=<sp/>0</highlight></codeline>
<codeline lineno="24"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;ERROR&quot;,<sp/><sp/><sp/><sp/><sp/>//<sp/>LogLevelError<sp/>=<sp/>1</highlight></codeline>
<codeline lineno="25"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;WARNING&quot;,<sp/><sp/><sp/>//<sp/>LogLevelWarning<sp/>=<sp/>2</highlight></codeline>
<codeline lineno="26"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;INFO&quot;,<sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>LogLevelInfo<sp/>=<sp/>3</highlight></codeline>
<codeline lineno="27"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;VERBOSE&quot;,<sp/><sp/><sp/>//<sp/>LogLevelVerbose<sp/>=<sp/>4</highlight></codeline>
<codeline lineno="28"><highlight class="comment"><sp/><sp/><sp/><sp/>&quot;DEBUG&quot;<sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>LogLevelDebug<sp/>=<sp/>5</highlight></codeline>
<codeline lineno="29"><highlight class="comment">};</highlight></codeline>
<codeline lineno="30"><highlight class="comment">*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Global<sp/>variables<sp/>for<sp/>file<sp/>logging</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33" refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" refkind="member"><highlight class="normal">HANDLE<sp/><ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref><sp/>=<sp/>NULL;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Handle<sp/>for<sp/>the<sp/>log<sp/>file</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" refkind="member"><highlight class="normal">UNICODE_STRING<sp/><ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Unicode<sp/>string<sp/>for<sp/>the<sp/>log<sp/>file<sp/>path</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" refkind="member"><highlight class="normal">WCHAR<sp/><ref refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" kindref="member">g_LogFilePathBuffer</ref>[260];<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Buffer<sp/>for<sp/>the<sp/>log<sp/>file<sp/>path<sp/>(MAX_PATH<sp/>approx)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="comment">//<sp/>---<sp/>END<sp/>REQUIRED<sp/>DEFINITIONS<sp/>---</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="40"><highlight class="comment"><sp/>*<sp/>Forward<sp/>declarations<sp/>for<sp/>static<sp/>helper<sp/>functions<sp/>if<sp/>needed,</highlight></codeline>
<codeline lineno="41"><highlight class="comment"><sp/>*<sp/>or<sp/>ensure<sp/>they<sp/>are<sp/>defined<sp/>before<sp/>use.</highlight></codeline>
<codeline lineno="42"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43"><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="45" refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" refkind="member"><highlight class="normal"><ref refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" kindref="member">LogUninitialize</ref>(</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="47"><highlight class="normal">)<sp/>{</highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref>)<sp/>{</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Log<sp/>system<sp/>not<sp/>initialized<sp/>or<sp/>already<sp/>uninitialized\n&quot;</highlight><highlight class="normal">));</highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="52"><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Uninitializing<sp/>log<sp/>system...\n&quot;</highlight><highlight class="normal">));</highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Close<sp/>the<sp/>log<sp/>file<sp/>if<sp/>it<sp/>was<sp/>opened</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Closing<sp/>log<sp/>file:<sp/>%wZ\n&quot;</highlight><highlight class="normal">,<sp/>&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>));</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Optionally<sp/>write<sp/>a<sp/>footer</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CHAR<sp/>fileFooter[128];</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>footerLength;</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>IO_STATUS_BLOCK<sp/>ioStatusBlock;</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NTSTATUS<sp/>footerStatus<sp/>=<sp/>RtlStringCchPrintfA(fileFooter,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(fileFooter),<sp/></highlight><highlight class="stringliteral">&quot;---<sp/>Log<sp/>Uninitialized:<sp/>%s<sp/>%s<sp/>---\r\n&quot;</highlight><highlight class="normal">,<sp/>__DATE__,<sp/>__TIME__);</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(footerStatus))<sp/>{</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>footerStatus<sp/>=<sp/>RtlStringCchLengthA(fileFooter,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(fileFooter),<sp/>&amp;footerLength);</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(footerStatus))<sp/>{</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(void)ZwWriteFile(<ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref>,<sp/>NULL,<sp/>NULL,<sp/>NULL,<sp/>&amp;ioStatusBlock,<sp/>fileFooter,<sp/>(ULONG)footerLength,<sp/>NULL,<sp/>NULL);</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>ZwClose(<ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref>);</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Release<sp/>other<sp/>resources<sp/>like<sp/>ETW,<sp/>WPP<sp/>if<sp/>they<sp/>were<sp/>initialized</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Mark<sp/>as<sp/>uninitialized</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref><sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="77"><highlight class="normal"></highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Destroy<sp/>the<sp/>spin<sp/>lock<sp/>if<sp/>it<sp/>was<sp/>created</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WdfSpinLock<sp/>objects<sp/>are<sp/>WDFOBJECTs.<sp/>If<sp/>g_LogLock<sp/>was<sp/>parented<sp/>to<sp/>DriverObject,</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>it<sp/>will<sp/>be<sp/>deleted<sp/>automatically<sp/>when<sp/>DriverObject<sp/>is<sp/>deleted.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>No<sp/>explicit<sp/>WdfObjectDelete(g_LogLock)<sp/>is<sp/>typically<sp/>needed<sp/>here.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kindref="member">g_LogLock</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kindref="member">g_LogLock</ref><sp/>=<sp/>NULL;<sp/></highlight><highlight class="comment">//<sp/>Just<sp/>NULL<sp/>it,<sp/>WDF<sp/>handles<sp/>actual<sp/>deletion.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="85"><highlight class="normal"></highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Log<sp/>system<sp/>uninitialized.\n&quot;</highlight><highlight class="normal">));</highlight></codeline>
<codeline lineno="87"><highlight class="normal">}</highlight></codeline>
<codeline lineno="88"><highlight class="normal"></highlight></codeline>
<codeline lineno="89"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="90" refid="driver__log_8c_1aa7f5f3b01615029c1cb54753c0b03175" refkind="member"><highlight class="normal"><ref refid="driver__log_8c_1aa7f5f3b01615029c1cb54753c0b03175" kindref="member">LogConfigInit</ref>(</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref><sp/>LogConfig</highlight></codeline>
<codeline lineno="92"><highlight class="normal">)<sp/>{</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Initializes<sp/>the<sp/>logging<sp/>configuration<sp/>structure<sp/>with<sp/>default<sp/>values.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(LogConfig<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(LogConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>));</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Set<sp/>default<sp/>values</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LogConfig-&gt;MinLevel<sp/>=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>;<sp/></highlight><highlight class="comment">//<sp/>Default<sp/>to<sp/>info<sp/>level</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LogConfig-&gt;LogTargets<sp/>=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kindref="member">LogTypeDebugger</ref>;<sp/></highlight><highlight class="comment">//<sp/>Default<sp/>to<sp/>debugger<sp/>output</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="100"><highlight class="normal">}</highlight></codeline>
<codeline lineno="101"><highlight class="normal"></highlight></codeline>
<codeline lineno="102"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="103" refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" refkind="member"><highlight class="normal"><ref refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" kindref="member">LogMessageVA</ref>(</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/>Level,</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PCSTR<sp/>Function,</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Line,</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PCSTR<sp/>Format,</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>va_list<sp/>Args</highlight></codeline>
<codeline lineno="109"><highlight class="normal">)<sp/>{</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Implementation<sp/>for<sp/>LogMessageVA</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>This<sp/>is<sp/>a<sp/>placeholder.<sp/>Actual<sp/>WPP<sp/>logging<sp/>requires<sp/>specific<sp/>setup.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we&apos;ll<sp/>just<sp/>ensure<sp/>the<sp/>file<sp/>compiles.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WPP_RECORDER_SF_q(TraceHandle,<sp/>0,<sp/>0,<sp/>0,<sp/>0);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="114"><highlight class="normal"></highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref><sp/>||<sp/>Level<sp/>&gt;<sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.MinLevel)<sp/>{</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="118"><highlight class="normal"></highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Example:<sp/>Output<sp/>to<sp/>debugger<sp/>if<sp/>enabled</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.LogTargets<sp/>&amp;<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127" kindref="member">LogTypeDebugger</ref>)<sp/>{</highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CHAR<sp/>messageBuffer[512];<sp/></highlight><highlight class="comment">//<sp/>Small<sp/>buffer<sp/>for<sp/>debugger<sp/>output</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>HRESULT<sp/>hr<sp/>=<sp/>RtlStringCchVPrintfA(messageBuffer,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(messageBuffer),<sp/>Format,<sp/>Args);</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(hr))<sp/>{</highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DbgPrintEx(DPFLTR_IHVDRIVER_ID,<sp/>DPFLTR_INFO_LEVEL,<sp/></highlight><highlight class="stringliteral">&quot;%s:%lu<sp/>%s\n&quot;</highlight><highlight class="normal">,<sp/>Function,<sp/>Line,<sp/>messageBuffer);</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DbgPrintEx(DPFLTR_IHVDRIVER_ID,<sp/>DPFLTR_ERROR_LEVEL,<sp/></highlight><highlight class="stringliteral">&quot;LogMessageVA:<sp/>RtlStringCchVPrintfA<sp/>failed<sp/>with<sp/>HRESULT<sp/>0x%X\n&quot;</highlight><highlight class="normal">,<sp/>hr);</highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="129"><highlight class="normal">}</highlight></codeline>
<codeline lineno="130"><highlight class="normal"></highlight></codeline>
<codeline lineno="131"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="132"><highlight class="comment"><sp/>*<sp/>LogInitialize<sp/>-<sp/>Initializes<sp/>the<sp/>logging<sp/>system</highlight></codeline>
<codeline lineno="133"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="134"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="135" refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" refkind="member"><highlight class="normal"><ref refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" kindref="member">LogInitialize</ref>(</highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1acd2f53446ede16834cc0bd30335e71cb" kindref="member">WDFDRIVER</ref><sp/>DriverObject,</highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>CONST<sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>*<sp/>InitialConfig</highlight></codeline>
<codeline lineno="138"><highlight class="normal">)</highlight></codeline>
<codeline lineno="139"><highlight class="normal">{</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>lockAttributes;</highlight></codeline>
<codeline lineno="142"><highlight class="normal"></highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>DriverObject<sp/>is<sp/>used<sp/>as<sp/>the<sp/>ParentObject<sp/>for<sp/>WDF<sp/>resources<sp/>like<sp/>g_LogLock.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="144"><highlight class="normal"></highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref>)<sp/>{</highlight></codeline>
<codeline lineno="146"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Log<sp/>system<sp/>already<sp/>initialized\n&quot;</highlight><highlight class="normal">));</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="core__types_8h_1aa4e1fcf1f71277fe7bbb792e4bc4f1f8" kindref="member">STATUS_ALREADY_INITIALIZED</ref>;<sp/></highlight><highlight class="comment">//<sp/>Or<sp/>just<sp/>STATUS_SUCCESS<sp/>if<sp/>re-init<sp/>is<sp/>okay</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="149"><highlight class="normal"></highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Copy<sp/>the<sp/>initial<sp/>configuration</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(InitialConfig<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="152"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>,<sp/>InitialConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>));</highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Use<sp/>default<sp/>if<sp/>no<sp/>config<sp/>provided<sp/>(g_LogConfig<sp/>already<sp/>has<sp/>defaults)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>No<sp/>initial<sp/>log<sp/>config<sp/>provided,<sp/>using<sp/>defaults.\n&quot;</highlight><highlight class="normal">));</highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="157"><highlight class="normal"></highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Validate<sp/>log<sp/>level<sp/>from<sp/>config</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.MinLevel<sp/>&gt;=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f" kindref="member">LogLevelMax</ref>)<sp/>{</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.MinLevel<sp/>=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>;<sp/></highlight><highlight class="comment">//<sp/>Default<sp/>to<sp/>a<sp/>safe<sp/>level<sp/>if<sp/>invalid</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG-WARN]<sp/>Invalid<sp/>initial<sp/>log<sp/>level<sp/>(%d)<sp/>in<sp/>config,<sp/>defaulting<sp/>to<sp/>INFO.\n&quot;</highlight><highlight class="normal">,<sp/>InitialConfig<sp/>?<sp/>InitialConfig-&gt;MinLevel<sp/>:<sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.MinLevel));</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="163"><highlight class="normal"></highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Create<sp/>a<sp/>spin<sp/>lock<sp/>for<sp/>thread-safe<sp/>logging</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;lockAttributes);</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/>lockAttributes.ParentObject<sp/>=<sp/>DriverObject;<sp/></highlight><highlight class="comment">//<sp/>Associate<sp/>with<sp/>driver<sp/>lifetime</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfSpinLockCreate(&amp;lockAttributes,<sp/>&amp;<ref refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kindref="member">g_LogLock</ref>);</highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG-ERROR]<sp/>Failed<sp/>to<sp/>create<sp/>log<sp/>spinlock:<sp/>0x%X\n&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>));</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1a9bcd4366f4ea891736b831d3d798ab36" kindref="member">g_LogLock</ref><sp/>=<sp/>NULL;<sp/></highlight><highlight class="comment">//<sp/>Ensure<sp/>it&apos;s<sp/>NULL<sp/>on<sp/>failure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Decide<sp/>if<sp/>this<sp/>is<sp/>a<sp/>fatal<sp/>error<sp/>for<sp/>logging.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>let&apos;s<sp/>proceed<sp/>but<sp/>log<sp/>the<sp/>issue.<sp/>To<sp/>make<sp/>it<sp/>fatal:<sp/>return<sp/>status;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="174"><highlight class="normal"></highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>If<sp/>file<sp/>logging<sp/>is<sp/>enabled,<sp/>initialize<sp/>the<sp/>log<sp/>file</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.LogTargets<sp/>&amp;<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kindref="member">LogTypeFile</ref>)<sp/>{</highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NTSTATUS<sp/>fileStatus;</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>OBJECT_ATTRIBUTES<sp/>objAttributes;</highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>IO_STATUS_BLOCK<sp/>ioStatusBlock;</highlight></codeline>
<codeline lineno="180"><highlight class="normal"></highlight></codeline>
<codeline lineno="181"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Define<sp/>the<sp/>log<sp/>file<sp/>path</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="182"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>RtlInitEmptyUnicodeString(&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>,<sp/><ref refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" kindref="member">g_LogFilePathBuffer</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="driver__log_8c_1a1dbaa3e63f60a2732e920d5fa35b54e4" kindref="member">g_LogFilePathBuffer</ref>));</highlight></codeline>
<codeline lineno="183"><highlight class="normal"></highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Using<sp/>\SystemRoot\Temp\<sp/>which<sp/>typically<sp/>resolves<sp/>to<sp/>C:\Windows\Temp\</highlight></codeline>
<codeline lineno="185"><highlight class="comment"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>Ensure<sp/>this<sp/>path<sp/>is<sp/>accessible<sp/>for<sp/>writing<sp/>by<sp/>the<sp/>driver&apos;s<sp/>execution<sp/>context<sp/>(SYSTEM)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="186"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NTSTATUS<sp/>pathStatus<sp/>=<sp/>RtlUnicodeStringPrintf(&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>,<sp/>L</highlight><highlight class="stringliteral">&quot;\\SystemRoot\\Temp\\KMDFDriver1.log&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(pathStatus))<sp/>{</highlight></codeline>
<codeline lineno="188"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG-ERROR]<sp/>Failed<sp/>to<sp/>construct<sp/>log<sp/>file<sp/>path<sp/>string<sp/>(0x%X).<sp/>Disabling<sp/>file<sp/>logging.\n&quot;</highlight><highlight class="normal">,<sp/>pathStatus));</highlight></codeline>
<codeline lineno="189"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.LogTargets<sp/>&amp;=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kindref="member">~LogTypeFile</ref>;<sp/></highlight><highlight class="comment">//<sp/>Disable<sp/>file<sp/>logging</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="190"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="191"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InitializeObjectAttributes(&amp;objAttributes,</highlight></codeline>
<codeline lineno="192"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>,</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>OBJ_CASE_INSENSITIVE<sp/>|<sp/>OBJ_KERNEL_HANDLE,</highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL);</highlight></codeline>
<codeline lineno="196"><highlight class="normal"></highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>fileStatus<sp/>=<sp/>ZwCreateFile(&amp;<ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref>,</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>FILE_APPEND_DATA<sp/>|<sp/>SYNCHRONIZE,<sp/></highlight><highlight class="comment">//<sp/>Append<sp/>data<sp/>and<sp/>allow<sp/>sync<sp/>I/O</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;objAttributes,</highlight></codeline>
<codeline lineno="200"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;ioStatusBlock,</highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>AllocationSize<sp/>(optional)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>FILE_ATTRIBUTE_NORMAL,</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>FILE_SHARE_READ,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Share<sp/>for<sp/>reading</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>FILE_OPEN_IF,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Open<sp/>if<sp/>exists,<sp/>create<sp/>if<sp/>not</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>FILE_SYNCHRONOUS_IO_NONALERT,<sp/></highlight><highlight class="comment">//<sp/>Flags<sp/>for<sp/>synchronous<sp/>I/O</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>EaBuffer<sp/>(optional)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0);<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>EaLength</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="208"><highlight class="normal"></highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(fileStatus))<sp/>{</highlight></codeline>
<codeline lineno="210"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG-ERROR]<sp/>Failed<sp/>to<sp/>open/create<sp/>log<sp/>file<sp/>%wZ,<sp/>status:<sp/>0x%X.<sp/>Disabling<sp/>file<sp/>logging.\n&quot;</highlight><highlight class="normal">,<sp/>&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>,<sp/>fileStatus));</highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref><sp/>=<sp/>NULL;<sp/></highlight><highlight class="comment">//<sp/>Ensure<sp/>handle<sp/>is<sp/>NULL<sp/>on<sp/>failure</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="212"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.LogTargets<sp/>&amp;=<sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kindref="member">~LogTypeFile</ref>;<sp/></highlight><highlight class="comment">//<sp/>Disable<sp/>file<sp/>logging<sp/>if<sp/>open<sp/>failed</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="213"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Successfully<sp/>opened/created<sp/>log<sp/>file:<sp/>%wZ\n&quot;</highlight><highlight class="normal">,<sp/>&amp;<ref refid="driver__log_8c_1a20c97100dfe2e67b76338f86895b9514" kindref="member">g_LogFilePath</ref>));</highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Optionally<sp/>write<sp/>a<sp/>header<sp/>to<sp/>the<sp/>log<sp/>file</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CHAR<sp/>fileHeader[128];</highlight></codeline>
<codeline lineno="217"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordtype">size_t</highlight><highlight class="normal"><sp/>headerLength;</highlight></codeline>
<codeline lineno="218"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Using<sp/>__DATE__<sp/>and<sp/>__TIME__<sp/>for<sp/>a<sp/>timestamp.<sp/>__TIMESTAMP__<sp/>is<sp/>also<sp/>an<sp/>option.</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="219"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NTSTATUS<sp/>headerStatus<sp/>=<sp/>RtlStringCchPrintfA(fileHeader,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(fileHeader),<sp/></highlight><highlight class="stringliteral">&quot;---<sp/>Log<sp/>Initialized:<sp/>%s<sp/>%s<sp/>---\r\n&quot;</highlight><highlight class="normal">,<sp/>__DATE__,<sp/>__TIME__);</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(headerStatus))<sp/>{</highlight></codeline>
<codeline lineno="221"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>headerStatus<sp/>=<sp/>RtlStringCchLengthA(fileHeader,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(fileHeader),<sp/>&amp;headerLength);</highlight></codeline>
<codeline lineno="222"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(headerStatus)<sp/>&amp;&amp;<sp/><ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref><sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="223"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(void)ZwWriteFile(<ref refid="driver__log_8c_1a0ac5b4438a064ed7f81749a6fdb8c459" kindref="member">g_LogFileHandle</ref>,</highlight></codeline>
<codeline lineno="224"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Event</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>ApcRoutine</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>ApcContext</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;ioStatusBlock,</highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>fileHeader,</highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(ULONG)headerLength,</highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>ByteOffset<sp/>(NULL<sp/>for<sp/>append<sp/>with<sp/>FILE_APPEND_DATA)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL);<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Key</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="237"><highlight class="normal"></highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="driver__log_8c_1acd7f22e672d8bbb5ac97c70459b869fb" kindref="member">g_LogInitialized</ref><sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/>KdPrint((</highlight><highlight class="stringliteral">&quot;[LOG]<sp/>Log<sp/>system<sp/>initialized.<sp/>MinLevel:<sp/>%s,<sp/>LogTargets:<sp/>0x%X\n&quot;</highlight><highlight class="normal">,<sp/><ref refid="src_2core_2log_2driver__log_8h_1ad2099051d14962ced91f03017c7021f3" kindref="member">g_LogLevelNames</ref>[<ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.MinLevel],<sp/><ref refid="driver__log_8c_1af114289be71fc27e5ce43d55d4d6622c" kindref="member">g_LogConfig</ref>.LogTargets));</highlight></codeline>
<codeline lineno="240"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;<sp/></highlight><highlight class="comment">//<sp/>Return<sp/>the<sp/>status<sp/>of<sp/>spinlock<sp/>creation<sp/>or<sp/>overall<sp/>success</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="241"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/core/log/driver_log.c"/>
  </compounddef>
</doxygen>
