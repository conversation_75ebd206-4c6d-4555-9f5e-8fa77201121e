<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li class="current"><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_p.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>ParentObject&#160;:&#160;<a class="el" href="gpio__core_8c.html#a9bfd47e9b367e692d16fa7a38e3944cc">gpio_core.c</a></li>
<li>PassiveHandling&#160;:&#160;<a class="el" href="gpio__core_8c.html#a41283b4328aa08e8f095578a73755e08">gpio_core.c</a></li>
<li>PBUS_CONFIG&#160;:&#160;<a class="el" href="kmdf__bus__common_8h.html#aeca4f45b7bb946c601f53bb52b08b170">kmdf_bus_common.h</a></li>
<li>PBUS_TRANSFER_PACKET&#160;:&#160;<a class="el" href="kmdf__bus__common_8h.html#a41f78cc35bd82acd5a7e7fd5fc51d00d">kmdf_bus_common.h</a></li>
<li>PCORE_CONFIG&#160;:&#160;<a class="el" href="core__types_8h.html#a874bd40bcd8acb69be8bc3133d852950">core_types.h</a></li>
<li>PCORE_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="core__types_8h.html#a585fa428acb755e88140079b19416f9a">core_types.h</a></li>
<li>PCORE_DEVICE_STATUS&#160;:&#160;<a class="el" href="core__types_8h.html#a567ab06ce289a42b52237fe208e5bcee">core_types.h</a></li>
<li>PCORE_REQUEST_CONTEXT&#160;:&#160;<a class="el" href="core__types_8h.html#ac6db23f2a14df51d8d00dac706f3756b">core_types.h</a></li>
<li>PCORE_STATISTICS&#160;:&#160;<a class="el" href="core__types_8h.html#aac5f1274e6c8f44dd15babfb28d8d92e">core_types.h</a></li>
<li>PCORE_STATUS&#160;:&#160;<a class="el" href="core__types_8h.html#a0cfe7c2eb9c5b617c4492b4d8253be39">core_types.h</a></li>
<li>PDEVICE_CONTEXT&#160;:&#160;<a class="el" href="device__manager_8h.html#ab05107b30d8429daed8e1e2165bafc32">device_manager.h</a></li>
<li>PDEVICE_INFO&#160;:&#160;<a class="el" href="device__manager_8h.html#ac8affe61bb901b8aa1b863ada6ac87bc">device_manager.h</a></li>
<li>PDEVICE_INIT_CONFIG&#160;:&#160;<a class="el" href="device__manager_8h.html#a57aff078ae347e96d32d9f9f76b00fb2">device_manager.h</a></li>
<li>PDRIVER_CONFIG&#160;:&#160;<a class="el" href="driver__core_8h.html#af3d65acd24f1c5a387f01ac14c86f337">driver_core.h</a></li>
<li>PDRIVER_CONTEXT&#160;:&#160;<a class="el" href="driver__core_8c.html#a703dc89b9de7ff2f89e1467ae811be52">driver_core.c</a></li>
<li>PDRIVER_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="driver__core_8h.html#abadaa97cf5a9145a7c73bebeccd17181">driver_core.h</a></li>
<li>PDRIVER_STATISTICS&#160;:&#160;<a class="el" href="driver__core_8h.html#a2c85f1797924669249bdab6c64fc6f07">driver_core.h</a></li>
<li>PDRIVER_VERSION&#160;:&#160;<a class="el" href="driver__core_8h.html#af034d45237796be01f25611c95f992b4">driver_core.h</a></li>
<li>PERROR_CODE_ENTRY&#160;:&#160;<a class="el" href="error__handling_8c.html#af6f1bd5b74ba972fc8a79164541e7af2">error_handling.c</a></li>
<li>PFN_WDF_DEVICE_CALLBACK&#160;:&#160;<a class="el" href="core__types_8h.html#a13ebe748cef10522645232bace79364b">core_types.h</a></li>
<li>PGPIO_DEVICE_CONFIG&#160;:&#160;<a class="el" href="gpio__device_8h.html#a3225b6d40b644661d13ce4d49580cc08">gpio_device.h</a></li>
<li>PGPIO_PIN_CONFIG&#160;:&#160;<a class="el" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">kmdf_gpio.h</a></li>
<li>PHAL_DEVICE_CONFIG&#160;:&#160;<a class="el" href="hal__interface_8h.html#a932781c14d58b9264941f4b01d6d0598">hal_interface.h</a></li>
<li>PHAL_RESOURCE&#160;:&#160;<a class="el" href="hal__interface_8h.html#a3ecd10532c8a96e3ab750ead2b0c941d">hal_interface.h</a></li>
<li>PI2C_ADDRESS&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#af11040ef31cae611dac879352c4fab17">kmdf_i2c.h</a></li>
<li>PI2C_CONFIG&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">kmdf_i2c.h</a></li>
<li>PI2C_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="i2c__core_8c.html#a9f0733f4be9833c3a734164c7711fbe5">i2c_core.c</a>, <a class="el" href="i2c__device_8c.html#a89c965cbcba7aedff1b61ea4c0498d3e">i2c_device.c</a></li>
<li>PI2C_STATISTICS&#160;:&#160;<a class="el" href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">i2c_device.h</a></li>
<li>PI2C_TRANSFER_PACKET&#160;:&#160;<a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">kmdf_i2c.h</a>, <a class="el" href="i2c__device_8h.html#a95996125e1f73ecc8e0ecf222dd14372">i2c_device.h</a></li>
<li>pinConfig()&#160;:&#160;<a class="el" href="gpio__device_8c.html#a546ebaed609861f7aa12740071033d54">gpio_device.c</a></li>
<li>pinContext()&#160;:&#160;<a class="el" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963">gpio_core.c</a></li>
<li>PinCount&#160;:&#160;<a class="el" href="gpio__core_8c.html#a3a285e05689c30bbe096ff555f7a5b68">gpio_core.c</a></li>
<li>PinNumber&#160;:&#160;<a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">gpio_device.c</a></li>
<li>Pins&#160;:&#160;<a class="el" href="gpio__core_8c.html#aa601b044abcd0035f84077010771020b">gpio_core.c</a></li>
<li>pinValue&#160;:&#160;<a class="el" href="gpio__device_8c.html#a8704f4f2bf5b602d6b300432f561fe4b">gpio_device.c</a></li>
<li>PLIST_ENTRY&#160;:&#160;<a class="el" href="core__types_8h.html#a377b615ce256badc15383ccb9216ac21">core_types.h</a></li>
<li>PLOG_CONFIG&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ab99d8d17b06b190b7fecbbadd3d6b7df">driver_log.h</a></li>
<li>Polarity&#160;:&#160;<a class="el" href="gpio__device_8c.html#ad6e10a2dc0aebdabca7a9c76612727a3">gpio_device.c</a></li>
<li>PrivateData&#160;:&#160;<a class="el" href="i2c__device_8c.html#a40d2c447ac37fcd86673f2a11b2ca094">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a40d2c447ac37fcd86673f2a11b2ca094">spi_device.c</a></li>
<li>PrivateDataSize&#160;:&#160;<a class="el" href="i2c__device_8c.html#a0470f3b47bad91bd5e08004c87a8d98a">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a0470f3b47bad91bd5e08004c87a8d98a">spi_device.c</a></li>
<li>PSPI_CONFIG&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">kmdf_spi.h</a></li>
<li>PSPI_DEVICE_CONTEXT&#160;:&#160;<a class="el" href="spi__core_8c.html#a5e8b4813a61b999753aee353d4944c23">spi_core.c</a>, <a class="el" href="spi__device_8c.html#a2d4e25dc12a54c28261d5ba390e3aa19">spi_device.c</a></li>
<li>PSPI_DEVICE_TRANSFER_PACKET&#160;:&#160;<a class="el" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">spi_device.h</a></li>
<li>PSPI_STATISTICS&#160;:&#160;<a class="el" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">spi_device.h</a></li>
<li>PSPI_TRANSFER_PACKET&#160;:&#160;<a class="el" href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">kmdf_spi.h</a></li>
<li>PTOUCH_POINT&#160;:&#160;<a class="el" href="device__manager_8h.html#a28f8698ae45fe8be0e44c195bea1e3ee">device_manager.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
