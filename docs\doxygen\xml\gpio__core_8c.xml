<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="gpio__core_8c" kind="file" language="C++">
    <compoundname>gpio_core.c</compoundname>
    <includes refid="precomp_8h" local="yes">../../precomp.h</includes>
    <includes refid="kmdf__gpio_8h" local="yes">../../../include/hal/bus/kmdf_gpio.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="19">
        <label>../../../include/hal/bus/kmdf_gpio.h</label>
        <link refid="kmdf__gpio_8h"/>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <innerclass refid="struct__GPIO__PIN__CONTEXT" prot="public">_GPIO_PIN_CONTEXT</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" prot="public" static="no">
        <name>GPIO_POOL_TAG</name>
        <initializer>&apos;OIPG&apos;</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="43" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="43" bodyend="-1"/>
        <referencedby refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" compoundref="gpio__core_8c" startline="140" endline="159">GPIOInitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
      </memberdef>
      <memberdef kind="define" id="gpio__core_8c_1a09573d341b2d8f94a213241de2444b0b" prot="public" static="no">
        <name>IOCTL_GPIO_GET_VALUE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="48" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="48" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="gpio__core_8c_1aa5235f4dd44bf922bf5befb2ef0b3b4b" prot="public" static="no">
        <name>IOCTL_GPIO_SET_DIRECTION</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="46" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="46" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="gpio__core_8c_1a921358974fe0b0cbe1288fd8bdc34196" prot="public" static="no">
        <name>IOCTL_GPIO_SET_VALUE</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="47" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="47" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" prot="public" static="no" mutable="no">
        <type>WDF_OBJECT_ATTRIBUTES_INIT &amp;</type>
        <definition>WDF_OBJECT_ATTRIBUTES_INIT&amp; attributes</definition>
        <argsstring></argsstring>
        <name>attributes</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="176" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="176" bodyend="-1"/>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" compoundref="gpio__core_8c" startline="80" endline="101">_GPIO_PIN_CONTEXT::if</referencedby>
        <referencedby refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" compoundref="gpio__device_8c" startline="195" endline="208">if</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1af0a4fb8c0dd33529dad81e93a0b0661f" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_DPC</type>
        <definition>EvtGpioInterruptDpc</definition>
        <argsstring></argsstring>
        <name>EvtGpioInterruptDpc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="134" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="134" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a4d9377385f26f2d958a494c7ffbcc04f" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_ISR</type>
        <definition>EvtGpioInterruptIsr</definition>
        <argsstring></argsstring>
        <name>EvtGpioInterruptIsr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="133" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="133" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" prot="public" static="no" mutable="no">
        <type>GENERIC_READ</type>
        <definition>GENERIC_READ GENERIC_WRITE</definition>
        <argsstring></argsstring>
        <name>GENERIC_WRITE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="154" column="27" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="154" bodyend="-1"/>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a33c00fdea3f12acb400049b8ef710ea9" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref></type>
        <definition>pinContext Initialized</definition>
        <argsstring></argsstring>
        <name>Initialized</name>
        <initializer>= TRUE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="213" column="15" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="213" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" prot="public" static="no" mutable="no">
        <type>&amp;</type>
        <definition>&amp; interruptConfig</definition>
        <argsstring></argsstring>
        <name>interruptConfig</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="169" column="13" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="169" bodyend="-1"/>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a4983b2b08534e2a12b6e0c30d87594f0" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref></type>
        <definition>interruptConfig InterruptTranslated</definition>
        <argsstring></argsstring>
        <name>InterruptTranslated</name>
        <initializer>= TRUE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="173" column="24" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="173" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1ac91080baa5062b15cd46c8e08028fd51" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref></type>
        <definition>WDFSPINLOCK Lock</definition>
        <argsstring></argsstring>
        <name>Lock</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="119" column="17" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="119" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" prot="public" static="no" mutable="no">
        <type>&amp;</type>
        <definition>&amp; openParams</definition>
        <argsstring></argsstring>
        <name>openParams</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="152" column="13" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="152" bodyend="-1"/>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a9bfd47e9b367e692d16fa7a38e3944cc" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref></type>
        <definition>attributes ParentObject</definition>
        <argsstring></argsstring>
        <name>ParentObject</name>
        <initializer>= Device</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="125" column="19" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="125" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a41283b4328aa08e8f095578a73755e08" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref></type>
        <definition>interruptConfig PassiveHandling</definition>
        <argsstring></argsstring>
        <name>PassiveHandling</name>
        <initializer>= FALSE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="174" column="24" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="174" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a3a285e05689c30bbe096ff555f7a5b68" prot="public" static="no" mutable="no">
        <type>gpioManager</type>
        <definition>gpioManager PinCount</definition>
        <argsstring></argsstring>
        <name>PinCount</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="210" column="16" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="210" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1aa601b044abcd0035f84077010771020b" prot="public" static="no" mutable="no">
        <type>PGPIO_PIN_CONTEXT</type>
        <definition>PGPIO_PIN_CONTEXT Pins[256]</definition>
        <argsstring>[256]</argsstring>
        <name>Pins</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="118" column="23" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="118" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" prot="public" static="no" mutable="no">
        <type>&amp;</type>
        <definition>&amp; spbDevicePath</definition>
        <argsstring></argsstring>
        <name>spbDevicePath</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="153" column="13" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="153" bodyend="-1"/>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1af781006198c718a3a6e46d55cdb1e74c" prot="public" static="no" mutable="no">
        <type>WDFIOTARGET</type>
        <definition>WDFIOTARGET SpbIoTarget</definition>
        <argsstring></argsstring>
        <name>SpbIoTarget</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="115" column="17" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="115" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" prot="public" static="no" mutable="no">
        <type></type>
        <definition>return status</definition>
        <argsstring></argsstring>
        <name>status</name>
        <initializer>= WdfIoTargetCreate(Device, &amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>, &amp;<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;<ref refid="gpio__core_8c_1af781006198c718a3a6e46d55cdb1e74c" kindref="member">SpbIoTarget</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="127" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="127" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" compoundref="device__manager_8c" startline="957" endline="976">DeviceD0Entry</referencedby>
        <referencedby refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" compoundref="device__manager_8c" startline="982" endline="1001">DeviceD0Exit</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" compoundref="device__manager_8c" startline="1113" endline="1145">DeviceManager_EvtDeviceAdd</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" compoundref="device__manager_8c" startline="876" endline="951">DeviceReleaseHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" compoundref="driver__core_8c" startline="255" endline="343">DriverCoreProcessIoControl</referencedby>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
        <referencedby refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" compoundref="gpio__device_8c" startline="102" endline="123">EvtDebounceTimerFunc</referencedby>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
        <referencedby refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" compoundref="gpio__core_8c" startline="140" endline="159">GPIOInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" compoundref="i2c__device_8c" startline="314" endline="326">I2cDeviceTransfer</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" compoundref="gpio__core_8c" startline="80" endline="101">_GPIO_PIN_CONTEXT::if</referencedby>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
        <referencedby refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" compoundref="gpio__device_8c" startline="195" endline="208">if</referencedby>
        <referencedby refid="gpio__core_8c_1a495a75edfacf86c27c05ce15ada3509e" compoundref="gpio__core_8c" startline="162" endline="183">if</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
        <referencedby refid="spi__device_8c_1a4b5c92f0859e4be1ead5d71edc903427" compoundref="spi__device_8c" startline="230" endline="232">if</referencedby>
        <referencedby refid="spi__device_8c_1a164e77dd43f69d29ea926ae0ec42969b" compoundref="spi__device_8c" startline="284" endline="286">if</referencedby>
        <referencedby refid="gpio__device_8c_1a14190d6765d5c660291c1d6839cc5428" compoundref="gpio__device_8c" startline="248" endline="251">if</referencedby>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" compoundref="spi__device_8c" startline="148" endline="160">SpiDeviceTransfer</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return STATUS_INVALID_PARAMETER</definition>
        <argsstring></argsstring>
        <name>STATUS_INVALID_PARAMETER</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="191" column="16" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="191" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
        <referencedby refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" compoundref="gpio__device_8c" startline="367" endline="385">GpioDeviceGetState</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" compoundref="gpio__device_8c" startline="391" endline="413">GpioDeviceRegisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" compoundref="gpio__device_8c" startline="419" endline="439">GpioDeviceUnregisterCallback</referencedby>
        <referencedby refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" compoundref="i2c__device_8c" startline="362" endline="372">I2cDeviceGetStatistics</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" compoundref="i2c__device_8c" startline="314" endline="326">I2cDeviceTransfer</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="spi__device_8c_1ae2be7c6b48ddf5b08876e1115879469d" compoundref="spi__device_8c" startline="298" endline="308">SpiDeviceGetStatistics</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" compoundref="spi__device_8c" startline="148" endline="160">SpiDeviceTransfer</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return STATUS_SUCCESS</definition>
        <argsstring></argsstring>
        <name>STATUS_SUCCESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="218" column="12" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="218" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1ae4d976e80d1c2e2961eed2dc2ff6318c" compoundref="device__manager_8c" startline="316" endline="354">DeviceConfigureIoQueue</referencedby>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1acb3d9726752bc4014673e7d6999b4e4b" compoundref="device__manager_8c" startline="957" endline="976">DeviceD0Entry</referencedby>
        <referencedby refid="device__manager_8c_1ad82beaffc976103892cb92d64dd6e2de" compoundref="device__manager_8c" startline="982" endline="1001">DeviceD0Exit</referencedby>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1a5f57ab0104efb724ddfbd5cd875a05d8" compoundref="device__manager_8c" startline="258" endline="272">DeviceInterruptDisable</referencedby>
        <referencedby refid="device__manager_8c_1a277804b1fb6ab9ee7541265ce68ae6bb" compoundref="device__manager_8c" startline="237" endline="252">DeviceInterruptEnable</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1afa743e52d2410ed36cf2f06a30c07fcd" compoundref="device__manager_8c" startline="146" endline="154">DeviceMapResources</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a214c96b10358f61af2f6a9ef3752ebc3" compoundref="device__manager_8c" startline="876" endline="951">DeviceReleaseHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="device__manager_8c_1ac506ca5136446bf4cc6f68c0747f56e0" compoundref="device__manager_8c" startline="172" endline="180">DeviceSetupInterrupt</referencedby>
        <referencedby refid="device__manager_8c_1a55323ce5aacec6667669f95f8abf7e22" compoundref="device__manager_8c" startline="160" endline="166">DeviceUnmapResources</referencedby>
        <referencedby refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" compoundref="driver__core_8c" startline="255" endline="343">DriverCoreProcessIoControl</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
        <referencedby refid="driver__main_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__main_8c" startline="38" endline="101">DriverEntry</referencedby>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" compoundref="gpio__device_8c" startline="367" endline="385">GpioDeviceGetState</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" compoundref="gpio__device_8c" startline="391" endline="413">GpioDeviceRegisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" compoundref="gpio__device_8c" startline="419" endline="439">GpioDeviceUnregisterCallback</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" compoundref="i2c__device_8c" startline="192" endline="208">I2cDeviceRead</referencedby>
        <referencedby refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" compoundref="i2c__device_8c" startline="314" endline="326">I2cDeviceTransfer</referencedby>
        <referencedby refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" compoundref="i2c__device_8c" startline="253" endline="269">I2cDeviceWrite</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__device_8c_1a3bc98267d67ee8988179bde952efaa87" compoundref="spi__device_8c" startline="194" endline="210">SpiDeviceRead</referencedby>
        <referencedby refid="spi__device_8c_1a2428921b9d71ab9d24f34e0a7b23487c" compoundref="spi__device_8c" startline="148" endline="160">SpiDeviceTransfer</referencedby>
        <referencedby refid="spi__device_8c_1ae90ccf3d865bebb54c2c76e10fcbcaa8" compoundref="spi__device_8c" startline="241" endline="257">SpiDeviceWrite</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="variable" id="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>deviceContext WdfDevice</definition>
        <argsstring></argsstring>
        <name>WdfDevice</name>
        <initializer>= Device</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="114" column="15" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="114" bodyend="-1"/>
        <referencedby refid="hal__interface_8h_1a77d5908ab2a098166969c80028859e28">HalDeviceOpen</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS if::GPIOInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig)</argsstring>
        <name>GPIOInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref></type>
          <declname>GpioConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="140" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="140" bodyend="159"/>
        <references refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" compoundref="gpio__core_8c" startline="43">GPIO_POOL_TAG</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID GPIOUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ ULONG PinNumber)</argsstring>
        <name>GPIOUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="225" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="225" bodyend="279"/>
        <references refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</references>
        <references refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" compoundref="gpio__core_8c" startline="43">GPIO_POOL_TAG</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" compoundref="gpio__device_8c" startline="212">PinNumber</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(!NT_SUCCESS(status))</argsstring>
        <name>if</name>
        <param>
          <type>!</type>
          <declname>NT_SUCCESS</declname>
          <array>status</array>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="128" column="9" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="128" bodyend="134"/>
        <references refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146">ExFreePoolWithTag</references>
        <references refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" compoundref="gpio__core_8c" startline="43">GPIO_POOL_TAG</references>
        <references refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b">LogError</references>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a495a75edfacf86c27c05ce15ada3509e" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if::if</definition>
        <argsstring>(gpioManager==NULL)</argsstring>
        <name>if</name>
        <param>
          <type>gpioManager</type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="162" column="5" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="162" bodyend="183"/>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>struct <ref refid="struct__GPIO__PIN__CONTEXT" kindref="compound">_GPIO_PIN_CONTEXT</ref></type>
        <definition>struct _GPIO_PIN_CONTEXT if</definition>
        <argsstring>(pinContext==NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref></type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="1" column="0" bodyfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" bodystart="113" bodyend="117"/>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if::LogError</definition>
        <argsstring>(ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;PinNumber)</argsstring>
        <name>LogError</name>
        <param>
          <type>ERROR_PIN_ALREADY_REGISTERED</type>
        </param>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;Pin %d is already configured or out of range&quot;</type>
        </param>
        <param>
          <type>GpioConfig-&gt;</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="188" column="9" declfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" declline="188" declcolumn="9"/>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a8f2aeb9f5f8525345d3f33df4340da13" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;GPIO pin %d initialized successfully&quot;, GpioConfig-&gt;PinNumber)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;GPIO pin %d initialized successfully&quot;</type>
        </param>
        <param>
          <type>GpioConfig-&gt;</type>
          <declname>PinNumber</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="217" column="5" declfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" declline="217" declcolumn="5"/>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref> &amp;</type>
        <definition>RtlCopyMemory &amp; pinContext</definition>
        <argsstring>(GPIO_PIN_CONFIG)</argsstring>
        <name>pinContext</name>
        <param>
          <type><ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref></type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="121" column="5" declfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" declline="121" declcolumn="5"/>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" compoundref="gpio__core_8c" startline="140" endline="159">GPIOInitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
        <referencedby refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b">LogError</referencedby>
        <referencedby refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</referencedby>
        <referencedby refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>RtlZeroMemory</definition>
        <argsstring>(pinContext, sizeof(GPIO_PIN_CONTEXT))</argsstring>
        <name>RtlZeroMemory</name>
        <param>
          <type><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref></type>
        </param>
        <param>
          <type>sizeof(GPIO_PIN_CONTEXT)</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="119" column="5" declfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" declline="119" declcolumn="5"/>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" compoundref="driver__core_8c" startline="221" endline="249">DriverCoreCleanup</referencedby>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="driver__log_8c_1aa7f5f3b01615029c1cb54753c0b03175" compoundref="driver__log_8c" startline="90" endline="100">LogConfigInit</referencedby>
      </memberdef>
      <memberdef kind="function" id="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>WdfSpinLockRelease</definition>
        <argsstring>(gpioManager-&gt;Lock)</argsstring>
        <name>WdfSpinLockRelease</name>
        <param>
          <type>gpioManager-&gt;</type>
          <declname>Lock</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c" line="190" column="9" declfile="C:/KMDF Driver1/src/hal/bus/gpio_core.c" declline="190" declcolumn="9"/>
        <references refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963">pinContext</references>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" compoundref="gpio__core_8c" startline="128" endline="134">if</referencedby>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1" refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" refkind="member"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>gpio_core.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>GPIO鎬荤嚎鏍稿績鍔熻兘瀹炵幇</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>======================</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="7"><highlight class="comment"><sp/>*<sp/>鐩殑锛氭彁渚汫PIO鎬荤嚎鍒濆鍖栥€佹帶鍒跺拰涓柇绠＄悊鍔熻兘</highlight></codeline>
<codeline lineno="8"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="9"><highlight class="comment"><sp/>*<sp/>鎬濊矾锛?<sp/>*<sp/>1.<sp/>鎻愪緵GPIO鎬荤嚎鍒濆鍖栥€佹帶鍒跺拰涓柇绠＄悊鍔熻兘</highlight></codeline>
<codeline lineno="10"><highlight class="comment"><sp/>*<sp/>2.<sp/>瀹炵幇閫氱敤鐨凣PIO鎿嶄綔鎺ュ彛锛屼究浜庝笂灞傚簲鐢?<sp/>*<sp/>3.<sp/>鏀寔杈撳叆杈撳嚭銆佷腑鏂Е鍙戝拰瀵瑰璁惧閫氫俊</highlight></codeline>
<codeline lineno="11"><highlight class="comment"><sp/>*<sp/>4.<sp/>浣跨敤WDF妗嗘灦瀹炵幇GPIO鎺у埗鍣ㄦ娊璞?<sp/>*</highlight></codeline>
<codeline lineno="12"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------------------+</highlight></codeline>
<codeline lineno="13"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/>GPIO鎺у埗鎺ュ彛<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="14"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------+-----------+</highlight></codeline>
<codeline lineno="15"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="16"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------v-----------+</highlight></codeline>
<codeline lineno="17"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/><sp/><sp/>涓柇鍜屼俊鍙峰鐞?<sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="18"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------+-----------+</highlight></codeline>
<codeline lineno="19"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="20"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------v-----------+</highlight></codeline>
<codeline lineno="21"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>|<sp/><sp/><sp/><sp/>纭欢鎶借薄灞?HAL)<sp/><sp/><sp/><sp/><sp/>|</highlight></codeline>
<codeline lineno="22"><highlight class="comment"><sp/>*<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>+------------------------+</highlight></codeline>
<codeline lineno="23"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">../../precomp.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__gpio_8h" kindref="compound">../../../include/hal/bus/kmdf_gpio.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO鐠佹儳顦稉濠佺瑓閺傚洨绮ㄩ弸鍕秼</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="struct__GPIO__PIN__CONTEXT" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__GPIO__PIN__CONTEXT" kindref="compound">_GPIO_PIN_CONTEXT</ref><sp/>{</highlight></codeline>
<codeline lineno="32" refid="struct__GPIO__PIN__CONTEXT_1a9e971cde7006142d4b5ac56689228e0b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a9e971cde7006142d4b5ac56689228e0b" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF鐠佹儳顦€电钖?<sp/><sp/><sp/><sp/>GPIO_PIN_CONFIG<sp/>Config;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>GPIO闁板秶鐤嗘穱鈩冧紖</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33" refid="struct__GPIO__PIN__CONTEXT_1a81f98266b74a3a8d80a632eead026240" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFIOTARGET<sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a81f98266b74a3a8d80a632eead026240" kindref="member">SpbIoTarget</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB<sp/>I/O閻╊喗鐖?<sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>娑擃厽鏌囩€电钖?<sp/><sp/><sp/><sp/>GPIO_INTERRUPT_CALLBACK<sp/>CallbackFunction;<sp/>//<sp/>閻劍鍩涙稉顓熸焽閸ョ偠鐨?<sp/><sp/><sp/><sp/>PVOID<sp/>CallbackContext;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>閸ョ偠鐨熸稉濠佺瑓閺?<sp/><sp/><sp/><sp/>BOOLEAN<sp/>Initialized;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>閸掓繂顫愰崠鏍ㄧ垼韫?}<sp/>GPIO_PIN_CONTEXT,<sp/>*PGPIO_PIN_CONTEXT;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO鐠佹儳顦粻锛勬倞閸ｃ劎绮ㄩ弸?typedef<sp/>struct<sp/>_GPIO_DEVICE_MANAGER<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__GPIO__PIN__CONTEXT_1a4dd51420a9c389d77c721c25d4349927" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_PIN_CONTEXT<sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a4dd51420a9c389d77c721c25d4349927" kindref="member">Pins</ref>[256];<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺€顖涘瘮閺堚偓婢?56娑擃亜绱╅懘?<sp/><sp/><sp/><sp/>ULONG<sp/>PinCount;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>瑜版挸澧犻柊宥囩枂閻ㄥ嫬绱╅懘姘殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__GPIO__PIN__CONTEXT_1ae1602e3f0f02ccfe7b30fdaa89b017a7" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1ae1602e3f0f02ccfe7b30fdaa89b017a7" kindref="member">Lock</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娣囨繃濮㈢拋鍧楁６閻ㄥ嫰鏀?}<sp/>GPIO_DEVICE_MANAGER,<sp/>*PGPIO_DEVICE_MANAGER;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鐠佹儳顦稉濠佺瑓閺傚洩顔栭梻顔兼珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal">WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(GPIO_DEVICE_MANAGER,<sp/>GetGpioManager)</highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight></codeline>
<codeline lineno="42"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO\u6c60\u6807\u8bb0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>GPIO_POOL_TAG<sp/>&apos;OIPG&apos;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight></codeline>
<codeline lineno="45"><highlight class="normal"></highlight><highlight class="comment">//<sp/>GPIO<sp/>IOCTL<sp/>\u4ee3\u7801</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="46" refid="gpio__core_8c_1aa5235f4dd44bf922bf5befb2ef0b3b4b" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_SET_DIRECTION<sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x800,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47" refid="gpio__core_8c_1a921358974fe0b0cbe1288fd8bdc34196" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_SET_VALUE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x801,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="gpio__core_8c_1a09573d341b2d8f94a213241de2444b0b" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_GPIO_GET_VALUE<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x802,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49"><highlight class="normal"></highlight></codeline>
<codeline lineno="50"><highlight class="normal"></highlight><highlight class="comment">//<sp/>閸撳秴鎮滄竟鐗堟</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51" refid="struct__GPIO__PIN__CONTEXT_1a4b4c364430ee4f37c01413a6552df5d0" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_ISR<sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a4b4c364430ee4f37c01413a6552df5d0" kindref="member">EvtGpioInterruptIsr</ref>;</highlight></codeline>
<codeline lineno="52" refid="struct__GPIO__PIN__CONTEXT_1a44db5b39e5c14ce83ac0637da253fb47" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_DPC<sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a44db5b39e5c14ce83ac0637da253fb47" kindref="member">EvtGpioInterruptDpc</ref>;</highlight></codeline>
<codeline lineno="53"><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="55"><highlight class="comment"><sp/>*<sp/>GPIOInitialize<sp/>-<sp/>閸掓繂顫愰崠鏈慞IO閹崵鍤庨幒銉ュ經</highlight></codeline>
<codeline lineno="56"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="58" refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" refkind="member"><highlight class="normal"><ref refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" kindref="member">GPIOInitialize</ref>(</highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__gpio_8h_1aa14439653449111e1deb51cf90c5b7a0" kindref="member">PGPIO_PIN_CONFIG</ref><sp/>GpioConfig</highlight></codeline>
<codeline lineno="61"><highlight class="normal">)</highlight></codeline>
<codeline lineno="62"><highlight class="normal">{</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_MANAGER<sp/>gpioManager<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_PIN_CONTEXT<sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>;</highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/>DECLARE_UNICODE_STRING_SIZE(<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,<sp/>RESOURCE_HUB_PATH_SIZE);</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_IO_TARGET_OPEN_PARAMS<sp/><ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>;</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_INTERRUPT_CONFIG<sp/><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>;</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG<sp/>lockConfig;</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>GPIO<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>GpioConfig<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰嘒PIO缁狅紕鎮婇崳?<sp/><sp/><sp/><sp/>gpioManager<sp/>=<sp/>GetGpioManager(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="80" refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(gpioManager<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>妫ｆ牗顐肩拫鍐暏閿涘苯鍨卞铏诡吀閻炲棗娅?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&amp;attributes,<sp/>GPIO_DEVICE_MANAGER);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfObjectAllocateContext(Device,<sp/>&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,<sp/>(PVOID*)&amp;gpioManager);</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>allocate<sp/>GPIO<sp/>manager<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍吀閻炲棗娅?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>RtlZeroMemory(gpioManager,<sp/>sizeof(GPIO_DEVICE_MANAGER));</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂撻柨?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG_INIT(&amp;lockConfig,<sp/>WdfSpinLockTypeExclusive);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>);</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfSpinLockCreate(&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,<sp/>&amp;lockConfig,<sp/>&amp;gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>GPIO<sp/>manager<sp/>lock&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>闁夸礁鐣剧粻锛勬倞閸?<sp/><sp/><sp/><sp/>WdfSpinLockAcquire(gpioManager-&gt;Lock);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濡偓閺屻儱绱╅懘姘Ц閸氾箑鍑＄紒蹇涘帳缂?<sp/><sp/><sp/><sp/>if<sp/>(GpioConfig-&gt;PinNumber<sp/>&gt;=<sp/>256<sp/>||<sp/>gpioManager-&gt;Pins[GpioConfig-&gt;PinNumber]<sp/>!=<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="106" refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a38aa560da9bc169cb9b45c3df5ea4454" kindref="member">LogError</ref>(ERROR_PIN_ALREADY_REGISTERED,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="stringliteral">&quot;Pin<sp/>%d<sp/>is<sp/>already<sp/>configured<sp/>or<sp/>out<sp/>of<sp/>range&quot;</highlight><highlight class="normal">,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="108" refid="struct__GPIO__PIN__CONTEXT_1a846739f679fc9ddea85cb263aa72dc9f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1a846739f679fc9ddea85cb263aa72dc9f" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="109" refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="struct__GPIO__PIN__CONTEXT_1ad95efe470c1ee3b4b0f0d81c7f3c53fb" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掑棝鍘ゅ鏇″壖娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>pinContext<sp/>=<sp/>ExAllocatePoolWithTag(NonPagedPool,<sp/>sizeof(GPIO_PIN_CONTEXT),<sp/>GPIO_POOL_TAG);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="114" refid="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a0e8ed8133680d2d6b8909e71b8048307" kindref="member">ERROR_NOT_ENOUGH_MEMORY</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>allocate<sp/>pin<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="115" refid="gpio__core_8c_1af781006198c718a3a6e46d55cdb1e74c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_INSUFFICIENT_RESOURCES;</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="118" refid="gpio__core_8c_1aa601b044abcd0035f84077010771020b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="119" refid="gpio__core_8c_1ac91080baa5062b15cd46c8e08028fd51" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aa5ccd638c5bf670b734784f2601b7ec7" kindref="member">RtlZeroMemory</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(GPIO_PIN_CONTEXT));</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="121" refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Config,<sp/>GpioConfig,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__gpio_8h_1ab852084eda7787e469301a172c7498a5" kindref="member">GPIO_PIN_CONFIG</ref>));</highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐幓鎰返娴滃摖PB鐠佹儳顦€电钖勯敍灞藉灡瀵ょ瘨/O閻╊喗鐖?<sp/><sp/><sp/><sp/>if<sp/>(GpioConfig-&gt;SpbDeviceObject<sp/>!=<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂揑O閻╊喗鐖?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;attributes);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="125" refid="gpio__core_8c_1a9bfd47e9b367e692d16fa7a38e3944cc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="127" refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfIoTargetCreate(Device,<sp/>&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,<sp/>&amp;<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="128" refid="gpio__core_8c_1a1a243a15dd793b6d0f7b7011461a8641" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>IO<sp/>target<sp/>for<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/><ref refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kindref="member">GPIO_POOL_TAG</ref>);</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="133" refid="gpio__core_8c_1a4d9377385f26f2d958a494c7ffbcc04f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="134" refid="gpio__core_8c_1af0a4fb8c0dd33529dad81e93a0b0661f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="135"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嬪嫰鈧嚞PB鐠у嫭绨捄顖氱窞</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>RESOURCE_HUB_CREATE_PATH_FROM_ID(</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;SpbConnectionId.LowPart,</highlight></codeline>
<codeline lineno="140" refid="gpio__core_8c_1a50ea4c2976c29c52387cd273dc289c3b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;SpbConnectionId.HighPart);</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="142"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>SPB<sp/>resource<sp/>path<sp/>for<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="144"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="146"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/><ref refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kindref="member">GPIO_POOL_TAG</ref>);</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹垫挸绱慖O閻╊喗鐖?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_IO_TARGET_OPEN_PARAMS_INIT_OPEN_BY_NAME(</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="152" refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>,</highlight></codeline>
<codeline lineno="153" refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,</highlight></codeline>
<codeline lineno="154" refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GENERIC_READ<sp/>|<sp/><ref refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" kindref="member">GENERIC_WRITE</ref>);</highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfIoTargetOpen(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget,<sp/>&amp;<ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>);</highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>open<sp/>SPB<sp/>IO<sp/>target<sp/>for<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/><ref refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kindref="member">GPIO_POOL_TAG</ref>);</highlight></codeline>
<codeline lineno="162" refid="gpio__core_8c_1a495a75edfacf86c27c05ce15ada3509e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夐柊宥囩枂娴滃棔鑵戦弬顓ㄧ礉閸掓稑缂撴稉顓熸焽鐎电钖?<sp/><sp/><sp/><sp/>if<sp/>(GpioConfig-&gt;InterruptType<sp/>!=<sp/>GpioInterruptNone)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂撴稉顓熸焽鐎电钖?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_INTERRUPT_CONFIG_INIT(</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="169" refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>,</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a4d9377385f26f2d958a494c7ffbcc04f" kindref="member">EvtGpioInterruptIsr</ref>,</highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1af0a4fb8c0dd33529dad81e93a0b0661f" kindref="member">EvtGpioInterruptDpc</ref>);</highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="173" refid="gpio__core_8c_1a4983b2b08534e2a12b6e0c30d87594f0" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>.InterruptTranslated<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="174" refid="gpio__core_8c_1a41283b4328aa08e8f095578a73755e08" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>.PassiveHandling<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="176" refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>);</highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfInterruptCreate(</highlight></codeline>
<codeline lineno="180"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Device,</highlight></codeline>
<codeline lineno="181"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>,</highlight></codeline>
<codeline lineno="182"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ac5d07e96f745ca56ee79420f0f039f66" kindref="member">attributes</ref>,</highlight></codeline>
<codeline lineno="183"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="185"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="186"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>interrupt<sp/>for<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="187"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="188" refid="gpio__core_8c_1a9f7acfc8c5f9d7d32170223f628f766b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="189"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="190" refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfIoTargetClose(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="191" refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="192"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/><ref refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kindref="member">GPIO_POOL_TAG</ref>);</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="199"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="200"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍х穿閼存碍鏌熼崥?<sp/><sp/><sp/><sp/>if<sp/>(GpioConfig-&gt;Direction<sp/>==<sp/>GpioDirectionOut)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐎甸€涚艾鏉堟挸鍤鏇″壖閿涘矁顔曠純顔煎灥婵鈧?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>BOOLEAN<sp/>initialValue<sp/>=<sp/>(GpioConfig-&gt;Polarity<sp/>==<sp/>GpioPolarityActiveLow)<sp/>?<sp/>TRUE<sp/>:<sp/>FALSE;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="kmdf__gpio_8h_1a81265230a3fa84b9f3a7851d8c9ebe3d" kindref="member">GPIOSetValue</ref>(Device,<sp/>GpioConfig-&gt;PinNumber,<sp/>initialValue);</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>set<sp/>initial<sp/>value<sp/>for<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娑撳秷绻戦崶鐐烘晩鐠囶垽绱濈紒褏鐢婚崚婵嗩潗閸?<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞ｈ濮為崚鎵吀閻炲棗娅?<sp/><sp/><sp/><sp/>gpioManager-&gt;Pins[GpioConfig-&gt;PinNumber]<sp/>=<sp/>pinContext;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="210" refid="gpio__core_8c_1a3a285e05689c30bbe096ff555f7a5b68" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>gpioManager-&gt;PinCount++;</highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="212"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嶅洩顔囨稉鍝勫灥婵瀵茬€瑰本鍨?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="213" refid="gpio__core_8c_1a33c00fdea3f12acb400049b8ef710ea9" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Initialized<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="217" refid="gpio__core_8c_1a8f2aeb9f5f8525345d3f33df4340da13" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>pin<sp/>%d<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">,<sp/>GpioConfig-&gt;PinNumber);</highlight></codeline>
<codeline lineno="218" refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="219"><highlight class="normal">}</highlight></codeline>
<codeline lineno="220"><highlight class="normal"></highlight></codeline>
<codeline lineno="221"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="222"><highlight class="comment"><sp/>*<sp/>GPIOUninitialize<sp/>-<sp/>濞撳懐鎮奊PIO閹崵鍤庨幒銉ュ經</highlight></codeline>
<codeline lineno="223"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="224"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="225" refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" refkind="member"><highlight class="normal"><ref refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" kindref="member">GPIOUninitialize</ref>(</highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref></highlight></codeline>
<codeline lineno="228"><highlight class="normal">)</highlight></codeline>
<codeline lineno="229"><highlight class="normal">{</highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_DEVICE_MANAGER<sp/>gpioManager<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/>PGPIO_PIN_CONTEXT<sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Uninitializing<sp/>GPIO<sp/>pin<sp/>%d&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>);</highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u83b7\u53d6GPIO\u7ba1\u7406\u5668</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/>gpioManager<sp/>=<sp/>GetGpioManager(Device);</highlight></codeline>
<codeline lineno="237"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(gpioManager<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>manager<sp/>not<sp/>found&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="239"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="240"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="241"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="242"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u9501\u5b9a\u7ba1\u7406\u5668</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfSpinLockAcquire(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="244"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="245"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u68c0\u67e5\u5f15\u811a\u662f\u5426\u5b58\u5728</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref><sp/>&gt;=<sp/>256<sp/>||<sp/>gpioManager-&gt;Pins[<ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>]<sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="247"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Pin<sp/>%d<sp/>is<sp/>not<sp/>configured&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>);</highlight></codeline>
<codeline lineno="248"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="249"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="250"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="251"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="252"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u83b7\u53d6\u5f15\u811a\u4e0a\u4e0b\u6587</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="253"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref><sp/>=<sp/>gpioManager-&gt;Pins[<ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>];</highlight></codeline>
<codeline lineno="254"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="255"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u505c\u6b62\u4e2d\u65ad</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="256"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Interrupt<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfInterruptDisable(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;Interrupt<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="260"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="261"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u5173\u95edSPB\u8fde\u63a5</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="264"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfIoTargetClose(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="265"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="266"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>-&gt;SpbIoTarget<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="267"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="268"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="269"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u4ece\u7ba1\u7406\u5668\u79fb\u9664</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="270"><highlight class="normal"><sp/><sp/><sp/><sp/>gpioManager-&gt;Pins[<ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>]<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/>gpioManager-&gt;PinCount--;</highlight></codeline>
<codeline lineno="272"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="273"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>\u91ca\u653e\u5f15\u811a\u4e0a\u4e0b\u6587</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="274"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1ae42ccb14fff6c8b1c06d1ff178b6c146" kindref="member">ExFreePoolWithTag</ref>(<ref refid="gpio__core_8c_1a401edc28835c1919fe788f9583c5b963" kindref="member">pinContext</ref>,<sp/><ref refid="gpio__core_8c_1aaccace669b39ad606306ac907224ae82" kindref="member">GPIO_POOL_TAG</ref>);</highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab" kindref="member">WdfSpinLockRelease</ref>(gpioManager-&gt;Lock);</highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="278"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;GPIO<sp/>pin<sp/>%d<sp/>uninitialized<sp/>successfully&quot;</highlight><highlight class="normal">,<sp/><ref refid="gpio__device_8c_1a2105af29d2c177b4d5c5d8e589b1caa3" kindref="member">PinNumber</ref>);</highlight></codeline>
<codeline lineno="279"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/bus/gpio_core.c"/>
  </compounddef>
</doxygen>
