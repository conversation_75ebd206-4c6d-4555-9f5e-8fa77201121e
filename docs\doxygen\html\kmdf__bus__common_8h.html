<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_bus_common.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__bus__common_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_bus_common.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for kmdf_bus_common.h:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__bus__common_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8h">
<area shape="rect" title=" " alt="" coords="5,5,202,48"/>
<area shape="rect" title=" " alt="" coords="20,104,73,131"/>
<area shape="poly" title=" " alt="" coords="92,50,65,92,60,89,88,47"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="97,96,227,139"/>
<area shape="poly" title=" " alt="" coords="119,47,143,82,138,85,115,50"/>
<area shape="rect" title=" " alt="" coords="130,187,194,213"/>
<area shape="poly" title=" " alt="" coords="165,139,165,171,160,171,160,139"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__bus__common_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8hdep" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__bus__common_8hdep">
<area shape="rect" title=" " alt="" coords="305,5,502,48"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="154,187,356,229"/>
<area shape="poly" title=" " alt="" coords="379,62,274,188,270,185,375,58"/>
<area shape="rect" href="driver__entry_8h.html" title="Brief description." alt="" coords="50,285,218,328"/>
<area shape="poly" title=" " alt="" coords="324,56,276,78,226,107,181,143,144,188,134,212,130,238,133,285,127,285,125,237,129,210,140,185,177,139,223,102,273,73,322,51"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="5,392,151,435"/>
<area shape="poly" title=" " alt="" coords="293,55,235,74,179,98,116,135,93,157,67,188,41,225,25,258,20,293,29,335,43,365,61,390,57,393,38,368,23,337,15,293,20,256,36,222,63,185,89,153,113,131,177,94,233,69,291,50"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="819,96,988,139"/>
<area shape="poly" title=" " alt="" coords="518,45,819,99,818,104,517,51"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="380,96,549,139"/>
<area shape="poly" title=" " alt="" coords="428,59,453,94,448,97,424,62"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="1126,96,1294,139"/>
<area shape="poly" title=" " alt="" coords="517,38,1126,104,1125,109,516,43"/>
<area shape="poly" title=" " alt="" coords="220,241,162,287,158,283,216,237"/>
<area shape="poly" title=" " alt="" coords="259,246,253,292,245,316,232,338,216,352,191,366,132,394,130,389,189,362,213,347,228,334,240,314,248,291,254,245"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="344,384,489,443"/>
<area shape="poly" title=" " alt="" coords="362,232,418,252,439,263,454,276,464,303,462,332,452,360,439,385,434,383,447,358,456,331,458,304,449,279,436,268,416,256,360,237"/>
<area shape="rect" href="driver__core_8c.html" title=" " alt="" coords="293,277,439,336"/>
<area shape="poly" title=" " alt="" coords="292,238,335,275,332,279,289,242"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="641,285,787,328"/>
<area shape="poly" title=" " alt="" coords="369,230,641,287,640,293,368,236"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,384,320,443"/>
<area shape="poly" title=" " alt="" coords="169,337,218,382,215,386,166,341"/>
<area shape="poly" title=" " alt="" coords="118,343,91,393,87,390,113,341"/>
<area shape="poly" title=" " alt="" coords="626,325,488,351,333,387,321,390,320,385,332,381,487,346,626,320"/>
<area shape="poly" title=" " alt="" coords="626,321,377,352,254,370,164,387,137,394,135,389,163,381,253,365,377,347,625,316"/>
<area shape="poly" title=" " alt="" coords="642,336,490,389,489,384,640,331"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="683,392,828,435"/>
<area shape="poly" title=" " alt="" coords="730,342,750,391,745,392,725,344"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="1021,392,1167,435"/>
<area shape="poly" title=" " alt="" coords="793,330,804,333,907,357,1011,381,1035,389,1034,394,1009,387,906,362,803,339,792,335"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="513,392,659,435"/>
<area shape="poly" title=" " alt="" coords="679,340,613,394,610,389,675,336"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1220,392,1365,435"/>
<area shape="poly" title=" " alt="" coords="790,330,804,333,909,352,991,359,1074,366,1180,381,1220,390,1219,395,1179,387,1074,371,991,365,908,357,803,339,788,335"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="852,392,997,435"/>
<area shape="poly" title=" " alt="" coords="770,333,884,389,882,394,768,338"/>
<area shape="poly" title=" " alt="" coords="959,145,984,163,1005,185,1040,239,1065,297,1083,351,1093,391,1087,393,1077,352,1060,299,1035,241,1001,188,980,167,956,149"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="816,187,991,229"/>
<area shape="poly" title=" " alt="" coords="906,154,906,186,901,186,901,154"/>
<area shape="poly" title=" " alt="" coords="851,239,756,287,753,282,848,234"/>
<area shape="rect" href="gpio__device_8c.html" title=" " alt="" coords="816,285,991,328"/>
<area shape="poly" title=" " alt="" coords="906,245,906,285,901,285,901,245"/>
<area shape="poly" title=" " alt="" coords="365,129,256,149,198,166,143,189,107,207,81,223,59,245,40,278,35,308,40,338,52,367,66,390,61,393,47,369,35,340,29,308,36,276,55,242,77,219,105,202,141,184,196,161,254,144,364,124"/>
<area shape="poly" title=" " alt="" coords="523,145,545,163,563,185,576,212,585,240,594,298,595,351,592,392,586,391,589,351,589,299,580,241,571,214,558,188,541,167,520,149"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="623,187,792,229"/>
<area shape="poly" title=" " alt="" coords="537,142,651,184,649,189,535,147"/>
<area shape="rect" href="i2c__device_8c.html" title=" " alt="" coords="380,187,549,229"/>
<area shape="poly" title=" " alt="" coords="467,154,467,186,462,186,462,154"/>
<area shape="poly" title=" " alt="" coords="712,244,715,285,710,285,707,245"/>
<area shape="poly" title=" " alt="" coords="1245,149,1268,186,1286,242,1295,300,1297,392,1291,392,1289,301,1281,243,1264,188,1240,152"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1066,187,1234,229"/>
<area shape="poly" title=" " alt="" coords="1190,153,1166,188,1162,185,1185,150"/>
<area shape="rect" href="spi__device_8c.html" title=" " alt="" coords="1091,285,1260,328"/>
<area shape="poly" title=" " alt="" coords="1235,151,1250,189,1253,210,1248,230,1229,262,1205,287,1201,283,1225,259,1244,228,1247,209,1245,190,1231,153"/>
<area shape="poly" title=" " alt="" coords="1051,228,932,251,804,280,777,287,776,282,803,275,931,246,1050,223"/>
<area shape="poly" title=" " alt="" coords="1162,244,1173,284,1167,286,1157,245"/>
</map>
</div>
</div>
<p><a href="kmdf__bus__common_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FBUS_5FCONFIG_5Fstruct_5F_5FBUS_5F_5FCONFIG" id="r__5FBUS_5FCONFIG_5Fstruct_5F_5FBUS_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__BUS__CONFIG">_BUS_CONFIG</a></td></tr>
<tr class="memitem:_5FBUS_5FTRANSFER_5FPACKET_5Fstruct_5F_5FBUS_5F_5FTRANSFER_5F_5FPACKET" id="r__5FBUS_5FTRANSFER_5FPACKET_5Fstruct_5F_5FBUS_5F_5FTRANSFER_5F_5FPACKET"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a90f573bacff80fd27b824b98e3e4fb9a" id="r_a90f573bacff80fd27b824b98e3e4fb9a"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__BUS__CONFIG">_BUS_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a90f573bacff80fd27b824b98e3e4fb9a">BUS_CONFIG</a></td></tr>
<tr class="memitem:a3709500586d6c79d8df0693c133a3f2d" id="r_a3709500586d6c79d8df0693c133a3f2d"><td class="memItemLeft" align="right" valign="top">typedef VOID(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a>) (<a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket)</td></tr>
<tr class="memitem:aac06c68a58c9667998bbe0975aa78c51" id="r_aac06c68a58c9667998bbe0975aa78c51"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:ab61a790fb09aa3a337c89ea002b5a76f" id="r_ab61a790fb09aa3a337c89ea002b5a76f"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a></td></tr>
<tr class="memitem:a070973cba55289cdfb72a260dc529d59" id="r_a070973cba55289cdfb72a260dc529d59"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a></td></tr>
<tr class="memitem:aeca4f45b7bb946c601f53bb52b08b170" id="r_aeca4f45b7bb946c601f53bb52b08b170"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__BUS__CONFIG">_BUS_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a></td></tr>
<tr class="memitem:a41f78cc35bd82acd5a7e7fd5fc51d00d" id="r_a41f78cc35bd82acd5a7e7fd5fc51d00d"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a5d19998cd5fa9d774a8166492799c52f" id="r_a5d19998cd5fa9d774a8166492799c52f"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a> { <br />
&#160;&#160;<a class="el" href="#a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947">BusTransferSuccess</a> = 0
, <a class="el" href="#a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51">BusTransferFailed</a>
, <a class="el" href="#a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726">BusTransferCancelled</a>
, <a class="el" href="#a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c">BusTransferInvalidParameter</a>
, <br />
&#160;&#160;<a class="el" href="#a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b">BusTransferDeviceNotReady</a>
, <a class="el" href="#a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519">BusTransferTimeout</a>
<br />
 }</td></tr>
<tr class="memitem:a40bc08609f724c5347a097e2bd6a1123" id="r_a40bc08609f724c5347a097e2bd6a1123"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a> { <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584">BusTypeI2C</a>
, <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60">BusTypeSPI</a>
, <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc">BusTypeUSB</a>
, <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38">BusTypeMax</a>
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a4f54d258241aeda6b5d01ee12110870f" id="r_a4f54d258241aeda6b5d01ee12110870f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4f54d258241aeda6b5d01ee12110870f">BusCancelTransfer</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket)</td></tr>
<tr class="memitem:ad5aa0e171a9d72e28ab08d06935ab2f5" id="r_ad5aa0e171a9d72e28ab08d06935ab2f5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad5aa0e171a9d72e28ab08d06935ab2f5">BusInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a> BusConfig)</td></tr>
<tr class="memitem:af23a4d40f37f1cf45dd8b2fc40cf6dff" id="r_af23a4d40f37f1cf45dd8b2fc40cf6dff"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af23a4d40f37f1cf45dd8b2fc40cf6dff">BusTransferAsynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket, _In_ <a class="el" href="#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a79b8f6be307f48971e34bb6cabfba958" id="r_a79b8f6be307f48971e34bb6cabfba958"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a79b8f6be307f48971e34bb6cabfba958">BusTransferSynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG Timeout)</td></tr>
<tr class="memitem:a2878b61cfb78224301b7d25175aa243e" id="r_a2878b61cfb78224301b7d25175aa243e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2878b61cfb78224301b7d25175aa243e">BusUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__BUS__CONFIG" id="struct__BUS__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__BUS__CONFIG">&#9670;&#160;</a></span>_BUS_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _BUS_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ae19319720b3c056dc60261c26e1dd43e" name="ae19319720b3c056dc60261c26e1dd43e"></a>PVOID</td>
<td class="fieldname">
BusSpecificConfig</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab3667ea857ae85e39b62c3a39b8d6761" name="ab3667ea857ae85e39b62c3a39b8d6761"></a><a class="el" href="#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a></td>
<td class="fieldname">
BusType</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a76efcd63df259ff464d8b6e9be040134" name="a76efcd63df259ff464d8b6e9be040134"></a>ULONG</td>
<td class="fieldname">
Flags</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a4dc4b392465c874dc7b75256fa2ac00a" name="a4dc4b392465c874dc7b75256fa2ac00a"></a>ULONG</td>
<td class="fieldname">
Speed</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="struct__BUS__TRANSFER__PACKET" id="struct__BUS__TRANSFER__PACKET"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__BUS__TRANSFER__PACKET">&#9670;&#160;</a></span>_BUS_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _BUS_TRANSFER_PACKET</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ab2d70f7d13f1b96499d1ed9fa11b881b" name="ab2d70f7d13f1b96499d1ed9fa11b881b"></a>PVOID</td>
<td class="fieldname">
Buffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="acd85870608d805b91015bd3e0c4302c4" name="acd85870608d805b91015bd3e0c4302c4"></a>SIZE_T</td>
<td class="fieldname">
BufferLength</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab3b945cbfe042fe6d4cf96ab9517ae6a" name="ab3b945cbfe042fe6d4cf96ab9517ae6a"></a>PVOID</td>
<td class="fieldname">
Context</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a66430abda4905c786c0b4e542757c518" name="a66430abda4905c786c0b4e542757c518"></a><a class="el" href="#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a></td>
<td class="fieldname">
Status</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a90f573bacff80fd27b824b98e3e4fb9a" name="a90f573bacff80fd27b824b98e3e4fb9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a90f573bacff80fd27b824b98e3e4fb9a">&#9670;&#160;</a></span>BUS_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__BUS__CONFIG">_BUS_CONFIG</a> <a class="el" href="#a90f573bacff80fd27b824b98e3e4fb9a">BUS_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3709500586d6c79d8df0693c133a3f2d" name="a3709500586d6c79d8df0693c133a3f2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3709500586d6c79d8df0693c133a3f2d">&#9670;&#160;</a></span>BUS_OPERATION_CALLBACK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef VOID(* BUS_OPERATION_CALLBACK) (<a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a> TransferPacket)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aac06c68a58c9667998bbe0975aa78c51" name="aac06c68a58c9667998bbe0975aa78c51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac06c68a58c9667998bbe0975aa78c51">&#9670;&#160;</a></span>BUS_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a> <a class="el" href="#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab61a790fb09aa3a337c89ea002b5a76f" name="ab61a790fb09aa3a337c89ea002b5a76f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab61a790fb09aa3a337c89ea002b5a76f">&#9670;&#160;</a></span>BUS_TRANSFER_STATUS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a> <a class="el" href="#ab61a790fb09aa3a337c89ea002b5a76f">BUS_TRANSFER_STATUS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a070973cba55289cdfb72a260dc529d59" name="a070973cba55289cdfb72a260dc529d59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a070973cba55289cdfb72a260dc529d59">&#9670;&#160;</a></span>BUS_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a> <a class="el" href="#a070973cba55289cdfb72a260dc529d59">BUS_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aeca4f45b7bb946c601f53bb52b08b170" name="aeca4f45b7bb946c601f53bb52b08b170"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeca4f45b7bb946c601f53bb52b08b170">&#9670;&#160;</a></span>PBUS_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__BUS__CONFIG">_BUS_CONFIG</a> * <a class="el" href="#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a41f78cc35bd82acd5a7e7fd5fc51d00d" name="a41f78cc35bd82acd5a7e7fd5fc51d00d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">&#9670;&#160;</a></span>PBUS_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__BUS__TRANSFER__PACKET">_BUS_TRANSFER_PACKET</a> * <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="a5d19998cd5fa9d774a8166492799c52f" name="a5d19998cd5fa9d774a8166492799c52f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d19998cd5fa9d774a8166492799c52f">&#9670;&#160;</a></span>_BUS_TRANSFER_STATUS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a5d19998cd5fa9d774a8166492799c52f">_BUS_TRANSFER_STATUS</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" name="a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947"></a>BusTransferSuccess&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" name="a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51"></a>BusTransferFailed&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726" name="a5d19998cd5fa9d774a8166492799c52fa2155907b14998449538dd9abf0c62726"></a>BusTransferCancelled&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c" name="a5d19998cd5fa9d774a8166492799c52fa14af10f4c6ffac28ed7326c41aa0566c"></a>BusTransferInvalidParameter&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b" name="a5d19998cd5fa9d774a8166492799c52fa9d50b293fd9806aa54c51e6085cf088b"></a>BusTransferDeviceNotReady&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519" name="a5d19998cd5fa9d774a8166492799c52fa4c6baf12b19a05c7fd456ad4fe594519"></a>BusTransferTimeout&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a40bc08609f724c5347a097e2bd6a1123" name="a40bc08609f724c5347a097e2bd6a1123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40bc08609f724c5347a097e2bd6a1123">&#9670;&#160;</a></span>_BUS_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a40bc08609f724c5347a097e2bd6a1123">_BUS_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584" name="a40bc08609f724c5347a097e2bd6a1123ac32c5ab40cdadb7f6017390cae74a584"></a>BusTypeI2C&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60" name="a40bc08609f724c5347a097e2bd6a1123ad6c36bac28f02b9e07da836f33ae9c60"></a>BusTypeSPI&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc" name="a40bc08609f724c5347a097e2bd6a1123aea2e27a6bea13db4cce4c1287554e8cc"></a>BusTypeUSB&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38" name="a40bc08609f724c5347a097e2bd6a1123a8028c2bd18a84c9eae010fdca79d6e38"></a>BusTypeMax&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a4f54d258241aeda6b5d01ee12110870f" name="a4f54d258241aeda6b5d01ee12110870f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f54d258241aeda6b5d01ee12110870f">&#9670;&#160;</a></span>BusCancelTransfer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS BusCancelTransfer </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad5aa0e171a9d72e28ab08d06935ab2f5" name="ad5aa0e171a9d72e28ab08d06935ab2f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5aa0e171a9d72e28ab08d06935ab2f5">&#9670;&#160;</a></span>BusInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS BusInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#aeca4f45b7bb946c601f53bb52b08b170">PBUS_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>BusConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af23a4d40f37f1cf45dd8b2fc40cf6dff" name="af23a4d40f37f1cf45dd8b2fc40cf6dff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af23a4d40f37f1cf45dd8b2fc40cf6dff">&#9670;&#160;</a></span>BusTransferAsynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS BusTransferAsynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>CompletionCallback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a79b8f6be307f48971e34bb6cabfba958" name="a79b8f6be307f48971e34bb6cabfba958"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79b8f6be307f48971e34bb6cabfba958">&#9670;&#160;</a></span>BusTransferSynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS BusTransferSynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a41f78cc35bd82acd5a7e7fd5fc51d00d">PBUS_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Timeout</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a2878b61cfb78224301b7d25175aa243e" name="a2878b61cfb78224301b7d25175aa243e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2878b61cfb78224301b7d25175aa243e">&#9670;&#160;</a></span>BusUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID BusUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
