var searchData=
[
  ['i2c_5faddress_0',['I2C_ADDRESS',['../kmdf__i2c_8h.html#a519fae2d9daaac809af65134907b2fb0',1,'kmdf_i2c.h']]],
  ['i2c_5faddress_5f10bit_1',['I2C_ADDRESS_10BIT',['../i2c__device_8h.html#a64738b9f6c1ba2f1f919ef7a61ad1e35',1,'i2c_device.h']]],
  ['i2c_5faddress_5f7bit_2',['I2C_ADDRESS_7BIT',['../i2c__device_8h.html#ab65b9830c26b77346e0f420c643d63b9',1,'i2c_device.h']]],
  ['i2c_5fconfig_3',['I2C_CONFIG',['../kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4',1,'kmdf_i2c.h']]],
  ['i2c_5fcore_2ec_4',['i2c_core.c',['../i2c__core_8c.html',1,'']]],
  ['i2c_5fdefault_5ftimeout_5',['I2C_DEFAULT_TIMEOUT',['../i2c__device_8c.html#a4bbdac9bba21cb3959a7355001ea590f',1,'i2c_device.c']]],
  ['i2c_5fdevice_2ec_6',['i2c_device.c',['../i2c__device_8c.html',1,'']]],
  ['i2c_5fdevice_2eh_7',['i2c_device.h',['../i2c__device_8h.html',1,'']]],
  ['i2c_5fdevice_5fcontext_8',['I2C_DEVICE_CONTEXT',['../i2c__core_8c.html#abad9f6d84bae65aada7d4a1cfcc2ba12',1,'I2C_DEVICE_CONTEXT:&#160;i2c_core.c'],['../i2c__device_8c.html#aa89f192944d335c9a60e5858325ed89a',1,'I2C_DEVICE_CONTEXT:&#160;i2c_device.c']]],
  ['i2c_5fstatistics_9',['I2C_STATISTICS',['../i2c__device_8h.html#a7d8f35e89fbf4832545b3dfab3bd1ae1',1,'i2c_device.h']]],
  ['i2c_5ftransfer_5fpacket_10',['I2C_TRANSFER_PACKET',['../kmdf__i2c_8h.html#a941c9f88004c4f54719bc4a3b7083fff',1,'I2C_TRANSFER_PACKET:&#160;kmdf_i2c.h'],['../i2c__device_8h.html#af8486d1fd9d3904ca8eb4408ec81d9c4',1,'I2C_TRANSFER_PACKET:&#160;i2c_device.h']]],
  ['i2c_5ftransfer_5fread_11',['I2C_TRANSFER_READ',['../i2c__device_8h.html#ad6922f686b3fc13f8365467975aba1d7',1,'i2c_device.h']]],
  ['i2c_5ftransfer_5ftype_12',['I2C_TRANSFER_TYPE',['../kmdf__i2c_8h.html#a70b5b0e5b59f4301d02402a14c4ecb0b',1,'kmdf_i2c.h']]],
  ['i2cconfig_13',['I2cConfig',['../i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501',1,'i2c_device.c']]],
  ['i2cdevicecleanup_14',['I2cDeviceCleanup',['../i2c__device_8c.html#a5da67a960d3cf99caa6874438a84629b',1,'I2cDeviceCleanup(_In_ WDFDEVICE Device):&#160;i2c_device.c'],['../i2c__device_8h.html#a5da67a960d3cf99caa6874438a84629b',1,'I2cDeviceCleanup(_In_ WDFDEVICE Device):&#160;i2c_device.c']]],
  ['i2cdevicegetstatistics_15',['I2cDeviceGetStatistics',['../i2c__device_8c.html#a709aca0009ccfb39adebbdd9ce97e252',1,'I2cDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics):&#160;i2c_device.c'],['../i2c__device_8h.html#a709aca0009ccfb39adebbdd9ce97e252',1,'I2cDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics):&#160;i2c_device.c']]],
  ['i2cdeviceinitialize_16',['I2cDeviceInitialize',['../i2c__device_8c.html#ab0c3b778b5a363d418c3d768cdb1e2d4',1,'I2cDeviceInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_device.c'],['../i2c__device_8h.html#ab0c3b778b5a363d418c3d768cdb1e2d4',1,'I2cDeviceInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_device.c']]],
  ['i2cdeviceread_17',['I2cDeviceRead',['../i2c__device_8c.html#a6576f1e3485d12c22c444244044c1d30',1,'I2cDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;i2c_device.c'],['../i2c__device_8h.html#a6576f1e3485d12c22c444244044c1d30',1,'I2cDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead):&#160;i2c_device.c']]],
  ['i2cdevicetransfer_18',['I2cDeviceTransfer',['../i2c__device_8c.html#ad84f26684684313ff193803d1d9c7c32',1,'I2cDeviceTransfer(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount):&#160;i2c_device.c'],['../i2c__device_8h.html#ad84f26684684313ff193803d1d9c7c32',1,'I2cDeviceTransfer(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount):&#160;i2c_device.c']]],
  ['i2cdevicewrite_19',['I2cDeviceWrite',['../i2c__device_8c.html#a580f2434082501937a3d8bc4d5591866',1,'I2cDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;i2c_device.c'],['../i2c__device_8h.html#a580f2434082501937a3d8bc4d5591866',1,'I2cDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten):&#160;i2c_device.c']]],
  ['i2cinitialize_20',['I2CInitialize',['../i2c__core_8c.html#a3730a6f611cf9feba7ba954330f41a6c',1,'I2CInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a5467da0184a8f514f9ff43ab28f7d2d0',1,'I2CInitialize(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig):&#160;i2c_core.c']]],
  ['i2cread_21',['I2CRead',['../kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e',1,'kmdf_i2c.h']]],
  ['i2creadregister_22',['I2CReadRegister',['../i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe',1,'I2CReadRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aad5c9145daea9c25554b814bfed47756',1,'I2CReadRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['i2cscanbus_23',['I2CScanBus',['../i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b',1,'I2CScanBus(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a1b937c9865418ca9d50b16766c8ceb66',1,'I2CScanBus(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount):&#160;i2c_core.c']]],
  ['i2ctransferasynchronous_24',['I2CTransferAsynchronous',['../i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4',1,'I2CTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#a363c4a8b2ee1e16d8a6aaf35b0e67722',1,'I2CTransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context):&#160;i2c_core.c']]],
  ['i2ctransfersynchronous_25',['I2CTransferSynchronous',['../i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06',1,'I2CTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#ae74bb3af98d6a79ba5774f9c3a480ca7',1,'I2CTransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['i2ctransfertimerexpired_26',['I2CTransferTimerExpired',['../i2c__core_8c.html#a465308d666d8357287980a516e216910',1,'I2CTransferTimerExpired:&#160;i2c_core.c'],['../i2c__core_8c.html#aefdc06b9d942e6b102424a8a81c0be8a',1,'I2CTransferTimerExpired(_In_ WDFTIMER Timer):&#160;i2c_core.c']]],
  ['i2cuninitialize_27',['I2CUninitialize',['../i2c__core_8c.html#ae1622080dc9f8424bde67b829ee735c7',1,'I2CUninitialize(_In_ WDFDEVICE Device):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aa8f6531c5b52bc6d04ca38fbaab3c223',1,'I2CUninitialize(_In_ WDFDEVICE Device):&#160;i2c_core.c']]],
  ['i2cwrite_28',['I2CWrite',['../kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630',1,'kmdf_i2c.h']]],
  ['i2cwriteread_29',['I2CWriteRead',['../kmdf__i2c_8h.html#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7',1,'kmdf_i2c.h']]],
  ['i2cwriteregister_30',['I2CWriteRegister',['../i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb',1,'I2CWriteRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c'],['../kmdf__i2c_8h.html#aa4838a1894b94b950fc4a7e73624d7ed',1,'I2CWriteRegister(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs):&#160;i2c_core.c']]],
  ['if_31',['if',['../struct__GPIO__PIN__CONTEXT.html#acd3cbb2291f76aabd30090072d539050',1,'_GPIO_PIN_CONTEXT::if()'],['../gpio__core_8c.html#a977bbe3e09136dd34381e7f1b889a570',1,'if(pinContext==NULL):&#160;gpio_core.c'],['../gpio__core_8c.html#a495a75edfacf86c27c05ce15ada3509e',1,'if(gpioManager==NULL):&#160;gpio_core.c'],['../gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;gpio_core.c'],['../gpio__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;gpio_device.c'],['../gpio__device_8c.html#ad94ec7eba667568bcd9afe3483282304',1,'if(GpioConfig-&gt;DebounceTime &gt; 0):&#160;gpio_device.c'],['../gpio__device_8c.html#a9eef4336d2e5308042aaa49f8966c7fa',1,'if(GpioConfig-&gt;EnableInterrupt):&#160;gpio_device.c'],['../gpio__device_8c.html#a14190d6765d5c660291c1d6839cc5428',1,'if(NT_SUCCESS(status)):&#160;gpio_device.c'],['../gpio__device_8c.html#aac20ced732c198d7484287c9eb39e413',1,'if(deviceContext-&gt;Config.EnableInterrupt):&#160;gpio_device.c'],['../i2c__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b',1,'if(deviceContext==NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;i2c_device.c'],['../i2c__device_8c.html#adfac0a96ec8249c69bd820670db7f2cd',1,'if(I2cConfig-&gt;InterruptEnabled):&#160;i2c_device.c'],['../i2c__device_8c.html#a161904443c5f73d8654306b3fa8d29bb',1,'if(deviceContext-&gt;Interrupt !=NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#ab29d05a3528131be0d35fe785e85590f',1,'if(deviceContext-&gt;HalHandle !=NULL):&#160;i2c_device.c'],['../i2c__device_8c.html#a6957e0e0f326c7986a222f431530dc94',1,'if(deviceContext-&gt;I2cConfig !=NULL):&#160;i2c_device.c'],['../spi__device_8c.html#a9d2d77fd6fa0d75751b40049e614b00b',1,'if(deviceContext==NULL):&#160;spi_device.c'],['../spi__device_8c.html#a1a243a15dd793b6d0f7b7011461a8641',1,'if(!NT_SUCCESS(status)):&#160;spi_device.c'],['../spi__device_8c.html#ab29d05a3528131be0d35fe785e85590f',1,'if(deviceContext-&gt;HalHandle !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#a4b5c92f0859e4be1ead5d71edc903427',1,'if(NT_SUCCESS(status) &amp;&amp;BytesRead !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#afff80b1a0000ef578da0277667a994ff',1,'if(writeBuffer==NULL):&#160;spi_device.c'],['../spi__device_8c.html#a164e77dd43f69d29ea926ae0ec42969b',1,'if(NT_SUCCESS(status) &amp;&amp;BytesWritten !=NULL):&#160;spi_device.c'],['../spi__device_8c.html#ac40f83943701ccbf4235e0c238583dfb',1,'if(deviceContext-&gt;SpiConfig !=NULL):&#160;spi_device.c']]],
  ['includecomponentname_32',['IncludeComponentName',['../include_2core_2log_2driver__log_8h.html#ac82d96af8adf8a1862d02ac0be15939d',1,'_LOG_CONFIG']]],
  ['includetimestamp_33',['IncludeTimestamp',['../include_2core_2log_2driver__log_8h.html#a860b2d89bf1aff272da84d5029dcb0b1',1,'_LOG_CONFIG']]],
  ['initguid_34',['INITGUID',['../device__manager_8c.html#af11aade3f3741fb554915d10d3f514eb',1,'INITGUID:&#160;device_manager.c'],['../driver__core_8c.html#af11aade3f3741fb554915d10d3f514eb',1,'INITGUID:&#160;driver_core.c']]],
  ['initialized_35',['Initialized',['../gpio__core_8c.html#a33c00fdea3f12acb400049b8ef710ea9',1,'gpio_core.c']]],
  ['initializedevice_36',['InitializeDevice',['../driver__core_8h.html#afc516515541c17e0ba39b5ac97a01636',1,'driver_core.h']]],
  ['initializelisthead_37',['InitializeListHead',['../core__types_8h.html#acec93a755836f578590339c921773e21',1,'core_types.h']]],
  ['insertheadlist_38',['InsertHeadList',['../core__types_8h.html#a14f3c1fe642e4927959b4beed2852e2a',1,'core_types.h']]],
  ['inserttaillist_39',['InsertTailList',['../core__types_8h.html#a0bf4f8c1a40d587fc04e48b16db5a8c3',1,'core_types.h']]],
  ['interrupt_40',['Interrupt',['../device__manager_8h.html#a43621987690ebe2010be900cf3a78207',1,'_DEVICE_CONTEXT::Interrupt'],['../driver__core_8h.html#aee0bdebc6bfac5c4a1320abc8b7611a4',1,'_DRIVER_DEVICE_CONTEXT::Interrupt'],['../hal__interface_8h.html#a1ce92afa20b1c7f6a4e5d0dc73e5c92a',1,'_HAL_RESOURCE.u::Interrupt']]],
  ['interruptconfig_41',['interruptConfig',['../gpio__core_8c.html#ac6c56d4f54252f6088c0d841efbc597e',1,'gpio_core.c']]],
  ['interruptcount_42',['InterruptCount',['../driver__core_8h.html#a20af12b36a08e1504e7c98eba79e990c',1,'_DRIVER_STATISTICS']]],
  ['interruptinpipe_43',['InterruptInPipe',['../device__manager_8h.html#a9c5d6e826cc40727b53a6986279e8ab1',1,'_DEVICE_CONTEXT']]],
  ['interruptinpipemaxpacketsize_44',['InterruptInPipeMaxPacketSize',['../device__manager_8h.html#a0ba2405294828f82bb2e4d967492fffc',1,'_DEVICE_CONTEXT']]],
  ['interruptmode_45',['InterruptMode',['../device__manager_8h.html#a0f2f683dcf52ec000fcb5dbef24806cb',1,'_DEVICE_CONTEXT']]],
  ['interrupttranslated_46',['InterruptTranslated',['../gpio__core_8c.html#a4983b2b08534e2a12b6e0c30d87594f0',1,'gpio_core.c']]],
  ['interrupttype_47',['InterruptType',['../kmdf__gpio_8h.html#a16e25974fff79d31225fbb4402059d42',1,'_GPIO_PIN_CONFIG::InterruptType'],['../gpio__device_8h.html#aed18d39f9d22f9f8fb5c65dc0c1887d8',1,'_GPIO_DEVICE_CONFIG::InterruptType']]],
  ['interruptvector_48',['InterruptVector',['../device__manager_8h.html#a30de6520f49b6aca93db8edc20ef678b',1,'_DEVICE_CONTEXT']]],
  ['ioctl_2eh_49',['ioctl.h',['../ioctl_8h.html',1,'']]],
  ['ioctl_5fdevice_5fspecific_5fcommand_50',['IOCTL_DEVICE_SPECIFIC_COMMAND',['../device__manager_8h.html#a06bac98d6f690509acee10555e212f41',1,'device_manager.h']]],
  ['ioctl_5fdriver_5fbase_51',['IOCTL_DRIVER_BASE',['../driver__core_8h.html#a0785b75b63fb30d328b99d809b6ef8d9',1,'driver_core.h']]],
  ['ioctl_5fdriver_5fget_5fstatistics_52',['IOCTL_DRIVER_GET_STATISTICS',['../driver__core_8h.html#a6d4bdae2f23ab96032a14098fb28b1f2',1,'driver_core.h']]],
  ['ioctl_5fdriver_5fget_5fversion_53',['IOCTL_DRIVER_GET_VERSION',['../driver__core_8h.html#a07c561db6f9baf7d9f5452605546aed9',1,'driver_core.h']]],
  ['ioctl_5fdriver_5freset_54',['IOCTL_DRIVER_RESET',['../driver__core_8h.html#a9cc7d1b6ef52a7692138d85aa247f989',1,'driver_core.h']]],
  ['ioctl_5fdriver_5fset_5flogging_55',['IOCTL_DRIVER_SET_LOGGING',['../driver__core_8h.html#a3cfaff7b63b2bbdd9583818a4f090d3b',1,'driver_core.h']]],
  ['ioctl_5fget_5fdevice_5finfo_56',['IOCTL_GET_DEVICE_INFO',['../device__manager_8h.html#a5d922ac9f0d09258cd06cb7d8d7160af',1,'device_manager.h']]],
  ['ioctl_5fgpio_5fdevice_5fbase_57',['IOCTL_GPIO_DEVICE_BASE',['../gpio__device_8h.html#a0ab51b588e38783851968f26ed81d41e',1,'gpio_device.h']]],
  ['ioctl_5fgpio_5fdevice_5fget_5fstate_58',['IOCTL_GPIO_DEVICE_GET_STATE',['../gpio__device_8h.html#a531a7dc21007b6e72d524a9fa29de43d',1,'gpio_device.h']]],
  ['ioctl_5fgpio_5fdevice_5fregister_5fcallback_59',['IOCTL_GPIO_DEVICE_REGISTER_CALLBACK',['../gpio__device_8h.html#ada8c56039bf34983b27bd0338982f351',1,'gpio_device.h']]],
  ['ioctl_5fgpio_5fdevice_5fset_5fstate_60',['IOCTL_GPIO_DEVICE_SET_STATE',['../gpio__device_8h.html#a15338fabc6809209e45575e75f4867e4',1,'gpio_device.h']]],
  ['ioctl_5fgpio_5fget_5fvalue_61',['IOCTL_GPIO_GET_VALUE',['../gpio__core_8c.html#a09573d341b2d8f94a213241de2444b0b',1,'gpio_core.c']]],
  ['ioctl_5fgpio_5fset_5fdirection_62',['IOCTL_GPIO_SET_DIRECTION',['../gpio__core_8c.html#aa5235f4dd44bf922bf5befb2ef0b3b4b',1,'gpio_core.c']]],
  ['ioctl_5fgpio_5fset_5fvalue_63',['IOCTL_GPIO_SET_VALUE',['../gpio__core_8c.html#a921358974fe0b0cbe1288fd8bdc34196',1,'gpio_core.c']]],
  ['ioctl_5fi2c_5fget_5fstatistics_64',['IOCTL_I2C_GET_STATISTICS',['../i2c__device_8h.html#a23164ebc8fc22800438176c588caa941',1,'i2c_device.h']]],
  ['ioctl_5fi2c_5freset_65',['IOCTL_I2C_RESET',['../i2c__device_8h.html#a69ebe19cf050058019d84f005052cc00',1,'i2c_device.h']]],
  ['ioctl_5fi2c_5fset_5fbus_5fspeed_66',['IOCTL_I2C_SET_BUS_SPEED',['../i2c__device_8h.html#a9c3881592ae1c10fbd5ba1b9ae7e85ec',1,'i2c_device.h']]],
  ['ioctl_5fi2c_5ftransfer_67',['IOCTL_I2C_TRANSFER',['../i2c__device_8h.html#a15204e5c2582622fc3ef40f01fe93322',1,'i2c_device.h']]],
  ['ioctl_5fi2c_5ftransfer_5fsequence_68',['IOCTL_I2C_TRANSFER_SEQUENCE',['../i2c__device_8h.html#a478905e3b3f7f700dd9e0975f42fe1a3',1,'i2c_device.h']]],
  ['ioctl_5freset_5fdevice_69',['IOCTL_RESET_DEVICE',['../device__manager_8h.html#a2f0ab8f1ea9c5b1dbd09bf73f736a7b6',1,'device_manager.h']]],
  ['ioctl_5fspi_5fbase_70',['IOCTL_SPI_BASE',['../spi__device_8h.html#aad9cdee9a56a867985b5110add53ed94',1,'spi_device.h']]],
  ['ioctl_5fspi_5fget_5fstatistics_71',['IOCTL_SPI_GET_STATISTICS',['../spi__device_8h.html#a97fe5a41276df38e46922527c4b9baf9',1,'spi_device.h']]],
  ['ioctl_5fspi_5freset_72',['IOCTL_SPI_RESET',['../spi__device_8h.html#a8442695a715b85f6516ff535bc5d1409',1,'spi_device.h']]],
  ['ioctl_5fspi_5fset_5fbus_5fspeed_73',['IOCTL_SPI_SET_BUS_SPEED',['../spi__device_8h.html#aaba9d20f35713a3c0dd088bfdb433a0b',1,'spi_device.h']]],
  ['ioctl_5fspi_5fset_5fmode_74',['IOCTL_SPI_SET_MODE',['../spi__device_8h.html#a471b24a3583fd2212e30e4619ae701be',1,'spi_device.h']]],
  ['ioctl_5fspi_5ftransfer_75',['IOCTL_SPI_TRANSFER',['../spi__device_8h.html#a72feec97101aca3be161a59ffe40cc2c',1,'spi_device.h']]],
  ['ioctl_5fspi_5ftransfer_5ffull_5fduplex_76',['IOCTL_SPI_TRANSFER_FULL_DUPLEX',['../spi__device_8h.html#a21382b2df65b9cb8a11da17114ab9491',1,'spi_device.h']]],
  ['ioctl_5ftouch_5fget_5fdata_77',['IOCTL_TOUCH_GET_DATA',['../ioctl_8h.html#ad40a8f5f93a2d0fdabdc3b13510850b6',1,'ioctl.h']]],
  ['irql_78',['Irql',['../hal__interface_8h.html#a936031363ea9c7f977507af852fdc666',1,'_HAL_RESOURCE.u.Interrupt']]],
  ['is10bitaddress_79',['Is10BitAddress',['../kmdf__i2c_8h.html#a4f627b1a2607231e3dcb321f5d728c22',1,'_I2C_CONFIG']]],
  ['isacceptingnewrequests_80',['IsAcceptingNewRequests',['../core__types_8h.html#a0882ddefacbd218a4ac5c51db2f1f44f',1,'_CORE_STATUS']]],
  ['islistempty_81',['IsListEmpty',['../core__types_8h.html#a1f65a0c67211ca77b491405e7c0d539e',1,'core_types.h']]],
  ['islsbfirst_82',['IsLsbFirst',['../kmdf__spi_8h.html#ad132bb074410f64505375ed71b0173f8',1,'_SPI_CONFIG']]],
  ['ismapped_83',['IsMapped',['../hal__interface_8h.html#a424c77aefa1f2068caca737b5239183f',1,'_HAL_RESOURCE.u.Memory']]],
  ['isremoved_84',['IsRemoved',['../core__types_8h.html#aa10d05e79e202f43369c77ef7cb7d984',1,'_CORE_DEVICE_STATUS']]],
  ['isvalid_85',['IsValid',['../device__manager_8h.html#a80ff6717c5b2724381d8aa3d6be00695',1,'_TOUCH_POINT']]]
];
