<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="kmdf__i2c_8h" kind="file" language="C++">
    <compoundname>kmdf_i2c.h</compoundname>
    <includes refid="kmdf__bus__common_8h" local="yes">kmdf_bus_common.h</includes>
    <includedby refid="i2c__device_8h" local="yes">C:/KMDF Driver1/include/hal/devices/i2c_device.h</includedby>
    <includedby refid="driver__main_8c" local="yes">C:/KMDF Driver1/src/driver_main.c</includedby>
    <includedby refid="i2c__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/i2c_core.c</includedby>
    <includedby refid="i2c__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/i2c_device.c</includedby>
    <incdepgraph>
      <node id="4">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/include/hal/devices/i2c_device.h</label>
        <link refid="i2c__device_8h"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/hal/devices/i2c_device.c</label>
        <link refid="i2c__device_8c"/>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__I2C__CONFIG" prot="public">_I2C_CONFIG</innerclass>
    <innerclass refid="struct__I2C__TRANSFER__PACKET" prot="public">_I2C_TRANSFER_PACKET</innerclass>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3" prot="public" static="no" strong="no">
        <type></type>
        <name>_I2C_TRANSFER_TYPE</name>
        <enumvalue id="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" prot="public">
          <name>I2CWrite</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" prot="public">
          <name>I2CRead</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" prot="public">
          <name>I2CWriteRead</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="24" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" bodystart="24" bodyend="28"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" prot="public" static="no">
        <type>USHORT</type>
        <definition>typedef USHORT I2C_ADDRESS</definition>
        <argsstring></argsstring>
        <name>I2C_ADDRESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="13" column="16" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" bodystart="13" bodyend="-1"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__CONFIG" kindref="compound">_I2C_CONFIG</ref></type>
        <definition>typedef struct _I2C_CONFIG I2C_CONFIG</definition>
        <argsstring></argsstring>
        <name>I2C_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="21" column="12"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__TRANSFER__PACKET" kindref="compound">_I2C_TRANSFER_PACKET</ref></type>
        <definition>typedef struct _I2C_TRANSFER_PACKET I2C_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>I2C_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="39" column="21"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a70b5b0e5b59f4301d02402a14c4ecb0b" prot="public" static="no">
        <type>enum <ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3" kindref="member">_I2C_TRANSFER_TYPE</ref></type>
        <definition>typedef enum _I2C_TRANSFER_TYPE I2C_TRANSFER_TYPE</definition>
        <argsstring></argsstring>
        <name>I2C_TRANSFER_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="28" column="19"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" prot="public" static="no">
        <type>USHORT *</type>
        <definition>typedef USHORT * PI2C_ADDRESS</definition>
        <argsstring></argsstring>
        <name>PI2C_ADDRESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="13" column="28" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" bodystart="13" bodyend="-1"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__CONFIG" kindref="compound">_I2C_CONFIG</ref> *</type>
        <definition>typedef struct _I2C_CONFIG * PI2C_CONFIG</definition>
        <argsstring></argsstring>
        <name>PI2C_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="21" column="25"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" prot="public" static="no">
        <type>struct <ref refid="struct__I2C__TRANSFER__PACKET" kindref="compound">_I2C_TRANSFER_PACKET</ref> *</type>
        <definition>typedef struct _I2C_TRANSFER_PACKET * PI2C_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>PI2C_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="39" column="43"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="kmdf__i2c_8h_1a5467da0184a8f514f9ff43ab28f7d2d0" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</argsstring>
        <name>I2CInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref></type>
          <declname>I2cConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="43" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="44" bodyend="105" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="43" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <references refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</references>
        <references refid="i2c__core_8c_1a465308d666d8357287980a516e216910" compoundref="i2c__core_8c" startline="38">I2CTransferTimerExpired</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1aad5c9145daea9c25554b814bfed47756" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CReadRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CReadRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref></type>
          <declname>SlaveAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_ PUCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="78" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="321" bodyend="355" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="78" declcolumn="1"/>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" compoundref="kmdf__i2c_8h" startline="27">I2CWriteRead</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" compoundref="kmdf__i2c_8h" startline="37">_I2C_TRANSFER_PACKET::ReadBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" compoundref="kmdf__i2c_8h" startline="38">_I2C_TRANSFER_PACKET::ReadBufferLength</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1a1b937c9865418ca9d50b16766c8ceb66" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CScanBus</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) PI2C_ADDRESS DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount)</argsstring>
        <name>I2CScanBus</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <ref refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kindref="member">PI2C_ADDRESS</ref></type>
          <declname>DeviceAddresses</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>MaxDeviceAddresses</declname>
        </param>
        <param>
          <type>_Out_ PULONG</type>
          <declname>DeviceCount</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="87" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="361" bodyend="431" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="87" declcolumn="1"/>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" compoundref="kmdf__bus__common_8h" startline="25">BusTransferSuccess</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" compoundref="kmdf__i2c_8h" startline="32">_I2C_TRANSFER_PACKET::Common</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" compoundref="error__codes_8h" startline="30">ERROR_DEVICE_NOT_READY</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="struct__BUS__TRANSFER__PACKET_1a66430abda4905c786c0b4e542757c518" compoundref="kmdf__bus__common_8h" startline="46">_BUS_TRANSFER_PACKET::Status</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1a363c4a8b2ee1e16d8a6aaf35b0e67722" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CTransferAsynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</argsstring>
        <name>I2CTransferAsynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref></type>
          <declname>CompletionCallback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="61" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="250" bodyend="278" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="61" declcolumn="1"/>
        <references refid="gpio__device_8c_1afee8ca080129faeb1d5683d9b67a1aa6" compoundref="gpio__device_8c" startline="23">Config</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1ae74bb3af98d6a79ba5774f9c3a480ca7" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CTransferSynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PI2C_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CTransferSynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="54" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="142" bodyend="244" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="54" declcolumn="1"/>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fae402a8ade71ae0a078c548b1ed640f51" compoundref="kmdf__bus__common_8h" startline="26">BusTransferFailed</references>
        <references refid="kmdf__bus__common_8h_1a5d19998cd5fa9d774a8166492799c52fa858789fa4a8337446333b104a59ea947" compoundref="kmdf__bus__common_8h" startline="25">BusTransferSuccess</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1adbe74e534b99eb53edd59a05ee8e426f" compoundref="error__codes_8h" startline="23">ERROR_DEVICE_INIT_FAILED</references>
        <references refid="error__codes_8h_1ac80b2a8fac0e8f846c5f16200c7bb19a" compoundref="error__codes_8h" startline="30">ERROR_DEVICE_NOT_READY</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" compoundref="kmdf__i2c_8h" startline="26">I2CRead</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" compoundref="kmdf__i2c_8h" startline="27">I2CWriteRead</references>
        <references refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" compoundref="src_2core_2log_2driver__log_8h" startline="130">LOG_ERROR</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <references refid="gpio__core_8c_1aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</references>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1aa8f6531c5b52bc6d04ca38fbaab3c223" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID I2CUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>I2CUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="49" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="111" bodyend="132" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="49" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="src_2core_2log_2driver__log_8h_1a7748b322eafa9e058c518fef49b110cb" compoundref="src_2core_2log_2driver__log_8h" startline="133">LOG_INFO</references>
        <references refid="src_2core_2log_2driver__log_8h_1a1dd05e1ef2b66fc68251edacaa75e9f7" compoundref="src_2core_2log_2driver__log_8h" startline="131">LOG_WARNING</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__i2c_8h_1aa4838a1894b94b950fc4a7e73624d7ed" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS I2CWriteRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ I2C_ADDRESS SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>I2CWriteRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref></type>
          <declname>SlaveAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" line="69" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/i2c_core.c" bodystart="283" bodyend="316" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h" declline="69" declcolumn="1"/>
        <references refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" compoundref="include_2core_2log_2driver__log_8h" startline="97" endline="98">FUNCTION_ENTRY</references>
        <references refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" compoundref="include_2core_2log_2driver__log_8h" startline="100" endline="101">FUNCTION_EXIT</references>
        <references refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</references>
        <references refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" compoundref="kmdf__i2c_8h" startline="25">I2CWrite</references>
        <references refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" compoundref="include_2core_2log_2driver__log_8h" startline="74" endline="75">LOG_VERBOSE</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" compoundref="kmdf__i2c_8h" startline="33">_I2C_TRANSFER_PACKET::SlaveAddress</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" compoundref="kmdf__i2c_8h" startline="34">_I2C_TRANSFER_PACKET::Type</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" compoundref="kmdf__i2c_8h" startline="35">_I2C_TRANSFER_PACKET::WriteBuffer</references>
        <references refid="spi__device_8c_1a7a33fa49b57196f5722a55916cff0a52" compoundref="spi__device_8c" startline="219">writeBuffer</references>
        <references refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" compoundref="kmdf__i2c_8h" startline="36">_I2C_TRANSFER_PACKET::WriteBufferLength</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>kmdf_i2c.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>I2C总线接口头文件<sp/>*<sp/>提供I2C特定的接口和数据结构</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_I2C_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_I2C_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__bus__common_8h" kindref="compound">kmdf_bus_common.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C地址类型</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13" refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/>USHORT<sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref>,<sp/>*<ref refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kindref="member">PI2C_ADDRESS</ref>;</highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C配置结构体</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="struct__I2C__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__I2C__CONFIG" kindref="compound">_I2C_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="17" refid="struct__I2C__CONFIG_1ae6dda89972493740920ba937c8d27897" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__CONFIG_1ae6dda89972493740920ba937c8d27897" kindref="member">ClockFrequency</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>I2C时钟频率<sp/>(Hz)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="struct__I2C__CONFIG_1a4f627b1a2607231e3dcb321f5d728c22" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__I2C__CONFIG_1a4f627b1a2607231e3dcb321f5d728c22" kindref="member">Is10BitAddress</ref>;<sp/><sp/></highlight><highlight class="comment">//<sp/>使用10位地址</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="struct__I2C__CONFIG_1a9942dbdaebaf13e2e51cef985bcc4d0d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__CONFIG_1a9942dbdaebaf13e2e51cef985bcc4d0d" kindref="member">TimeoutMs</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>默认超时时间<sp/>(ms)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="struct__I2C__CONFIG_1a2b8ec7e6589589f70cb5d4bbb55db565" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__I2C__CONFIG_1a2b8ec7e6589589f70cb5d4bbb55db565" kindref="member">RetryCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>失败重试次数</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>,<sp/>*<ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref>;</highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C传输类型枚举</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3" kindref="member">_I2C_TRANSFER_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="25" refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" kindref="member">I2CWrite</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" kindref="member">I2CRead</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" kindref="member">I2CWriteRead</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>先写后读操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="kmdf__i2c_8h_1a70b5b0e5b59f4301d02402a14c4ecb0b" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__i2c_8h_1a70b5b0e5b59f4301d02402a14c4ecb0b" kindref="member">I2C_TRANSFER_TYPE</ref>;</highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C传输包结构体</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="struct__I2C__TRANSFER__PACKET" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__I2C__TRANSFER__PACKET" kindref="compound">_I2C_TRANSFER_PACKET</ref><sp/>{</highlight></codeline>
<codeline lineno="32" refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" kindref="member">BUS_TRANSFER_PACKET</ref><sp/><ref refid="struct__I2C__TRANSFER__PACKET_1a20dd97830e1adfd27b9d7e46204415db" kindref="member">Common</ref>;<sp/><sp/></highlight><highlight class="comment">//<sp/>通用总线传输包</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33" refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/><ref refid="struct__I2C__TRANSFER__PACKET_1a75e9952dba35a8ba1937901272c7f340" kindref="member">SlaveAddress</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>从设备地址</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a70b5b0e5b59f4301d02402a14c4ecb0b" kindref="member">I2C_TRANSFER_TYPE</ref><sp/><ref refid="struct__I2C__TRANSFER__PACKET_1a6239118143a5327bfd92d6086107e101" kindref="member">Type</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>传输类型</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35" refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__I2C__TRANSFER__PACKET_1ab68810a9e3adeed2d53d5858fdd9cb3e" kindref="member">WriteBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写缓冲区</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>SIZE_T<sp/><ref refid="struct__I2C__TRANSFER__PACKET_1afaaced0212ee18a776559ff7045b7aa4" kindref="member">WriteBufferLength</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写缓冲区长度</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__I2C__TRANSFER__PACKET_1a1e07ec690f00c7a71deca7d96b3a97ad" kindref="member">ReadBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读缓冲区</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>SIZE_T<sp/><ref refid="struct__I2C__TRANSFER__PACKET_1aa2edc457f7179d999a40b1fe065c3532" kindref="member">ReadBufferLength</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读缓冲区长度</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref>,<sp/>*<ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref>;</highlight></codeline>
<codeline lineno="40"><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C总线接口函数声明</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="43"><highlight class="normal"><ref refid="kmdf__i2c_8h_1a5467da0184a8f514f9ff43ab28f7d2d0" kindref="member">I2CInitialize</ref>(</highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref><sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></highlight></codeline>
<codeline lineno="46"><highlight class="normal">);</highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight></codeline>
<codeline lineno="48"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><ref refid="kmdf__i2c_8h_1aa8f6531c5b52bc6d04ca38fbaab3c223" kindref="member">I2CUninitialize</ref>(</highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="51"><highlight class="normal">);</highlight></codeline>
<codeline lineno="52"><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><ref refid="kmdf__i2c_8h_1ae74bb3af98d6a79ba5774f9c3a480ca7" kindref="member">I2CTransferSynchronous</ref>(</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="58"><highlight class="normal">);</highlight></codeline>
<codeline lineno="59"><highlight class="normal"></highlight></codeline>
<codeline lineno="60"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><ref refid="kmdf__i2c_8h_1a363c4a8b2ee1e16d8a6aaf35b0e67722" kindref="member">I2CTransferAsynchronous</ref>(</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref><sp/>CompletionCallback,</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="66"><highlight class="normal">);</highlight></codeline>
<codeline lineno="67"><highlight class="normal"></highlight></codeline>
<codeline lineno="68"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><ref refid="kmdf__i2c_8h_1aa4838a1894b94b950fc4a7e73624d7ed" kindref="member">I2CWriteRegister</ref>(</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="75"><highlight class="normal">);</highlight></codeline>
<codeline lineno="76"><highlight class="normal"></highlight></codeline>
<codeline lineno="77"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><ref refid="kmdf__i2c_8h_1aad5c9145daea9c25554b814bfed47756" kindref="member">I2CReadRegister</ref>(</highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a519fae2d9daaac809af65134907b2fb0" kindref="member">I2C_ADDRESS</ref><sp/>SlaveAddress,</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PUCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="84"><highlight class="normal">);</highlight></codeline>
<codeline lineno="85"><highlight class="normal"></highlight></codeline>
<codeline lineno="86"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><ref refid="kmdf__i2c_8h_1a1b937c9865418ca9d50b16766c8ceb66" kindref="member">I2CScanBus</ref>(</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_to_(MaxDeviceAddresses,<sp/>*DeviceCount)<sp/><ref refid="kmdf__i2c_8h_1af11040ef31cae611dac879352c4fab17" kindref="member">PI2C_ADDRESS</ref><sp/>DeviceAddresses,</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>MaxDeviceAddresses,</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PULONG<sp/>DeviceCount</highlight></codeline>
<codeline lineno="92"><highlight class="normal">);</highlight></codeline>
<codeline lineno="93"><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_I2C_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h"/>
  </compounddef>
</doxygen>
