<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_spi.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__spi_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_spi.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="kmdf__bus__common_8h_source.html">kmdf_bus_common.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for kmdf_spi.h:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__spi_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="16,96,163,123"/>
<area shape="poly" title=" " alt="" coords="92,49,92,80,87,80,87,49"/>
<area shape="rect" title=" " alt="" coords="6,179,59,205"/>
<area shape="poly" title=" " alt="" coords="83,125,52,168,48,165,78,122"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="83,171,213,213"/>
<area shape="poly" title=" " alt="" coords="101,122,127,157,122,160,97,125"/>
<area shape="rect" title=" " alt="" coords="116,261,180,288"/>
<area shape="poly" title=" " alt="" coords="151,214,151,245,146,245,146,214"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__spi_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8hdep" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__spi_8hdep">
<area shape="rect" title=" " alt="" coords="659,5,828,48"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="659,96,828,139"/>
<area shape="poly" title=" " alt="" coords="746,64,746,96,741,96,741,64"/>
<area shape="rect" href="spi__device_8c.html" title=" " alt="" coords="490,187,658,229"/>
<area shape="poly" title=" " alt="" coords="697,59,649,98,613,144,587,188,582,185,608,141,645,94,694,55"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="1191,285,1336,328"/>
<area shape="poly" title=" " alt="" coords="796,53,1226,282,1224,287,794,58"/>
<area shape="poly" title=" " alt="" coords="692,149,615,189,612,184,689,144"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="683,187,828,229"/>
<area shape="poly" title=" " alt="" coords="751,154,755,186,750,187,746,155"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="5,277,151,336"/>
<area shape="poly" title=" " alt="" coords="668,232,525,248,412,252,301,258,237,266,163,280,152,282,150,277,161,275,236,261,301,253,412,247,524,243,667,227"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,277,320,336"/>
<area shape="poly" title=" " alt="" coords="668,232,572,246,497,254,424,262,332,280,321,283,320,278,331,275,423,257,497,248,572,241,667,227"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,285,489,328"/>
<area shape="poly" title=" " alt="" coords="668,234,501,280,478,287,476,282,500,275,666,229"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="513,285,659,328"/>
<area shape="poly" title=" " alt="" coords="707,240,624,287,621,282,705,235"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="683,285,828,328"/>
<area shape="poly" title=" " alt="" coords="758,245,758,285,753,285,753,245"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="852,285,997,328"/>
<area shape="poly" title=" " alt="" coords="806,235,890,282,887,287,803,240"/>
<area shape="poly" title=" " alt="" coords="844,224,858,227,949,240,1020,247,1090,256,1180,275,1207,282,1205,287,1179,280,1089,262,1019,253,948,245,858,232,843,229"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="1021,285,1167,328"/>
<area shape="poly" title=" " alt="" coords="844,229,1011,275,1034,282,1033,287,1009,280,843,234"/>
</map>
</div>
</div>
<p><a href="kmdf__spi_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FSPI_5FCONFIG_5Fstruct_5F_5FSPI_5F_5FCONFIG" id="r__5FSPI_5FCONFIG_5Fstruct_5F_5FSPI_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__SPI__CONFIG">_SPI_CONFIG</a></td></tr>
<tr class="memitem:_5FSPI_5FTRANSFER_5FPACKET_5Fstruct_5F_5FSPI_5F_5FTRANSFER_5F_5FPACKET" id="r__5FSPI_5FTRANSFER_5FPACKET_5Fstruct_5F_5FSPI_5F_5FTRANSFER_5F_5FPACKET"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a25212ee83b198babc11d7c726564c07c" id="r_a25212ee83b198babc11d7c726564c07c"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__CONFIG">_SPI_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td></tr>
<tr class="memitem:a8c0c38014d644418137aa056ce518223" id="r_a8c0c38014d644418137aa056ce518223"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:a02075f39766ac5419ad37fbd5e96de57" id="r_a02075f39766ac5419ad37fbd5e96de57"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a></td></tr>
<tr class="memitem:aa750b6896a759b95054bedea9ad132d9" id="r_aa750b6896a759b95054bedea9ad132d9"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__CONFIG">_SPI_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a></td></tr>
<tr class="memitem:ae9c35ffd537d30a103775489f57c24cc" id="r_ae9c35ffd537d30a103775489f57c24cc"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></td></tr>
<tr class="memitem:ae53b781a84db92ccef86085a53448289" id="r_ae53b781a84db92ccef86085a53448289"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae53b781a84db92ccef86085a53448289">SPI_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:a9920771d941aa8c6e1b7b97ce21e77ca" id="r_a9920771d941aa8c6e1b7b97ce21e77ca"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:af2e782dfd4d775865e0976660817e6e2" id="r_af2e782dfd4d775865e0976660817e6e2"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a> { <br />
&#160;&#160;<a class="el" href="#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2">SpiBusSpeed1MHz</a> = 1000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab">SpiBusSpeed2MHz</a> = 2000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94">SpiBusSpeed4MHz</a> = 4000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928">SpiBusSpeed8MHz</a> = 8000000
, <br />
&#160;&#160;<a class="el" href="#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997">SpiBusSpeed10MHz</a> = 10000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90">SpiBusSpeed20MHz</a> = 20000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef">SpiBusSpeed25MHz</a> = 25000000
, <a class="el" href="#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc">SpiBusSpeed50MHz</a> = 50000000
<br />
 }</td></tr>
<tr class="memitem:a500fe65207e47be6e52eee4a885d4374" id="r_a500fe65207e47be6e52eee4a885d4374"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a> { <a class="el" href="#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249">SpiMode0</a> = 0
, <a class="el" href="#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972">SpiMode1</a> = 1
, <a class="el" href="#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e">SpiMode2</a> = 2
, <a class="el" href="#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470">SpiMode3</a> = 3
 }</td></tr>
<tr class="memitem:ab41da20e3858f2c27bb25ef675858c21" id="r_ab41da20e3858f2c27bb25ef675858c21"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a> { <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a">SpiWrite</a>
, <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d">SpiRead</a>
, <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36">SpiWriteRead</a>
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a685d8d7731e750c1512b975df16cc030" id="r_a685d8d7731e750c1512b975df16cc030"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a685d8d7731e750c1512b975df16cc030">SPIInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="el" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a>)</td></tr>
<tr class="memitem:adb5a94e2dc80b87a505aea6c78f3b885" id="r_adb5a94e2dc80b87a505aea6c78f3b885"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adb5a94e2dc80b87a505aea6c78f3b885">SPIReadRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:a571fb3ea7eed247b3c46c57f506fa033" id="r_a571fb3ea7eed247b3c46c57f506fa033"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a571fb3ea7eed247b3c46c57f506fa033">SPITransferAsynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a> TransferPacket, _In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:a682c974659ab89363d0baa22470a386c" id="r_a682c974659ab89363d0baa22470a386c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a682c974659ab89363d0baa22470a386c">SPITransferSynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:ad756f8e3b06fdfa545a7048661038513" id="r_ad756f8e3b06fdfa545a7048661038513"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad756f8e3b06fdfa545a7048661038513">SPIUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a038c52771ec4b0654c0e59f37fccb29f" id="r_a038c52771ec4b0654c0e59f37fccb29f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a038c52771ec4b0654c0e59f37fccb29f">SPIWriteRead</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_reads_bytes_(WriteBufferLength) PVOID <a class="el" href="spi__device_8c.html#ad26ded9b73e8b14b4117614b39440d86">WriteBuffer</a>, _In_ ULONG WriteBufferLength, _Out_writes_bytes_(ReadBufferLength) PVOID <a class="el" href="spi__device_8c.html#ac2677e024009c29e2bcee99e0c32c735">ReadBuffer</a>, _In_ ULONG ReadBufferLength, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:a261c6752bd8e05e7e4d7eb1e60ed64f8" id="r_a261c6752bd8e05e7e4d7eb1e60ed64f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a261c6752bd8e05e7e4d7eb1e60ed64f8">SPIWriteRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__SPI__CONFIG" id="struct__SPI__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__SPI__CONFIG">&#9670;&#160;</a></span>_SPI_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _SPI_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a5568dcea126ea711e8d0f6b40dc951cc" name="a5568dcea126ea711e8d0f6b40dc951cc"></a>ULONG</td>
<td class="fieldname">
ChipSelectLine</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a23bf21cffc603724543545d3c677093a" name="a23bf21cffc603724543545d3c677093a"></a>BOOLEAN</td>
<td class="fieldname">
ChipSelectPolarity</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ad132bb074410f64505375ed71b0173f8" name="ad132bb074410f64505375ed71b0173f8"></a>BOOLEAN</td>
<td class="fieldname">
IsLsbFirst</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a6cf9c836c9e4e1a5b0dd2ffc18e218e6" name="a6cf9c836c9e4e1a5b0dd2ffc18e218e6"></a><a class="el" href="#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a></td>
<td class="fieldname">
MaxClockFrequency</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a5995a0bc6695e2e61567693ec8e494ca" name="a5995a0bc6695e2e61567693ec8e494ca"></a><a class="el" href="#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></td>
<td class="fieldname">
Mode</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a3eecf9d65abadb2106c4930606d31c7a" name="a3eecf9d65abadb2106c4930606d31c7a"></a>ULONG</td>
<td class="fieldname">
RetryCount</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aed13983e08690b185df2a6c06f44ffd3" name="aed13983e08690b185df2a6c06f44ffd3"></a>LARGE_INTEGER</td>
<td class="fieldname">
SpbConnectionId</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a065d24ae185eeafe1fc5ce74cf823454" name="a065d24ae185eeafe1fc5ce74cf823454"></a>PVOID</td>
<td class="fieldname">
SpbDeviceObject</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a85af30c409a08baa8e9c3e31567c07c2" name="a85af30c409a08baa8e9c3e31567c07c2"></a>ULONG</td>
<td class="fieldname">
TimeoutMs</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a2fb798d2a102a3bc148918a6528aa329" name="a2fb798d2a102a3bc148918a6528aa329"></a>ULONG</td>
<td class="fieldname">
WordLength</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="struct__SPI__TRANSFER__PACKET" id="struct__SPI__TRANSFER__PACKET"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__SPI__TRANSFER__PACKET">&#9670;&#160;</a></span>_SPI_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _SPI_TRANSFER_PACKET</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><div class="dynheader">
Collaboration diagram for _SPI_TRANSFER_PACKET:</div>
<div class="dyncontent">
<div class="center"><img src="struct__SPI__TRANSFER__PACKET__coll__graph.png" border="0" usemap="#a__SPI__TRANSFER__PACKET_coll__map" loading="lazy" alt="Collaboration graph"/></div>
<map name="a__SPI__TRANSFER__PACKET_coll__map" id="a__SPI__TRANSFER__PACKET_coll__map">
<area shape="rect" title=" " alt="" coords="8,104,199,131"/>
<area shape="rect" href="kmdf__bus__common_8h.html#struct__BUS__TRANSFER__PACKET" title=" " alt="" coords="5,5,202,32"/>
<area shape="poly" title=" " alt="" coords="106,48,106,104,101,104,101,48"/>
</map>
<center><span class="legend">[<a target="top" href="graph_legend.html">legend</a>]</span></center></div>
<table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ab7012a8e826ed6c62ef8b4e77d5dac24" name="ab7012a8e826ed6c62ef8b4e77d5dac24"></a>BOOLEAN</td>
<td class="fieldname">
AssertChipSelect</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a00931840236e9b48912a56116bd0d109" name="a00931840236e9b48912a56116bd0d109"></a><a class="el" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></td>
<td class="fieldname">
Common</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a2c4bee3baf8bbe9d12ea2b02a12deb1c" name="a2c4bee3baf8bbe9d12ea2b02a12deb1c"></a>BOOLEAN</td>
<td class="fieldname">
DeassertChipSelect</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a1bd99745fd3afc8bf6567a4460b85f28" name="a1bd99745fd3afc8bf6567a4460b85f28"></a>PVOID</td>
<td class="fieldname">
ReadBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="afc8f8284a74ea4a78d2531d7b09838ed" name="afc8f8284a74ea4a78d2531d7b09838ed"></a>SIZE_T</td>
<td class="fieldname">
ReadBufferLength</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ae3ca53594a2138cf5aea46199d1f00fc" name="ae3ca53594a2138cf5aea46199d1f00fc"></a><a class="el" href="#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a></td>
<td class="fieldname">
Type</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="adfd0120a5d0f6554506d7a8c4454609b" name="adfd0120a5d0f6554506d7a8c4454609b"></a>PVOID</td>
<td class="fieldname">
WriteBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a60c1c9ddb35c8b5aa7ef2d65b6a278a7" name="a60c1c9ddb35c8b5aa7ef2d65b6a278a7"></a>SIZE_T</td>
<td class="fieldname">
WriteBufferLength</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a25212ee83b198babc11d7c726564c07c" name="a25212ee83b198babc11d7c726564c07c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a25212ee83b198babc11d7c726564c07c">&#9670;&#160;</a></span>PSPI_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__CONFIG">_SPI_CONFIG</a> * <a class="el" href="#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8c0c38014d644418137aa056ce518223" name="a8c0c38014d644418137aa056ce518223"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c0c38014d644418137aa056ce518223">&#9670;&#160;</a></span>PSPI_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a> * <a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a02075f39766ac5419ad37fbd5e96de57" name="a02075f39766ac5419ad37fbd5e96de57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02075f39766ac5419ad37fbd5e96de57">&#9670;&#160;</a></span>SPI_BUS_SPEED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a> <a class="el" href="#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa750b6896a759b95054bedea9ad132d9" name="aa750b6896a759b95054bedea9ad132d9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa750b6896a759b95054bedea9ad132d9">&#9670;&#160;</a></span>SPI_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__CONFIG">_SPI_CONFIG</a> <a class="el" href="#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae9c35ffd537d30a103775489f57c24cc" name="ae9c35ffd537d30a103775489f57c24cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9c35ffd537d30a103775489f57c24cc">&#9670;&#160;</a></span>SPI_MODE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a> <a class="el" href="#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae53b781a84db92ccef86085a53448289" name="ae53b781a84db92ccef86085a53448289"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae53b781a84db92ccef86085a53448289">&#9670;&#160;</a></span>SPI_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a> <a class="el" href="#ae53b781a84db92ccef86085a53448289">SPI_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9920771d941aa8c6e1b7b97ce21e77ca" name="a9920771d941aa8c6e1b7b97ce21e77ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9920771d941aa8c6e1b7b97ce21e77ca">&#9670;&#160;</a></span>SPI_TRANSFER_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a> <a class="el" href="#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="af2e782dfd4d775865e0976660817e6e2" name="af2e782dfd4d775865e0976660817e6e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2e782dfd4d775865e0976660817e6e2">&#9670;&#160;</a></span>_SPI_BUS_SPEED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2" name="af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2"></a>SpiBusSpeed1MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab" name="af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab"></a>SpiBusSpeed2MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94" name="af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94"></a>SpiBusSpeed4MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928" name="af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928"></a>SpiBusSpeed8MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997" name="af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997"></a>SpiBusSpeed10MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90" name="af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90"></a>SpiBusSpeed20MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef" name="af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef"></a>SpiBusSpeed25MHz&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc" name="af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc"></a>SpiBusSpeed50MHz&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="a500fe65207e47be6e52eee4a885d4374" name="a500fe65207e47be6e52eee4a885d4374"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a500fe65207e47be6e52eee4a885d4374">&#9670;&#160;</a></span>_SPI_MODE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249" name="a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249"></a>SpiMode0&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972" name="a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972"></a>SpiMode1&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e" name="a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e"></a>SpiMode2&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470" name="a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470"></a>SpiMode3&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a id="ab41da20e3858f2c27bb25ef675858c21" name="ab41da20e3858f2c27bb25ef675858c21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab41da20e3858f2c27bb25ef675858c21">&#9670;&#160;</a></span>_SPI_TRANSFER_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a" name="ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a"></a>SpiWrite&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d" name="ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d"></a>SpiRead&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36" name="ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36"></a>SpiWriteRead&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a685d8d7731e750c1512b975df16cc030" name="a685d8d7731e750c1512b975df16cc030"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a685d8d7731e750c1512b975df16cc030">&#9670;&#160;</a></span>SPIInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPIInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>SpiConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_cgraph.png" border="0" usemap="#akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_cgraph" id="akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_cgraph">
<area shape="rect" title=" " alt="" coords="5,67,97,93"/>
<area shape="poly" title=" " alt="" coords="24,67,20,58,23,48,34,42,51,40,69,43,79,49,76,54,68,48,51,45,35,47,27,52,25,57,29,65"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="320,29,426,56"/>
<area shape="poly" title=" " alt="" coords="95,63,144,52,228,43,305,40,305,45,228,48,145,57,96,69"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="168,117,238,144"/>
<area shape="poly" title=" " alt="" coords="94,91,154,112,152,117,93,97"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="145,67,261,93"/>
<area shape="poly" title=" " alt="" coords="97,77,129,77,129,83,97,83"/>
<area shape="poly" title=" " alt="" coords="344,30,339,21,342,11,354,5,373,3,394,5,405,12,402,16,392,10,373,8,356,10,346,15,344,20,348,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="309,92,437,119"/>
<area shape="poly" title=" " alt="" coords="238,123,293,114,294,120,239,128"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="326,143,421,169"/>
<area shape="poly" title=" " alt="" coords="239,133,311,144,310,149,238,138"/>
<area shape="poly" title=" " alt="" coords="261,65,305,55,306,60,262,70"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_icgraph.png" border="0" usemap="#akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_icgraph" loading="lazy" alt=""/></div>
<map name="akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_icgraph" id="akmdf__spi_8h_a685d8d7731e750c1512b975df16cc030_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,97,56"/>
<area shape="poly" title=" " alt="" coords="65,15,60,10,51,8,42,10,38,14,37,20,39,28,34,30,31,20,33,11,40,5,51,3,63,5,69,12"/>
</map>
</div>

</div>
</div>
<a id="adb5a94e2dc80b87a505aea6c78f3b885" name="adb5a94e2dc80b87a505aea6c78f3b885"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb5a94e2dc80b87a505aea6c78f3b885">&#9670;&#160;</a></span>SPIReadRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPIReadRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PUCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a571fb3ea7eed247b3c46c57f506fa033" name="a571fb3ea7eed247b3c46c57f506fa033"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a571fb3ea7eed247b3c46c57f506fa033">&#9670;&#160;</a></span>SPITransferAsynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPITransferAsynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>CompletionCallback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a682c974659ab89363d0baa22470a386c" name="a682c974659ab89363d0baa22470a386c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a682c974659ab89363d0baa22470a386c">&#9670;&#160;</a></span>SPITransferSynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPITransferSynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad756f8e3b06fdfa545a7048661038513" name="ad756f8e3b06fdfa545a7048661038513"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad756f8e3b06fdfa545a7048661038513">&#9670;&#160;</a></span>SPIUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID SPIUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__spi_8h_ad756f8e3b06fdfa545a7048661038513_cgraph.png" border="0" usemap="#akmdf__spi_8h_ad756f8e3b06fdfa545a7048661038513_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__spi_8h_ad756f8e3b06fdfa545a7048661038513_cgraph" id="akmdf__spi_8h_ad756f8e3b06fdfa545a7048661038513_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,113,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="161,29,266,56"/>
<area shape="poly" title=" " alt="" coords="113,40,145,40,145,45,113,45"/>
<area shape="poly" title=" " alt="" coords="186,30,182,21,185,11,196,5,213,3,232,5,242,12,239,16,230,10,213,8,198,10,189,15,187,20,191,28"/>
</map>
</div>

</div>
</div>
<a id="a038c52771ec4b0654c0e59f37fccb29f" name="a038c52771ec4b0654c0e59f37fccb29f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a038c52771ec4b0654c0e59f37fccb29f">&#9670;&#160;</a></span>SPIWriteRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPIWriteRead </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_bytes_(WriteBufferLength) PVOID</td>          <td class="paramname"><span class="paramname"><em>WriteBuffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>WriteBufferLength</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_bytes_(ReadBufferLength) PVOID</td>          <td class="paramname"><span class="paramname"><em>ReadBuffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>ReadBufferLength</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a261c6752bd8e05e7e4d7eb1e60ed64f8" name="a261c6752bd8e05e7e4d7eb1e60ed64f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a261c6752bd8e05e7e4d7eb1e60ed64f8">&#9670;&#160;</a></span>SPIWriteRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS SPIWriteRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__spi_8h.html">kmdf_spi.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
