<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li class="current"><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_l.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>LastPinValue&#160;:&#160;<a class="el" href="gpio__device_8c.html#af4f2a770562d41e27d370140ab383393">gpio_device.c</a></li>
<li>LIST_ENTRY&#160;:&#160;<a class="el" href="core__types_8h.html#afe2819542d515b3631539098349c6d52">core_types.h</a></li>
<li>Lock&#160;:&#160;<a class="el" href="gpio__core_8c.html#ac91080baa5062b15cd46c8e08028fd51">gpio_core.c</a></li>
<li>LOG_ALERT&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#aa1911455782e83f3b06fab600be0e43e">driver_log.h</a></li>
<li>LOG_ALERT_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a4c42b3fa94110619ab8458eb672d189d">driver_log.h</a></li>
<li>LOG_CONFIG&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a565ca8d986ea85865e5e0e69c0fccc9d">driver_log.h</a></li>
<li>LOG_CRITICAL&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#abc03884460a6987df33fea0d5cae8302">driver_log.h</a></li>
<li>LOG_CRITICAL_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ab48ce4a2ee7f0b5f74153fedf6ad7c25">driver_log.h</a></li>
<li>LOG_DEBUG&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#acfe39a25e08737b535dc881071ebf149">driver_log.h</a>, <a class="el" href="Common_8h.html#acfe39a25e08737b535dc881071ebf149">Common.h</a>, <a class="el" href="include_2core_2log_2driver__log_8h.html#abd0b0523397fb05f0ed46fc217fb630f">driver_log.h</a></li>
<li>LOG_DEBUG_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ae930e4b3ae4e59dc6a7b6a4feefb116f">driver_log.h</a></li>
<li>LOG_EMERGENCY&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#ad706db1253940848e01bbc71ede868ef">driver_log.h</a></li>
<li>LOG_EMERGENCY_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a570937723f42dd301b24b631ec455b58">driver_log.h</a></li>
<li>LOG_ERROR&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a29e75b488d8e8ef5641c5bd16709faec">driver_log.h</a></li>
<li>LOG_ERROR_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#aff0a0cc082f6b2fad9ed0979da6e8a9b">driver_log.h</a></li>
<li>LOG_INFO&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7748b322eafa9e058c518fef49b110cb">driver_log.h</a></li>
<li>LOG_INFO_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a94d7f96857344352ffbc6ee65e9f2390">driver_log.h</a></li>
<li>LOG_LEVEL&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#aa90925833aff044f4ba03f43f8084bf7">driver_log.h</a></li>
<li>LOG_NOTICE&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a05bf2404451e701f51d18409e72321fd">driver_log.h</a></li>
<li>LOG_NOTICE_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a6cf4240fc51cea71e901acf9df797b98">driver_log.h</a></li>
<li>LOG_TRACE&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a0972af62c9ad7b688924604669d7d762">driver_log.h</a></li>
<li>LOG_TRACE_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a8793c218a97ef927e72271b80a872495">driver_log.h</a></li>
<li>LOG_TYPE&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a00e4548dd1db35b54cbeb1ee0fe45f66">driver_log.h</a></li>
<li>LOG_TYPES&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a5c3fab47ae6bd7de107b55a48ff20591">driver_log.h</a></li>
<li>LOG_VERBOSE&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a6594ece0df59e19da1473edfc079fd45">driver_log.h</a></li>
<li>LOG_WARNING&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a1dd05e1ef2b66fc68251edacaa75e9f7">driver_log.h</a></li>
<li>LOG_WARNING_IF&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a238f142a1b0fcbd8378c38d99b233baa">driver_log.h</a></li>
<li>LogCleanup()&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7">driver_log.h</a></li>
<li>LogConfigInit()&#160;:&#160;<a class="el" href="driver__log_8c.html#aa7f5f3b01615029c1cb54753c0b03175">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#aa7f5f3b01615029c1cb54753c0b03175">driver_log.h</a></li>
<li>LogError()&#160;:&#160;<a class="el" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e">error_handling.c</a>, <a class="el" href="gpio__core_8c.html#a9f7acfc8c5f9d7d32170223f628f766b">gpio_core.c</a></li>
<li>LogFunctionEntry()&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81">driver_log.h</a></li>
<li>LogFunctionExit()&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740">driver_log.h</a></li>
<li>LogInfo&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a1f2322291188e5d028f82ff2f340e5e3">driver_log.h</a>, <a class="el" href="gpio__core_8c.html#a8f2aeb9f5f8525345d3f33df4340da13">gpio_core.c</a>, <a class="el" href="gpio__device_8c.html#ac410021632e38928f5eb4b3bb6939ab9">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a90ec17a9895e508ccdb9077fed539682">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a92b63e772873a034bea01d26f382ed57">spi_device.c</a></li>
<li>LogInitialize()&#160;:&#160;<a class="el" href="driver__log_8c.html#aa2e9424857371175fc265253fbabcc5d">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#aa2e9424857371175fc265253fbabcc5d">driver_log.h</a></li>
<li>LogLevelAlert&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2cc88529cf5141237f19b9b2b2a89a7c">driver_log.h</a></li>
<li>LogLevelCritical&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fafa95954c0943fbc85aa4abe3d4336408">driver_log.h</a></li>
<li>LogLevelDebug&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053">driver_log.h</a></li>
<li>LogLevelDisabled&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16">driver_log.h</a></li>
<li>LogLevelEmergency&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac4cac90e34ed0a5807fdc3e001fde131">driver_log.h</a></li>
<li>LogLevelError&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e">driver_log.h</a></li>
<li>LogLevelInfo&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c">driver_log.h</a></li>
<li>LogLevelMax&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fad6e9a0209bc4ac9dbc6613aae7188f9f">driver_log.h</a></li>
<li>LogLevelNotice&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa7acde1ddb77a67b2e59d9fa45674e6d0">driver_log.h</a></li>
<li>LogLevelTrace&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa5b1acccc8777a03da0504525b30a9816">driver_log.h</a></li>
<li>LogLevelVerbose&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b">driver_log.h</a></li>
<li>LogLevelWarning&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180">driver_log.h</a></li>
<li>LogMessage()&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1">driver_log.h</a></li>
<li>LogMessageVA()&#160;:&#160;<a class="el" href="driver__log_8c.html#a8e8711da6408af7b3b313f892121215e">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#a8e8711da6408af7b3b313f892121215e">driver_log.h</a></li>
<li>LogSetLevel()&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185">driver_log.h</a></li>
<li>LogTypeAll&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee">driver_log.h</a></li>
<li>LogTypeDebugger&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a5aae8ab99c89db2d8942438f56e3f127">driver_log.h</a></li>
<li>LogTypeETW&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3">driver_log.h</a></li>
<li>LogTypeFile&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d">driver_log.h</a></li>
<li>LogTypeKdPrint&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543">driver_log.h</a></li>
<li>LogTypeNone&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5acb5d9b87dd013da99196bb1257679ad1">driver_log.h</a></li>
<li>LogTypeWPP&#160;:&#160;<a class="el" href="src_2core_2log_2driver__log_8h.html#a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7">driver_log.h</a></li>
<li>LogUninitialize()&#160;:&#160;<a class="el" href="driver__log_8c.html#aab8bcb7121136bc236fe5d55778fbaf2">driver_log.c</a>, <a class="el" href="src_2core_2log_2driver__log_8h.html#aab8bcb7121136bc236fe5d55778fbaf2">driver_log.h</a></li>
<li>LogWarning&#160;:&#160;<a class="el" href="include_2core_2log_2driver__log_8h.html#aa47a100aaaa86f29c113feda40125d64">driver_log.h</a>, <a class="el" href="gpio__device_8c.html#a1fbae9652e19914386dbb4fc4848b63d">gpio_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
