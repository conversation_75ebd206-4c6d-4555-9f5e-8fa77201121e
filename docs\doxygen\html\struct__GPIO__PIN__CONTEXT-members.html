<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('struct__GPIO__PIN__CONTEXT.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">_GPIO_PIN_CONTEXT Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a44db5b39e5c14ce83ac0637da253fb47">EvtGpioInterruptDpc</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a4b4c364430ee4f37c01413a6552df5d0">EvtGpioInterruptIsr</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#ab343a956248ebb07de6a72eaeb55ec35">GPIOInitialize</a>(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig)</td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#acd3cbb2291f76aabd30090072d539050">if</a>(gpioManager==NULL)</td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#ae1602e3f0f02ccfe7b30fdaa89b017a7">Lock</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454">LogError</a>(ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;PinNumber)</td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a4dd51420a9c389d77c721c25d4349927">Pins</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a81f98266b74a3a8d80a632eead026240">SpbIoTarget</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#ad95efe470c1ee3b4b0f0d81c7f3c53fb">STATUS_INVALID_PARAMETER</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a9e971cde7006142d4b5ac56689228e0b">WdfDevice</a></td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html#a846739f679fc9ddea85cb263aa72dc9f">WdfSpinLockRelease</a>(gpioManager-&gt;Lock)</td><td class="entry"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
