<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="globals.html#index__5F"><span>_</span></a></li>
      <li><a href="globals_a.html#index_a"><span>a</span></a></li>
      <li><a href="globals_b.html#index_b"><span>b</span></a></li>
      <li><a href="globals_c.html#index_c"><span>c</span></a></li>
      <li><a href="globals_d.html#index_d"><span>d</span></a></li>
      <li class="current"><a href="globals_e.html#index_e"><span>e</span></a></li>
      <li><a href="globals_f.html#index_f"><span>f</span></a></li>
      <li><a href="globals_g.html#index_g"><span>g</span></a></li>
      <li><a href="globals_h.html#index_h"><span>h</span></a></li>
      <li><a href="globals_i.html#index_i"><span>i</span></a></li>
      <li><a href="globals_k.html#index_k"><span>k</span></a></li>
      <li><a href="globals_l.html#index_l"><span>l</span></a></li>
      <li><a href="globals_m.html#index_m"><span>m</span></a></li>
      <li><a href="globals_n.html#index_n"><span>n</span></a></li>
      <li><a href="globals_o.html#index_o"><span>o</span></a></li>
      <li><a href="globals_p.html#index_p"><span>p</span></a></li>
      <li><a href="globals_r.html#index_r"><span>r</span></a></li>
      <li><a href="globals_s.html#index_s"><span>s</span></a></li>
      <li><a href="globals_t.html#index_t"><span>t</span></a></li>
      <li><a href="globals_w.html#index_w"><span>w</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('globals_e.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="contents">
<div class="textblock">Here is a list of all file members with links to the files they belong to:</div>

<h3 class="doxsection"><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>else&#160;:&#160;<a class="el" href="gpio__device_8c.html#a0544c3fe466e421738dae463968b70ba">gpio_device.c</a>, <a class="el" href="i2c__device_8c.html#a0544c3fe466e421738dae463968b70ba">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#a0544c3fe466e421738dae463968b70ba">spi_device.c</a></li>
<li>ERROR_BUS_PROTOCOL_ERROR&#160;:&#160;<a class="el" href="error__codes_8h.html#affd17c80938eed361b46758b3ab696cd">error_codes.h</a></li>
<li>ERROR_CODE_ENTRY&#160;:&#160;<a class="el" href="error__handling_8c.html#a94fd8be88deeace96d579af0616d0d13">error_handling.c</a></li>
<li>ERROR_DEVICE_HARDWARE_ERROR&#160;:&#160;<a class="el" href="error__codes_8h.html#a254cfc13872e4cf4fe0f89067d00f86a">error_codes.h</a></li>
<li>ERROR_DEVICE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#adbe74e534b99eb53edd59a05ee8e426f">error_codes.h</a></li>
<li>ERROR_DEVICE_NOT_AVAILABLE&#160;:&#160;<a class="el" href="error__codes_8h.html#a869e1fe828b9b51d971bcfe6595101bd">error_codes.h</a></li>
<li>ERROR_DEVICE_NOT_READY&#160;:&#160;<a class="el" href="error__codes_8h.html#ac80b2a8fac0e8f846c5f16200c7bb19a">error_codes.h</a></li>
<li>ERROR_DRIVER_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a2db6445dc95ce4a34ce59d355566545c">error_codes.h</a></li>
<li>ERROR_DRIVER_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#af5020ced0fe4cabcbd18b06de7b4fd7f">error_codes.h</a></li>
<li>ERROR_HARDWARE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a1ee37513091f1fa4fd23ad7a31815169">error_codes.h</a></li>
<li>ERROR_I2C_ARBITRATION_LOST&#160;:&#160;<a class="el" href="error__codes_8h.html#abf05011deed9a7688487446dcedb7734">error_codes.h</a></li>
<li>ERROR_I2C_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a37b24e0918ff0d7e358234acfd7514bf">error_codes.h</a></li>
<li>ERROR_I2C_BUS_BUSY&#160;:&#160;<a class="el" href="error__codes_8h.html#a361a624ddf35d38e3673f8fd648bc533">error_codes.h</a></li>
<li>ERROR_I2C_DEVICE_NOT_RESPONDING&#160;:&#160;<a class="el" href="error__codes_8h.html#a8977c5e15064527b2c7e501feb3915de">error_codes.h</a></li>
<li>ERROR_INSTALL_FAILURE&#160;:&#160;<a class="el" href="error__codes_8h.html#a117a0e9b2ae10c0775f947eb6aaf2e43">error_codes.h</a></li>
<li>ERROR_INVALID_PARAMETER&#160;:&#160;<a class="el" href="error__codes_8h.html#a176960f93d11db6f58d10a6957a80f38">error_codes.h</a></li>
<li>ERROR_IO_DEVICE&#160;:&#160;<a class="el" href="error__codes_8h.html#a1d5275aca16a6e82736bccadf6d54f51">error_codes.h</a></li>
<li>ERROR_IO_OPERATION_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#ab62592f2d0fe2a25aceff8a2aea7120d">error_codes.h</a></li>
<li>ERROR_MEMORY_ALLOCATION_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a8c7e35c51169754be6ad9a33b4d0ede3">error_codes.h</a></li>
<li>ERROR_NOT_ENOUGH_MEMORY&#160;:&#160;<a class="el" href="error__codes_8h.html#a0e8ed8133680d2d6b8909e71b8048307">error_codes.h</a></li>
<li>ERROR_NOT_READY&#160;:&#160;<a class="el" href="error__codes_8h.html#a61a95049738405a02053f09a5707e1c9">error_codes.h</a></li>
<li>ERROR_QUEUE_INIT_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a1dc03de0e9017ea32c0bab1658627cab">error_codes.h</a></li>
<li>ERROR_RESOURCE_NOT_FOUND&#160;:&#160;<a class="el" href="error__codes_8h.html#add601dcfffde2047b7051c4990d109e6">error_codes.h</a></li>
<li>ERROR_SPI_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a224c1c26e72afd5d9b1eb990a3808ba0">error_codes.h</a></li>
<li>ERROR_SPI_TRANSFER_FAILED&#160;:&#160;<a class="el" href="error__codes_8h.html#a8a14c6072318a642cb973e5e1b3749cf">error_codes.h</a></li>
<li>ERROR_SUCCESS&#160;:&#160;<a class="el" href="error__codes_8h.html#aea0ae801b7d25c979655a7eb20d034af">error_codes.h</a></li>
<li>ERROR_TIMEOUT&#160;:&#160;<a class="el" href="error__codes_8h.html#ad58fd51c3e56b5d9555ce48be5cc53bd">error_codes.h</a></li>
<li>ERROR_USB_BASE&#160;:&#160;<a class="el" href="error__codes_8h.html#a3b6ff704bf43afbc0cd69efc9d429f64">error_codes.h</a></li>
<li>ERROR_USB_ENDPOINT_NOT_FOUND&#160;:&#160;<a class="el" href="error__codes_8h.html#a23a63dc8ca4262eacbd1b165c5c9de52">error_codes.h</a></li>
<li>ErrorCodeToString()&#160;:&#160;<a class="el" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487">error_handling.c</a>, <a class="el" href="error__codes_8h.html#a14083fcce33766b91f8d08998cde8487">error_codes.h</a></li>
<li>ErrorCount&#160;:&#160;<a class="el" href="i2c__device_8c.html#ab9707a002fb8033fdc202e8c8b8f8569">i2c_device.c</a>, <a class="el" href="spi__device_8c.html#ab9707a002fb8033fdc202e8c8b8f8569">spi_device.c</a></li>
<li>ErrorTable&#160;:&#160;<a class="el" href="error__handling_8c.html#aecb63565dd13e1edddcd31120d17a148">error_handling.c</a></li>
<li>EvtDebounceTimerFunc&#160;:&#160;<a class="el" href="gpio__device_8c.html#addddad69ceb0e8964c3018cd853b60d6">gpio_device.c</a></li>
<li>EvtDriverDeviceAdd()&#160;:&#160;<a class="el" href="driver__entry_8c.html#a0776c179fdcbdd09df07ee264e7e78e6">driver_entry.c</a>, <a class="el" href="driver__main_8c.html#a0776c179fdcbdd09df07ee264e7e78e6">driver_main.c</a></li>
<li>EvtDriverUnload()&#160;:&#160;<a class="el" href="driver__entry_8c.html#a075700d7117ddde115f3bb0db54b619e">driver_entry.c</a>, <a class="el" href="driver__main_8c.html#a075700d7117ddde115f3bb0db54b619e">driver_main.c</a></li>
<li>EvtGpioInterruptDpc&#160;:&#160;<a class="el" href="gpio__core_8c.html#af0a4fb8c0dd33529dad81e93a0b0661f">gpio_core.c</a></li>
<li>EvtGpioInterruptIsr&#160;:&#160;<a class="el" href="gpio__core_8c.html#a4d9377385f26f2d958a494c7ffbcc04f">gpio_core.c</a></li>
<li>EvtI2cInterruptDpc&#160;:&#160;<a class="el" href="i2c__device_8c.html#a02a7df72146aec5cbd7c5a854a9adcda">i2c_device.c</a></li>
<li>EvtI2cInterruptIsr&#160;:&#160;<a class="el" href="i2c__device_8c.html#ab9ce8c5b03a473cd94f2812fa8622837">i2c_device.c</a></li>
<li>EvtI2cRequestCompletion&#160;:&#160;<a class="el" href="i2c__device_8c.html#a99bc0f18d2d6fca4d292cd4026a2435f">i2c_device.c</a></li>
<li>EvtUsbInterruptPipeReadComplete&#160;:&#160;<a class="el" href="device__manager_8h.html#a07afcd8a4d15d856ac651241642ebcd8">device_manager.h</a>, <a class="el" href="device__manager_8c.html#a091b9ef55e7ab6472a25567a30b1bf5a">device_manager.c</a></li>
<li>ExFreePoolWithTag()&#160;:&#160;<a class="el" href="spi__device_8c.html#ae42ccb14fff6c8b1c06d1ff178b6c146">spi_device.c</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
