<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_gpio.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__gpio_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_gpio.h</div></div>
</div><!--header-->
<div class="contents">
<a href="kmdf__gpio_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * kmdf_gpio.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * GPIO总线接口头文件</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * 提供GPIO特定的接口和数据结构</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> */</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span> </div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#ifndef KMDF_GPIO_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#define KMDF_GPIO_H</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span> </div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#include &quot;<a class="code" href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a>&quot;</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span> </div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment">// GPIO总线特定函数声明 GPIO事件回调 GPIO特定配置结构体 GPIO中断类型 GPIO高低电平有效或低电平有效 GPIO方向</span></div>
<div class="foldopen" id="foldopen00014" data-start="{" data-end="};">
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4b">   14</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a> {</div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2">   15</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2">GpioDirectionIn</a> = 0,     <span class="comment">// 输入</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c">   16</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c">GpioDirectionOut</a> = 1,    <span class="comment">// 输出</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">   17</a></span>} <a class="code hl_typedef" href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a>;</div>
</div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span> </div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00020" data-start="{" data-end="};">
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070">   20</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a> {</div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78">   21</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78">GpioPolarityActiveLow</a> = 0,  <span class="comment">// 低电平有效</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d">   22</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d">GpioPolarityActiveHigh</a> = 1  <span class="comment">// 高电平有效</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a96127f2b5d39ef12184a23f9ad42999b">   23</a></span>} <a class="code hl_typedef" href="kmdf__gpio_8h.html#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a>;</div>
</div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span> </div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00026" data-start="{" data-end="};">
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33">   26</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a> {</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76">   27</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76">GpioInterruptNone</a> = 0,       <span class="comment">// 无中断</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560">   28</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560">GpioInterruptRising</a> = 1,     <span class="comment">// 上升沿中断</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84">   29</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84">GpioInterruptFalling</a> = 2,    <span class="comment">// 下降沿中断</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64">   30</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64">GpioInterruptBoth</a> = 3,       <span class="comment">// 双沿中断</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42">   31</a></span>    <a class="code hl_enumvalue" href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42">GpioInterruptLevel</a> = 4       <span class="comment">// 电平中断</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">   32</a></span>} <a class="code hl_typedef" href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a>;</div>
</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span> </div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="comment">//</span></div>
<div class="foldopen" id="foldopen00035" data-start="{" data-end="};">
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html">   35</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__gpio_8h.html#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a> {</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#acc85fb800144aa8336d910544113014c">   36</a></span>    ULONG <a class="code hl_variable" href="kmdf__gpio_8h.html#acc85fb800144aa8336d910544113014c">PinNumber</a>;                 <span class="comment">// GPIO引脚编号</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#aa3d8c873130efce02f77b07f742a05ff">   37</a></span>    <a class="code hl_typedef" href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a> <a class="code hl_variable" href="kmdf__gpio_8h.html#aa3d8c873130efce02f77b07f742a05ff">Direction</a>;   <span class="comment">// 输入/输出</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a3298588b14792c3fdd790313352c673e">   38</a></span>    <a class="code hl_typedef" href="kmdf__gpio_8h.html#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a> <a class="code hl_variable" href="kmdf__gpio_8h.html#a3298588b14792c3fdd790313352c673e">Polarity</a>;     <span class="comment">// 极性</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a16e25974fff79d31225fbb4402059d42">   39</a></span>    <a class="code hl_typedef" href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a> <a class="code hl_variable" href="kmdf__gpio_8h.html#a16e25974fff79d31225fbb4402059d42">InterruptType</a>; <span class="comment">// 中断类型</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a8d011afe85a8ab79a1f0ab8a0aeeee9f">   40</a></span>    ULONG <a class="code hl_variable" href="kmdf__gpio_8h.html#a8d011afe85a8ab79a1f0ab8a0aeeee9f">DebounceTime</a>;              <span class="comment">// 去抖时间(ms)</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a0de26b1a7ce28b0b9aa018fe961fdf60">   41</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__gpio_8h.html#a0de26b1a7ce28b0b9aa018fe961fdf60">PullUp</a>;                  <span class="comment">// 启用上拉</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#afd66b730c3f6bec3b15b28ccd0832f85">   42</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__gpio_8h.html#afd66b730c3f6bec3b15b28ccd0832f85">PullDown</a>;                <span class="comment">// 启用下拉</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#abd8ea283a387003ee58595a12fb2b5ec">   43</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__gpio_8h.html#abd8ea283a387003ee58595a12fb2b5ec">OpenDrain</a>;               <span class="comment">// 开漏模式</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#aec4597ae991a215e755336600b15e60a">   44</a></span>    PVOID <a class="code hl_variable" href="kmdf__gpio_8h.html#aec4597ae991a215e755336600b15e60a">SpbDeviceObject</a>;           <span class="comment">// SPB设备对象</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a6065b7e884557238880cacf2002110d3">   45</a></span>    LARGE_INTEGER <a class="code hl_variable" href="kmdf__gpio_8h.html#a6065b7e884557238880cacf2002110d3">SpbConnectionId</a>;   <span class="comment">// SPB连接ID</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">   46</a></span>} <a class="code hl_typedef" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a>, *<a class="code hl_typedef" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a>;</div>
</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span> </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="comment">//</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="keyword">typedef</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a9643691e5b435f14c69e6016c2fae45a">   50</a></span>VOID</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>(*<a class="code hl_typedef" href="kmdf__gpio_8h.html#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a>)(</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>    _In_ BOOLEAN PinValue,</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>    _In_opt_ PVOID Context</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>    );</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span> </div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span><span class="comment">//</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><a class="code hl_function" href="kmdf__gpio_8h.html#a0a73c23c89291af0e81cef7098d10e29">GPIOInitialize</a>(</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    _In_ <a class="code hl_typedef" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a> GpioConfig</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>);</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span> </div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><a class="code hl_function" href="kmdf__gpio_8h.html#a785f00e9c0879fb478077d2cdce99906">GPIOUninitialize</a>(</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>);</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span> </div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#aab159dfcef06f528d7ebdb9fa3ce8be4">   72</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#aab159dfcef06f528d7ebdb9fa3ce8be4">GPIOSetDirection</a>(</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>    _In_ <a class="code hl_typedef" href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a> <a class="code hl_variable" href="gpio__device_8c.html#a130124259198fee8d71747e31a529e96">Direction</a></div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>);</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span> </div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a81265230a3fa84b9f3a7851d8c9ebe3d">   79</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#a81265230a3fa84b9f3a7851d8c9ebe3d">GPIOSetValue</a>(</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    _In_ BOOLEAN Value</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>);</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span> </div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#adcddf2e62a93fe5cc3fa7f46b67845bb">   86</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#adcddf2e62a93fe5cc3fa7f46b67845bb">GPIOGetValue</a>(</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    _Out_ PBOOLEAN Value</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>);</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span> </div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a818408e823499cbc8bf3a09f74062a48">   93</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#a818408e823499cbc8bf3a09f74062a48">GPIOEnableInterrupt</a>(</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    _In_ <a class="code hl_typedef" href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a> InterruptType,</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    _In_ <a class="code hl_typedef" href="kmdf__gpio_8h.html#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a> Callback,</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>    _In_opt_ PVOID Context</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>);</div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span> </div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4">  102</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4">GPIODisableInterrupt</a>(</div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a></div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>);</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span> </div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno"><a class="line" href="kmdf__gpio_8h.html#ade1cb652014fc4a3984567ff49900d81">  108</a></span><a class="code hl_function" href="kmdf__gpio_8h.html#ade1cb652014fc4a3984567ff49900d81">GPIOPulse</a>(</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    _In_ ULONG <a class="code hl_variable" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>,</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    _In_ ULONG PulseDurationMs</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>);</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span> </div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span><span class="preprocessor">#endif </span><span class="comment">// KMDF_GPIO_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="acore__types_8h_html_a1cb14808a3eba8cd3fcc47bd1207a805"><div class="ttname"><a href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a></div><div class="ttdeci">#define WDFAPI</div><div class="ttdef"><b>Definition</b> core_types.h:21</div></div>
<div class="ttc" id="agpio__device_8c_html_a130124259198fee8d71747e31a529e96"><div class="ttname"><a href="gpio__device_8c.html#a130124259198fee8d71747e31a529e96">Direction</a></div><div class="ttdeci">pinConfig Direction</div><div class="ttdef"><b>Definition</b> gpio_device.c:213</div></div>
<div class="ttc" id="agpio__device_8c_html_a2105af29d2c177b4d5c5d8e589b1caa3"><div class="ttname"><a href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a></div><div class="ttdeci">pinConfig PinNumber</div><div class="ttdef"><b>Definition</b> gpio_device.c:212</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html"><div class="ttname"><a href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a></div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a0a73c23c89291af0e81cef7098d10e29"><div class="ttname"><a href="kmdf__gpio_8h.html#a0a73c23c89291af0e81cef7098d10e29">GPIOInitialize</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOInitialize(_In_ WDFDEVICE Device, _In_ PGPIO_PIN_CONFIG GpioConfig)</div><div class="ttdef"><b>Definition</b> gpio_core.c:140</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a0de26b1a7ce28b0b9aa018fe961fdf60"><div class="ttname"><a href="kmdf__gpio_8h.html#a0de26b1a7ce28b0b9aa018fe961fdf60">_GPIO_PIN_CONFIG::PullUp</a></div><div class="ttdeci">BOOLEAN PullUp</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:41</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a16e25974fff79d31225fbb4402059d42"><div class="ttname"><a href="kmdf__gpio_8h.html#a16e25974fff79d31225fbb4402059d42">_GPIO_PIN_CONFIG::InterruptType</a></div><div class="ttdeci">GPIO_INTERRUPT_TYPE InterruptType</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:39</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a1ca9004cc1371bfb961fb7b894e2cd0a"><div class="ttname"><a href="kmdf__gpio_8h.html#a1ca9004cc1371bfb961fb7b894e2cd0a">GPIO_PIN_DIRECTION</a></div><div class="ttdeci">enum _GPIO_PIN_DIRECTION GPIO_PIN_DIRECTION</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a3298588b14792c3fdd790313352c673e"><div class="ttname"><a href="kmdf__gpio_8h.html#a3298588b14792c3fdd790313352c673e">_GPIO_PIN_CONFIG::Polarity</a></div><div class="ttdeci">GPIO_PIN_POLARITY Polarity</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:38</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a32c9f2fdbf98b7e59c2b494d61f465a4"><div class="ttname"><a href="kmdf__gpio_8h.html#a32c9f2fdbf98b7e59c2b494d61f465a4">GPIODisableInterrupt</a></div><div class="ttdeci">WDFAPI VOID GPIODisableInterrupt(_In_ WDFDEVICE Device, _In_ ULONG PinNumber)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a33cdce91cf0e8b3834911035d71d7c4b"><div class="ttname"><a href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4b">_GPIO_PIN_DIRECTION</a></div><div class="ttdeci">_GPIO_PIN_DIRECTION</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:14</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c"><div class="ttname"><a href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4ba07a779f2f51b06712c24bde308bb7b7c">GpioDirectionOut</a></div><div class="ttdeci">@ GpioDirectionOut</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:16</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2"><div class="ttname"><a href="kmdf__gpio_8h.html#a33cdce91cf0e8b3834911035d71d7c4bafaf939fe7b997a1a692a503ce7f083f2">GpioDirectionIn</a></div><div class="ttdeci">@ GpioDirectionIn</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:15</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a6065b7e884557238880cacf2002110d3"><div class="ttname"><a href="kmdf__gpio_8h.html#a6065b7e884557238880cacf2002110d3">_GPIO_PIN_CONFIG::SpbConnectionId</a></div><div class="ttdeci">LARGE_INTEGER SpbConnectionId</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:45</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a785f00e9c0879fb478077d2cdce99906"><div class="ttname"><a href="kmdf__gpio_8h.html#a785f00e9c0879fb478077d2cdce99906">GPIOUninitialize</a></div><div class="ttdeci">WDFAPI VOID GPIOUninitialize(_In_ WDFDEVICE Device, _In_ ULONG PinNumber)</div><div class="ttdef"><b>Definition</b> gpio_core.c:225</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33">_GPIO_INTERRUPT_TYPE</a></div><div class="ttdeci">_GPIO_INTERRUPT_TYPE</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:26</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a42589558f21e451585d2f0ef519a5e42">GpioInterruptLevel</a></div><div class="ttdeci">@ GpioInterruptLevel</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:31</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a52f24dd2bcfffa0b78ffca36efa06f84">GpioInterruptFalling</a></div><div class="ttdeci">@ GpioInterruptFalling</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:29</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7c1a7c1edd720f0ad70241165485ac76">GpioInterruptNone</a></div><div class="ttdeci">@ GpioInterruptNone</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:27</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33a7f1ac3c20d64a2a35e1d3cfcc1933a64">GpioInterruptBoth</a></div><div class="ttdeci">@ GpioInterruptBoth</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:30</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560"><div class="ttname"><a href="kmdf__gpio_8h.html#a7a6307ef3793d4b6fcafc75f54030d33acbcea140c80d327bf2f7d637b3efe560">GpioInterruptRising</a></div><div class="ttdeci">@ GpioInterruptRising</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:28</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a81265230a3fa84b9f3a7851d8c9ebe3d"><div class="ttname"><a href="kmdf__gpio_8h.html#a81265230a3fa84b9f3a7851d8c9ebe3d">GPIOSetValue</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOSetValue(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN Value)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a818408e823499cbc8bf3a09f74062a48"><div class="ttname"><a href="kmdf__gpio_8h.html#a818408e823499cbc8bf3a09f74062a48">GPIOEnableInterrupt</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOEnableInterrupt(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ GPIO_INTERRUPT_TYPE InterruptType, _In_ GPIO_INTERRUPT_CALLBACK Callback, _In_opt_ PVOID Context)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a8d011afe85a8ab79a1f0ab8a0aeeee9f"><div class="ttname"><a href="kmdf__gpio_8h.html#a8d011afe85a8ab79a1f0ab8a0aeeee9f">_GPIO_PIN_CONFIG::DebounceTime</a></div><div class="ttdeci">ULONG DebounceTime</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:40</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a90e508a8bbe068b0558a8fbabf471070"><div class="ttname"><a href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070">_GPIO_PIN_POLARITY</a></div><div class="ttdeci">_GPIO_PIN_POLARITY</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:20</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d"><div class="ttname"><a href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a8513c8aa5e5b9486129979a5469b3a1d">GpioPolarityActiveHigh</a></div><div class="ttdeci">@ GpioPolarityActiveHigh</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:22</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78"><div class="ttname"><a href="kmdf__gpio_8h.html#a90e508a8bbe068b0558a8fbabf471070a85e90552b3c4bbda7e7313c1df022a78">GpioPolarityActiveLow</a></div><div class="ttdeci">@ GpioPolarityActiveLow</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:21</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a941806f1dcee3e7fc53c46d5b60b362b"><div class="ttname"><a href="kmdf__gpio_8h.html#a941806f1dcee3e7fc53c46d5b60b362b">GPIO_INTERRUPT_TYPE</a></div><div class="ttdeci">enum _GPIO_INTERRUPT_TYPE GPIO_INTERRUPT_TYPE</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a96127f2b5d39ef12184a23f9ad42999b"><div class="ttname"><a href="kmdf__gpio_8h.html#a96127f2b5d39ef12184a23f9ad42999b">GPIO_PIN_POLARITY</a></div><div class="ttdeci">enum _GPIO_PIN_POLARITY GPIO_PIN_POLARITY</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_a9643691e5b435f14c69e6016c2fae45a"><div class="ttname"><a href="kmdf__gpio_8h.html#a9643691e5b435f14c69e6016c2fae45a">GPIO_INTERRUPT_CALLBACK</a></div><div class="ttdeci">VOID(* GPIO_INTERRUPT_CALLBACK)(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ BOOLEAN PinValue, _In_opt_ PVOID Context)</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:51</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_aa14439653449111e1deb51cf90c5b7a0"><div class="ttname"><a href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></div><div class="ttdeci">struct _GPIO_PIN_CONFIG * PGPIO_PIN_CONFIG</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_aa3d8c873130efce02f77b07f742a05ff"><div class="ttname"><a href="kmdf__gpio_8h.html#aa3d8c873130efce02f77b07f742a05ff">_GPIO_PIN_CONFIG::Direction</a></div><div class="ttdeci">GPIO_PIN_DIRECTION Direction</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:37</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_aab159dfcef06f528d7ebdb9fa3ce8be4"><div class="ttname"><a href="kmdf__gpio_8h.html#aab159dfcef06f528d7ebdb9fa3ce8be4">GPIOSetDirection</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOSetDirection(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ GPIO_PIN_DIRECTION Direction)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_ab852084eda7787e469301a172c7498a5"><div class="ttname"><a href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a></div><div class="ttdeci">struct _GPIO_PIN_CONFIG GPIO_PIN_CONFIG</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_abd8ea283a387003ee58595a12fb2b5ec"><div class="ttname"><a href="kmdf__gpio_8h.html#abd8ea283a387003ee58595a12fb2b5ec">_GPIO_PIN_CONFIG::OpenDrain</a></div><div class="ttdeci">BOOLEAN OpenDrain</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:43</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_acc85fb800144aa8336d910544113014c"><div class="ttname"><a href="kmdf__gpio_8h.html#acc85fb800144aa8336d910544113014c">_GPIO_PIN_CONFIG::PinNumber</a></div><div class="ttdeci">ULONG PinNumber</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:36</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_adcddf2e62a93fe5cc3fa7f46b67845bb"><div class="ttname"><a href="kmdf__gpio_8h.html#adcddf2e62a93fe5cc3fa7f46b67845bb">GPIOGetValue</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOGetValue(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _Out_ PBOOLEAN Value)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_ade1cb652014fc4a3984567ff49900d81"><div class="ttname"><a href="kmdf__gpio_8h.html#ade1cb652014fc4a3984567ff49900d81">GPIOPulse</a></div><div class="ttdeci">WDFAPI NTSTATUS GPIOPulse(_In_ WDFDEVICE Device, _In_ ULONG PinNumber, _In_ ULONG PulseDurationMs)</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_aec4597ae991a215e755336600b15e60a"><div class="ttname"><a href="kmdf__gpio_8h.html#aec4597ae991a215e755336600b15e60a">_GPIO_PIN_CONFIG::SpbDeviceObject</a></div><div class="ttdeci">PVOID SpbDeviceObject</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:44</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_afd66b730c3f6bec3b15b28ccd0832f85"><div class="ttname"><a href="kmdf__gpio_8h.html#afd66b730c3f6bec3b15b28ccd0832f85">_GPIO_PIN_CONFIG::PullDown</a></div><div class="ttdeci">BOOLEAN PullDown</div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:42</div></div>
<div class="ttc" id="akmdf__gpio_8h_html_struct__GPIO__PIN__CONFIG"><div class="ttname"><a href="kmdf__gpio_8h.html#struct__GPIO__PIN__CONFIG">_GPIO_PIN_CONFIG</a></div><div class="ttdef"><b>Definition</b> kmdf_gpio.h:35</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__gpio_8h.html">kmdf_gpio.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
