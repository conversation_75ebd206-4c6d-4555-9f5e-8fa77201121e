<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_i2c.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__i2c_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_i2c.h File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="kmdf__bus__common_8h_source.html">kmdf_bus_common.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for kmdf_i2c.h:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8h" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8h">
<area shape="rect" title=" " alt="" coords="5,5,174,48"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="16,96,163,123"/>
<area shape="poly" title=" " alt="" coords="92,49,92,80,87,80,87,49"/>
<area shape="rect" title=" " alt="" coords="6,179,59,205"/>
<area shape="poly" title=" " alt="" coords="83,125,52,168,48,165,78,122"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="83,171,213,213"/>
<area shape="poly" title=" " alt="" coords="101,122,127,157,122,160,97,125"/>
<area shape="rect" title=" " alt="" coords="116,261,180,288"/>
<area shape="poly" title=" " alt="" coords="151,214,151,245,146,245,146,214"/>
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h__dep__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8hdep" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8hdep" id="aC_1_2KMDF_01Driver1_2include_2hal_2bus_2kmdf__i2c_8hdep">
<area shape="rect" title=" " alt="" coords="1035,5,1204,48"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="780,96,949,139"/>
<area shape="poly" title=" " alt="" coords="1046,56,925,98,924,93,1044,51"/>
<area shape="rect" href="driver__main_8c.html" title=" " alt="" coords="1021,285,1167,328"/>
<area shape="poly" title=" " alt="" coords="1119,64,1099,285,1093,285,1113,63"/>
<area shape="rect" href="i2c__core_8c.html" title=" " alt="" coords="1191,285,1336,328"/>
<area shape="poly" title=" " alt="" coords="1137,61,1172,137,1254,284,1249,286,1168,140,1132,64"/>
<area shape="rect" href="i2c__device_8c.html" title=" " alt="" coords="1182,96,1350,139"/>
<area shape="poly" title=" " alt="" coords="1168,54,1233,93,1230,98,1165,59"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="683,187,828,229"/>
<area shape="poly" title=" " alt="" coords="829,151,783,188,779,184,826,146"/>
<area shape="rect" href="device__manager_8c.html" title=" " alt="" coords="5,277,151,336"/>
<area shape="poly" title=" " alt="" coords="667,216,437,236,300,255,163,280,152,283,150,277,161,275,299,249,437,231,667,211"/>
<area shape="rect" href="driver__entry_8c.html" title=" " alt="" coords="175,277,320,336"/>
<area shape="poly" title=" " alt="" coords="668,222,510,244,332,280,321,283,320,278,331,275,509,239,667,217"/>
<area shape="rect" href="driver__log_8c.html" title=" " alt="" coords="344,285,489,328"/>
<area shape="poly" title=" " alt="" coords="668,234,501,280,478,287,476,282,500,275,666,229"/>
<area shape="poly" title=" " alt="" coords="844,229,1011,275,1034,282,1033,287,1009,280,843,234"/>
<area shape="rect" href="gpio__core_8c.html" title=" " alt="" coords="513,285,659,328"/>
<area shape="poly" title=" " alt="" coords="707,240,624,287,621,282,705,235"/>
<area shape="poly" title=" " alt="" coords="844,217,1001,239,1180,275,1207,282,1205,287,1179,280,1000,244,843,222"/>
<area shape="rect" href="spi__core_8c.html" title=" " alt="" coords="683,285,828,328"/>
<area shape="poly" title=" " alt="" coords="758,245,758,285,753,285,753,245"/>
<area shape="rect" href="precomp_8c.html" title=" " alt="" coords="852,285,997,328"/>
<area shape="poly" title=" " alt="" coords="806,235,890,282,887,287,803,240"/>
</map>
</div>
</div>
<p><a href="kmdf__i2c_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FI2C_5FCONFIG_5Fstruct_5F_5FI2C_5F_5FCONFIG" id="r__5FI2C_5FCONFIG_5Fstruct_5F_5FI2C_5F_5FCONFIG"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__I2C__CONFIG">_I2C_CONFIG</a></td></tr>
<tr class="memitem:_5FI2C_5FTRANSFER_5FPACKET_5Fstruct_5F_5FI2C_5F_5FTRANSFER_5F_5FPACKET" id="r__5FI2C_5FTRANSFER_5FPACKET_5Fstruct_5F_5FI2C_5F_5FTRANSFER_5F_5FPACKET"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-typedef-members" class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a519fae2d9daaac809af65134907b2fb0" id="r_a519fae2d9daaac809af65134907b2fb0"><td class="memItemLeft" align="right" valign="top">typedef USHORT&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td></tr>
<tr class="memitem:a8275fd1e76bc02628ddb4cf647c947c4" id="r_a8275fd1e76bc02628ddb4cf647c947c4"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__CONFIG">_I2C_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a></td></tr>
<tr class="memitem:a941c9f88004c4f54719bc4a3b7083fff" id="r_a941c9f88004c4f54719bc4a3b7083fff"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a></td></tr>
<tr class="memitem:a70b5b0e5b59f4301d02402a14c4ecb0b" id="r_a70b5b0e5b59f4301d02402a14c4ecb0b"><td class="memItemLeft" align="right" valign="top">typedef enum <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a></td></tr>
<tr class="memitem:af11040ef31cae611dac879352c4fab17" id="r_af11040ef31cae611dac879352c4fab17"><td class="memItemLeft" align="right" valign="top">typedef USHORT *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a></td></tr>
<tr class="memitem:a9d4df46fafece7b304c57d2e0e1bfd51" id="r_a9d4df46fafece7b304c57d2e0e1bfd51"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__CONFIG">_I2C_CONFIG</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td></tr>
<tr class="memitem:a26d8a1f8a56e4808ad0856f1dc02461c" id="r_a26d8a1f8a56e4808ad0856f1dc02461c"><td class="memItemLeft" align="right" valign="top">typedef struct <a class="el" href="#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-enum-members" class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a77f19b8dc0e1c39c18d00d90e211afb3" id="r_a77f19b8dc0e1c39c18d00d90e211afb3"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a> { <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630">I2CWrite</a>
, <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e">I2CRead</a>
, <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7">I2CWriteRead</a>
 }</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a5467da0184a8f514f9ff43ab28f7d2d0" id="r_a5467da0184a8f514f9ff43ab28f7d2d0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5467da0184a8f514f9ff43ab28f7d2d0">I2CInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="el" href="i2c__device_8c.html#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>)</td></tr>
<tr class="memitem:aad5c9145daea9c25554b814bfed47756" id="r_aad5c9145daea9c25554b814bfed47756"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aad5c9145daea9c25554b814bfed47756">I2CReadRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:a1b937c9865418ca9d50b16766c8ceb66" id="r_a1b937c9865418ca9d50b16766c8ceb66"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1b937c9865418ca9d50b16766c8ceb66">I2CScanBus</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <a class="el" href="#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a> DeviceAddresses, _In_ ULONG MaxDeviceAddresses, _Out_ PULONG DeviceCount)</td></tr>
<tr class="memitem:a363c4a8b2ee1e16d8a6aaf35b0e67722" id="r_a363c4a8b2ee1e16d8a6aaf35b0e67722"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a363c4a8b2ee1e16d8a6aaf35b0e67722">I2CTransferAsynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket, _In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback, _In_opt_ PVOID Context)</td></tr>
<tr class="memitem:ae74bb3af98d6a79ba5774f9c3a480ca7" id="r_ae74bb3af98d6a79ba5774f9c3a480ca7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae74bb3af98d6a79ba5774f9c3a480ca7">I2CTransferSynchronous</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Inout_ <a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> TransferPacket, _In_ ULONG TimeoutMs)</td></tr>
<tr class="memitem:aa8f6531c5b52bc6d04ca38fbaab3c223" id="r_aa8f6531c5b52bc6d04ca38fbaab3c223"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa8f6531c5b52bc6d04ca38fbaab3c223">I2CUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:aa4838a1894b94b950fc4a7e73624d7ed" id="r_aa4838a1894b94b950fc4a7e73624d7ed"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa4838a1894b94b950fc4a7e73624d7ed">I2CWriteRegister</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a> SlaveAddress, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</td></tr>
</table>
<hr/><h2 id="header-inline_5Fclasses" class="groupheader">Class Documentation</h2>
<a name="struct__I2C__CONFIG" id="struct__I2C__CONFIG"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__I2C__CONFIG">&#9670;&#160;</a></span>_I2C_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _I2C_CONFIG</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="ae6dda89972493740920ba937c8d27897" name="ae6dda89972493740920ba937c8d27897"></a>ULONG</td>
<td class="fieldname">
ClockFrequency</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a4f627b1a2607231e3dcb321f5d728c22" name="a4f627b1a2607231e3dcb321f5d728c22"></a>BOOLEAN</td>
<td class="fieldname">
Is10BitAddress</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a2b8ec7e6589589f70cb5d4bbb55db565" name="a2b8ec7e6589589f70cb5d4bbb55db565"></a>ULONG</td>
<td class="fieldname">
RetryCount</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a9942dbdaebaf13e2e51cef985bcc4d0d" name="a9942dbdaebaf13e2e51cef985bcc4d0d"></a>ULONG</td>
<td class="fieldname">
TimeoutMs</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="struct__I2C__TRANSFER__PACKET" id="struct__I2C__TRANSFER__PACKET"></a>
<h2 class="memtitle"><span class="permalink"><a href="#struct__I2C__TRANSFER__PACKET">&#9670;&#160;</a></span>_I2C_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct _I2C_TRANSFER_PACKET</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"></div><div class="dynheader">
Collaboration diagram for _I2C_TRANSFER_PACKET:</div>
<div class="dyncontent">
<div class="center"><img src="struct__I2C__TRANSFER__PACKET__coll__graph.png" border="0" usemap="#a__I2C__TRANSFER__PACKET_coll__map" loading="lazy" alt="Collaboration graph"/></div>
<map name="a__I2C__TRANSFER__PACKET_coll__map" id="a__I2C__TRANSFER__PACKET_coll__map">
<area shape="rect" title=" " alt="" coords="9,104,198,131"/>
<area shape="rect" href="kmdf__bus__common_8h.html#struct__BUS__TRANSFER__PACKET" title=" " alt="" coords="5,5,202,32"/>
<area shape="poly" title=" " alt="" coords="106,48,106,104,101,104,101,48"/>
</map>
<center><span class="legend">[<a target="top" href="graph_legend.html">legend</a>]</span></center></div>
<table class="fieldtable">
<tr><th colspan="3">Class Members</th></tr>
<tr><td class="fieldtype">
<a id="a20dd97830e1adfd27b9d7e46204415db" name="a20dd97830e1adfd27b9d7e46204415db"></a><a class="el" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></td>
<td class="fieldname">
Common</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a1e07ec690f00c7a71deca7d96b3a97ad" name="a1e07ec690f00c7a71deca7d96b3a97ad"></a>PVOID</td>
<td class="fieldname">
ReadBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="aa2edc457f7179d999a40b1fe065c3532" name="aa2edc457f7179d999a40b1fe065c3532"></a>SIZE_T</td>
<td class="fieldname">
ReadBufferLength</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a75e9952dba35a8ba1937901272c7f340" name="a75e9952dba35a8ba1937901272c7f340"></a><a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>
<td class="fieldname">
SlaveAddress</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="a6239118143a5327bfd92d6086107e101" name="a6239118143a5327bfd92d6086107e101"></a><a class="el" href="#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a></td>
<td class="fieldname">
Type</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="ab68810a9e3adeed2d53d5858fdd9cb3e" name="ab68810a9e3adeed2d53d5858fdd9cb3e"></a>PVOID</td>
<td class="fieldname">
WriteBuffer</td>
<td class="fielddoc">
</td></tr>
<tr><td class="fieldtype">
<a id="afaaced0212ee18a776559ff7045b7aa4" name="afaaced0212ee18a776559ff7045b7aa4"></a>SIZE_T</td>
<td class="fieldname">
WriteBufferLength</td>
<td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<a name="doc-typedef-members" id="doc-typedef-members"></a><h2 id="header-doc-typedef-members" class="groupheader">Typedef Documentation</h2>
<a id="a519fae2d9daaac809af65134907b2fb0" name="a519fae2d9daaac809af65134907b2fb0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a519fae2d9daaac809af65134907b2fb0">&#9670;&#160;</a></span>I2C_ADDRESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef USHORT <a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a8275fd1e76bc02628ddb4cf647c947c4" name="a8275fd1e76bc02628ddb4cf647c947c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8275fd1e76bc02628ddb4cf647c947c4">&#9670;&#160;</a></span>I2C_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__CONFIG">_I2C_CONFIG</a> <a class="el" href="#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a941c9f88004c4f54719bc4a3b7083fff" name="a941c9f88004c4f54719bc4a3b7083fff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a941c9f88004c4f54719bc4a3b7083fff">&#9670;&#160;</a></span>I2C_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a> <a class="el" href="#a941c9f88004c4f54719bc4a3b7083fff">I2C_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a70b5b0e5b59f4301d02402a14c4ecb0b" name="a70b5b0e5b59f4301d02402a14c4ecb0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a70b5b0e5b59f4301d02402a14c4ecb0b">&#9670;&#160;</a></span>I2C_TRANSFER_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef enum <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a> <a class="el" href="#a70b5b0e5b59f4301d02402a14c4ecb0b">I2C_TRANSFER_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af11040ef31cae611dac879352c4fab17" name="af11040ef31cae611dac879352c4fab17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af11040ef31cae611dac879352c4fab17">&#9670;&#160;</a></span>PI2C_ADDRESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef USHORT * <a class="el" href="#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9d4df46fafece7b304c57d2e0e1bfd51" name="a9d4df46fafece7b304c57d2e0e1bfd51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d4df46fafece7b304c57d2e0e1bfd51">&#9670;&#160;</a></span>PI2C_CONFIG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__CONFIG">_I2C_CONFIG</a> * <a class="el" href="#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a26d8a1f8a56e4808ad0856f1dc02461c" name="a26d8a1f8a56e4808ad0856f1dc02461c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26d8a1f8a56e4808ad0856f1dc02461c">&#9670;&#160;</a></span>PI2C_TRANSFER_PACKET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct <a class="el" href="#struct__I2C__TRANSFER__PACKET">_I2C_TRANSFER_PACKET</a> * <a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-enum-members" id="doc-enum-members"></a><h2 id="header-doc-enum-members" class="groupheader">Enumeration Type Documentation</h2>
<a id="a77f19b8dc0e1c39c18d00d90e211afb3" name="a77f19b8dc0e1c39c18d00d90e211afb3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77f19b8dc0e1c39c18d00d90e211afb3">&#9670;&#160;</a></span>_I2C_TRANSFER_TYPE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a77f19b8dc0e1c39c18d00d90e211afb3">_I2C_TRANSFER_TYPE</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630" name="a77f19b8dc0e1c39c18d00d90e211afb3a220ffb3b87720c58618a375ced342630"></a>I2CWrite&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e" name="a77f19b8dc0e1c39c18d00d90e211afb3afd6001ae9f16dd5782ea8a1641fca47e"></a>I2CRead&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7" name="a77f19b8dc0e1c39c18d00d90e211afb3a0447720972f80cf0df6377ac4624e3e7"></a>I2CWriteRead&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a5467da0184a8f514f9ff43ab28f7d2d0" name="a5467da0184a8f514f9ff43ab28f7d2d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5467da0184a8f514f9ff43ab28f7d2d0">&#9670;&#160;</a></span>I2CInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>I2cConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_cgraph.png" border="0" usemap="#akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_cgraph" id="akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,96,56"/>
<area shape="poly" title=" " alt="" coords="24,30,20,21,23,11,33,5,51,3,69,5,79,12,76,16,67,10,50,8,35,10,27,14,25,20,29,28"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="308,29,413,56"/>
<area shape="poly" title=" " alt="" coords="96,40,292,40,292,45,96,45"/>
<area shape="rect" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0" title=" " alt="" coords="144,55,260,81"/>
<area shape="poly" title=" " alt="" coords="96,48,129,53,128,58,96,53"/>
<area shape="poly" title=" " alt="" coords="333,30,328,21,332,11,343,5,361,3,380,5,390,12,387,16,378,10,361,8,344,10,336,15,334,20,338,28"/>
<area shape="poly" title=" " alt="" coords="260,56,292,51,293,56,261,61"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_icgraph.png" border="0" usemap="#akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_icgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_icgraph" id="akmdf__i2c_8h_a5467da0184a8f514f9ff43ab28f7d2d0_icgraph">
<area shape="rect" title=" " alt="" coords="5,29,96,56"/>
<area shape="poly" title=" " alt="" coords="64,15,59,10,50,8,42,10,37,14,36,20,39,28,34,30,31,20,33,12,40,5,51,3,62,5,68,12"/>
</map>
</div>

</div>
</div>
<a id="aad5c9145daea9c25554b814bfed47756" name="aad5c9145daea9c25554b814bfed47756"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad5c9145daea9c25554b814bfed47756">&#9670;&#160;</a></span>I2CReadRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CReadRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>SlaveAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PUCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_aad5c9145daea9c25554b814bfed47756_cgraph.png" border="0" usemap="#akmdf__i2c_8h_aad5c9145daea9c25554b814bfed47756_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_aad5c9145daea9c25554b814bfed47756_cgraph" id="akmdf__i2c_8h_aad5c9145daea9c25554b814bfed47756_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,128,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="176,55,342,81"/>
<area shape="poly" title=" " alt="" coords="128,65,160,65,160,71,128,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="411,29,516,56"/>
<area shape="poly" title=" " alt="" coords="342,55,395,48,396,54,343,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="390,80,537,107"/>
<area shape="poly" title=" " alt="" coords="343,76,375,80,375,85,342,81"/>
<area shape="poly" title=" " alt="" coords="435,30,430,21,434,11,445,5,464,3,483,5,494,12,491,16,482,10,464,8,447,10,438,15,436,20,440,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="585,80,670,107"/>
<area shape="poly" title=" " alt="" coords="538,91,570,91,570,96,538,96"/>
<area shape="poly" title=" " alt="" coords="599,81,594,71,598,62,609,56,628,53,647,56,658,62,655,67,646,61,627,59,611,61,602,65,599,71,604,78"/>
</map>
</div>

</div>
</div>
<a id="a1b937c9865418ca9d50b16766c8ceb66" name="a1b937c9865418ca9d50b16766c8ceb66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1b937c9865418ca9d50b16766c8ceb66">&#9670;&#160;</a></span>I2CScanBus()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CScanBus </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_to_(MaxDeviceAddresses, *DeviceCount) <a class="el" href="#af11040ef31cae611dac879352c4fab17">PI2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>DeviceAddresses</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>MaxDeviceAddresses</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ PULONG</td>          <td class="paramname"><span class="paramname"><em>DeviceCount</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_a1b937c9865418ca9d50b16766c8ceb66_cgraph.png" border="0" usemap="#akmdf__i2c_8h_a1b937c9865418ca9d50b16766c8ceb66_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_a1b937c9865418ca9d50b16766c8ceb66_cgraph" id="akmdf__i2c_8h_a1b937c9865418ca9d50b16766c8ceb66_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,101,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="384,29,489,56"/>
<area shape="poly" title=" " alt="" coords="101,62,368,44,368,50,101,68"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="149,80,315,107"/>
<area shape="poly" title=" " alt="" coords="101,72,133,77,133,82,101,77"/>
<area shape="poly" title=" " alt="" coords="408,30,403,21,407,11,418,5,437,3,456,5,467,12,464,16,455,10,437,8,420,10,411,15,409,20,413,28"/>
<area shape="poly" title=" " alt="" coords="288,77,368,57,369,62,290,82"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="363,80,510,107"/>
<area shape="poly" title=" " alt="" coords="315,91,348,91,348,96,315,96"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="558,80,643,107"/>
<area shape="poly" title=" " alt="" coords="511,91,543,91,543,96,511,96"/>
<area shape="poly" title=" " alt="" coords="572,81,567,71,571,62,582,56,601,53,620,56,631,62,628,67,619,61,600,59,584,61,575,65,572,71,577,78"/>
</map>
</div>

</div>
</div>
<a id="a363c4a8b2ee1e16d8a6aaf35b0e67722" name="a363c4a8b2ee1e16d8a6aaf35b0e67722"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a363c4a8b2ee1e16d8a6aaf35b0e67722">&#9670;&#160;</a></span>I2CTransferAsynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CTransferAsynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></td>          <td class="paramname"><span class="paramname"><em>CompletionCallback</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_opt_ PVOID</td>          <td class="paramname"><span class="paramname"><em>Context</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_a363c4a8b2ee1e16d8a6aaf35b0e67722_cgraph.png" border="0" usemap="#akmdf__i2c_8h_a363c4a8b2ee1e16d8a6aaf35b0e67722_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_a363c4a8b2ee1e16d8a6aaf35b0e67722_cgraph" id="akmdf__i2c_8h_a363c4a8b2ee1e16d8a6aaf35b0e67722_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,179,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="227,55,393,81"/>
<area shape="poly" title=" " alt="" coords="179,65,211,65,211,71,179,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="462,29,567,56"/>
<area shape="poly" title=" " alt="" coords="393,55,446,48,447,54,394,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="441,80,588,107"/>
<area shape="poly" title=" " alt="" coords="394,76,426,80,426,85,393,81"/>
<area shape="poly" title=" " alt="" coords="486,30,481,21,485,11,496,5,515,3,534,5,545,12,542,16,533,10,515,8,498,10,489,15,487,20,491,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="636,80,721,107"/>
<area shape="poly" title=" " alt="" coords="589,91,621,91,621,96,589,96"/>
<area shape="poly" title=" " alt="" coords="650,81,645,71,649,62,660,56,679,53,698,56,709,62,706,67,697,61,678,59,662,61,653,65,650,71,655,78"/>
</map>
</div>

</div>
</div>
<a id="ae74bb3af98d6a79ba5774f9c3a480ca7" name="ae74bb3af98d6a79ba5774f9c3a480ca7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae74bb3af98d6a79ba5774f9c3a480ca7">&#9670;&#160;</a></span>I2CTransferSynchronous()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CTransferSynchronous </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Inout_ <a class="el" href="#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>TransferPacket</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_cgraph.png" border="0" usemap="#akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_cgraph" id="akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,172,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="241,29,346,56"/>
<area shape="poly" title=" " alt="" coords="172,55,225,48,225,54,172,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="220,80,367,107"/>
<area shape="poly" title=" " alt="" coords="172,76,205,80,204,85,172,81"/>
<area shape="poly" title=" " alt="" coords="265,30,260,21,263,11,275,5,293,3,313,5,324,12,321,16,311,10,293,8,276,10,267,15,265,20,270,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="415,80,499,107"/>
<area shape="poly" title=" " alt="" coords="367,91,399,91,399,96,367,96"/>
<area shape="poly" title=" " alt="" coords="429,81,424,71,427,62,439,56,457,53,477,56,488,62,485,67,475,61,457,59,440,61,431,65,429,71,433,78"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_icgraph.png" border="0" usemap="#akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_icgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_icgraph" id="akmdf__i2c_8h_ae74bb3af98d6a79ba5774f9c3a480ca7_icgraph">
<area shape="rect" title=" " alt="" coords="227,81,393,108"/>
<area shape="rect" href="i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe" title=" " alt="" coords="31,5,153,32"/>
<area shape="poly" title=" " alt="" coords="260,78,178,47,142,35,144,30,180,41,262,73"/>
<area shape="rect" href="i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b" title=" " alt="" coords="44,56,140,83"/>
<area shape="poly" title=" " alt="" coords="211,86,140,77,140,72,212,81"/>
<area shape="rect" href="i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4" title=" " alt="" coords="5,107,179,133"/>
<area shape="poly" title=" " alt="" coords="212,109,179,113,179,107,211,103"/>
<area shape="rect" href="i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb" title=" " alt="" coords="31,157,153,184"/>
<area shape="poly" title=" " alt="" coords="262,117,180,148,144,159,142,154,178,143,260,112"/>
</map>
</div>

</div>
</div>
<a id="aa8f6531c5b52bc6d04ca38fbaab3c223" name="aa8f6531c5b52bc6d04ca38fbaab3c223"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8f6531c5b52bc6d04ca38fbaab3c223">&#9670;&#160;</a></span>I2CUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID I2CUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_aa8f6531c5b52bc6d04ca38fbaab3c223_cgraph.png" border="0" usemap="#akmdf__i2c_8h_aa8f6531c5b52bc6d04ca38fbaab3c223_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_aa8f6531c5b52bc6d04ca38fbaab3c223_cgraph" id="akmdf__i2c_8h_aa8f6531c5b52bc6d04ca38fbaab3c223_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,112,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="160,29,265,56"/>
<area shape="poly" title=" " alt="" coords="112,40,144,40,144,45,112,45"/>
<area shape="poly" title=" " alt="" coords="186,30,181,21,184,11,195,5,212,3,231,5,241,12,238,16,229,10,212,8,197,10,188,14,186,20,190,28"/>
</map>
</div>

</div>
</div>
<a id="aa4838a1894b94b950fc4a7e73624d7ed" name="aa4838a1894b94b950fc4a7e73624d7ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4838a1894b94b950fc4a7e73624d7ed">&#9670;&#160;</a></span>I2CWriteRegister()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS I2CWriteRegister </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="#a519fae2d9daaac809af65134907b2fb0">I2C_ADDRESS</a></td>          <td class="paramname"><span class="paramname"><em>SlaveAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>Value</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TimeoutMs</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="kmdf__i2c_8h_aa4838a1894b94b950fc4a7e73624d7ed_cgraph.png" border="0" usemap="#akmdf__i2c_8h_aa4838a1894b94b950fc4a7e73624d7ed_cgraph" loading="lazy" alt=""/></div>
<map name="akmdf__i2c_8h_aa4838a1894b94b950fc4a7e73624d7ed_cgraph" id="akmdf__i2c_8h_aa4838a1894b94b950fc4a7e73624d7ed_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,127,81"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="175,55,341,81"/>
<area shape="poly" title=" " alt="" coords="127,65,159,65,159,71,127,71"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="410,29,515,56"/>
<area shape="poly" title=" " alt="" coords="341,55,394,48,395,54,342,60"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="389,80,536,107"/>
<area shape="poly" title=" " alt="" coords="342,76,374,80,374,85,341,81"/>
<area shape="poly" title=" " alt="" coords="434,30,429,21,433,11,444,5,463,3,482,5,493,12,490,16,481,10,463,8,446,10,437,15,435,20,439,28"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="584,80,669,107"/>
<area shape="poly" title=" " alt="" coords="537,91,569,91,569,96,537,96"/>
<area shape="poly" title=" " alt="" coords="598,81,593,71,597,62,608,56,627,53,646,56,657,62,654,67,645,61,626,59,610,61,601,65,598,71,603,78"/>
</map>
</div>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__i2c_8h.html">kmdf_i2c.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
