# Copyright (c) 2012 - 2020, <PERSON>
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without modification,
# are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
#    list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
#    this list of conditions and the following disclaimer in the documentation
#    and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its contributors
#    may be used to endorse or promote products derived from this software without
#    specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
# ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
# ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# CHANGES:
#   2020-05-13, Lars Bilke: Add Code coverage support for Visual Studio 2019
#   2019-11-08, Lars Bilke: Add Code coverage support for Visual Studio 2019
#   2019-07-31, Lars Bilke: Add Code coverage support for Clang 8
#   2019-03-12, Lars Bilke: Add Code coverage support for Clang 7
#   2018-07-24, Lars Bilke: Add Code coverage support for Clang 6
#   2018-03-12, Lars Bilke: Add Code coverage support for Clang/LLVM 6.0.0+
#   2018-01-26, Lars Bilke: Add Code coverage support for Clang/LLVM 5.0.0+
#   2017-10-29, Lars Bilke: Add Code coverage support for Clang/LLVM 4.0.0+
#   2017-09-20, Lars Bilke: Add Code coverage support for Clang/LLVM 3.9.0+
#   2017-08-01, Lars Bilke: Add Code coverage support for Clang/LLVM 3.8.0+
#   2017-07-30, Lars Bilke: Add Code coverage support for Clang/LLVM 3.7.0+
#   2017-05-31, Lars Bilke: Add Code coverage support for Clang/LLVM 3.6.0+
#   2017-05-14, Lars Bilke: Add Code coverage support for Clang/LLVM 3.5.0+
#   2017-04-10, Lars Bilke: Add Code coverage support for Clang/LLVM 3.4.0+
#   2017-03-22, Lars Bilke: Add Code coverage support for Clang/LLVM 3.3.0+
#   2017-03-20, Lars Bilke: Add Code coverage support for Clang/LLVM 3.2.0+
#   2017-03-18, Lars Bilke: Add Code coverage support for Clang/LLVM 3.1.0+
#   2017-03-17, Lars Bilke: Add Code coverage support for Clang/LLVM 3.0.0+
#   2017-03-16, Lars Bilke: Add Code coverage support for Clang/LLVM 2.9.0+
#   2017-03-15, Lars Bilke: Add Code coverage support for Clang/LLVM 2.8.0+
#   2017-03-14, Lars Bilke: Add Code coverage support for Clang/LLVM 2.7.0+
#   2017-03-13, Lars Bilke: Add Code coverage support for Clang/LLVM 2.6.0+
#   2017-03-12, Lars Bilke: Add Code coverage support for Clang/LLVM 2.5.0+
#   2017-03-11, Lars Bilke: Add Code coverage support for Clang/LLVM 2.4.0+
#   2017-03-10, Lars Bilke: Add Code coverage support for Clang/LLVM 2.3.0+
#   2017-03-09, Lars Bilke: Add Code coverage support for Clang/LLVM 2.2.0+
#   2017-03-08, Lars Bilke: Add Code coverage support for Clang/LLVM 2.1.0+
#   2017-03-07, Lars Bilke: Add Code coverage support for Clang/LLVM 2.0.0+
#   2017-03-06, Lars Bilke: Add Code coverage support for Clang/LLVM 1.1.0+
#   2017-03-05, Lars Bilke: Add Code coverage support for Clang/LLVM 1.0.0+
#   2017-03-04, Lars Bilke: Add Code coverage support for Clang/LLVM 0.9.0+
#   2017-03-03, Lars Bilke: Add Code coverage support for Clang/LLVM 0.8.0+
#   2017-03-02, Lars Bilke: Add Code coverage support for Clang/LLVM 0.7.0+
#   2017-03-01, Lars Bilke: Add Code coverage support for Clang/LLVM 0.6.0+
#   2017-02-28, Lars Bilke: Add Code coverage support for Clang/LLVM 0.5.0+
#   2017-02-27, Lars Bilke: Add Code coverage support for Clang/LLVM 0.4.0+
#   2017-02-26, Lars Bilke: Add Code coverage support for Clang/LLVM 0.3.0+
#   2017-02-25, Lars Bilke: Add Code coverage support for Clang/LLVM 0.2.0+
#   2017-02-24, Lars Bilke: Add Code coverage support for Clang/LLVM 0.1.0+
#   2017-02-23, Lars Bilke: Add Code coverage support for Clang/LLVM 0.0.0+
#   2017-02-22, Lars Bilke: Initial version

# Check prereqs
find_program(GCOV_PATH gcov)
find_program(LCOV_PATH lcov)
find_program(GENHTML_PATH genhtml)
find_program(GCOVR_PATH gcovr PATHS ${CMAKE_SOURCE_DIR}/tests)

if(NOT GCOV_PATH)
    message(FATAL_ERROR "gcov not found! Aborting...")
endif()

if(NOT CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(WARNING "Code coverage results with an optimized (non-Debug) build may be misleading")
endif() # NOT CMAKE_BUILD_TYPE STREQUAL "Debug"

# Setup compiler options
add_compile_options(-fprofile-arcs -ftest-coverage)
add_link_options(-fprofile-arcs -ftest-coverage)

# Setup target
function(setup_target_for_coverage)
    set(options NONE)
    set(oneValueArgs NAME EXECUTABLE EXECUTABLE_ARGS WORKING_DIRECTORY)
    set(multiValueArgs DEPENDENCIES)
    cmake_parse_arguments(Coverage "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    if(NOT LCOV_PATH)
        message(FATAL_ERROR "lcov not found! Aborting...")
    endif()

    if(NOT GENHTML_PATH)
        message(FATAL_ERROR "genhtml not found! Aborting...")
    endif()

    # Setup target
    add_custom_target(${Coverage_NAME}
        # Cleanup lcov
        COMMAND ${LCOV_PATH} --directory . --zerocounters
        # Run tests
        COMMAND ${Coverage_EXECUTABLE} ${Coverage_EXECUTABLE_ARGS}
        # Capturing lcov counters and generating report
        COMMAND ${LCOV_PATH} --directory . --capture --output-file ${Coverage_NAME}.info
        COMMAND ${LCOV_PATH} --remove ${Coverage_NAME}.info '/usr/*' '*/tests/*' '*/test/*' '*/googletest/*' '*/gmock/*' --output-file ${Coverage_NAME}.info.cleaned
        COMMAND ${GENHTML_PATH} -o ${Coverage_NAME} ${Coverage_NAME}.info.cleaned
        COMMAND ${CMAKE_COMMAND} -E remove ${Coverage_NAME}.info ${Coverage_NAME}.info.cleaned
        WORKING_DIRECTORY ${Coverage_WORKING_DIRECTORY}
        DEPENDS ${Coverage_DEPENDENCIES}
        COMMENT "Resetting code coverage counters to zero.\nProcessing code coverage counters and generating report."
    )
endfunction() # setup_target_for_coverage
