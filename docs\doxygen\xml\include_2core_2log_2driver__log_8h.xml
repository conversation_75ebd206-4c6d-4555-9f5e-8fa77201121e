<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="include_2core_2log_2driver__log_8h" kind="file" language="C++">
    <compoundname>driver_log.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includedby refid="driver__core_8h" local="yes">C:/KMDF Driver1/include/core/driver/driver_core.h</includedby>
    <includedby refid="driver__entry_8h" local="yes">C:/KMDF Driver1/include/core/driver/driver_entry.h</includedby>
    <includedby refid="device__manager_8c" local="yes">C:/KMDF Driver1/src/core/device/device_manager.c</includedby>
    <includedby refid="driver__core_8c" local="yes">C:/KMDF Driver1/src/core/driver/driver_core.c</includedby>
    <includedby refid="error__handling_8c" local="yes">C:/KMDF Driver1/src/core/error/error_handling.c</includedby>
    <includedby refid="driver__main_8c" local="yes">C:/KMDF Driver1/src/driver_main.c</includedby>
    <includedby refid="gpio__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/gpio_core.c</includedby>
    <includedby refid="i2c__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/i2c_core.c</includedby>
    <includedby refid="spi__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/spi_core.c</includedby>
    <includedby refid="gpio__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/gpio_device.c</includedby>
    <includedby refid="i2c__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/i2c_device.c</includedby>
    <includedby refid="spi__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/spi_device.c</includedby>
    <includedby refid="precomp_8h" local="yes">C:/KMDF Driver1/src/precomp.h</includedby>
    <incdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="2">
        <label>C:/KMDF Driver1/include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>C:/KMDF Driver1/include/core/driver/driver_entry.h</label>
        <link refid="driver__entry_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
      </node>
      <node id="7">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/core/driver/driver_core.c</label>
        <link refid="driver__core_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/core/driver/driver_entry.c</label>
        <link refid="driver__entry_8c"/>
      </node>
      <node id="14">
        <label>C:/KMDF Driver1/src/core/error/error_handling.c</label>
        <link refid="error__handling_8c"/>
      </node>
      <node id="8">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="9">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="10">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="11">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="15">
        <label>C:/KMDF Driver1/src/hal/devices/gpio_device.c</label>
        <link refid="gpio__device_8c"/>
      </node>
      <node id="16">
        <label>C:/KMDF Driver1/src/hal/devices/i2c_device.c</label>
        <link refid="i2c__device_8c"/>
      </node>
      <node id="17">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
      </node>
      <node id="12">
        <label>C:/KMDF Driver1/src/precomp.c</label>
        <link refid="precomp_8c"/>
      </node>
      <node id="6">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__LOG__CONFIG" prot="public">_LOG_CONFIG</innerclass>
    <sectiondef kind="define">
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" prot="public" static="no">
        <name>FUNCTION_ENTRY</name>
        <param></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1a8b05bbaba9e1fe6f53057c15e4a53a81" kindref="member">LogFunctionEntry</ref>(__FUNCTION__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="97" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="97" bodyend="98"/>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1ae1622080dc9f8424bde67b829ee735c7" compoundref="i2c__core_8c" startline="111" endline="132">I2CUninitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" prot="public" static="no">
        <name>FUNCTION_EXIT</name>
        <param><defname>status</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1a2ebea8a6c7cbdde9ba74e856c73a2740" kindref="member">LogFunctionExit</ref>(__FUNCTION__, <ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="100" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="100" bodyend="101"/>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1ac03ee248114c6e0f051a792462609cb4" compoundref="i2c__core_8c" startline="250" endline="278">I2CTransferAsynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1abd0b0523397fb05f0ed46fc217fb630f" prot="public" static="no">
        <name>LOG_DEBUG</name>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kindref="member">LogLevelDebug</ref>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="77" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="77" bodyend="78"/>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1abffaf9cecb61026cac6db71a16ace9c5" prot="public" static="no">
        <name>LOG_ERROR</name>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kindref="member">LogLevelError</ref>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="65" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="65" bodyend="66"/>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a89681da4efde0b54dc7f2839665082c8" prot="public" static="no">
        <name>LOG_INFO</name>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="71" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="71" bodyend="72"/>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" prot="public" static="no">
        <name>LOG_VERBOSE</name>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" kindref="member">LogLevelVerbose</ref>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="74" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="74" bodyend="75"/>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a1c60134b1702d179d9b86bc618f416fe" prot="public" static="no">
        <name>LOG_WARNING</name>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref>, __FUNCTION__, __LINE__, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="68" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="68" bodyend="69"/>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" prot="public" static="no">
        <name>LogInfo</name>
        <param><defname>function</defname></param>
        <param><defname>line</defname></param>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref>, function, line, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="80" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="80" bodyend="81"/>
        <referencedby refid="driver__core_8c_1abc51f0e6ed5304a27afddf92da4720e6" compoundref="driver__core_8c" startline="113" endline="134">DriverCoreAddDevice</referencedby>
        <referencedby refid="driver__core_8c_1aac97f3e68a787ac88617369283c60b79" compoundref="driver__core_8c" startline="221" endline="249">DriverCoreCleanup</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" compoundref="driver__core_8c" startline="255" endline="343">DriverCoreProcessIoControl</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__entry_8c" startline="148" endline="157">EvtDriverUnload</referencedby>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
        <referencedby refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" compoundref="gpio__device_8c" startline="271" endline="286">GpioDeviceCleanup</referencedby>
        <referencedby refid="gpio__device_8c_1a0bd26e3410adfbb26bba326602f8fec6" compoundref="gpio__device_8c" startline="367" endline="385">GpioDeviceGetState</referencedby>
        <referencedby refid="gpio__device_8c_1a21428e126bf8c8996ea3405e6a8be2f2" compoundref="gpio__device_8c" startline="160" endline="177">GpioDeviceInitialize</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="gpio__device_8c_1a6448a7e48d735f67501f3273b75485fd" compoundref="gpio__device_8c" startline="391" endline="413">GpioDeviceRegisterCallback</referencedby>
        <referencedby refid="gpio__device_8c_1a731812dec996a670e7d557a282535d3d" compoundref="gpio__device_8c" startline="302" endline="340">GpioDeviceSetState</referencedby>
        <referencedby refid="gpio__device_8c_1afbd2bd91a99c594504ca275fd8b45825" compoundref="gpio__device_8c" startline="419" endline="439">GpioDeviceUnregisterCallback</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1ab343a956248ebb07de6a72eaeb55ec35" compoundref="gpio__core_8c" startline="58" endline="77">_GPIO_PIN_CONTEXT::GPIOInitialize</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" compoundref="i2c__device_8c" startline="157" endline="186">I2cDeviceCleanup</referencedby>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
        <referencedby refid="spi__device_8c_1a052b57a96b994325a574bcb9f3db837a" compoundref="spi__device_8c" startline="113" endline="142">SpiDeviceCleanup</referencedby>
        <referencedby refid="spi__device_8c_1a6939e12311ec72f975bcd03a4250a3e2" compoundref="spi__device_8c" startline="31" endline="45">SpiDeviceInitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
        <referencedby refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" compoundref="spi__core_8c" startline="138" endline="167">SPIUninitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" prot="public" static="no">
        <name>LogWarning</name>
        <param><defname>function</defname></param>
        <param><defname>line</defname></param>
        <param><defname>format</defname></param>
        <param><defname>...</defname></param>
        <initializer>    <ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(<ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref>, function, line, format, ##__VA_ARGS__)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="83" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="83" bodyend="84"/>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="driver__core_8c_1ae610e53c6df75743831a2e87ef9e746b" compoundref="driver__core_8c" startline="255" endline="343">DriverCoreProcessIoControl</referencedby>
        <referencedby refid="driver__core_8c_1a89627789114e389118ee51bda8684ab6" compoundref="driver__core_8c" startline="140" endline="177">DriverCoreRemoveDevice</referencedby>
        <referencedby refid="gpio__device_8c_1ac3d3347067d7be6497e71b6ba4b389c2" compoundref="gpio__device_8c" startline="271" endline="286">GpioDeviceCleanup</referencedby>
        <referencedby refid="gpio__core_8c_1a785f00e9c0879fb478077d2cdce99906" compoundref="gpio__core_8c" startline="225" endline="279">GPIOUninitialize</referencedby>
        <referencedby refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" compoundref="spi__core_8c" startline="138" endline="167">SPIUninitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="include_2core_2log_2driver__log_8h_1a3adf7dc8e9dcfe1da6e33aa8043a80c3" prot="public" static="no">
        <name>MAX_LOG_MESSAGE_LENGTH</name>
        <initializer>512</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="7" column="9" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="7" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" prot="public" static="no" strong="no">
        <type></type>
        <name>_LOG_LEVEL</name>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16" prot="public">
          <name>LogLevelDisabled</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" prot="public">
          <name>LogLevelError</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" prot="public">
          <name>LogLevelWarning</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" prot="public">
          <name>LogLevelInfo</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" prot="public">
          <name>LogLevelVerbose</name>
          <initializer>= 4</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" prot="public">
          <name>LogLevelDebug</name>
          <initializer>= 5</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="9" column="1" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="9" bodyend="16"/>
      </memberdef>
      <memberdef kind="enum" id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867" prot="public" static="no" strong="no">
        <type></type>
        <name>_LOG_TYPE</name>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" prot="public">
          <name>LogTypeKdPrint</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7" prot="public">
          <name>LogTypeWPP</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3" prot="public">
          <name>LogTypeETW</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d" prot="public">
          <name>LogTypeFile</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee" prot="public">
          <name>LogTypeAll</name>
          <initializer>= 0xFF</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="18" column="1" bodyfile="C:/KMDF Driver1/include/core/log/driver_log.h" bodystart="18" bodyend="24"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="include_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" prot="public" static="no">
        <type>struct <ref refid="struct__LOG__CONFIG" kindref="compound">_LOG_CONFIG</ref></type>
        <definition>typedef struct _LOG_CONFIG LOG_CONFIG</definition>
        <argsstring></argsstring>
        <name>LOG_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="33" column="12"/>
      </memberdef>
      <memberdef kind="typedef" id="include_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" prot="public" static="no">
        <type>enum <ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kindref="member">_LOG_LEVEL</ref></type>
        <definition>typedef enum _LOG_LEVEL LOG_LEVEL</definition>
        <argsstring></argsstring>
        <name>LOG_LEVEL</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="16" column="11"/>
      </memberdef>
      <memberdef kind="typedef" id="include_2core_2log_2driver__log_8h_1a00e4548dd1db35b54cbeb1ee0fe45f66" prot="public" static="no">
        <type>enum <ref refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867" kindref="member">_LOG_TYPE</ref></type>
        <definition>typedef enum _LOG_TYPE LOG_TYPE</definition>
        <argsstring></argsstring>
        <name>LOG_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="24" column="10"/>
      </memberdef>
      <memberdef kind="typedef" id="include_2core_2log_2driver__log_8h_1a1736725db1db6ce0d7662411d9fbf587" prot="public" static="no">
        <type>struct <ref refid="struct__LOG__CONFIG" kindref="compound">_LOG_CONFIG</ref> *</type>
        <definition>typedef struct _LOG_CONFIG * PLOG_CONFIG</definition>
        <argsstring></argsstring>
        <name>PLOG_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="33" column="25"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogCleanup</definition>
        <argsstring>(VOID)</argsstring>
        <name>LogCleanup</name>
        <param>
          <type>VOID</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="47" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="47" declcolumn="1"/>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
        <referencedby refid="driver__entry_8c_1a075700d7117ddde115f3bb0db54b619e" compoundref="driver__entry_8c" startline="148" endline="157">EvtDriverUnload</referencedby>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1ab4caba1729c833f0c7cce2e72c20e30a" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogConfigInit</definition>
        <argsstring>(PLOG_CONFIG LogConfig)</argsstring>
        <name>LogConfigInit</name>
        <param>
          <type><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref></type>
          <declname>LogConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="36" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="36" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1a8b05bbaba9e1fe6f53057c15e4a53a81" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogFunctionEntry</definition>
        <argsstring>(PCSTR FunctionName)</argsstring>
        <name>LogFunctionEntry</name>
        <param>
          <type>PCSTR</type>
          <declname>FunctionName</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="87" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="87" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1a2ebea8a6c7cbdde9ba74e856c73a2740" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogFunctionExit</definition>
        <argsstring>(PCSTR FunctionName, NTSTATUS Status)</argsstring>
        <name>LogFunctionExit</name>
        <param>
          <type>PCSTR</type>
          <declname>FunctionName</declname>
        </param>
        <param>
          <type>NTSTATUS</type>
          <declname>Status</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="92" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="92" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1ae2910293c9c672800cca68427812b7c9" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogInitialize</definition>
        <argsstring>(PDRIVER_OBJECT DriverObject, PLOG_CONFIG LogConfig)</argsstring>
        <name>LogInitialize</name>
        <param>
          <type>PDRIVER_OBJECT</type>
          <declname>DriverObject</declname>
        </param>
        <param>
          <type><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref></type>
          <declname>LogConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="41" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="41" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogMessage</definition>
        <argsstring>(LOG_LEVEL Level, PCSTR Function, ULONG Line, PCSTR Format,...)</argsstring>
        <name>LogMessage</name>
        <param>
          <type><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref></type>
          <declname>Level</declname>
        </param>
        <param>
          <type>PCSTR</type>
          <declname>Function</declname>
        </param>
        <param>
          <type>ULONG</type>
          <declname>Line</declname>
        </param>
        <param>
          <type>PCSTR</type>
          <declname>Format</declname>
        </param>
        <param>
          <type>...</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="57" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="57" declcolumn="1"/>
        <referencedby refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</referencedby>
      </memberdef>
      <memberdef kind="function" id="include_2core_2log_2driver__log_8h_1abac3042c22899daa9d6987d7f15e0185" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID LogSetLevel</definition>
        <argsstring>(LOG_LEVEL Level)</argsstring>
        <name>LogSetLevel</name>
        <param>
          <type><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref></type>
          <declname>Level</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/log/driver_log.h" line="52" column="1" declfile="C:/KMDF Driver1/include/core/log/driver_log.h" declline="52" declcolumn="1"/>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="preprocessor">#ifndef<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight></codeline>
<codeline lineno="7" refid="include_2core_2log_2driver__log_8h_1a3adf7dc8e9dcfe1da6e33aa8043a80c3" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>MAX_LOG_MESSAGE_LENGTH<sp/>512</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight></codeline>
<codeline lineno="9" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843f" kindref="member">_LOG_LEVEL</ref><sp/>{</highlight></codeline>
<codeline lineno="10" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa9d8a630c4849ad7e0789aafadcc37c16" kindref="member">LogLevelDisabled</ref><sp/>=<sp/>0,</highlight></codeline>
<codeline lineno="11" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa3eca4474ae828804e2feadd4c6dbdf9e" kindref="member">LogLevelError</ref><sp/>=<sp/>1,</highlight></codeline>
<codeline lineno="12" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fa2e7279c94cf16e16b4b3f989f8c3a180" kindref="member">LogLevelWarning</ref><sp/>=<sp/>2,</highlight></codeline>
<codeline lineno="13" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faf0d147e8f63ea72df7e05e647240114c" kindref="member">LogLevelInfo</ref><sp/>=<sp/>3,</highlight></codeline>
<codeline lineno="14" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843fac5a4aff6eabd1003662ee2a25f626d4b" kindref="member">LogLevelVerbose</ref><sp/>=<sp/>4,</highlight></codeline>
<codeline lineno="15" refid="include_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7898a2c3a87496daad04bfb45321843faeba7c1b39c0ba8e5e5bc04c27df81053" kindref="member">LogLevelDebug</ref><sp/>=<sp/>5</highlight></codeline>
<codeline lineno="16" refid="include_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" refkind="member"><highlight class="normal">}<sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref>;</highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867" kindref="member">_LOG_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="19" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ab10995a521ca73d1235d2bb739ae3543" kindref="member">LogTypeKdPrint</ref><sp/>=<sp/>0,</highlight></codeline>
<codeline lineno="20" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a50d85472055959d167ebb2f2af3b50c7" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a50d85472055959d167ebb2f2af3b50c7" kindref="member">LogTypeWPP</ref><sp/>=<sp/>1,</highlight></codeline>
<codeline lineno="21" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867aaae6b9860136f6b4a12f64f0fb0f1ca3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5aaae6b9860136f6b4a12f64f0fb0f1ca3" kindref="member">LogTypeETW</ref><sp/>=<sp/>2,</highlight></codeline>
<codeline lineno="22" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867a1f8523bcbcc08515d2ddcee9efd6170d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5a1f8523bcbcc08515d2ddcee9efd6170d" kindref="member">LogTypeFile</ref><sp/>=<sp/>3,</highlight></codeline>
<codeline lineno="23" refid="include_2core_2log_2driver__log_8h_1a050bae65361e276b294f785581894867ad80cca7728a8757ee3c396f1ccad21ee" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1a7f7a95369342d65f5886d79f0c1845e5ad80cca7728a8757ee3c396f1ccad21ee" kindref="member">LogTypeAll</ref><sp/>=<sp/>0xFF</highlight></codeline>
<codeline lineno="24" refid="include_2core_2log_2driver__log_8h_1a00e4548dd1db35b54cbeb1ee0fe45f66" refkind="member"><highlight class="normal">}<sp/><ref refid="include_2core_2log_2driver__log_8h_1a00e4548dd1db35b54cbeb1ee0fe45f66" kindref="member">LOG_TYPE</ref>;</highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__LOG__CONFIG" kindref="compound">_LOG_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="27" refid="struct__LOG__CONFIG_1ab67683f62a123026ec78fe405cdbb177" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/><ref refid="struct__LOG__CONFIG_1ab67683f62a123026ec78fe405cdbb177" kindref="member">LogLevel</ref>;</highlight></codeline>
<codeline lineno="28" refid="struct__LOG__CONFIG_1a4b99237eff4b17c1d4fbfa9697528f70" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a00e4548dd1db35b54cbeb1ee0fe45f66" kindref="member">LOG_TYPE</ref><sp/><ref refid="struct__LOG__CONFIG_1a4b99237eff4b17c1d4fbfa9697528f70" kindref="member">LogTypes</ref>;</highlight></codeline>
<codeline lineno="29" refid="struct__LOG__CONFIG_1a860b2d89bf1aff272da84d5029dcb0b1" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__LOG__CONFIG_1a860b2d89bf1aff272da84d5029dcb0b1" kindref="member">IncludeTimestamp</ref>;</highlight></codeline>
<codeline lineno="30" refid="struct__LOG__CONFIG_1ac82d96af8adf8a1862d02ac0be15939d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__LOG__CONFIG_1ac82d96af8adf8a1862d02ac0be15939d" kindref="member">IncludeComponentName</ref>;</highlight></codeline>
<codeline lineno="31" refid="struct__LOG__CONFIG_1a21ca685eeee730a0d8a179892b840d5f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PWSTR<sp/><ref refid="struct__LOG__CONFIG_1a21ca685eeee730a0d8a179892b840d5f" kindref="member">LogFilePath</ref>;</highlight></codeline>
<codeline lineno="32" refid="struct__LOG__CONFIG_1a7aa67c4af6a6176801a2b2af65ac3cbb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__LOG__CONFIG_1a7aa67c4af6a6176801a2b2af65ac3cbb" kindref="member">MaxLogFileSize</ref>;</highlight></codeline>
<codeline lineno="33" refid="include_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" refkind="member"><highlight class="normal">}<sp/><ref refid="src_2core_2log_2driver__log_8h_1a565ca8d986ea85865e5e0e69c0fccc9d" kindref="member">LOG_CONFIG</ref>,<sp/>*<ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref>;</highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="36" refid="include_2core_2log_2driver__log_8h_1ab4caba1729c833f0c7cce2e72c20e30a" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1ab4caba1729c833f0c7cce2e72c20e30a" kindref="member">LogConfigInit</ref>(</highlight></codeline>
<codeline lineno="37"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref><sp/>LogConfig</highlight></codeline>
<codeline lineno="38"><highlight class="normal">);</highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight></codeline>
<codeline lineno="40"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="41" refid="include_2core_2log_2driver__log_8h_1ae2910293c9c672800cca68427812b7c9" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1ae2910293c9c672800cca68427812b7c9" kindref="member">LogInitialize</ref>(</highlight></codeline>
<codeline lineno="42"><highlight class="normal"><sp/><sp/><sp/><sp/>PDRIVER_OBJECT<sp/>DriverObject,</highlight></codeline>
<codeline lineno="43"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1ab99d8d17b06b190b7fecbbadd3d6b7df" kindref="member">PLOG_CONFIG</ref><sp/>LogConfig</highlight></codeline>
<codeline lineno="44"><highlight class="normal">);</highlight></codeline>
<codeline lineno="45"><highlight class="normal"></highlight></codeline>
<codeline lineno="46"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="47" refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1a93b035f39214ba5782080d504ae3ebc7" kindref="member">LogCleanup</ref>(</highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/>VOID</highlight></codeline>
<codeline lineno="49"><highlight class="normal">);</highlight></codeline>
<codeline lineno="50"><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="52" refid="include_2core_2log_2driver__log_8h_1abac3042c22899daa9d6987d7f15e0185" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1abac3042c22899daa9d6987d7f15e0185" kindref="member">LogSetLevel</ref>(</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/>Level</highlight></codeline>
<codeline lineno="54"><highlight class="normal">);</highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="57" refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1ae483585a71d174709d7049cc4b4758e1" kindref="member">LogMessage</ref>(</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="src_2core_2log_2driver__log_8h_1aa90925833aff044f4ba03f43f8084bf7" kindref="member">LOG_LEVEL</ref><sp/>Level,</highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/>PCSTR<sp/>Function,</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/>Line,</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/>PCSTR<sp/>Format,</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>...</highlight></codeline>
<codeline lineno="63"><highlight class="normal">);</highlight></codeline>
<codeline lineno="64"><highlight class="normal"></highlight></codeline>
<codeline lineno="65" refid="include_2core_2log_2driver__log_8h_1abffaf9cecb61026cac6db71a16ace9c5" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_ERROR(format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="66"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelError,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="67"><highlight class="normal"></highlight></codeline>
<codeline lineno="68" refid="include_2core_2log_2driver__log_8h_1a1c60134b1702d179d9b86bc618f416fe" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_WARNING(format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="69"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelWarning,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="70"><highlight class="normal"></highlight></codeline>
<codeline lineno="71" refid="include_2core_2log_2driver__log_8h_1a89681da4efde0b54dc7f2839665082c8" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_INFO(format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="72"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelInfo,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight></codeline>
<codeline lineno="74" refid="include_2core_2log_2driver__log_8h_1a6594ece0df59e19da1473edfc079fd45" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_VERBOSE(format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="75"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelVerbose,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="76"><highlight class="normal"></highlight></codeline>
<codeline lineno="77" refid="include_2core_2log_2driver__log_8h_1abd0b0523397fb05f0ed46fc217fb630f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LOG_DEBUG(format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="78"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelDebug,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="79"><highlight class="normal"></highlight></codeline>
<codeline lineno="80" refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LogInfo(function,<sp/>line,<sp/>format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="81"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelInfo,<sp/>function,<sp/>line,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="82"><highlight class="normal"></highlight></codeline>
<codeline lineno="83" refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>LogWarning(function,<sp/>line,<sp/>format,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="84"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogMessage(LogLevelWarning,<sp/>function,<sp/>line,<sp/>format,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="85"><highlight class="normal"></highlight></codeline>
<codeline lineno="86"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="87" refid="include_2core_2log_2driver__log_8h_1a8b05bbaba9e1fe6f53057c15e4a53a81" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1a8b05bbaba9e1fe6f53057c15e4a53a81" kindref="member">LogFunctionEntry</ref>(</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>PCSTR<sp/>FunctionName</highlight></codeline>
<codeline lineno="89"><highlight class="normal">);</highlight></codeline>
<codeline lineno="90"><highlight class="normal"></highlight></codeline>
<codeline lineno="91"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="92" refid="include_2core_2log_2driver__log_8h_1a2ebea8a6c7cbdde9ba74e856c73a2740" refkind="member"><highlight class="normal"><ref refid="include_2core_2log_2driver__log_8h_1a2ebea8a6c7cbdde9ba74e856c73a2740" kindref="member">LogFunctionExit</ref>(</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/>PCSTR<sp/>FunctionName,</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/>Status</highlight></codeline>
<codeline lineno="95"><highlight class="normal">);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"></highlight></codeline>
<codeline lineno="97" refid="include_2core_2log_2driver__log_8h_1ac56df030fb93601c871fd894e289601a" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>FUNCTION_ENTRY()<sp/>\</highlight></codeline>
<codeline lineno="98"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogFunctionEntry(__FUNCTION__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight></codeline>
<codeline lineno="100" refid="include_2core_2log_2driver__log_8h_1a347d8e7da8a8e1d1cebfd45f4055a4a8" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>FUNCTION_EXIT(status)<sp/>\</highlight></codeline>
<codeline lineno="101"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>LogFunctionExit(__FUNCTION__,<sp/>status)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="102"><highlight class="normal"></highlight></codeline>
<codeline lineno="103"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>DRIVER_LOG_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/core/log/driver_log.h"/>
  </compounddef>
</doxygen>
