<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/devices/spi_device.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('spi__device_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">spi_device.h</div></div>
</div><!--header-->
<div class="contents">
<a href="spi__device_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * spi_device.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * SPIu8bbeu5907u9a71u52a8u63a5u53e3u5934u6587u4ef6</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * u63d0u4f9bSPIu8bbeu5907u64cdu4f5cu7684u7edfu4e00u63a5u53e3</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> */</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span> </div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#ifndef SPI_DEVICE_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#define SPI_DEVICE_H</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span> </div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#include &lt;ntddk.h&gt;</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="preprocessor">#include &lt;wdf.h&gt;</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="preprocessor">#include &quot;<a class="code" href="kmdf__spi_8h.html">../bus/kmdf_spi.h</a>&quot;</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span> </div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment">// u5e38u91cfu548cu7ed3u6784u5b9au4e49</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span> </div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment">// SPIu4f20u8f93u6807u5fd7</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a935c3756801c209968fb16a7be795396">   20</a></span><span class="preprocessor">#define SPI_TRANSFER_READ        0x00000001  </span><span class="comment">// u8bfbu4f20u8f93</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno"><a class="line" href="spi__device_8h.html#af27a6537c3222c6796876ff953298b42">   21</a></span><span class="preprocessor">#define SPI_TRANSFER_WRITE       0x00000002  </span><span class="comment">// u5199u4f20u8f93</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aa7a1a825b415e6aa12c47463eefc0bb7">   22</a></span><span class="preprocessor">#define SPI_TRANSFER_FULL_DUPLEX 0x00000004  </span><span class="comment">// u5168u53ccu5de5u4f20u8f93</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a8595e49b5f8ddb021462587455bd2ff5">   23</a></span><span class="preprocessor">#define SPI_TRANSFER_NO_CHIPSEL  0x00000008  </span><span class="comment">// u4e0du64cdu4f5cu7247u9009u4fe1u53f7</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span> </div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="comment">// SPIu4f20u8f93u6570u636eu5305u7ed3u6784</span></div>
<div class="foldopen" id="foldopen00026" data-start="{" data-end="};">
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="spi__device_8h.html">   26</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="spi__device_8h.html#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a> {</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="spi__device_8h.html#ace00b8a3ea9807f1b300e84fd6a8d7be">   27</a></span>    PVOID <a class="code hl_variable" href="spi__device_8h.html#ace00b8a3ea9807f1b300e84fd6a8d7be">WriteBuffer</a>;           <span class="comment">// u5199u6570u636eu7f13u51b2u533a</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a8575b3351fe18405d3eb99caf11e503b">   28</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#a8575b3351fe18405d3eb99caf11e503b">WriteLength</a>;           <span class="comment">// u5199u6570u636eu957fu5ea6</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a182672e24da62e3c9fcbc4baf9b285a1">   29</a></span>    PVOID <a class="code hl_variable" href="spi__device_8h.html#a182672e24da62e3c9fcbc4baf9b285a1">ReadBuffer</a>;            <span class="comment">// u8bfbu6570u636eu7f13u51b2u533a</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="spi__device_8h.html#ab93e815b31b5919f672691f7f01b06af">   30</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#ab93e815b31b5919f672691f7f01b06af">ReadLength</a>;            <span class="comment">// u8bfbu6570u636eu957fu5ea6</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a5ce41e65ae89fce3ef5107acc4fb11d2">   31</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#a5ce41e65ae89fce3ef5107acc4fb11d2">Flags</a>;                 <span class="comment">// u4f20u8f93u6807u5fd7</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a744606c6d58de9e0a6e0fe4e4acfbec6">   32</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#a744606c6d58de9e0a6e0fe4e4acfbec6">DelayInMicroseconds</a>;   <span class="comment">// u4f20u8f93u540eu7684u5ef6u8fdfuff08u5faeu79d2uff09</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">   33</a></span>} <a class="code hl_typedef" href="spi__device_8h.html#a22213828588d3110e34d053c7f191fc0">SPI_DEVICE_TRANSFER_PACKET</a>, *<a class="code hl_typedef" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a>;</div>
</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span> </div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="comment">// SPIu7edfu8ba1u4fe1u606fu7ed3u6784</span></div>
<div class="foldopen" id="foldopen00036" data-start="{" data-end="};">
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="spi__device_8h.html">   36</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="spi__device_8h.html#struct__SPI__STATISTICS">_SPI_STATISTICS</a> {</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aed0da9f11638910222e8bcf387a1d38f">   37</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#aed0da9f11638910222e8bcf387a1d38f">TransactionCount</a>;      <span class="comment">// u4f20u8f93u603bu6570</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="spi__device_8h.html#af7be253804f923ed13df884c7c93cdeb">   38</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#af7be253804f923ed13df884c7c93cdeb">ErrorCount</a>;            <span class="comment">// u9519u8befu603bu6570</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a254521d63fa6f195accebd432ad9a2c9">   39</a></span>    BOOLEAN <a class="code hl_variable" href="spi__device_8h.html#a254521d63fa6f195accebd432ad9a2c9">DeviceInitialized</a>;   <span class="comment">// u8bbeu5907u662fu5426u5df2u521du59cbu5316</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aea910d0fe28ff0978ec2ecfec60b8c52">   40</a></span>    ULONG <a class="code hl_variable" href="spi__device_8h.html#aea910d0fe28ff0978ec2ecfec60b8c52">ClockFrequency</a>;        <span class="comment">// u603bu7ebfu65f6u949fu9891u7387</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aaddc6c47a910b922a46c02072a72b0ef">   41</a></span>    <a class="code hl_typedef" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a> <a class="code hl_variable" href="spi__device_8h.html#aaddc6c47a910b922a46c02072a72b0ef">Mode</a>;               <span class="comment">// SPIu6a21u5f0f</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">   42</a></span>} <a class="code hl_typedef" href="spi__device_8h.html#aa83effe237db8be37288dceaeeab8d57">SPI_STATISTICS</a>, *<a class="code hl_typedef" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a>;</div>
</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span> </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="comment">// SPI IOCTL u63a7u5236u7801</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aad9cdee9a56a867985b5110add53ed94">   45</a></span><span class="preprocessor">#define IOCTL_SPI_BASE                      0x8200</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a72feec97101aca3be161a59ffe40cc2c">   46</a></span><span class="preprocessor">#define IOCTL_SPI_TRANSFER                  CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 0, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a21382b2df65b9cb8a11da17114ab9491">   47</a></span><span class="preprocessor">#define IOCTL_SPI_TRANSFER_FULL_DUPLEX      CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 1, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a97fe5a41276df38e46922527c4b9baf9">   48</a></span><span class="preprocessor">#define IOCTL_SPI_GET_STATISTICS            CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 2, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a8442695a715b85f6516ff535bc5d1409">   49</a></span><span class="preprocessor">#define IOCTL_SPI_RESET                     CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 3, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="spi__device_8h.html#aaba9d20f35713a3c0dd088bfdb433a0b">   50</a></span><span class="preprocessor">#define IOCTL_SPI_SET_BUS_SPEED             CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 4, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a471b24a3583fd2212e30e4619ae701be">   51</a></span><span class="preprocessor">#define IOCTL_SPI_SET_MODE                  CTL_CODE(FILE_DEVICE_UNKNOWN, IOCTL_SPI_BASE + 5, METHOD_BUFFERED, FILE_ANY_ACCESS)</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span> </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="comment">// SPIu8bbeu5907u63a5u53e3u51fdu6570u58f0u660e</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span><span class="comment">//===============================================================================</span></div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span></div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span>NTSTATUS</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><a class="code hl_function" href="spi__device_8h.html#a6939e12311ec72f975bcd03a4250a3e2">SpiDeviceInitialize</a>(</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    _In_ <a class="code hl_typedef" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="code hl_variable" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>);</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span></div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>VOID</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span><a class="code hl_function" href="spi__device_8h.html#a052b57a96b994325a574bcb9f3db837a">SpiDeviceCleanup</a>(</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>);</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span></div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>NTSTATUS</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span><a class="code hl_function" href="spi__device_8h.html#a2428921b9d71ab9d24f34e0a7b23487c">SpiDeviceTransfer</a>(</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>    _In_ <a class="code hl_typedef" href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>);</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>NTSTATUS</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span><a class="code hl_function" href="spi__device_8h.html#a3bc98267d67ee8988179bde952efaa87">SpiDeviceRead</a>(</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    _Out_writes_bytes_(Length) PVOID Buffer,</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>    _In_ ULONG Length,</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    _Out_opt_ PULONG BytesRead</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>);</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span>NTSTATUS</div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span><a class="code hl_function" href="spi__device_8h.html#ae90ccf3d865bebb54c2c76e10fcbcaa8">SpiDeviceWrite</a>(</div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span>    _In_reads_bytes_(Length) PVOID Buffer,</div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>    _In_ ULONG Length,</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span>    _Out_opt_ PULONG BytesWritten</div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span>);</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span>NTSTATUS</div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span><a class="code hl_function" href="spi__device_8h.html#ae2be7c6b48ddf5b08876e1115879469d">SpiDeviceGetStatistics</a>(</div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span>    _Out_ <a class="code hl_typedef" href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a> Statistics</div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span>);</div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span></div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span>NTSTATUS</div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a3c91b33450d309fa46affa22959f8607">  160</a></span><a class="code hl_function" href="spi__device_8h.html#a3c91b33450d309fa46affa22959f8607">SpiDeviceSetMode</a>(</div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span>    _In_ <a class="code hl_typedef" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a> Mode</div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span>);</div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span></div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span>NTSTATUS</div>
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno"><a class="line" href="spi__device_8h.html#a30b9d7f482d2a1343e50a60ea8d4135a">  174</a></span><a class="code hl_function" href="spi__device_8h.html#a30b9d7f482d2a1343e50a60ea8d4135a">SpiDeviceSetClockFrequency</a>(</div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span>    _In_ ULONG ClockFrequency</div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span>);</div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span> </div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span><span class="preprocessor">#endif </span><span class="comment">// SPI_DEVICE_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="akmdf__spi_8h_html"><div class="ttname"><a href="kmdf__spi_8h.html">kmdf_spi.h</a></div></div>
<div class="ttc" id="akmdf__spi_8h_html_a25212ee83b198babc11d7c726564c07c"><div class="ttname"><a href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></div><div class="ttdeci">struct _SPI_CONFIG * PSPI_CONFIG</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ae9c35ffd537d30a103775489f57c24cc"><div class="ttname"><a href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></div><div class="ttdeci">enum _SPI_MODE SPI_MODE</div></div>
<div class="ttc" id="aspi__device_8c_html_addbc5753ca32543e25382ea5a386d59b"><div class="ttname"><a href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></div><div class="ttdeci">PSPI_CONFIG SpiConfig</div><div class="ttdef"><b>Definition</b> spi_device.c:20</div></div>
<div class="ttc" id="aspi__device_8h_html_a052b57a96b994325a574bcb9f3db837a"><div class="ttname"><a href="spi__device_8h.html#a052b57a96b994325a574bcb9f3db837a">SpiDeviceCleanup</a></div><div class="ttdeci">VOID SpiDeviceCleanup(_In_ WDFDEVICE Device)</div><div class="ttdef"><b>Definition</b> spi_device.c:113</div></div>
<div class="ttc" id="aspi__device_8h_html_a182672e24da62e3c9fcbc4baf9b285a1"><div class="ttname"><a href="spi__device_8h.html#a182672e24da62e3c9fcbc4baf9b285a1">_SPI_DEVICE_TRANSFER_PACKET::ReadBuffer</a></div><div class="ttdeci">PVOID ReadBuffer</div><div class="ttdef"><b>Definition</b> spi_device.h:29</div></div>
<div class="ttc" id="aspi__device_8h_html_a22213828588d3110e34d053c7f191fc0"><div class="ttname"><a href="spi__device_8h.html#a22213828588d3110e34d053c7f191fc0">SPI_DEVICE_TRANSFER_PACKET</a></div><div class="ttdeci">struct _SPI_DEVICE_TRANSFER_PACKET SPI_DEVICE_TRANSFER_PACKET</div></div>
<div class="ttc" id="aspi__device_8h_html_a2428921b9d71ab9d24f34e0a7b23487c"><div class="ttname"><a href="spi__device_8h.html#a2428921b9d71ab9d24f34e0a7b23487c">SpiDeviceTransfer</a></div><div class="ttdeci">NTSTATUS SpiDeviceTransfer(_In_ WDFDEVICE Device, _In_ PSPI_DEVICE_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</div><div class="ttdef"><b>Definition</b> spi_device.c:148</div></div>
<div class="ttc" id="aspi__device_8h_html_a254521d63fa6f195accebd432ad9a2c9"><div class="ttname"><a href="spi__device_8h.html#a254521d63fa6f195accebd432ad9a2c9">_SPI_STATISTICS::DeviceInitialized</a></div><div class="ttdeci">BOOLEAN DeviceInitialized</div><div class="ttdef"><b>Definition</b> spi_device.h:39</div></div>
<div class="ttc" id="aspi__device_8h_html_a30b9d7f482d2a1343e50a60ea8d4135a"><div class="ttname"><a href="spi__device_8h.html#a30b9d7f482d2a1343e50a60ea8d4135a">SpiDeviceSetClockFrequency</a></div><div class="ttdeci">NTSTATUS SpiDeviceSetClockFrequency(_In_ WDFDEVICE Device, _In_ ULONG ClockFrequency)</div></div>
<div class="ttc" id="aspi__device_8h_html_a3bc98267d67ee8988179bde952efaa87"><div class="ttname"><a href="spi__device_8h.html#a3bc98267d67ee8988179bde952efaa87">SpiDeviceRead</a></div><div class="ttdeci">NTSTATUS SpiDeviceRead(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</div><div class="ttdef"><b>Definition</b> spi_device.c:194</div></div>
<div class="ttc" id="aspi__device_8h_html_a3c91b33450d309fa46affa22959f8607"><div class="ttname"><a href="spi__device_8h.html#a3c91b33450d309fa46affa22959f8607">SpiDeviceSetMode</a></div><div class="ttdeci">NTSTATUS SpiDeviceSetMode(_In_ WDFDEVICE Device, _In_ SPI_MODE Mode)</div></div>
<div class="ttc" id="aspi__device_8h_html_a5ce41e65ae89fce3ef5107acc4fb11d2"><div class="ttname"><a href="spi__device_8h.html#a5ce41e65ae89fce3ef5107acc4fb11d2">_SPI_DEVICE_TRANSFER_PACKET::Flags</a></div><div class="ttdeci">ULONG Flags</div><div class="ttdef"><b>Definition</b> spi_device.h:31</div></div>
<div class="ttc" id="aspi__device_8h_html_a6939e12311ec72f975bcd03a4250a3e2"><div class="ttname"><a href="spi__device_8h.html#a6939e12311ec72f975bcd03a4250a3e2">SpiDeviceInitialize</a></div><div class="ttdeci">NTSTATUS SpiDeviceInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</div><div class="ttdef"><b>Definition</b> spi_device.c:31</div></div>
<div class="ttc" id="aspi__device_8h_html_a744606c6d58de9e0a6e0fe4e4acfbec6"><div class="ttname"><a href="spi__device_8h.html#a744606c6d58de9e0a6e0fe4e4acfbec6">_SPI_DEVICE_TRANSFER_PACKET::DelayInMicroseconds</a></div><div class="ttdeci">ULONG DelayInMicroseconds</div><div class="ttdef"><b>Definition</b> spi_device.h:32</div></div>
<div class="ttc" id="aspi__device_8h_html_a8575b3351fe18405d3eb99caf11e503b"><div class="ttname"><a href="spi__device_8h.html#a8575b3351fe18405d3eb99caf11e503b">_SPI_DEVICE_TRANSFER_PACKET::WriteLength</a></div><div class="ttdeci">ULONG WriteLength</div><div class="ttdef"><b>Definition</b> spi_device.h:28</div></div>
<div class="ttc" id="aspi__device_8h_html_a8d5f40e6c769c7d8420d120a84cd711a"><div class="ttname"><a href="spi__device_8h.html#a8d5f40e6c769c7d8420d120a84cd711a">PSPI_DEVICE_TRANSFER_PACKET</a></div><div class="ttdeci">struct _SPI_DEVICE_TRANSFER_PACKET * PSPI_DEVICE_TRANSFER_PACKET</div></div>
<div class="ttc" id="aspi__device_8h_html_aa83effe237db8be37288dceaeeab8d57"><div class="ttname"><a href="spi__device_8h.html#aa83effe237db8be37288dceaeeab8d57">SPI_STATISTICS</a></div><div class="ttdeci">struct _SPI_STATISTICS SPI_STATISTICS</div></div>
<div class="ttc" id="aspi__device_8h_html_aaddc6c47a910b922a46c02072a72b0ef"><div class="ttname"><a href="spi__device_8h.html#aaddc6c47a910b922a46c02072a72b0ef">_SPI_STATISTICS::Mode</a></div><div class="ttdeci">SPI_MODE Mode</div><div class="ttdef"><b>Definition</b> spi_device.h:41</div></div>
<div class="ttc" id="aspi__device_8h_html_ab93e815b31b5919f672691f7f01b06af"><div class="ttname"><a href="spi__device_8h.html#ab93e815b31b5919f672691f7f01b06af">_SPI_DEVICE_TRANSFER_PACKET::ReadLength</a></div><div class="ttdeci">ULONG ReadLength</div><div class="ttdef"><b>Definition</b> spi_device.h:30</div></div>
<div class="ttc" id="aspi__device_8h_html_ace00b8a3ea9807f1b300e84fd6a8d7be"><div class="ttname"><a href="spi__device_8h.html#ace00b8a3ea9807f1b300e84fd6a8d7be">_SPI_DEVICE_TRANSFER_PACKET::WriteBuffer</a></div><div class="ttdeci">PVOID WriteBuffer</div><div class="ttdef"><b>Definition</b> spi_device.h:27</div></div>
<div class="ttc" id="aspi__device_8h_html_ad0637463ce63cba4d22faa4adab1949d"><div class="ttname"><a href="spi__device_8h.html#ad0637463ce63cba4d22faa4adab1949d">PSPI_STATISTICS</a></div><div class="ttdeci">struct _SPI_STATISTICS * PSPI_STATISTICS</div></div>
<div class="ttc" id="aspi__device_8h_html_ae2be7c6b48ddf5b08876e1115879469d"><div class="ttname"><a href="spi__device_8h.html#ae2be7c6b48ddf5b08876e1115879469d">SpiDeviceGetStatistics</a></div><div class="ttdeci">NTSTATUS SpiDeviceGetStatistics(_In_ WDFDEVICE Device, _Out_ PSPI_STATISTICS Statistics)</div><div class="ttdef"><b>Definition</b> spi_device.c:298</div></div>
<div class="ttc" id="aspi__device_8h_html_ae90ccf3d865bebb54c2c76e10fcbcaa8"><div class="ttname"><a href="spi__device_8h.html#ae90ccf3d865bebb54c2c76e10fcbcaa8">SpiDeviceWrite</a></div><div class="ttdeci">NTSTATUS SpiDeviceWrite(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</div><div class="ttdef"><b>Definition</b> spi_device.c:241</div></div>
<div class="ttc" id="aspi__device_8h_html_aea910d0fe28ff0978ec2ecfec60b8c52"><div class="ttname"><a href="spi__device_8h.html#aea910d0fe28ff0978ec2ecfec60b8c52">_SPI_STATISTICS::ClockFrequency</a></div><div class="ttdeci">ULONG ClockFrequency</div><div class="ttdef"><b>Definition</b> spi_device.h:40</div></div>
<div class="ttc" id="aspi__device_8h_html_aed0da9f11638910222e8bcf387a1d38f"><div class="ttname"><a href="spi__device_8h.html#aed0da9f11638910222e8bcf387a1d38f">_SPI_STATISTICS::TransactionCount</a></div><div class="ttdeci">ULONG TransactionCount</div><div class="ttdef"><b>Definition</b> spi_device.h:37</div></div>
<div class="ttc" id="aspi__device_8h_html_af7be253804f923ed13df884c7c93cdeb"><div class="ttname"><a href="spi__device_8h.html#af7be253804f923ed13df884c7c93cdeb">_SPI_STATISTICS::ErrorCount</a></div><div class="ttdeci">ULONG ErrorCount</div><div class="ttdef"><b>Definition</b> spi_device.h:38</div></div>
<div class="ttc" id="aspi__device_8h_html_struct__SPI__DEVICE__TRANSFER__PACKET"><div class="ttname"><a href="spi__device_8h.html#struct__SPI__DEVICE__TRANSFER__PACKET">_SPI_DEVICE_TRANSFER_PACKET</a></div><div class="ttdef"><b>Definition</b> spi_device.h:26</div></div>
<div class="ttc" id="aspi__device_8h_html_struct__SPI__STATISTICS"><div class="ttname"><a href="spi__device_8h.html#struct__SPI__STATISTICS">_SPI_STATISTICS</a></div><div class="ttdef"><b>Definition</b> spi_device.h:36</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_f51f2e86ea53a1a257ee2ea690474c95.html">devices</a></li><li class="navelem"><a href="spi__device_8h.html">spi_device.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
