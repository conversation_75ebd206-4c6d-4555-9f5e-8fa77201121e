var searchData=
[
  ['logcleanup_0',['LogCleanup',['../include_2core_2log_2driver__log_8h.html#a93b035f39214ba5782080d504ae3ebc7',1,'driver_log.h']]],
  ['logconfiginit_1',['LogConfigInit',['../driver__log_8c.html#aa7f5f3b01615029c1cb54753c0b03175',1,'LogConfigInit(_Out_ PLOG_CONFIG LogConfig):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aa7f5f3b01615029c1cb54753c0b03175',1,'LogConfigInit(_Out_ PLOG_CONFIG LogConfig):&#160;driver_log.c'],['../include_2core_2log_2driver__log_8h.html#ab4caba1729c833f0c7cce2e72c20e30a',1,'LogConfigInit(PLOG_CONFIG LogConfig):&#160;driver_log.h']]],
  ['logerror_2',['LogError',['../struct__GPIO__PIN__CONTEXT.html#a38aa560da9bc169cb9b45c3df5ea4454',1,'_GPIO_PIN_CONTEXT::LogError()'],['../error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e',1,'LogError(_In_ NTSTATUS Status, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Message):&#160;error_handling.c'],['../gpio__core_8c.html#a9f7acfc8c5f9d7d32170223f628f766b',1,'LogError(ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;PinNumber):&#160;gpio_core.c']]],
  ['logfunctionentry_3',['LogFunctionEntry',['../include_2core_2log_2driver__log_8h.html#a8b05bbaba9e1fe6f53057c15e4a53a81',1,'driver_log.h']]],
  ['logfunctionexit_4',['LogFunctionExit',['../include_2core_2log_2driver__log_8h.html#a2ebea8a6c7cbdde9ba74e856c73a2740',1,'driver_log.h']]],
  ['loginfo_5',['LogInfo',['../gpio__core_8c.html#a8f2aeb9f5f8525345d3f33df4340da13',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO pin %d initialized successfully&quot;, GpioConfig-&gt;PinNumber):&#160;gpio_core.c'],['../gpio__device_8c.html#ac410021632e38928f5eb4b3bb6939ab9',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO device initialized successfully, type=%d, pin=%d&quot;, GpioConfig-&gt;DeviceType, GpioConfig-&gt;PinNumber):&#160;gpio_device.c'],['../gpio__device_8c.html#a3156531cfce23bfe597c3aaad2f23aad',1,'LogInfo(__FUNCTION__, __LINE__, &quot;GPIO device resources cleaned up&quot;):&#160;gpio_device.c'],['../i2c__device_8c.html#a90ec17a9895e508ccdb9077fed539682',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C device initialized successfully&quot;):&#160;i2c_device.c'],['../i2c__device_8c.html#ae957556f2bac1175f6f4b37cd8f268f9',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C read succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesRead ? *BytesRead :0):&#160;i2c_device.c'],['../i2c__device_8c.html#a6e8f3cbefed6c462cd6392131f5f0a29',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C write succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesWritten ? *BytesWritten :0):&#160;i2c_device.c'],['../i2c__device_8c.html#aabdf6e2791329ae7f1d903b2c7b47add',1,'LogInfo(__FUNCTION__, __LINE__, &quot;I2C transfer sequence succeeded, transfers: %d&quot;, TransferCount):&#160;i2c_device.c'],['../spi__device_8c.html#a92b63e772873a034bea01d26f382ed57',1,'LogInfo(__FUNCTION__, __LINE__, &quot;SPI device initialized successfully&quot;):&#160;spi_device.c'],['../spi__device_8c.html#aef91cc299dacb19ea73324acf8c8ff2c',1,'LogInfo(__FUNCTION__, __LINE__, &quot;SPI transfer succeeded&quot;):&#160;spi_device.c']]],
  ['loginitialize_6',['LogInitialize',['../driver__log_8c.html#aa2e9424857371175fc265253fbabcc5d',1,'LogInitialize(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aa2e9424857371175fc265253fbabcc5d',1,'LogInitialize(_In_ WDFDRIVER DriverObject, _In_opt_ CONST LOG_CONFIG *InitialConfig):&#160;driver_log.c'],['../include_2core_2log_2driver__log_8h.html#ae2910293c9c672800cca68427812b7c9',1,'LogInitialize(PDRIVER_OBJECT DriverObject, PLOG_CONFIG LogConfig):&#160;driver_log.h']]],
  ['logmessage_7',['LogMessage',['../include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1',1,'driver_log.h']]],
  ['logmessageva_8',['LogMessageVA',['../driver__log_8c.html#a8e8711da6408af7b3b313f892121215e',1,'LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#a8e8711da6408af7b3b313f892121215e',1,'LogMessageVA(_In_ LOG_LEVEL Level, _In_ PCSTR Function, _In_ ULONG Line, _In_ PCSTR Format, _In_ va_list Args):&#160;driver_log.c']]],
  ['logsetlevel_9',['LogSetLevel',['../include_2core_2log_2driver__log_8h.html#abac3042c22899daa9d6987d7f15e0185',1,'driver_log.h']]],
  ['loguninitialize_10',['LogUninitialize',['../driver__log_8c.html#aab8bcb7121136bc236fe5d55778fbaf2',1,'LogUninitialize(VOID):&#160;driver_log.c'],['../src_2core_2log_2driver__log_8h.html#aab8bcb7121136bc236fe5d55778fbaf2',1,'LogUninitialize(VOID):&#160;driver_log.c']]],
  ['logwarning_11',['LogWarning',['../gpio__device_8c.html#a1fbae9652e19914386dbb4fc4848b63d',1,'gpio_device.c']]]
];
