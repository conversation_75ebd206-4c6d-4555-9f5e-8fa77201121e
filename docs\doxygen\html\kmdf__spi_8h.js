var kmdf__spi_8h =
[
    [ "_SPI_CONFIG", "kmdf__spi_8h.html#struct__SPI__CONFIG", [
      [ "ChipSelectLine", "kmdf__spi_8h.html#a5568dcea126ea711e8d0f6b40dc951cc", null ],
      [ "ChipSelectPolarity", "kmdf__spi_8h.html#a23bf21cffc603724543545d3c677093a", null ],
      [ "IsLsbFirst", "kmdf__spi_8h.html#ad132bb074410f64505375ed71b0173f8", null ],
      [ "MaxClockFrequency", "kmdf__spi_8h.html#a6cf9c836c9e4e1a5b0dd2ffc18e218e6", null ],
      [ "Mode", "kmdf__spi_8h.html#a5995a0bc6695e2e61567693ec8e494ca", null ],
      [ "RetryCount", "kmdf__spi_8h.html#a3eecf9d65abadb2106c4930606d31c7a", null ],
      [ "SpbConnectionId", "kmdf__spi_8h.html#aed13983e08690b185df2a6c06f44ffd3", null ],
      [ "SpbDeviceObject", "kmdf__spi_8h.html#a065d24ae185eeafe1fc5ce74cf823454", null ],
      [ "TimeoutMs", "kmdf__spi_8h.html#a85af30c409a08baa8e9c3e31567c07c2", null ],
      [ "WordLength", "kmdf__spi_8h.html#a2fb798d2a102a3bc148918a6528aa329", null ]
    ] ],
    [ "_SPI_TRANSFER_PACKET", "kmdf__spi_8h.html#struct__SPI__TRANSFER__PACKET", [
      [ "AssertChipSelect", "kmdf__spi_8h.html#ab7012a8e826ed6c62ef8b4e77d5dac24", null ],
      [ "Common", "kmdf__spi_8h.html#a00931840236e9b48912a56116bd0d109", null ],
      [ "DeassertChipSelect", "kmdf__spi_8h.html#a2c4bee3baf8bbe9d12ea2b02a12deb1c", null ],
      [ "ReadBuffer", "kmdf__spi_8h.html#a1bd99745fd3afc8bf6567a4460b85f28", null ],
      [ "ReadBufferLength", "kmdf__spi_8h.html#afc8f8284a74ea4a78d2531d7b09838ed", null ],
      [ "Type", "kmdf__spi_8h.html#ae3ca53594a2138cf5aea46199d1f00fc", null ],
      [ "WriteBuffer", "kmdf__spi_8h.html#adfd0120a5d0f6554506d7a8c4454609b", null ],
      [ "WriteBufferLength", "kmdf__spi_8h.html#a60c1c9ddb35c8b5aa7ef2d65b6a278a7", null ]
    ] ],
    [ "PSPI_CONFIG", "kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c", null ],
    [ "PSPI_TRANSFER_PACKET", "kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223", null ],
    [ "SPI_BUS_SPEED", "kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57", null ],
    [ "SPI_CONFIG", "kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9", null ],
    [ "SPI_MODE", "kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc", null ],
    [ "SPI_TRANSFER_PACKET", "kmdf__spi_8h.html#ae53b781a84db92ccef86085a53448289", null ],
    [ "SPI_TRANSFER_TYPE", "kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca", null ],
    [ "_SPI_BUS_SPEED", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2", [
      [ "SpiBusSpeed1MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2", null ],
      [ "SpiBusSpeed2MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab", null ],
      [ "SpiBusSpeed4MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94", null ],
      [ "SpiBusSpeed8MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928", null ],
      [ "SpiBusSpeed10MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997", null ],
      [ "SpiBusSpeed20MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90", null ],
      [ "SpiBusSpeed25MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef", null ],
      [ "SpiBusSpeed50MHz", "kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc", null ]
    ] ],
    [ "_SPI_MODE", "kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374", [
      [ "SpiMode0", "kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249", null ],
      [ "SpiMode1", "kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972", null ],
      [ "SpiMode2", "kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e", null ],
      [ "SpiMode3", "kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470", null ]
    ] ],
    [ "_SPI_TRANSFER_TYPE", "kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21", [
      [ "SpiWrite", "kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a", null ],
      [ "SpiRead", "kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d", null ],
      [ "SpiWriteRead", "kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36", null ]
    ] ],
    [ "SPIInitialize", "kmdf__spi_8h.html#a685d8d7731e750c1512b975df16cc030", null ],
    [ "SPIReadRegister", "kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885", null ],
    [ "SPITransferAsynchronous", "kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033", null ],
    [ "SPITransferSynchronous", "kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c", null ],
    [ "SPIUninitialize", "kmdf__spi_8h.html#ad756f8e3b06fdfa545a7048661038513", null ],
    [ "SPIWriteRead", "kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f", null ],
    [ "SPIWriteRegister", "kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8", null ]
];