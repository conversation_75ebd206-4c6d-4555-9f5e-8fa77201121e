<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="ioctl_8h" kind="file" language="C++">
    <compoundname>ioctl.h</compoundname>
    <includes local="no">devioctl.h</includes>
    <includedby refid="device__manager_8c" local="yes">C:/KMDF Driver1/src/core/device/device_manager.c</includedby>
    <includedby refid="precomp_8h" local="yes">C:/KMDF Driver1/src/precomp.h</includedby>
    <incdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>devioctl.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/core/driver/driver_entry.c</label>
        <link refid="driver__entry_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="6">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="7">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="8">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="9">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="10">
        <label>C:/KMDF Driver1/src/precomp.c</label>
        <link refid="precomp_8c"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="ioctl_8h_1ad40a8f5f93a2d0fdabdc3b13510850b6" prot="public" static="no">
        <name>IOCTL_TOUCH_GET_DATA</name>
        <initializer>CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_READ_ACCESS)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/common/ioctl.h" line="19" column="9" bodyfile="C:/KMDF Driver1/include/common/ioctl.h" bodystart="19" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">//<sp/>ioctl.h<sp/>-<sp/>IOCTL<sp/>definitions<sp/>for<sp/>KMDFDriver1</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>__IOCTL_H__</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>__IOCTL_H__</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Include<sp/>necessary<sp/>headers<sp/>for<sp/>CTL_CODE</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;devioctl.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Define<sp/>a<sp/>GUID<sp/>for<sp/>the<sp/>device<sp/>interface<sp/>if<sp/>not<sp/>already<sp/>defined<sp/>elsewhere</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="comment">//DEFINE_GUID(GUID_DEVINTERFACE_KMDFDriver1,<sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/>0xYOUR_GUID_DATA1,<sp/>0xYOUR_GUID_DATA2,<sp/>0xYOUR_GUID_DATA3,<sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/>0xYOUR_GUID_BYTE0,<sp/>0xYOUR_GUID_BYTE1,<sp/>0xYOUR_GUID_BYTE2,<sp/>0xYOUR_GUID_BYTE3,<sp/></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/><sp/><sp/><sp/>0xYOUR_GUID_BYTE4,<sp/>0xYOUR_GUID_BYTE5,<sp/>0xYOUR_GUID_BYTE6,<sp/>0xYOUR_GUID_BYTE7);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="comment">//<sp/>For<sp/>now,<sp/>we&apos;ll<sp/>assume<sp/>a<sp/>generic<sp/>FILE_DEVICE_UNKNOWN<sp/>or<sp/>a<sp/>specific<sp/>one<sp/>if<sp/>available</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="comment">//<sp/>IOCTL<sp/>to<sp/>get<sp/>touch<sp/>data</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Function<sp/>code<sp/>0x801<sp/>(custom<sp/>codes<sp/>typically<sp/>start<sp/>from<sp/>0x800)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight><highlight class="comment">//<sp/>FILE_DEVICE_UNKNOWN<sp/>can<sp/>be<sp/>replaced<sp/>with<sp/>a<sp/>custom<sp/>device<sp/>type<sp/>if<sp/>defined</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="ioctl_8h_1ad40a8f5f93a2d0fdabdc3b13510850b6" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IOCTL_TOUCH_GET_DATA<sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x801,<sp/>METHOD_BUFFERED,<sp/>FILE_READ_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Example<sp/>IOCTLs<sp/>(from<sp/>device_manager.c,<sp/>can<sp/>be<sp/>moved<sp/>here<sp/>if<sp/>intended<sp/>for<sp/>public<sp/>use)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight><highlight class="comment">//<sp/>#define<sp/>IOCTL_CUSTOM_GET_DEVICE_INFO<sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x802,<sp/>METHOD_BUFFERED,<sp/>FILE_ANY_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="comment">//<sp/>#define<sp/>IOCTL_CUSTOM_SET_PARAMETER<sp/>CTL_CODE(FILE_DEVICE_UNKNOWN,<sp/>0x803,<sp/>METHOD_BUFFERED,<sp/>FILE_WRITE_ACCESS)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>__IOCTL_H__</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/common/ioctl.h"/>
  </compounddef>
</doxygen>
