<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="error__handling_8h" kind="file" language="C++">
    <compoundname>error_handling.h</compoundname>
    <includes refid="error__codes_8h" local="yes">core/error/error_codes.h</includes>
    <includes local="yes">core/log/driver_log.h</includes>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <incdepgraph>
      <node id="2">
        <label>core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/core/error/error_handling.h</label>
        <link refid="error__handling_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>core/log/driver_log.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="error__handling_8h_1a3a8ba39810c16fad65af2271c2dd6a6c" prot="public" static="no">
        <name>DRIVER_ASSERT</name>
        <param><defname>_condition</defname></param>
        <param><defname>_message</defname></param>
        <param><defname>...</defname></param>
        <initializer>((void)0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_handling.h" line="92" column="10" bodyfile="C:/KMDF Driver1/include/core/error/error_handling.h" bodystart="92" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="error__handling_8h_1a98899d26e6d8086d4ae7268a1a953c01" prot="public" static="no">
        <name>RETURN_ON_FAILURE</name>
        <param><defname>_status</defname></param>
        <param><defname>_message</defname></param>
        <param><defname>...</defname></param>
        <initializer>    do { \
        <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(_status)) { \
            <ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(&quot;错误: %s (%!STATUS!) - &quot; _message, __FUNCTION__, _status, ##__VA_ARGS__); \
            return _status; \
        } \
    } while (0)</initializer>
        <briefdescription>
<para>检查NTSTATUS是否成功，如果失败则记录错误并返回 </para>
        </briefdescription>
        <detaileddescription>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername>_status</parametername>
</parameternamelist>
<parameterdescription>
<para>要检查的NTSTATUS值 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>_message</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息字符串 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>...</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息的格式化参数 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>如果_status失败，则从当前函数返回_status </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_handling.h" line="34" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_handling.h" bodystart="34" bodyend="40"/>
      </memberdef>
      <memberdef kind="define" id="error__handling_8h_1a2929f142620a357d4bb33852c676e822" prot="public" static="no">
        <name>RETURN_ON_FALSE</name>
        <param><defname>_condition</defname></param>
        <param><defname>_return_status</defname></param>
        <param><defname>_message</defname></param>
        <param><defname>...</defname></param>
        <initializer>    do { \
        <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> (!(_condition)) { \
            <ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(&quot;断言失败: %s - &quot; _message, __FUNCTION__, ##__VA_ARGS__); \
            return _return_status; \
        } \
    } while (0)</initializer>
        <briefdescription>
<para>检查条件是否为真，如果为假则记录错误并返回指定的NTSTATUS </para>
        </briefdescription>
        <detaileddescription>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername>_condition</parametername>
</parameternamelist>
<parameterdescription>
<para>要检查的条件 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>_return_status</parametername>
</parameternamelist>
<parameterdescription>
<para>如果条件为假，要返回的NTSTATUS值 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>_message</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息字符串 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>...</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息的格式化参数 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>如果_condition为假，则从当前函数返回_return_status </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_handling.h" line="50" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_handling.h" bodystart="50" bodyend="56"/>
      </memberdef>
      <memberdef kind="define" id="error__handling_8h_1a726809026748fc6eba00fc55e5a26ff0" prot="public" static="no">
        <name>RETURN_ON_NULL</name>
        <param><defname>_ptr</defname></param>
        <param><defname>_message</defname></param>
        <param><defname>...</defname></param>
        <initializer>    do { \
        <ref refid="gpio__core_8c_1a977bbe3e09136dd34381e7f1b889a570" kindref="member">if</ref> ((_ptr) == NULL) { \
            <ref refid="src_2core_2log_2driver__log_8h_1a29e75b488d8e8ef5641c5bd16709faec" kindref="member">LOG_ERROR</ref>(&quot;空指针错误: %s - &quot; _message, __FUNCTION__, ##__VA_ARGS__); \
            return STATUS_INSUFFICIENT_RESOURCES; \
        } \
    } while (0)</initializer>
        <briefdescription>
<para>检查指针是否为空，如果为空则记录错误并返回STATUS_INSUFFICIENT_RESOURCES </para>
        </briefdescription>
        <detaileddescription>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername>_ptr</parametername>
</parameternamelist>
<parameterdescription>
<para>要检查的指针 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>_message</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息字符串 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername>...</parametername>
</parameternamelist>
<parameterdescription>
<para>错误消息的格式化参数 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>如果_ptr为空，则从当前函数返回STATUS_INSUFFICIENT_RESOURCES </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/core/error/error_handling.h" line="65" column="9" bodyfile="C:/KMDF Driver1/include/core/error/error_handling.h" bodystart="65" bodyend="71"/>
      </memberdef>
    </sectiondef>
    <briefdescription>
<para>驱动程序错误处理和断言宏定义 </para>
    </briefdescription>
    <detaileddescription>
<para>包含用于错误代码检查、断言和日志记录的宏，以提高驱动程序的健壮性和可调试性。</para>
<para><simplesect kind="author"><para>KMDF Team </para>
</simplesect>
<simplesect kind="date"><para>2025-05-12 </para>
</simplesect>
</para>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_DRIVER1_INCLUDE_CORE_ERROR_ERROR_HANDLING_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_DRIVER1_INCLUDE_CORE_ERROR_ERROR_HANDLING_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>Include<sp/>necessary<sp/>headers</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"><sp/></highlight><highlight class="comment">//<sp/>包含错误代码定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;core/log/driver_log.h&quot;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>包含日志记录接口</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="preprocessor">#ifdef<sp/>__cplusplus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight><highlight class="keyword">extern</highlight><highlight class="normal"><sp/></highlight><highlight class="stringliteral">&quot;C&quot;</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="comment">//<sp/>===========================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight><highlight class="comment">//<sp/>错误处理宏定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//<sp/>===========================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="error__handling_8h_1a98899d26e6d8086d4ae7268a1a953c01" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>RETURN_ON_FAILURE(_status,<sp/>_message,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="35"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>do<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="36"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(!NT_SUCCESS(_status))<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="37"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LOG_ERROR(&quot;错误:<sp/>%s<sp/>(%!STATUS!)<sp/>-<sp/>&quot;<sp/>_message,<sp/>__FUNCTION__,<sp/>_status,<sp/>##__VA_ARGS__);<sp/>\</highlight></codeline>
<codeline lineno="38"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>return<sp/>_status;<sp/>\</highlight></codeline>
<codeline lineno="39"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/>\</highlight></codeline>
<codeline lineno="40"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}<sp/>while<sp/>(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41"><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="error__handling_8h_1a2929f142620a357d4bb33852c676e822" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>RETURN_ON_FALSE(_condition,<sp/>_return_status,<sp/>_message,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="51"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>do<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="52"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(!(_condition))<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="53"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LOG_ERROR(&quot;断言失败:<sp/>%s<sp/>-<sp/>&quot;<sp/>_message,<sp/>__FUNCTION__,<sp/>##__VA_ARGS__);<sp/>\</highlight></codeline>
<codeline lineno="54"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>return<sp/>_return_status;<sp/>\</highlight></codeline>
<codeline lineno="55"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/>\</highlight></codeline>
<codeline lineno="56"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}<sp/>while<sp/>(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"></highlight></codeline>
<codeline lineno="65" refid="error__handling_8h_1a726809026748fc6eba00fc55e5a26ff0" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>RETURN_ON_NULL(_ptr,<sp/>_message,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="66"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>do<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="67"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>((_ptr)<sp/>==<sp/>NULL)<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="68"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LOG_ERROR(&quot;空指针错误:<sp/>%s<sp/>-<sp/>&quot;<sp/>_message,<sp/>__FUNCTION__,<sp/>##__VA_ARGS__);<sp/>\</highlight></codeline>
<codeline lineno="69"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>return<sp/>STATUS_INSUFFICIENT_RESOURCES;<sp/>\</highlight></codeline>
<codeline lineno="70"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/>\</highlight></codeline>
<codeline lineno="71"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}<sp/>while<sp/>(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"></highlight><highlight class="comment">//<sp/>===========================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="74"><highlight class="normal"></highlight><highlight class="comment">//<sp/>断言宏定义<sp/>(仅在调试模式下有效)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="75"><highlight class="normal"></highlight><highlight class="comment">//<sp/>===========================================================================</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="76"><highlight class="normal"></highlight></codeline>
<codeline lineno="77"><highlight class="normal"></highlight><highlight class="preprocessor">#if<sp/>DBG</highlight><highlight class="preprocessor"></highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="84"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_ASSERT(_condition,<sp/>_message,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="85"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>do<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="86"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>if<sp/>(!(_condition))<sp/>{<sp/>\</highlight></codeline>
<codeline lineno="87"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>LOG_CRITICAL(&quot;断言失败<sp/>(%s:%d):<sp/>&quot;<sp/>_message,<sp/>__FILE__,<sp/>__LINE__,<sp/>##__VA_ARGS__);<sp/>\</highlight></codeline>
<codeline lineno="88"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>DbgBreakPoint();<sp/>\</highlight></codeline>
<codeline lineno="89"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}<sp/>\</highlight></codeline>
<codeline lineno="90"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>}<sp/>while<sp/>(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="91"><highlight class="normal"></highlight><highlight class="preprocessor">#else</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="92" refid="error__handling_8h_1a3a8ba39810c16fad65af2271c2dd6a6c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DRIVER_ASSERT(_condition,<sp/>_message,<sp/>...)<sp/>((void)0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="93"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>DBG</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="94"><highlight class="normal"></highlight></codeline>
<codeline lineno="95"><highlight class="normal"></highlight><highlight class="preprocessor">#ifdef<sp/>__cplusplus</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="96"><highlight class="normal">}</highlight></codeline>
<codeline lineno="97"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="98"><highlight class="normal"></highlight></codeline>
<codeline lineno="99"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_DRIVER1_INCLUDE_CORE_ERROR_ERROR_HANDLING_H_INCLUDED</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/core/error/error_handling.h"/>
  </compounddef>
</doxygen>
