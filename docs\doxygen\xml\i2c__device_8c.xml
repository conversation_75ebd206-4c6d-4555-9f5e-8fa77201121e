<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="i2c__device_8c" kind="file" language="C++">
    <compoundname>i2c_device.c</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes refid="hal__interface_8h" local="yes">../../../include/hal/hal_interface.h</includes>
    <includes refid="kmdf__i2c_8h" local="yes">../../../include/hal/bus/kmdf_i2c.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="5">
        <label>../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="8">
        <label>../../../include/core/log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="7">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="6">
        <label>../../../include/hal/bus/kmdf_i2c.h</label>
        <link refid="kmdf__i2c_8h"/>
        <childnode refid="7" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>../../../include/hal/hal_interface.h</label>
        <link refid="hal__interface_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/devices/i2c_device.c</label>
        <link refid="i2c__device_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="i2c__device_8c_1a4bbdac9bba21cb3959a7355001ea590f" prot="public" static="no">
        <name>I2C_DEFAULT_TIMEOUT</name>
        <initializer>1000</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="28" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="28" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="i2c__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" prot="public" static="no" mutable="no">
        <type>Exit</type>
        <definition>Exit __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="130" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="134" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" prot="public" static="no" mutable="no">
        <type>WDFMEMORY</type>
        <definition>deviceContext ConfigurationMemory</definition>
        <argsstring></argsstring>
        <name>ConfigurationMemory</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="18" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="18" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a7cd334185ddc76afeacf2cd57615dc81" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet Data</definition>
        <argsstring></argsstring>
        <name>Data</name>
        <initializer>= Buffer</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="218" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="218" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a63268b1e9e5ee12309a44d8d6c9fc652" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet DataAddress</definition>
        <argsstring></argsstring>
        <name>DataAddress</name>
        <initializer>= RegisterAddress</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="217" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="217" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1adb5ea4ad84a87209e6f98b3e012adbbf" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet DataLength</definition>
        <argsstring></argsstring>
        <name>DataLength</name>
        <initializer>= Length</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="219" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="219" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" prot="public" static="no" mutable="no">
        <type>packet</type>
        <definition>packet DelayInMicroseconds</definition>
        <argsstring></argsstring>
        <name>DelayInMicroseconds</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="221" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="221" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1ae49d231a428d107c888f925e845daf62" prot="public" static="no" mutable="no">
        <type>BOOLEAN</type>
        <definition>Statistics DeviceInitialized</definition>
        <argsstring></argsstring>
        <name>DeviceInitialized</name>
        <initializer>= FALSE</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="21" column="13" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="21" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1abf852046373359fb294f66a784b38263" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig DeviceType</definition>
        <argsstring></argsstring>
        <name>DeviceType</name>
        <initializer>= <ref refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d" kindref="member">HalDeviceI2C</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="116" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="116" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a0544c3fe466e421738dae463968b70ba" prot="public" static="no" mutable="no">
        <type></type>
        <definition>else</definition>
        <argsstring></argsstring>
        <name>else</name>
        <initializer>{
        
        InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" kindref="member">TransactionCount</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="239" column="6" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="239" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" prot="public" static="no" mutable="no">
        <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
        <definition>Statistics ErrorCount</definition>
        <argsstring></argsstring>
        <name>ErrorCount</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="67" column="18" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="67" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_DPC</type>
        <definition>EVT_WDF_INTERRUPT_DPC EvtI2cInterruptDpc</definition>
        <argsstring></argsstring>
        <name>EvtI2cInterruptDpc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="32" column="23" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="32" bodyend="-1"/>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" prot="public" static="no" mutable="no">
        <type>EVT_WDF_INTERRUPT_ISR</type>
        <definition>EVT_WDF_INTERRUPT_ISR EvtI2cInterruptIsr</definition>
        <argsstring></argsstring>
        <name>EvtI2cInterruptIsr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="31" column="23" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="31" bodyend="-1"/>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" prot="public" static="no" mutable="no">
        <type>EVT_WDF_REQUEST_COMPLETION_ROUTINE</type>
        <definition>EVT_WDF_REQUEST_COMPLETION_ROUTINE EvtI2cRequestCompletion</definition>
        <argsstring></argsstring>
        <name>EvtI2cRequestCompletion</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="33" column="36" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="33" bodyend="-1"/>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>packet Flags</definition>
        <argsstring></argsstring>
        <name>Flags</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="117" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="117" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" prot="public" static="no" mutable="no">
        <type><ref refid="hal__interface_8h_1a2f4ba870132c1fd57e2d74ba94e39805" kindref="member">HAL_DEVICE_HANDLE</ref></type>
        <definition>HAL_DEVICE_HANDLE HalHandle</definition>
        <argsstring></argsstring>
        <name>HalHandle</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="20" column="23" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="20" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1aa89f192944d335c9a60e5858325ed89a" prot="public" static="no" mutable="no">
        <type></type>
        <definition>I2C_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>I2C_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="23" column="2" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="23" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" prot="public" static="no" mutable="no">
        <type><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref></type>
        <definition>deviceContext I2cConfig</definition>
        <argsstring></argsstring>
        <name>I2cConfig</name>
        <initializer>= NULL</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="19" column="17" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="19" bodyend="-1"/>
        <referencedby refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" compoundref="i2c__device_8c" startline="39" endline="55">I2cDeviceInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
        <referencedby refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</referencedby>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a89c965cbcba7aedff1b61ea4c0498d3e" prot="public" static="no" mutable="no">
        <type>*</type>
        <definition>* PI2C_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>PI2C_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="23" column="21" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="23" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig PrivateData</definition>
        <argsstring></argsstring>
        <name>PrivateData</name>
        <initializer>= <ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="118" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="118" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" prot="public" static="no" mutable="no">
        <type>halConfig</type>
        <definition>halConfig PrivateDataSize</definition>
        <argsstring></argsstring>
        <name>PrivateDataSize</name>
        <initializer>= sizeof(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="119" column="14" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="119" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a9611b3a00430a86619b5923de30f9fdb" prot="public" static="no" mutable="no">
        <type></type>
        <definition>return status</definition>
        <argsstring></argsstring>
        <name>status</name>
        <initializer>= WdfMemoryCreate(
        WDF_NO_OBJECT_ATTRIBUTES,
        NonPagedPoolNx,
        0,
        sizeof(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>),
        &amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kindref="member">ConfigurationMemory</ref>,
        (PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>
    )</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="70" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="70" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a77b4762318f24dff847f94f382cfeea6" prot="public" static="no" mutable="no">
        <type>return</type>
        <definition>return STATUS_SUCCESS</definition>
        <argsstring></argsstring>
        <name>STATUS_SUCCESS</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="390" column="12" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="390" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" prot="public" static="no" mutable="no">
        <type>ULONG</type>
        <definition>Statistics TransactionCount</definition>
        <argsstring></argsstring>
        <name>TransactionCount</name>
        <initializer>= 0</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="22" column="11" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="22" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="i2c__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" prot="public" static="no" mutable="no">
        <type><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
        <definition>deviceContext WdfDevice</definition>
        <argsstring></argsstring>
        <name>WdfDevice</name>
        <initializer>= Device</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="17" column="15" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="17" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>BOOLEAN</type>
        <definition>BOOLEAN EvtI2cInterruptIsr</definition>
        <argsstring>(_In_ WDFINTERRUPT Interrupt, _In_ ULONG MessageID)</argsstring>
        <name>EvtI2cInterruptIsr</name>
        <param>
          <type>_In_ WDFINTERRUPT</type>
          <declname>Interrupt</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>MessageID</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="397" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="397" bodyend="-1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" compoundref="i2c__device_8c" startline="32">EvtI2cInterruptDpc</references>
        <references refid="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" compoundref="i2c__device_8c" startline="33">EvtI2cRequestCompletion</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>VOID</type>
        <definition>VOID I2cDeviceCleanup</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>I2cDeviceCleanup</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceCleanup - 娓呯悊I2C璁惧璧勬簮</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
</parameterlist>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="157" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="157" bodyend="186"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceGetStatistics</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Out_ PI2C_STATISTICS Statistics)</argsstring>
        <name>I2cDeviceGetStatistics</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Out_ <ref refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kindref="member">PI2C_STATISTICS</ref></type>
          <declname>Statistics</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceGetStatistics - 鑾峰彇I2C璁惧缁熻淇℃伅</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Statistics</parametername>
</parameternamelist>
<parameterdescription>
<para>缁熻淇℃伅缁撴瀯</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="362" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="362" bodyend="372"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PI2C_CONFIG I2cConfig)</argsstring>
        <name>I2cDeviceInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref></type>
          <declname>I2cConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceInitialize - 鍒濆鍖朓2C璁惧</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">I2cConfig</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C閰嶇疆</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="39" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="39" bodyend="55"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <references refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" compoundref="gpio__core_8c" startline="169">interruptConfig</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceRead</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</argsstring>
        <name>I2cDeviceRead</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>DeviceAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_writes_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesRead</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceRead - 浠嶪2C璁惧璇诲彇鏁版嵁</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">DeviceAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C璁惧鍦板潃 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>瀵勫瓨鍣ㄥ湴鍧€ </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>鏁版嵁缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>缂撳啿鍖洪暱搴? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesRead</parametername>
</parameternamelist>
<parameterdescription>
<para>瀹為檯璇诲彇鐨勫瓧鑺傛暟</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="192" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="192" bodyend="208"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceTransfer</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_reads_(TransferCount) PI2C_TRANSFER_PACKET Transfers, _In_ ULONG TransferCount)</argsstring>
        <name>I2cDeviceTransfer</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_reads_(TransferCount) <ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref></type>
          <declname>Transfers</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TransferCount</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceTransfer - 鎵ц澶嶆潅I2C浼犺緭</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Transfers</parametername>
</parameternamelist>
<parameterdescription>
<para>浼犺緭鏁版嵁鍖呮暟缁? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">TransferCount</parametername>
</parameternamelist>
<parameterdescription>
<para>浼犺緭鏁版嵁鍖呮暟閲? * </para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="314" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="314" bodyend="326"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type>NTSTATUS</type>
        <definition>NTSTATUS I2cDeviceWrite</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR DeviceAddress, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</argsstring>
        <name>I2cDeviceWrite</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>DeviceAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_reads_bytes_(Length) PVOID</type>
          <declname>Buffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>Length</declname>
        </param>
        <param>
          <type>_Out_opt_ PULONG</type>
          <declname>BytesWritten</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>I2cDeviceWrite - 鍚慖2C璁惧鍐欏叆鏁版嵁</para>
<para><parameterlist kind="param"><parameteritem>
<parameternamelist>
<parametername direction="in">Device</parametername>
</parameternamelist>
<parameterdescription>
<para>WDF璁惧瀵硅薄 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">DeviceAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>I2C璁惧鍦板潃 </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">RegisterAddress</parametername>
</parameternamelist>
<parameterdescription>
<para>瀵勫瓨鍣ㄥ湴鍧€ </para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Buffer</parametername>
</parameternamelist>
<parameterdescription>
<para>鏁版嵁缂撳啿鍖? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="in">Length</parametername>
</parameternamelist>
<parameterdescription>
<para>缂撳啿鍖洪暱搴? *</para>
</parameterdescription>
</parameteritem>
<parameteritem>
<parameternamelist>
<parametername direction="out">BytesWritten</parametername>
</parameternamelist>
<parameterdescription>
<para>瀹為檯鍐欏叆鐨勫瓧鑺傛暟</para>
</parameterdescription>
</parameteritem>
</parameterlist>
<simplesect kind="return"><para>NTSTATUS 鐘舵€佺爜 </para>
</simplesect>
</para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="253" column="1" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="253" bodyend="269"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(!NT_SUCCESS(status))</argsstring>
        <name>if</name>
        <param>
          <type>!</type>
          <declname>NT_SUCCESS</declname>
          <array>status</array>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="79" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="79" bodyend="82"/>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1ab29d05a3528131be0d35fe785e85590f" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;HalHandle !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kindref="member">HalHandle</ref> !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="144" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="144" bodyend="147"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189">HalDeviceClose</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a6957e0e0f326c7986a222f431530dc94" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;I2cConfig !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;<ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref> !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="385" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="385" bodyend="388"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a161904443c5f73d8654306b3fa8d29bb" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext-&gt;Interrupt !=NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt !</type>
          <defval>NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="139" column="9" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="139" bodyend="142"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(deviceContext==NULL)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref></type>
          <defval>=NULL</defval>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="58" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="58" bodyend="61"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>if</definition>
        <argsstring>(I2cConfig-&gt;InterruptEnabled)</argsstring>
        <name>if</name>
        <param>
          <type><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>-&gt;</type>
          <declname>InterruptEnabled</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="88" column="5" bodyfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" bodystart="88" bodyend="113"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" compoundref="i2c__device_8c" startline="32">EvtI2cInterruptDpc</references>
        <references refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" compoundref="i2c__device_8c" startline="31">EvtI2cInterruptIsr</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <references refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" compoundref="gpio__core_8c" startline="169">interruptConfig</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a90ec17a9895e508ccdb9077fed539682" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;I2C device initialized successfully&quot;)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;I2C device initialized successfully&quot;</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="128" column="5" declfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" declline="128" declcolumn="5"/>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1ae957556f2bac1175f6f4b37cd8f268f9" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;I2C read succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesRead ? *BytesRead :0)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;I2C read</type>
          <declname>succeeded</declname>
        </param>
        <param>
          <type>device:0x%</type>
          <declname>02X</declname>
        </param>
        <param>
          <type>register:0x%</type>
          <declname>02X</declname>
        </param>
        <param>
          <type>bytes:%d&quot;</type>
        </param>
        <param>
          <type><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref></type>
        </param>
        <param>
          <type>RegisterAddress</type>
        </param>
        <param>
          <type>BytesRead ? *BytesRead :0</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="242" column="9" declfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" declline="242" declcolumn="9"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1aabdf6e2791329ae7f1d903b2c7b47add" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;I2C transfer sequence succeeded, transfers: %d&quot;, TransferCount)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;I2C transfer sequence</type>
          <declname>succeeded</declname>
        </param>
        <param>
          <type>transfers:%d&quot;</type>
        </param>
        <param>
          <type>TransferCount</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="352" column="9" declfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" declline="352" declcolumn="9"/>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1a6e8f3cbefed6c462cd6392131f5f0a29" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>LogInfo</definition>
        <argsstring>(__FUNCTION__, __LINE__, &quot;I2C write succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, DeviceAddress, RegisterAddress, BytesWritten ? *BytesWritten :0)</argsstring>
        <name>LogInfo</name>
        <param>
          <type>__FUNCTION__</type>
        </param>
        <param>
          <type>__LINE__</type>
        </param>
        <param>
          <type>&quot;I2C write</type>
          <declname>succeeded</declname>
        </param>
        <param>
          <type>device:0x%</type>
          <declname>02X</declname>
        </param>
        <param>
          <type>register:0x%</type>
          <declname>02X</declname>
        </param>
        <param>
          <type>bytes:%d&quot;</type>
        </param>
        <param>
          <type><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref></type>
        </param>
        <param>
          <type>RegisterAddress</type>
        </param>
        <param>
          <type>BytesWritten ? *BytesWritten :0</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="303" column="9" declfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" declline="303" declcolumn="9"/>
        <references refid="i2c__device_8h_1a862821561008426245a34e458d02a093" compoundref="i2c__device_8h" startline="24">DeviceAddress</references>
      </memberdef>
      <memberdef kind="function" id="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type></type>
        <definition>RtlCopyMemory</definition>
        <argsstring>(deviceContext-&gt;I2cConfig, I2cConfig, sizeof(I2C_CONFIG))</argsstring>
        <name>RtlCopyMemory</name>
        <param>
          <type><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;</type>
          <declname>I2cConfig</declname>
        </param>
        <param>
          <type><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></type>
        </param>
        <param>
          <type>sizeof(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>)</type>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c" line="85" column="5" declfile="C:/KMDF Driver1/src/hal/devices/i2c_device.c" declline="85" declcolumn="5"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" compoundref="i2c__device_8c" startline="19">I2cConfig</references>
        <referencedby refid="device__manager_8c_1aebab0b9bc330432c9faaf78df6cfb6b2" compoundref="device__manager_8c" startline="278" endline="310">DeviceInitContext</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="driver__core_8c_1acdb452dbcae039af8967376463c758b9" compoundref="driver__core_8c" startline="62" endline="107">DriverCoreInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>i2c_device.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>I2C鐠佹儳顦す鍗炲З鐎圭偟骞?</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>閹绘劒绶甸柅姘辨暏I2C鐠佹儳顦惃鍕徔娴ｆ挸鐤勯悳甯礉閸╄桨绨幋鎴滄粦閻ㄥ嫭膩閸ф瀵查弸鑸电€?</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="hal__interface_8h" kindref="compound">../../../include/hal/hal_interface.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__i2c_8h" kindref="compound">../../../include/hal/bus/kmdf_i2c.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight><highlight class="comment">//<sp/>I2C鐠佹儳顦稉濠佺瑓閺傚洨绮ㄩ弸?typedef<sp/>struct<sp/>_I2C_DEVICE_CONTEXT<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="i2c__device_8c_1a11ec07dcb5c1cea421134a0b149443a5" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="gpio__core_8c_1a11ec07dcb5c1cea421134a0b149443a5" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF鐠佹儳顦€电钖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFMEMORY<sp/><ref refid="i2c__device_8c_1aff83b2530e0944d77d4ee0965b41ad89" kindref="member">ConfigurationMemory</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹儳顦柊宥囩枂閸愬懎鐡?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref><sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>I2C闁板秶鐤?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20" refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a2f4ba870132c1fd57e2d74ba94e39805" kindref="member">HAL_DEVICE_HANDLE</ref><sp/><ref refid="i2c__device_8c_1a96ba6885a1d23da9ee577cfc9b91ae60" kindref="member">HalHandle</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>HAL鐠佹儳顦崣銉︾労</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="21" refid="i2c__device_8c_1ae49d231a428d107c888f925e845daf62" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="i2c__device_8c_1ae49d231a428d107c888f925e845daf62" kindref="member">DeviceInitialized</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹儳顦崚婵嗩潗閸栨牗鐖ｈ箛?<sp/><sp/><sp/><sp/>WDFINTERRUPT<sp/>Interrupt;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>娑擃厽鏌囩€电钖勯敍鍫濐洤閺堝绱?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="i2c__device_8c_1a63212990a463669c2face6cfbfd28d26" kindref="member">TransactionCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹绨ㄩ崝陇顓搁弫?<sp/><sp/><sp/><sp/>ULONG<sp/>ErrorCount;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>闁挎瑨顕ょ拋鈩冩殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23" refid="i2c__device_8c_1aa89f192944d335c9a60e5858325ed89a" refkind="member"><highlight class="normal">}<sp/><ref refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" kindref="member">I2C_DEVICE_CONTEXT</ref>,<sp/>*<ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref>;</highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="comment">//<sp/>娴犲豆DF鐠佹儳顦懢宄板絿I2C鐠佹儳顦稉濠佺瑓閺?WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(I2C_DEVICE_CONTEXT,<sp/>GetI2cDeviceContext)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>姒涙顓籌2C娴滃濮熺搾鍛閺冨爼妫?(濮ｎ偆顫?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="i2c__device_8c_1a4bbdac9bba21cb3959a7355001ea590f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>I2C_DEFAULT_TIMEOUT<sp/>1000</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight><highlight class="comment">//<sp/>閸撳秴鎮滄竟鐗堟<sp/>I/O<sp/>娴滃娆㈤崶鐐剁殶閸戣姤鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_ISR<sp/><ref refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" kindref="member">EvtI2cInterruptIsr</ref>;</highlight></codeline>
<codeline lineno="32" refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" refkind="member"><highlight class="normal">EVT_WDF_INTERRUPT_DPC<sp/><ref refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" kindref="member">EvtI2cInterruptDpc</ref>;</highlight></codeline>
<codeline lineno="33" refid="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" refkind="member"><highlight class="normal">EVT_WDF_REQUEST_COMPLETION_ROUTINE<sp/><ref refid="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" kindref="member">EvtI2cRequestCompletion</ref>;</highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="36"><highlight class="comment"><sp/>*<sp/>I2cDeviceInitialize<sp/>-<sp/>閸掓繂顫愰崠鏈?C鐠佹儳顦?</highlight></codeline>
<codeline lineno="37"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="39" refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1ab0c3b778b5a363d418c3d768cdb1e2d4" kindref="member">I2cDeviceInitialize</ref>(</highlight></codeline>
<codeline lineno="40"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="41"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__i2c_8h_1a9d4df46fafece7b304c57d2e0e1bfd51" kindref="member">PI2C_CONFIG</ref><sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref></highlight></codeline>
<codeline lineno="42"><highlight class="normal">)</highlight></codeline>
<codeline lineno="43"><highlight class="normal">{</highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a9de221d82717e7709cdfc025378bc222" kindref="member">HAL_DEVICE_CONFIG</ref><sp/>halConfig<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_INTERRUPT_CONFIG<sp/><ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>;</highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>interruptAttributes;</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>I2C<sp/>device&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>I2cConfig<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58" refid="i2c__device_8c_1a9d2d77fd6fa0d75751b40049e614b00b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_UNSUCCESSFUL;</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍啎婢跺洣绗傛稉瀣瀮</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="67" refid="i2c__device_8c_1ab9707a002fb8033fdc202e8c8b8f8569" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓稑缂揑2C闁板秶鐤嗛崘鍛摠閸擃垱婀?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="70" refid="i2c__device_8c_1a9611b3a00430a86619b5923de30f9fdb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfMemoryCreate(</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_NO_OBJECT_ATTRIBUTES,</highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NonPagedPoolNx,</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0,</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>),</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory,</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>(PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="79" refid="i2c__device_8c_1a1a243a15dd793b6d0f7b7011461a8641" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>configuration<sp/>memory&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢跺秴鍩桰2C闁板秶鐤?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="85" refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig,<sp/><ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>));</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵″倹鐏夌拋鎯ь槵閺€顖涘瘮娑擃厽鏌囬敍灞藉灥婵瀵叉稉顓熸焽鐎电钖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="88" refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="i2c__device_8c_1a3e1e82f2b44144b87469685950b3b501" kindref="member">I2cConfig</ref>-&gt;InterruptEnabled)<sp/>{</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Configuring<sp/>device<sp/>interrupt&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_INTERRUPT_CONFIG_INIT(</highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>,</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" kindref="member">EvtI2cInterruptIsr</ref>,</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" kindref="member">EvtI2cInterruptDpc</ref></highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;interruptAttributes,</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1abad9f6d84bae65aada7d4a1cfcc2ba12" kindref="member">I2C_DEVICE_CONTEXT</ref></highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfInterruptCreate(</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Device,</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ac6c56d4f54252f6088c0d841efbc597e" kindref="member">interruptConfig</ref>,</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;interruptAttributes,</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>interrupt<sp/>object&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>闁板秶鐤咹AL鐠佹儳顦?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="116" refid="i2c__device_8c_1abf852046373359fb294f66a784b38263" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.DeviceType<sp/>=<sp/><ref refid="hal__interface_8h_1ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d" kindref="member">HalDeviceI2C</ref>;</highlight></codeline>
<codeline lineno="117" refid="i2c__device_8c_1aaea9f9b32650901ecb0d31cb5066cd7f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.Flags<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="118" refid="i2c__device_8c_1a40d2c447ac37fcd86673f2a11b2ca094" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.PrivateData<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig;</highlight></codeline>
<codeline lineno="119" refid="i2c__device_8c_1a0470f3b47bad91bd5e08004c87a8d98a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>halConfig.PrivateDataSize<sp/>=<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a8275fd1e76bc02628ddb4cf647c947c4" kindref="member">I2C_CONFIG</ref>);</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掓繂顫愰崠鏍啎婢跺槗AL鐏?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>HalDeviceOpen(Device,<sp/>&amp;halConfig,<sp/>&amp;deviceContext-&gt;HalHandle);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>open<sp/>HAL<sp/>device&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">goto</highlight><highlight class="normal"><sp/>Exit;</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐎瑰本鍨氶崚婵嗩潗閸?<sp/><sp/><sp/><sp/>deviceContext-&gt;DeviceInitialized<sp/>=<sp/>TRUE;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="128" refid="i2c__device_8c_1a90ec17a9895e508ccdb9077fed539682" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>device<sp/>initialized<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="130" refid="i2c__device_8c_1aee3e2abd9dd27ddfc3e158a9ffce1746" refkind="member"><highlight class="normal">Exit:</highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="132"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>濞撳懐鎮婃径杈Е閻ㄥ嫬鍨垫慨瀣</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="133"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="134"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory);</highlight></codeline>
<codeline lineno="135"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="136"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="137"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="138"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="139" refid="i2c__device_8c_1a161904443c5f73d8654306b3fa8d29bb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="140"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt);</highlight></codeline>
<codeline lineno="141"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Interrupt<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="142"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="144" refid="i2c__device_8c_1ab29d05a3528131be0d35fe785e85590f" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189" kindref="member">HalDeviceClose</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle);</highlight></codeline>
<codeline lineno="146"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="151"><highlight class="normal">}</highlight></codeline>
<codeline lineno="152"><highlight class="normal"></highlight></codeline>
<codeline lineno="153"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="154"><highlight class="comment"><sp/>*<sp/>I2cDeviceCleanup<sp/>-<sp/>濞撳懐鎮奍2C鐠佹儳顦挧鍕爱</highlight></codeline>
<codeline lineno="155"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="156"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="157" refid="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1a5da67a960d3cf99caa6874438a84629b" kindref="member">I2cDeviceCleanup</ref>(</highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="159"><highlight class="normal">)</highlight></codeline>
<codeline lineno="160"><highlight class="normal">{</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Cleaning<sp/>up<sp/>I2C<sp/>device<sp/>resources&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="167"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="168"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="169"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="170"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="171"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸忔娊妫碒AL鐠佹儳顦崣銉︾労</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="172"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="173"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="hal__interface_8h_1a40a0e8d142c3033b41a5ad463c064189" kindref="member">HalDeviceClose</ref>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle);</highlight></codeline>
<codeline lineno="174"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="175"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="176"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="177"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸掔娀娅庨柊宥囩枂閸愬懎鐡?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="178"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="179"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory);</highlight></codeline>
<codeline lineno="180"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ConfigurationMemory<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="181"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="182"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="183"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="184"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="185"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>device<sp/>cleanup<sp/>completed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="186"><highlight class="normal">}</highlight></codeline>
<codeline lineno="187"><highlight class="normal"></highlight></codeline>
<codeline lineno="188"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="189"><highlight class="comment"><sp/>*<sp/>I2cDeviceRead<sp/>-<sp/>娴犲丢2C鐠佹儳顦拠璇插絿閺佺増宓?</highlight></codeline>
<codeline lineno="190"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="191"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="192" refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1a6576f1e3485d12c22c444244044c1d30" kindref="member">I2cDeviceRead</ref>(</highlight></codeline>
<codeline lineno="193"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="194"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,</highlight></codeline>
<codeline lineno="195"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="196"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="197"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="198"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesRead</highlight></codeline>
<codeline lineno="199"><highlight class="normal">)</highlight></codeline>
<codeline lineno="200"><highlight class="normal">{</highlight></codeline>
<codeline lineno="201"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="202"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="203"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref><sp/>packet<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="204"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="205"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Buffer<sp/>==<sp/>NULL<sp/>||<sp/>Length<sp/>==<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="206"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="207"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="208"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="209"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="210"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="211"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="212"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="213"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="214"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="215"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="216"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嬪嫬缂揑2C娴肩姾绶崠?<sp/><sp/><sp/><sp/>packet.DeviceAddress<sp/>=<sp/>DeviceAddress;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="217" refid="i2c__device_8c_1a63268b1e9e5ee12309a44d8d6c9fc652" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DataAddress<sp/>=<sp/>RegisterAddress;</highlight></codeline>
<codeline lineno="218" refid="i2c__device_8c_1a7cd334185ddc76afeacf2cd57615dc81" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Data<sp/>=<sp/>Buffer;</highlight></codeline>
<codeline lineno="219" refid="i2c__device_8c_1adb5ea4ad84a87209e6f98b3e012adbbf" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DataLength<sp/>=<sp/>Length;</highlight></codeline>
<codeline lineno="220"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Flags<sp/>=<sp/><ref refid="i2c__device_8h_1ad6922f686b3fc13f8365467975aba1d7" kindref="member">I2C_TRANSFER_READ</ref>;</highlight></codeline>
<codeline lineno="221" refid="i2c__device_8c_1af9a881dabb7ea1e15ee2808cca09fd6a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DelayInMicroseconds<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="222"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="223"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽I2C娴肩姾绶?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="224"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="hal__interface_8h_1ab3ade1341d2d05db754ab4a9e3ee7198" kindref="member">HalDeviceIoControl</ref>(</highlight></codeline>
<codeline lineno="225"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle,</highlight></codeline>
<codeline lineno="226"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a15204e5c2582622fc3ef40f01fe93322" kindref="member">IOCTL_I2C_TRANSFER</ref>,</highlight></codeline>
<codeline lineno="227"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;packet,</highlight></codeline>
<codeline lineno="228"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref>),</highlight></codeline>
<codeline lineno="229"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Buffer,</highlight></codeline>
<codeline lineno="230"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Length,</highlight></codeline>
<codeline lineno="231"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>BytesRead</highlight></codeline>
<codeline lineno="232"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="233"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="234"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="235"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮為柨娆掝嚖鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="236"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount);</highlight></codeline>
<codeline lineno="237"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>read<sp/>failed,<sp/>device:<sp/>0x%02X,<sp/>register:<sp/>0x%02X&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="238"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,<sp/>RegisterAddress);</highlight></codeline>
<codeline lineno="239" refid="i2c__device_8c_1a0544c3fe466e421738dae463968b70ba" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="240"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮炴禍瀣鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="241"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount);</highlight></codeline>
<codeline lineno="242" refid="i2c__device_8c_1ae957556f2bac1175f6f4b37cd8f268f9" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>read<sp/>succeeded,<sp/>device:<sp/>0x%02X,<sp/>register:<sp/>0x%02X,<sp/>bytes:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="243"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,<sp/>RegisterAddress,<sp/>BytesRead<sp/>?<sp/>*BytesRead<sp/>:<sp/>0);</highlight></codeline>
<codeline lineno="244"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="245"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="246"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="247"><highlight class="normal">}</highlight></codeline>
<codeline lineno="248"><highlight class="normal"></highlight></codeline>
<codeline lineno="249"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="250"><highlight class="comment"><sp/>*<sp/>I2cDeviceWrite<sp/>-<sp/>閸氭厲2C鐠佹儳顦崘娆忓弳閺佺増宓?</highlight></codeline>
<codeline lineno="251"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="252"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="253" refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1a580f2434082501937a3d8bc4d5591866" kindref="member">I2cDeviceWrite</ref>(</highlight></codeline>
<codeline lineno="254"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="255"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,</highlight></codeline>
<codeline lineno="256"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="257"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_bytes_(Length)<sp/>PVOID<sp/>Buffer,</highlight></codeline>
<codeline lineno="258"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>Length,</highlight></codeline>
<codeline lineno="259"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_opt_<sp/>PULONG<sp/>BytesWritten</highlight></codeline>
<codeline lineno="260"><highlight class="normal">)</highlight></codeline>
<codeline lineno="261"><highlight class="normal">{</highlight></codeline>
<codeline lineno="262"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="263"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="264"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref><sp/>packet<sp/>=<sp/>{0};</highlight></codeline>
<codeline lineno="265"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="266"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Buffer<sp/>==<sp/>NULL<sp/>||<sp/>Length<sp/>==<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="267"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="268"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="269"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="270"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="271"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="272"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="273"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="274"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="275"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="276"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="277"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閺嬪嫬缂揑2C娴肩姾绶崠?<sp/><sp/><sp/><sp/>packet.DeviceAddress<sp/>=<sp/>DeviceAddress;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="278"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DataAddress<sp/>=<sp/>RegisterAddress;</highlight></codeline>
<codeline lineno="279"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Data<sp/>=<sp/>Buffer;</highlight></codeline>
<codeline lineno="280"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DataLength<sp/>=<sp/>Length;</highlight></codeline>
<codeline lineno="281"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.Flags<sp/>=<sp/>I2C_TRANSFER_WRITE;</highlight></codeline>
<codeline lineno="282"><highlight class="normal"><sp/><sp/><sp/><sp/>packet.DelayInMicroseconds<sp/>=<sp/>0;</highlight></codeline>
<codeline lineno="283"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="284"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽I2C娴肩姾绶?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="285"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="hal__interface_8h_1ab3ade1341d2d05db754ab4a9e3ee7198" kindref="member">HalDeviceIoControl</ref>(</highlight></codeline>
<codeline lineno="286"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle,</highlight></codeline>
<codeline lineno="287"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a15204e5c2582622fc3ef40f01fe93322" kindref="member">IOCTL_I2C_TRANSFER</ref>,</highlight></codeline>
<codeline lineno="288"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;packet,</highlight></codeline>
<codeline lineno="289"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref>),</highlight></codeline>
<codeline lineno="290"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,</highlight></codeline>
<codeline lineno="291"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0,</highlight></codeline>
<codeline lineno="292"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>BytesWritten</highlight></codeline>
<codeline lineno="293"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="294"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="295"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="296"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮為柨娆掝嚖鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="297"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount);</highlight></codeline>
<codeline lineno="298"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>write<sp/>failed,<sp/>device:<sp/>0x%02X,<sp/>register:<sp/>0x%02X&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="299"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,<sp/>RegisterAddress);</highlight></codeline>
<codeline lineno="300"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="301"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮炴禍瀣鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="302"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount);</highlight></codeline>
<codeline lineno="303" refid="i2c__device_8c_1a6e8f3cbefed6c462cd6392131f5f0a29" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>write<sp/>succeeded,<sp/>device:<sp/>0x%02X,<sp/>register:<sp/>0x%02X,<sp/>bytes:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="304"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a862821561008426245a34e458d02a093" kindref="member">DeviceAddress</ref>,<sp/>RegisterAddress,<sp/>BytesWritten<sp/>?<sp/>*BytesWritten<sp/>:<sp/>0);</highlight></codeline>
<codeline lineno="305"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="306"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="307"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="308"><highlight class="normal">}</highlight></codeline>
<codeline lineno="309"><highlight class="normal"></highlight></codeline>
<codeline lineno="310"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="311"><highlight class="comment"><sp/>*<sp/>I2cDeviceTransfer<sp/>-<sp/>閹笛嗩攽婢跺秵娼匢2C娴肩姾绶?</highlight></codeline>
<codeline lineno="312"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="313"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="314" refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1ad84f26684684313ff193803d1d9c7c32" kindref="member">I2cDeviceTransfer</ref>(</highlight></codeline>
<codeline lineno="315"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="316"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_(TransferCount)<sp/><ref refid="kmdf__i2c_8h_1a26d8a1f8a56e4808ad0856f1dc02461c" kindref="member">PI2C_TRANSFER_PACKET</ref><sp/>Transfers,</highlight></codeline>
<codeline lineno="317"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TransferCount</highlight></codeline>
<codeline lineno="318"><highlight class="normal">)</highlight></codeline>
<codeline lineno="319"><highlight class="normal">{</highlight></codeline>
<codeline lineno="320"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="321"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="322"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="323"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Transfers<sp/>==<sp/>NULL<sp/>||<sp/>TransferCount<sp/>==<sp/>0)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="324"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="325"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="326"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="327"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="328"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="329"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL<sp/>||<sp/>!<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="330"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Device<sp/>not<sp/>initialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="331"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_DEVICE_NOT_READY;</highlight></codeline>
<codeline lineno="332"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="333"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="334"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閹笛嗩攽I2C娴肩姾绶惔蹇撳灙</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="335"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="hal__interface_8h_1ab3ade1341d2d05db754ab4a9e3ee7198" kindref="member">HalDeviceIoControl</ref>(</highlight></codeline>
<codeline lineno="336"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;HalHandle,</highlight></codeline>
<codeline lineno="337"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="i2c__device_8h_1a478905e3b3f7f700dd9e0975f42fe1a3" kindref="member">IOCTL_I2C_TRANSFER_SEQUENCE</ref>,</highlight></codeline>
<codeline lineno="338"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Transfers,</highlight></codeline>
<codeline lineno="339"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__i2c_8h_1a941c9f88004c4f54719bc4a3b7083fff" kindref="member">I2C_TRANSFER_PACKET</ref>)<sp/>*<sp/>TransferCount,</highlight></codeline>
<codeline lineno="340"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL,</highlight></codeline>
<codeline lineno="341"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>0,</highlight></codeline>
<codeline lineno="342"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>NULL</highlight></codeline>
<codeline lineno="343"><highlight class="normal"><sp/><sp/><sp/><sp/>);</highlight></codeline>
<codeline lineno="344"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="345"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="346"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮為柨娆掝嚖鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="347"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount);</highlight></codeline>
<codeline lineno="348"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>transfer<sp/>sequence<sp/>failed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="349"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="350"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婢х偛濮炴禍瀣鐠佲剝鏆?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="351"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedExchangeAdd(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount,<sp/>TransferCount);</highlight></codeline>
<codeline lineno="352" refid="i2c__device_8c_1aabdf6e2791329ae7f1d903b2c7b47add" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>transfer<sp/>sequence<sp/>succeeded,<sp/>transfers:<sp/>%d&quot;</highlight><highlight class="normal">,<sp/>TransferCount);</highlight></codeline>
<codeline lineno="353"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="354"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="355"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="356"><highlight class="normal">}</highlight></codeline>
<codeline lineno="357"><highlight class="normal"></highlight></codeline>
<codeline lineno="358"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="359"><highlight class="comment"><sp/>*<sp/>I2cDeviceGetStatistics<sp/>-<sp/>閼惧嘲褰嘔2C鐠佹儳顦紒鐔活吀娣団剝浼?</highlight></codeline>
<codeline lineno="360"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="361"><highlight class="normal">NTSTATUS</highlight></codeline>
<codeline lineno="362" refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1a709aca0009ccfb39adebbdd9ce97e252" kindref="member">I2cDeviceGetStatistics</ref>(</highlight></codeline>
<codeline lineno="363"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="364"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/><ref refid="i2c__device_8h_1aeb652cfe1149dff5ee42abab74d96813" kindref="member">PI2C_STATISTICS</ref><sp/>Statistics</highlight></codeline>
<codeline lineno="365"><highlight class="normal">)</highlight></codeline>
<codeline lineno="366"><highlight class="normal">{</highlight></codeline>
<codeline lineno="367"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="368"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="369"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸欏倹鏆熷Λ鈧弻?<sp/><sp/><sp/><sp/>if<sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/>Statistics<sp/>==<sp/>NULL)<sp/>{</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="370"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="371"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="372"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="373"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="374"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拋鎯ь槵娑撳﹣绗呴弬?<sp/><sp/><sp/><sp/>deviceContext<sp/>=<sp/>GetI2cDeviceContext(Device);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="375"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="376"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(ERROR_INVALID_DEVICE_STATE,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>get<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="377"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>STATUS_UNSUCCESSFUL;</highlight></codeline>
<codeline lineno="378"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="379"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="380"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>婵夘偄鍘栫紒鐔活吀娣団剝浼?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="381"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;TransactionCount<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransactionCount;</highlight></codeline>
<codeline lineno="382"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;ErrorCount<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount;</highlight></codeline>
<codeline lineno="383"><highlight class="normal"><sp/><sp/><sp/><sp/>Statistics-&gt;DeviceInitialized<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized;</highlight></codeline>
<codeline lineno="384"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="385" refid="i2c__device_8c_1a6957e0e0f326c7986a222f431530dc94" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="386"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Statistics-&gt;BusClockFrequency<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig-&gt;ClockFrequency;</highlight></codeline>
<codeline lineno="387"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Statistics-&gt;BusNumber<sp/>=<sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;I2cConfig-&gt;BusNumber;</highlight></codeline>
<codeline lineno="388"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="389"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="390" refid="i2c__device_8c_1a77b4762318f24dff847f94f382cfeea6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="391"><highlight class="normal">}</highlight></codeline>
<codeline lineno="392"><highlight class="normal"></highlight></codeline>
<codeline lineno="393"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="394"><highlight class="comment"><sp/>*<sp/>EvtI2cInterruptIsr<sp/>-<sp/>I2C鐠佹儳顦稉顓熸焽閺堝秴濮熸笟瀣柤</highlight></codeline>
<codeline lineno="395"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="396"><highlight class="normal">BOOLEAN</highlight></codeline>
<codeline lineno="397" refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" refkind="member"><highlight class="normal"><ref refid="i2c__device_8c_1ab9ce8c5b03a473cd94f2812fa8622837" kindref="member">EvtI2cInterruptIsr</ref>(</highlight></codeline>
<codeline lineno="398"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="399"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>MessageID</highlight></codeline>
<codeline lineno="400"><highlight class="normal">)</highlight></codeline>
<codeline lineno="401"><highlight class="normal">{</highlight></codeline>
<codeline lineno="402"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device<sp/>=<sp/>WdfInterruptGetDevice(Interrupt);</highlight></codeline>
<codeline lineno="403"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2cDeviceContext(device);</highlight></codeline>
<codeline lineno="404"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="405"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(MessageID);</highlight></codeline>
<codeline lineno="406"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="407"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸︹問SR娑擃厺绗夐幍褑顢戞径宥嗘絽閹垮秳缍旈敍灞藉涧閺嶅洩顔囨稉顓熸焽瀹歌尪袝閸欐垵鑻熺拫鍐ㄥDPC</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="408"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>interrupt<sp/>received&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="409"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="410"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠囬攱鐪癉PC婢跺嫮鎮?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="411"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfInterruptQueueDpcForIsr(Interrupt);</highlight></codeline>
<codeline lineno="412"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="413"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/>TRUE;<sp/></highlight><highlight class="comment">//<sp/>鐞涖劎銇氬鎻掝槱閻炲棔鑵戦弬?}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="414"><highlight class="normal"></highlight></codeline>
<codeline lineno="415"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="416"><highlight class="comment"><sp/>*<sp/>EvtI2cInterruptDpc<sp/>-<sp/>I2C鐠佹儳顦鎯扮箿鏉╁洨鈻肩拫鍐暏</highlight></codeline>
<codeline lineno="417"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="418"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="419"><highlight class="normal"><ref refid="i2c__device_8c_1a02a7df72146aec5cbd7c5a854a9adcda" kindref="member">EvtI2cInterruptDpc</ref>(</highlight></codeline>
<codeline lineno="420"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFINTERRUPT<sp/>Interrupt,</highlight></codeline>
<codeline lineno="421"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a3921100f8f21c9e6503c02e51eb84644" kindref="member">WDFOBJECT</ref><sp/>AssociatedObject</highlight></codeline>
<codeline lineno="422"><highlight class="normal">)</highlight></codeline>
<codeline lineno="423"><highlight class="normal">{</highlight></codeline>
<codeline lineno="424"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>device<sp/>=<sp/>WdfInterruptGetDevice(Interrupt);</highlight></codeline>
<codeline lineno="425"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetI2cDeviceContext(device);</highlight></codeline>
<codeline lineno="426"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="427"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(AssociatedObject);</highlight></codeline>
<codeline lineno="428"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="429"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閸︹€昉C娑擃厽澧界悰瀛?C娑擃厽鏌囨径鍕倞</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="430"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Processing<sp/>I2C<sp/>interrupt<sp/>in<sp/>DPC&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="431"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="432"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>TODO:<sp/>閺嶈宓侀悧鐟扮暰鐠佹儳顦惃鍕付鐟曚礁顦╅悶鍡曡厬閺傤厺绨ㄦ禒?}</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="433"><highlight class="normal"></highlight></codeline>
<codeline lineno="434"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="435"><highlight class="comment"><sp/>*<sp/>EvtI2cRequestCompletion<sp/>-<sp/>I2C鐠囬攱鐪扮€瑰本鍨氭笟瀣柤</highlight></codeline>
<codeline lineno="436"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="437"><highlight class="normal">VOID</highlight></codeline>
<codeline lineno="438"><highlight class="normal"><ref refid="i2c__device_8c_1a99bc0f18d2d6fca4d292cd4026a2435f" kindref="member">EvtI2cRequestCompletion</ref>(</highlight></codeline>
<codeline lineno="439"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a5bbb7f7db295e12f4f24ca6ed92554f3" kindref="member">WDFREQUEST</ref><sp/>Request,</highlight></codeline>
<codeline lineno="440"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFIOTARGET<sp/>Target,</highlight></codeline>
<codeline lineno="441"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>PWDF_REQUEST_COMPLETION_PARAMS<sp/>Params,</highlight></codeline>
<codeline lineno="442"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>WDFCONTEXT<sp/>Context</highlight></codeline>
<codeline lineno="443"><highlight class="normal">)</highlight></codeline>
<codeline lineno="444"><highlight class="normal">{</highlight></codeline>
<codeline lineno="445"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="446"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>(<ref refid="i2c__core_8c_1a9f0733f4be9833c3a734164c7711fbe5" kindref="member">PI2C_DEVICE_CONTEXT</ref>)Context;</highlight></codeline>
<codeline lineno="447"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="448"><highlight class="normal"><sp/><sp/><sp/><sp/>UNREFERENCED_PARAMETER(Target);</highlight></codeline>
<codeline lineno="449"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="450"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>閼惧嘲褰囩拠閿嬬湴閻樿埖鈧?<sp/><sp/><sp/><sp/>status<sp/>=<sp/>WdfRequestGetStatus(Request);</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="451"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="452"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐠佹澘缍嶇拠閿嬬湴鐎瑰本鍨氱紒鎾寸亯</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="453"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="454"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>request<sp/>completed<sp/>successfully,<sp/>bytes<sp/>transferred:<sp/>%d&quot;</highlight><highlight class="normal">,</highlight></codeline>
<codeline lineno="455"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>Params-&gt;IoStatus.Information);</highlight></codeline>
<codeline lineno="456"><highlight class="normal"><sp/><sp/><sp/><sp/>}<sp/></highlight><highlight class="keywordflow">else</highlight><highlight class="normal"><sp/>{</highlight></codeline>
<codeline lineno="457"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;I2C<sp/>request<sp/>failed&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="458"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>InterlockedIncrement(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;ErrorCount);</highlight></codeline>
<codeline lineno="459"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="460"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="461"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>鐎瑰本鍨氶崢鐔奉潗鐠囬攱鐪?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="462"><highlight class="normal"><sp/><sp/><sp/><sp/>WdfRequestComplete(Request,<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>);</highlight></codeline>
<codeline lineno="463"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/devices/i2c_device.c"/>
  </compounddef>
</doxygen>
