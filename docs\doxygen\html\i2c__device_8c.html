<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/devices/i2c_device.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('i2c__device_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">i2c_device.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
<code>#include &quot;<a class="el" href="hal__interface_8h_source.html">../../../include/hal/hal_interface.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="kmdf__i2c_8h_source.html">../../../include/hal/bus/kmdf_i2c.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for i2c_device.c:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2devices_2i2c__device_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2i2c__device_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2devices_2i2c__device_8c">
<area shape="rect" title=" " alt="" coords="218,5,386,48"/>
<area shape="rect" title=" " alt="" coords="271,368,336,395"/>
<area shape="poly" title=" " alt="" coords="359,46,404,68,452,98,497,137,516,160,531,186,540,218,533,248,515,276,487,300,454,322,419,340,352,368,349,363,416,336,452,317,484,296,510,272,528,246,534,218,526,188,512,163,493,141,449,103,401,73,357,51"/>
<area shape="rect" title=" " alt="" coords="272,285,325,312"/>
<area shape="poly" title=" " alt="" coords="311,48,329,127,333,178,329,230,314,272,309,270,324,229,328,178,324,128,306,49"/>
<area shape="rect" href="hal__interface_8h.html" title=" " alt="" coords="24,187,143,229"/>
<area shape="poly" title=" " alt="" coords="255,51,216,72,177,98,139,136,108,176,103,172,135,133,174,94,213,67,253,46"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="39,277,157,320"/>
<area shape="poly" title=" " alt="" coords="218,49,162,68,104,97,77,115,52,136,31,160,14,188,8,208,14,228,28,249,47,266,44,270,24,252,9,230,3,208,9,186,26,157,48,132,73,111,101,92,160,63,217,44"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="188,96,307,139"/>
<area shape="poly" title=" " alt="" coords="292,50,270,84,266,81,287,47"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="389,187,517,229"/>
<area shape="poly" title=" " alt="" coords="326,46,372,94,433,172,428,176,368,98,322,50"/>
<area shape="poly" title=" " alt="" coords="112,228,277,356,274,360,108,232"/>
<area shape="poly" title=" " alt="" coords="135,227,258,278,256,283,133,232"/>
<area shape="poly" title=" " alt="" coords="90,229,95,262,90,263,84,230"/>
<area shape="poly" title=" " alt="" coords="152,318,258,359,256,364,150,323"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="168,195,314,221"/>
<area shape="poly" title=" " alt="" coords="249,139,246,179,240,179,243,139"/>
<area shape="poly" title=" " alt="" coords="251,220,284,271,280,274,247,223"/>
<area shape="poly" title=" " alt="" coords="222,224,146,271,143,267,219,220"/>
<area shape="poly" title=" " alt="" coords="437,232,326,358,322,354,433,228"/>
<area shape="poly" title=" " alt="" coords="418,232,335,280,332,275,415,228"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a4bbdac9bba21cb3959a7355001ea590f" id="r_a4bbdac9bba21cb3959a7355001ea590f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4bbdac9bba21cb3959a7355001ea590f">I2C_DEFAULT_TIMEOUT</a>&#160;&#160;&#160;1000</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a50d1319f95a5bfb01ed5c3ab0f60bf8b" id="r_a50d1319f95a5bfb01ed5c3ab0f60bf8b"><td class="memItemLeft" align="right" valign="top">BOOLEAN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a50d1319f95a5bfb01ed5c3ab0f60bf8b">EvtI2cInterruptIsr</a> (_In_ WDFINTERRUPT Interrupt, _In_ ULONG MessageID)</td></tr>
<tr class="memitem:a5da67a960d3cf99caa6874438a84629b" id="r_a5da67a960d3cf99caa6874438a84629b"><td class="memItemLeft" align="right" valign="top">VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5da67a960d3cf99caa6874438a84629b">I2cDeviceCleanup</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device)</td></tr>
<tr class="memitem:a709aca0009ccfb39adebbdd9ce97e252" id="r_a709aca0009ccfb39adebbdd9ce97e252"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a709aca0009ccfb39adebbdd9ce97e252">I2cDeviceGetStatistics</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _Out_ <a class="el" href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a> Statistics)</td></tr>
<tr class="memitem:ab0c3b778b5a363d418c3d768cdb1e2d4" id="r_ab0c3b778b5a363d418c3d768cdb1e2d4"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab0c3b778b5a363d418c3d768cdb1e2d4">I2cDeviceInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a> <a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>)</td></tr>
<tr class="memitem:a6576f1e3485d12c22c444244044c1d30" id="r_a6576f1e3485d12c22c444244044c1d30"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6576f1e3485d12c22c444244044c1d30">I2cDeviceRead</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR <a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>, _In_ UCHAR RegisterAddress, _Out_writes_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesRead)</td></tr>
<tr class="memitem:ad84f26684684313ff193803d1d9c7c32" id="r_ad84f26684684313ff193803d1d9c7c32"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad84f26684684313ff193803d1d9c7c32">I2cDeviceTransfer</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_reads_(TransferCount) <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a> Transfers, _In_ ULONG TransferCount)</td></tr>
<tr class="memitem:a580f2434082501937a3d8bc4d5591866" id="r_a580f2434082501937a3d8bc4d5591866"><td class="memItemLeft" align="right" valign="top">NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a580f2434082501937a3d8bc4d5591866">I2cDeviceWrite</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ UCHAR <a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>, _In_ UCHAR RegisterAddress, _In_reads_bytes_(Length) PVOID Buffer, _In_ ULONG Length, _Out_opt_ PULONG BytesWritten)</td></tr>
<tr class="memitem:a1a243a15dd793b6d0f7b7011461a8641" id="r_a1a243a15dd793b6d0f7b7011461a8641"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a243a15dd793b6d0f7b7011461a8641">if</a> (!<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="gpio__core_8c.html#a9611b3a00430a86619b5923de30f9fdb">status</a>))</td></tr>
<tr class="memitem:ab29d05a3528131be0d35fe785e85590f" id="r_ab29d05a3528131be0d35fe785e85590f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab29d05a3528131be0d35fe785e85590f">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a> !=NULL)</td></tr>
<tr class="memitem:a6957e0e0f326c7986a222f431530dc94" id="r_a6957e0e0f326c7986a222f431530dc94"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6957e0e0f326c7986a222f431530dc94">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a> !=NULL)</td></tr>
<tr class="memitem:a161904443c5f73d8654306b3fa8d29bb" id="r_a161904443c5f73d8654306b3fa8d29bb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a161904443c5f73d8654306b3fa8d29bb">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;Interrupt !=NULL)</td></tr>
<tr class="memitem:a9d2d77fd6fa0d75751b40049e614b00b" id="r_a9d2d77fd6fa0d75751b40049e614b00b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9d2d77fd6fa0d75751b40049e614b00b">if</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>==NULL)</td></tr>
<tr class="memitem:adfac0a96ec8249c69bd820670db7f2cd" id="r_adfac0a96ec8249c69bd820670db7f2cd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adfac0a96ec8249c69bd820670db7f2cd">if</a> (<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>-&gt;InterruptEnabled)</td></tr>
<tr class="memitem:a90ec17a9895e508ccdb9077fed539682" id="r_a90ec17a9895e508ccdb9077fed539682"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a90ec17a9895e508ccdb9077fed539682">LogInfo</a> (__FUNCTION__, __LINE__, &quot;I2C device initialized successfully&quot;)</td></tr>
<tr class="memitem:ae957556f2bac1175f6f4b37cd8f268f9" id="r_ae957556f2bac1175f6f4b37cd8f268f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae957556f2bac1175f6f4b37cd8f268f9">LogInfo</a> (__FUNCTION__, __LINE__, &quot;I2C read succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, <a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>, RegisterAddress, BytesRead ? *BytesRead :0)</td></tr>
<tr class="memitem:aabdf6e2791329ae7f1d903b2c7b47add" id="r_aabdf6e2791329ae7f1d903b2c7b47add"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aabdf6e2791329ae7f1d903b2c7b47add">LogInfo</a> (__FUNCTION__, __LINE__, &quot;I2C transfer sequence succeeded, transfers: %d&quot;, TransferCount)</td></tr>
<tr class="memitem:a6e8f3cbefed6c462cd6392131f5f0a29" id="r_a6e8f3cbefed6c462cd6392131f5f0a29"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6e8f3cbefed6c462cd6392131f5f0a29">LogInfo</a> (__FUNCTION__, __LINE__, &quot;I2C write succeeded, device: 0x%02X, register: 0x%02X, bytes: %d&quot;, <a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a>, RegisterAddress, BytesWritten ? *BytesWritten :0)</td></tr>
<tr class="memitem:ae00ba03b0ccf840fa864cc07b330dbd0" id="r_ae00ba03b0ccf840fa864cc07b330dbd0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a> (<a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>, <a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>, sizeof(<a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>))</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:aee3e2abd9dd27ddfc3e158a9ffce1746" id="r_aee3e2abd9dd27ddfc3e158a9ffce1746"><td class="memItemLeft" align="right" valign="top">Exit&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aee3e2abd9dd27ddfc3e158a9ffce1746">__pad0__</a></td></tr>
<tr class="memitem:aff83b2530e0944d77d4ee0965b41ad89" id="r_aff83b2530e0944d77d4ee0965b41ad89"><td class="memItemLeft" align="right" valign="top">WDFMEMORY&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aff83b2530e0944d77d4ee0965b41ad89">ConfigurationMemory</a> = NULL</td></tr>
<tr class="memitem:a7cd334185ddc76afeacf2cd57615dc81" id="r_a7cd334185ddc76afeacf2cd57615dc81"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7cd334185ddc76afeacf2cd57615dc81">Data</a> = Buffer</td></tr>
<tr class="memitem:a63268b1e9e5ee12309a44d8d6c9fc652" id="r_a63268b1e9e5ee12309a44d8d6c9fc652"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a63268b1e9e5ee12309a44d8d6c9fc652">DataAddress</a> = RegisterAddress</td></tr>
<tr class="memitem:adb5ea4ad84a87209e6f98b3e012adbbf" id="r_adb5ea4ad84a87209e6f98b3e012adbbf"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adb5ea4ad84a87209e6f98b3e012adbbf">DataLength</a> = Length</td></tr>
<tr class="memitem:af9a881dabb7ea1e15ee2808cca09fd6a" id="r_af9a881dabb7ea1e15ee2808cca09fd6a"><td class="memItemLeft" align="right" valign="top">packet&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af9a881dabb7ea1e15ee2808cca09fd6a">DelayInMicroseconds</a> = 0</td></tr>
<tr class="memitem:ae49d231a428d107c888f925e845daf62" id="r_ae49d231a428d107c888f925e845daf62"><td class="memItemLeft" align="right" valign="top">BOOLEAN&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae49d231a428d107c888f925e845daf62">DeviceInitialized</a> = FALSE</td></tr>
<tr class="memitem:abf852046373359fb294f66a784b38263" id="r_abf852046373359fb294f66a784b38263"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abf852046373359fb294f66a784b38263">DeviceType</a> = <a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d">HalDeviceI2C</a></td></tr>
<tr class="memitem:a0544c3fe466e421738dae463968b70ba" id="r_a0544c3fe466e421738dae463968b70ba"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0544c3fe466e421738dae463968b70ba">else</a></td></tr>
<tr class="memitem:ab9707a002fb8033fdc202e8c8b8f8569" id="r_ab9707a002fb8033fdc202e8c8b8f8569"><td class="memItemLeft" align="right" valign="top"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab9707a002fb8033fdc202e8c8b8f8569">ErrorCount</a> = 0</td></tr>
<tr class="memitem:a02a7df72146aec5cbd7c5a854a9adcda" id="r_a02a7df72146aec5cbd7c5a854a9adcda"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_DPC&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a02a7df72146aec5cbd7c5a854a9adcda">EvtI2cInterruptDpc</a></td></tr>
<tr class="memitem:ab9ce8c5b03a473cd94f2812fa8622837" id="r_ab9ce8c5b03a473cd94f2812fa8622837"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_ISR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab9ce8c5b03a473cd94f2812fa8622837">EvtI2cInterruptIsr</a></td></tr>
<tr class="memitem:a99bc0f18d2d6fca4d292cd4026a2435f" id="r_a99bc0f18d2d6fca4d292cd4026a2435f"><td class="memItemLeft" align="right" valign="top">EVT_WDF_REQUEST_COMPLETION_ROUTINE&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a99bc0f18d2d6fca4d292cd4026a2435f">EvtI2cRequestCompletion</a></td></tr>
<tr class="memitem:aaea9f9b32650901ecb0d31cb5066cd7f" id="r_aaea9f9b32650901ecb0d31cb5066cd7f"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaea9f9b32650901ecb0d31cb5066cd7f">Flags</a> = 0</td></tr>
<tr class="memitem:a96ba6885a1d23da9ee577cfc9b91ae60" id="r_a96ba6885a1d23da9ee577cfc9b91ae60"><td class="memItemLeft" align="right" valign="top"><a class="el" href="hal__interface_8h.html#a2f4ba870132c1fd57e2d74ba94e39805">HAL_DEVICE_HANDLE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a></td></tr>
<tr class="memitem:aa89f192944d335c9a60e5858325ed89a" id="r_aa89f192944d335c9a60e5858325ed89a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa89f192944d335c9a60e5858325ed89a">I2C_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:a3e1e82f2b44144b87469685950b3b501" id="r_a3e1e82f2b44144b87469685950b3b501"><td class="memItemLeft" align="right" valign="top"><a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a> = NULL</td></tr>
<tr class="memitem:a89c965cbcba7aedff1b61ea4c0498d3e" id="r_a89c965cbcba7aedff1b61ea4c0498d3e"><td class="memItemLeft" align="right" valign="top">*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a89c965cbcba7aedff1b61ea4c0498d3e">PI2C_DEVICE_CONTEXT</a></td></tr>
<tr class="memitem:a40d2c447ac37fcd86673f2a11b2ca094" id="r_a40d2c447ac37fcd86673f2a11b2ca094"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a40d2c447ac37fcd86673f2a11b2ca094">PrivateData</a> = <a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></td></tr>
<tr class="memitem:a0470f3b47bad91bd5e08004c87a8d98a" id="r_a0470f3b47bad91bd5e08004c87a8d98a"><td class="memItemLeft" align="right" valign="top">halConfig&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0470f3b47bad91bd5e08004c87a8d98a">PrivateDataSize</a> = sizeof(<a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>)</td></tr>
<tr class="memitem:a9611b3a00430a86619b5923de30f9fdb" id="r_a9611b3a00430a86619b5923de30f9fdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9611b3a00430a86619b5923de30f9fdb">status</a></td></tr>
<tr class="memitem:a77b4762318f24dff847f94f382cfeea6" id="r_a77b4762318f24dff847f94f382cfeea6"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77b4762318f24dff847f94f382cfeea6">STATUS_SUCCESS</a></td></tr>
<tr class="memitem:a63212990a463669c2face6cfbfd28d26" id="r_a63212990a463669c2face6cfbfd28d26"><td class="memItemLeft" align="right" valign="top">ULONG&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a63212990a463669c2face6cfbfd28d26">TransactionCount</a> = 0</td></tr>
<tr class="memitem:a11ec07dcb5c1cea421134a0b149443a5" id="r_a11ec07dcb5c1cea421134a0b149443a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11ec07dcb5c1cea421134a0b149443a5">WdfDevice</a> = Device</td></tr>
</table>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="a4bbdac9bba21cb3959a7355001ea590f" name="a4bbdac9bba21cb3959a7355001ea590f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bbdac9bba21cb3959a7355001ea590f">&#9670;&#160;</a></span>I2C_DEFAULT_TIMEOUT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define I2C_DEFAULT_TIMEOUT&#160;&#160;&#160;1000</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a50d1319f95a5bfb01ed5c3ab0f60bf8b" name="a50d1319f95a5bfb01ed5c3ab0f60bf8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50d1319f95a5bfb01ed5c3ab0f60bf8b">&#9670;&#160;</a></span>EvtI2cInterruptIsr()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">BOOLEAN EvtI2cInterruptIsr </td>
          <td>(</td>
          <td class="paramtype">_In_ WDFINTERRUPT</td>          <td class="paramname"><span class="paramname"><em>Interrupt</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>MessageID</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a50d1319f95a5bfb01ed5c3ab0f60bf8b_cgraph.png" border="0" usemap="#ai2c__device_8c_a50d1319f95a5bfb01ed5c3ab0f60bf8b_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a50d1319f95a5bfb01ed5c3ab0f60bf8b_cgraph" id="ai2c__device_8c_a50d1319f95a5bfb01ed5c3ab0f60bf8b_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,128,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="176,29,281,56"/>
<area shape="poly" title=" " alt="" coords="128,56,160,51,161,56,128,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="193,80,264,107"/>
<area shape="poly" title=" " alt="" coords="128,75,178,83,177,88,128,80"/>
<area shape="poly" title=" " alt="" coords="200,30,195,21,199,11,210,5,228,3,248,5,258,12,256,16,246,10,228,8,212,10,203,15,201,20,205,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="329,55,457,81"/>
<area shape="poly" title=" " alt="" coords="264,85,313,78,314,83,264,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="346,105,441,132"/>
<area shape="poly" title=" " alt="" coords="264,96,331,106,330,112,264,101"/>
</map>
</div>

</div>
</div>
<a id="a5da67a960d3cf99caa6874438a84629b" name="a5da67a960d3cf99caa6874438a84629b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5da67a960d3cf99caa6874438a84629b">&#9670;&#160;</a></span>I2cDeviceCleanup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">VOID I2cDeviceCleanup </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceCleanup - 娓呯悊I2C璁惧璧勬簮</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a5da67a960d3cf99caa6874438a84629b_cgraph.png" border="0" usemap="#ai2c__device_8c_a5da67a960d3cf99caa6874438a84629b_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a5da67a960d3cf99caa6874438a84629b_cgraph" id="ai2c__device_8c_a5da67a960d3cf99caa6874438a84629b_cgraph">
<area shape="rect" title=" " alt="" coords="5,80,134,107"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="187,29,292,56"/>
<area shape="poly" title=" " alt="" coords="116,77,177,58,179,63,118,82"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="182,80,298,107"/>
<area shape="poly" title=" " alt="" coords="134,91,166,91,166,96,134,96"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="205,131,275,157"/>
<area shape="poly" title=" " alt="" coords="118,105,191,127,189,132,116,110"/>
<area shape="poly" title=" " alt="" coords="210,30,205,21,209,11,221,5,240,3,260,5,271,12,269,16,259,10,240,8,222,10,213,15,211,20,215,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="346,105,474,132"/>
<area shape="poly" title=" " alt="" coords="275,136,330,128,331,133,276,141"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="362,156,458,183"/>
<area shape="poly" title=" " alt="" coords="276,147,348,157,347,163,275,152"/>
</map>
</div>

</div>
</div>
<a id="a709aca0009ccfb39adebbdd9ce97e252" name="a709aca0009ccfb39adebbdd9ce97e252"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a709aca0009ccfb39adebbdd9ce97e252">&#9670;&#160;</a></span>I2cDeviceGetStatistics()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceGetStatistics </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_ <a class="el" href="i2c__device_8h.html#aeb652cfe1149dff5ee42abab74d96813">PI2C_STATISTICS</a></td>          <td class="paramname"><span class="paramname"><em>Statistics</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceGetStatistics - 鑾峰彇I2C璁惧缁熻淇℃伅</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Statistics</td><td>缁熻淇℃伅缁撴瀯</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a709aca0009ccfb39adebbdd9ce97e252_cgraph.png" border="0" usemap="#ai2c__device_8c_a709aca0009ccfb39adebbdd9ce97e252_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a709aca0009ccfb39adebbdd9ce97e252_cgraph" id="ai2c__device_8c_a709aca0009ccfb39adebbdd9ce97e252_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,162,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="210,29,315,56"/>
<area shape="poly" title=" " alt="" coords="162,54,194,50,195,55,162,60"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="227,80,298,107"/>
<area shape="poly" title=" " alt="" coords="162,76,212,84,211,89,162,82"/>
<area shape="poly" title=" " alt="" coords="234,30,229,21,232,11,244,5,262,3,282,5,293,12,290,16,280,10,262,8,245,10,236,15,234,20,238,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="363,55,491,81"/>
<area shape="poly" title=" " alt="" coords="298,85,347,78,348,83,298,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="380,105,475,132"/>
<area shape="poly" title=" " alt="" coords="298,96,365,106,364,112,298,101"/>
</map>
</div>

</div>
</div>
<a id="ab0c3b778b5a363d418c3d768cdb1e2d4" name="ab0c3b778b5a363d418c3d768cdb1e2d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0c3b778b5a363d418c3d768cdb1e2d4">&#9670;&#160;</a></span>I2cDeviceInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__i2c_8h.html#a9d4df46fafece7b304c57d2e0e1bfd51">PI2C_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>I2cConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceInitialize - 鍒濆鍖朓2C璁惧</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">I2cConfig</td><td>I2C閰嶇疆</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph.png" border="0" usemap="#ai2c__device_8c_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph" id="ai2c__device_8c_ab0c3b778b5a363d418c3d768cdb1e2d4_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,134,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="182,29,287,56"/>
<area shape="poly" title=" " alt="" coords="134,55,166,50,167,56,134,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="199,80,270,107"/>
<area shape="poly" title=" " alt="" coords="134,75,184,83,183,88,134,81"/>
<area shape="poly" title=" " alt="" coords="206,30,201,21,204,11,216,5,234,3,254,5,265,12,262,16,252,10,234,8,217,10,208,15,206,20,210,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="335,55,463,81"/>
<area shape="poly" title=" " alt="" coords="270,85,319,78,320,83,270,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,447,132"/>
<area shape="poly" title=" " alt="" coords="270,96,337,106,336,112,270,101"/>
</map>
</div>

</div>
</div>
<a id="a6576f1e3485d12c22c444244044c1d30" name="a6576f1e3485d12c22c444244044c1d30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6576f1e3485d12c22c444244044c1d30">&#9670;&#160;</a></span>I2cDeviceRead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceRead </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>DeviceAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_writes_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesRead</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceRead - 浠嶪2C璁惧璇诲彇鏁版嵁</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">DeviceAddress</td><td>I2C璁惧鍦板潃 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>瀵勫瓨鍣ㄥ湴鍧€ </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">Buffer</td><td>鏁版嵁缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>缂撳啿鍖洪暱搴? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesRead</td><td>瀹為檯璇诲彇鐨勫瓧鑺傛暟</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a6576f1e3485d12c22c444244044c1d30_cgraph.png" border="0" usemap="#ai2c__device_8c_a6576f1e3485d12c22c444244044c1d30_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a6576f1e3485d12c22c444244044c1d30_cgraph" id="ai2c__device_8c_a6576f1e3485d12c22c444244044c1d30_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,117,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="165,29,270,56"/>
<area shape="poly" title=" " alt="" coords="117,56,149,51,150,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="182,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,167,83,166,88,117,80"/>
<area shape="poly" title=" " alt="" coords="190,30,185,21,189,11,200,5,217,3,236,5,246,12,243,16,234,10,217,8,201,10,193,15,191,20,195,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="318,55,446,81"/>
<area shape="poly" title=" " alt="" coords="253,85,302,78,303,83,253,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,430,132"/>
<area shape="poly" title=" " alt="" coords="253,96,320,106,319,112,253,101"/>
</map>
</div>

</div>
</div>
<a id="ad84f26684684313ff193803d1d9c7c32" name="ad84f26684684313ff193803d1d9c7c32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad84f26684684313ff193803d1d9c7c32">&#9670;&#160;</a></span>I2cDeviceTransfer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceTransfer </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_(TransferCount) <a class="el" href="kmdf__i2c_8h.html#a26d8a1f8a56e4808ad0856f1dc02461c">PI2C_TRANSFER_PACKET</a></td>          <td class="paramname"><span class="paramname"><em>Transfers</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>TransferCount</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceTransfer - 鎵ц澶嶆潅I2C浼犺緭</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Transfers</td><td>浼犺緭鏁版嵁鍖呮暟缁? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">TransferCount</td><td>浼犺緭鏁版嵁鍖呮暟閲? * </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_ad84f26684684313ff193803d1d9c7c32_cgraph.png" border="0" usemap="#ai2c__device_8c_ad84f26684684313ff193803d1d9c7c32_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_ad84f26684684313ff193803d1d9c7c32_cgraph" id="ai2c__device_8c_ad84f26684684313ff193803d1d9c7c32_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,134,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="182,29,287,56"/>
<area shape="poly" title=" " alt="" coords="134,55,166,50,167,56,134,61"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="199,80,270,107"/>
<area shape="poly" title=" " alt="" coords="134,75,184,83,183,88,134,81"/>
<area shape="poly" title=" " alt="" coords="206,30,201,21,204,11,216,5,234,3,254,5,265,12,262,16,252,10,234,8,217,10,208,15,206,20,210,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="335,55,463,81"/>
<area shape="poly" title=" " alt="" coords="270,85,319,78,320,83,270,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="352,105,447,132"/>
<area shape="poly" title=" " alt="" coords="270,96,337,106,336,112,270,101"/>
</map>
</div>

</div>
</div>
<a id="a580f2434082501937a3d8bc4d5591866" name="a580f2434082501937a3d8bc4d5591866"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a580f2434082501937a3d8bc4d5591866">&#9670;&#160;</a></span>I2cDeviceWrite()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NTSTATUS I2cDeviceWrite </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>DeviceAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ UCHAR</td>          <td class="paramname"><span class="paramname"><em>RegisterAddress</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_reads_bytes_(Length) PVOID</td>          <td class="paramname"><span class="paramname"><em>Buffer</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>Length</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_Out_opt_ PULONG</td>          <td class="paramname"><span class="paramname"><em>BytesWritten</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>I2cDeviceWrite - 鍚慖2C璁惧鍐欏叆鏁版嵁</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Device</td><td>WDF璁惧瀵硅薄 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">DeviceAddress</td><td>I2C璁惧鍦板潃 </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">RegisterAddress</td><td>瀵勫瓨鍣ㄥ湴鍧€ </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Buffer</td><td>鏁版嵁缂撳啿鍖? *</td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">Length</td><td>缂撳啿鍖洪暱搴? *</td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">BytesWritten</td><td>瀹為檯鍐欏叆鐨勫瓧鑺傛暟</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NTSTATUS 鐘舵€佺爜 </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a580f2434082501937a3d8bc4d5591866_cgraph.png" border="0" usemap="#ai2c__device_8c_a580f2434082501937a3d8bc4d5591866_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a580f2434082501937a3d8bc4d5591866_cgraph" id="ai2c__device_8c_a580f2434082501937a3d8bc4d5591866_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,116,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="164,29,269,56"/>
<area shape="poly" title=" " alt="" coords="116,56,148,51,149,56,117,62"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="181,80,252,107"/>
<area shape="poly" title=" " alt="" coords="117,74,166,83,165,88,116,80"/>
<area shape="poly" title=" " alt="" coords="189,30,185,21,188,11,199,5,216,3,235,5,245,12,242,16,233,10,216,8,200,10,192,15,190,20,194,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="317,55,445,81"/>
<area shape="poly" title=" " alt="" coords="252,85,301,78,302,83,252,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="334,105,429,132"/>
<area shape="poly" title=" " alt="" coords="252,96,319,106,318,112,252,101"/>
</map>
</div>

</div>
</div>
<a id="a1a243a15dd793b6d0f7b7011461a8641" name="a1a243a15dd793b6d0f7b7011461a8641"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a243a15dd793b6d0f7b7011461a8641">&#9670;&#160;</a></span>if() <span class="overload">[1/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>NT_SUCCESS</em></span>status</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph.png" border="0" usemap="#ai2c__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" id="ai2c__device_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph">
<area shape="rect" title=" " alt="" coords="5,31,44,57"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="92,31,162,57"/>
<area shape="poly" title=" " alt="" coords="44,41,76,41,76,47,44,47"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="210,5,338,32"/>
<area shape="poly" title=" " alt="" coords="162,35,195,30,195,35,163,41"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="227,56,322,83"/>
<area shape="poly" title=" " alt="" coords="163,47,212,56,211,61,162,53"/>
</map>
</div>

</div>
</div>
<a id="ab29d05a3528131be0d35fe785e85590f" name="ab29d05a3528131be0d35fe785e85590f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab29d05a3528131be0d35fe785e85590f">&#9670;&#160;</a></span>if() <span class="overload">[2/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a96ba6885a1d23da9ee577cfc9b91ae60">HalHandle</a> !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph.png" border="0" usemap="#ai2c__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph" id="ai2c__device_8c_ab29d05a3528131be0d35fe785e85590f_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="97,29,203,56"/>
<area shape="poly" title=" " alt="" coords="43,62,82,54,83,59,45,67"/>
<area shape="rect" href="hal__interface_8h.html#a40a0e8d142c3033b41a5ad463c064189" title=" " alt="" coords="92,80,208,107"/>
<area shape="poly" title=" " alt="" coords="45,69,77,76,76,81,43,74"/>
<area shape="poly" title=" " alt="" coords="128,30,124,21,126,11,135,5,150,3,165,5,174,12,170,16,163,10,150,8,137,10,131,14,129,20,132,28"/>
</map>
</div>

</div>
</div>
<a id="a6957e0e0f326c7986a222f431530dc94" name="a6957e0e0f326c7986a222f431530dc94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6957e0e0f326c7986a222f431530dc94">&#9670;&#160;</a></span>if() <span class="overload">[3/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a> !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a6957e0e0f326c7986a222f431530dc94_cgraph.png" border="0" usemap="#ai2c__device_8c_a6957e0e0f326c7986a222f431530dc94_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a6957e0e0f326c7986a222f431530dc94_cgraph" id="ai2c__device_8c_a6957e0e0f326c7986a222f431530dc94_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,44,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,40,76,40,76,45,44,45"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
</map>
</div>

</div>
</div>
<a id="a161904443c5f73d8654306b3fa8d29bb" name="a161904443c5f73d8654306b3fa8d29bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a161904443c5f73d8654306b3fa8d29bb">&#9670;&#160;</a></span>if() <span class="overload">[4/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;Interrupt !</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a161904443c5f73d8654306b3fa8d29bb_cgraph.png" border="0" usemap="#ai2c__device_8c_a161904443c5f73d8654306b3fa8d29bb_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a161904443c5f73d8654306b3fa8d29bb_cgraph" id="ai2c__device_8c_a161904443c5f73d8654306b3fa8d29bb_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,44,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,40,76,40,76,45,44,45"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
</map>
</div>

</div>
</div>
<a id="a9d2d77fd6fa0d75751b40049e614b00b" name="a9d2d77fd6fa0d75751b40049e614b00b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d2d77fd6fa0d75751b40049e614b00b">&#9670;&#160;</a></span>if() <span class="overload">[5/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph.png" border="0" usemap="#ai2c__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph" id="ai2c__device_8c_a9d2d77fd6fa0d75751b40049e614b00b_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,61,76,54,77,60,45,67"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="109,80,180,107"/>
<area shape="poly" title=" " alt="" coords="45,69,94,80,93,85,44,75"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="245,55,373,81"/>
<area shape="poly" title=" " alt="" coords="180,85,229,78,230,83,181,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="262,105,357,132"/>
<area shape="poly" title=" " alt="" coords="181,96,247,106,246,112,180,101"/>
</map>
</div>

</div>
</div>
<a id="adfac0a96ec8249c69bd820670db7f2cd" name="adfac0a96ec8249c69bd820670db7f2cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adfac0a96ec8249c69bd820670db7f2cd">&#9670;&#160;</a></span>if() <span class="overload">[6/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a>-&gt;</td>          <td class="paramname"><span class="paramname"><em>InterruptEnabled</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_adfac0a96ec8249c69bd820670db7f2cd_cgraph.png" border="0" usemap="#ai2c__device_8c_adfac0a96ec8249c69bd820670db7f2cd_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_adfac0a96ec8249c69bd820670db7f2cd_cgraph" id="ai2c__device_8c_adfac0a96ec8249c69bd820670db7f2cd_cgraph">
<area shape="rect" title=" " alt="" coords="5,55,44,81"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="92,29,197,56"/>
<area shape="poly" title=" " alt="" coords="44,61,76,54,77,60,45,67"/>
<area shape="rect" href="error__handling_8c.html#a1a6e352a97a92a34f977476294a16f4e" title=" " alt="" coords="109,80,180,107"/>
<area shape="poly" title=" " alt="" coords="45,69,94,80,93,85,44,75"/>
<area shape="poly" title=" " alt="" coords="123,30,119,20,122,11,130,5,144,3,159,5,167,12,164,16,157,10,144,8,133,10,126,14,125,20,128,28"/>
<area shape="rect" href="error__handling_8c.html#a14083fcce33766b91f8d08998cde8487" title=" " alt="" coords="245,55,373,81"/>
<area shape="poly" title=" " alt="" coords="180,85,229,78,230,83,181,91"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html#ae483585a71d174709d7049cc4b4758e1" title=" " alt="" coords="262,105,357,132"/>
<area shape="poly" title=" " alt="" coords="181,96,247,106,246,112,180,101"/>
</map>
</div>

</div>
</div>
<a id="a90ec17a9895e508ccdb9077fed539682" name="a90ec17a9895e508ccdb9077fed539682"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a90ec17a9895e508ccdb9077fed539682">&#9670;&#160;</a></span>LogInfo() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;I2C device initialized successfully&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae957556f2bac1175f6f4b37cd8f268f9" name="ae957556f2bac1175f6f4b37cd8f268f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae957556f2bac1175f6f4b37cd8f268f9">&#9670;&#160;</a></span>LogInfo() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;I2C read</td>          <td class="paramname"><span class="paramname"><em>succeeded</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">device:0x%</td>          <td class="paramname"><span class="paramname"><em>02X</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">register:0x%</td>          <td class="paramname"><span class="paramname"><em>02X</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bytes:%d&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">RegisterAddress</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">BytesRead ? *BytesRead :0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aabdf6e2791329ae7f1d903b2c7b47add" name="aabdf6e2791329ae7f1d903b2c7b47add"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabdf6e2791329ae7f1d903b2c7b47add">&#9670;&#160;</a></span>LogInfo() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;I2C transfer sequence</td>          <td class="paramname"><span class="paramname"><em>succeeded</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">transfers:%d&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">TransferCount</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a6e8f3cbefed6c462cd6392131f5f0a29" name="a6e8f3cbefed6c462cd6392131f5f0a29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e8f3cbefed6c462cd6392131f5f0a29">&#9670;&#160;</a></span>LogInfo() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;I2C write</td>          <td class="paramname"><span class="paramname"><em>succeeded</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">device:0x%</td>          <td class="paramname"><span class="paramname"><em>02X</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">register:0x%</td>          <td class="paramname"><span class="paramname"><em>02X</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bytes:%d&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="i2c__device_8h.html#a862821561008426245a34e458d02a093">DeviceAddress</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">RegisterAddress</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">BytesWritten ? *BytesWritten :0</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae00ba03b0ccf840fa864cc07b330dbd0" name="ae00ba03b0ccf840fa864cc07b330dbd0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae00ba03b0ccf840fa864cc07b330dbd0">&#9670;&#160;</a></span>RtlCopyMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RtlCopyMemory </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;</td>          <td class="paramname"><span class="paramname"><em>I2cConfig</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">sizeof(<a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>)</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_cgraph.png" border="0" usemap="#ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_cgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_cgraph" id="ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,122,56"/>
<area shape="rect" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974" title=" " alt="" coords="170,29,275,56"/>
<area shape="poly" title=" " alt="" coords="122,40,154,40,154,45,122,45"/>
<area shape="poly" title=" " alt="" coords="195,30,190,21,193,11,204,5,222,3,242,5,252,12,249,16,240,10,222,8,206,10,197,15,195,20,199,28"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="i2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_icgraph.png" border="0" usemap="#ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_icgraph" loading="lazy" alt=""/></div>
<map name="ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_icgraph" id="ai2c__device_8c_ae00ba03b0ccf840fa864cc07b330dbd0_icgraph">
<area shape="rect" title=" " alt="" coords="653,144,769,171"/>
<area shape="rect" href="device__manager_8c.html#aebab0b9bc330432c9faaf78df6cfb6b2" title=" " alt="" coords="478,5,605,32"/>
<area shape="poly" title=" " alt="" coords="689,133,652,89,603,46,583,35,585,30,606,42,656,86,693,130"/>
<area shape="rect" href="device__manager_8c.html#ad0a38f6ee5ec061af8f147cb6f9850aa" title=" " alt="" coords="484,56,599,83"/>
<area shape="poly" title=" " alt="" coords="669,139,603,104,569,86,571,81,606,99,672,134"/>
<area shape="rect" href="driver__core_8c.html#acdb452dbcae039af8967376463c758b9" title=" " alt="" coords="145,107,277,133"/>
<area shape="poly" title=" " alt="" coords="637,151,604,148,277,126,278,121,605,143,638,146"/>
<area shape="rect" href="i2c__core_8c.html#a3730a6f611cf9feba7ba954330f41a6c" title=" " alt="" coords="496,181,587,208"/>
<area shape="poly" title=" " alt="" coords="638,176,588,187,587,182,637,171"/>
<area shape="rect" href="driver__log_8c.html#aa2e9424857371175fc265253fbabcc5d" title=" " alt="" coords="334,213,425,240"/>
<area shape="poly" title=" " alt="" coords="682,182,647,205,605,223,558,232,510,237,425,234,425,229,510,231,557,227,604,217,644,200,679,178"/>
<area shape="rect" href="spi__core_8c.html#a685d8d7731e750c1512b975df16cc030" title=" " alt="" coords="496,307,587,333"/>
<area shape="poly" title=" " alt="" coords="688,184,607,266,560,308,557,304,603,262,684,180"/>
<area shape="rect" href="device__manager_8c.html#a85f48e60bea1385e67ec52def6e57442" title=" " alt="" coords="329,31,430,57"/>
<area shape="poly" title=" " alt="" coords="463,34,431,39,431,33,462,28"/>
<area shape="rect" href="driver__entry_8c.html#a0776c179fdcbdd09df07ee264e7e78e6" title=" " alt="" coords="141,43,281,69"/>
<area shape="poly" title=" " alt="" coords="314,51,282,54,281,48,313,46"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,107,93,133"/>
<area shape="poly" title=" " alt="" coords="162,78,86,109,84,104,160,73"/>
<area shape="poly" title=" " alt="" coords="129,123,93,123,93,117,129,117"/>
<area shape="poly" title=" " alt="" coords="569,168,559,162,541,160,525,162,516,167,514,172,518,180,513,182,509,173,512,163,523,157,542,155,561,157,572,164"/>
<area shape="poly" title=" " alt="" coords="320,211,92,136,94,131,322,205"/>
<area shape="poly" title=" " alt="" coords="569,294,559,288,541,285,525,287,516,292,514,297,518,305,513,307,509,298,512,288,523,282,542,280,561,283,572,289"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="aee3e2abd9dd27ddfc3e158a9ffce1746" name="aee3e2abd9dd27ddfc3e158a9ffce1746"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee3e2abd9dd27ddfc3e158a9ffce1746">&#9670;&#160;</a></span>__pad0__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Exit __pad0__</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aff83b2530e0944d77d4ee0965b41ad89" name="aff83b2530e0944d77d4ee0965b41ad89"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff83b2530e0944d77d4ee0965b41ad89">&#9670;&#160;</a></span>ConfigurationMemory</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> ConfigurationMemory = NULL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a7cd334185ddc76afeacf2cd57615dc81" name="a7cd334185ddc76afeacf2cd57615dc81"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7cd334185ddc76afeacf2cd57615dc81">&#9670;&#160;</a></span>Data</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet Data = Buffer</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a63268b1e9e5ee12309a44d8d6c9fc652" name="a63268b1e9e5ee12309a44d8d6c9fc652"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63268b1e9e5ee12309a44d8d6c9fc652">&#9670;&#160;</a></span>DataAddress</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet DataAddress = RegisterAddress</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="adb5ea4ad84a87209e6f98b3e012adbbf" name="adb5ea4ad84a87209e6f98b3e012adbbf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb5ea4ad84a87209e6f98b3e012adbbf">&#9670;&#160;</a></span>DataLength</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet DataLength = Length</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af9a881dabb7ea1e15ee2808cca09fd6a" name="af9a881dabb7ea1e15ee2808cca09fd6a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9a881dabb7ea1e15ee2808cca09fd6a">&#9670;&#160;</a></span>DelayInMicroseconds</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet DelayInMicroseconds = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ae49d231a428d107c888f925e845daf62" name="ae49d231a428d107c888f925e845daf62"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae49d231a428d107c888f925e845daf62">&#9670;&#160;</a></span>DeviceInitialized</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics DeviceInitialized = FALSE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="abf852046373359fb294f66a784b38263" name="abf852046373359fb294f66a784b38263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf852046373359fb294f66a784b38263">&#9670;&#160;</a></span>DeviceType</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig DeviceType = <a class="el" href="hal__interface_8h.html#ad036d8e298a658842c53aee423bbbbc5a923f9c5d03b9a7c494d4e08e9202910d">HalDeviceI2C</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0544c3fe466e421738dae463968b70ba" name="a0544c3fe466e421738dae463968b70ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0544c3fe466e421738dae463968b70ba">&#9670;&#160;</a></span>else</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">else</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">{</div>
<div class="line">        </div>
<div class="line">        InterlockedIncrement(&amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;TransactionCount)</div>
<div class="ttc" id="agpio__device_8c_html_a7b6a29716fe6f8117de54edaedc57974"><div class="ttname"><a href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a></div><div class="ttdeci">RtlCopyMemory &amp; deviceContext(GPIO_DEVICE_CONFIG)</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="ab9707a002fb8033fdc202e8c8b8f8569" name="ab9707a002fb8033fdc202e8c8b8f8569"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab9707a002fb8033fdc202e8c8b8f8569">&#9670;&#160;</a></span>ErrorCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics ErrorCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a02a7df72146aec5cbd7c5a854a9adcda" name="a02a7df72146aec5cbd7c5a854a9adcda"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02a7df72146aec5cbd7c5a854a9adcda">&#9670;&#160;</a></span>EvtI2cInterruptDpc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_INTERRUPT_DPC EvtI2cInterruptDpc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ab9ce8c5b03a473cd94f2812fa8622837" name="ab9ce8c5b03a473cd94f2812fa8622837"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab9ce8c5b03a473cd94f2812fa8622837">&#9670;&#160;</a></span>EvtI2cInterruptIsr</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_INTERRUPT_ISR EvtI2cInterruptIsr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a99bc0f18d2d6fca4d292cd4026a2435f" name="a99bc0f18d2d6fca4d292cd4026a2435f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a99bc0f18d2d6fca4d292cd4026a2435f">&#9670;&#160;</a></span>EvtI2cRequestCompletion</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EVT_WDF_REQUEST_COMPLETION_ROUTINE EvtI2cRequestCompletion</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aaea9f9b32650901ecb0d31cb5066cd7f" name="aaea9f9b32650901ecb0d31cb5066cd7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaea9f9b32650901ecb0d31cb5066cd7f">&#9670;&#160;</a></span>Flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">packet Flags = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a96ba6885a1d23da9ee577cfc9b91ae60" name="a96ba6885a1d23da9ee577cfc9b91ae60"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96ba6885a1d23da9ee577cfc9b91ae60">&#9670;&#160;</a></span>HalHandle</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="hal__interface_8h.html#a2f4ba870132c1fd57e2d74ba94e39805">HAL_DEVICE_HANDLE</a> HalHandle</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa89f192944d335c9a60e5858325ed89a" name="aa89f192944d335c9a60e5858325ed89a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa89f192944d335c9a60e5858325ed89a">&#9670;&#160;</a></span>I2C_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="i2c__core_8c.html#abad9f6d84bae65aada7d4a1cfcc2ba12">I2C_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3e1e82f2b44144b87469685950b3b501" name="a3e1e82f2b44144b87469685950b3b501"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e1e82f2b44144b87469685950b3b501">&#9670;&#160;</a></span>I2cConfig</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> I2cConfig = NULL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a89c965cbcba7aedff1b61ea4c0498d3e" name="a89c965cbcba7aedff1b61ea4c0498d3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a89c965cbcba7aedff1b61ea4c0498d3e">&#9670;&#160;</a></span>PI2C_DEVICE_CONTEXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">* <a class="el" href="i2c__core_8c.html#a9f0733f4be9833c3a734164c7711fbe5">PI2C_DEVICE_CONTEXT</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a40d2c447ac37fcd86673f2a11b2ca094" name="a40d2c447ac37fcd86673f2a11b2ca094"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40d2c447ac37fcd86673f2a11b2ca094">&#9670;&#160;</a></span>PrivateData</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig PrivateData = <a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;<a class="el" href="#a3e1e82f2b44144b87469685950b3b501">I2cConfig</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a0470f3b47bad91bd5e08004c87a8d98a" name="a0470f3b47bad91bd5e08004c87a8d98a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0470f3b47bad91bd5e08004c87a8d98a">&#9670;&#160;</a></span>PrivateDataSize</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">halConfig PrivateDataSize = sizeof(<a class="el" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9611b3a00430a86619b5923de30f9fdb" name="a9611b3a00430a86619b5923de30f9fdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9611b3a00430a86619b5923de30f9fdb">&#9670;&#160;</a></span>status</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return status</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= WdfMemoryCreate(</div>
<div class="line">        WDF_NO_OBJECT_ATTRIBUTES,</div>
<div class="line">        NonPagedPoolNx,</div>
<div class="line">        0,</div>
<div class="line">        <span class="keyword">sizeof</span>(<a class="code hl_typedef" href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a>),</div>
<div class="line">        &amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;ConfigurationMemory,</div>
<div class="line">        (PVOID*)&amp;<a class="code hl_function" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a>-&gt;I2cConfig</div>
<div class="line">    )</div>
<div class="ttc" id="akmdf__i2c_8h_html_a8275fd1e76bc02628ddb4cf647c947c4"><div class="ttname"><a href="kmdf__i2c_8h.html#a8275fd1e76bc02628ddb4cf647c947c4">I2C_CONFIG</a></div><div class="ttdeci">struct _I2C_CONFIG I2C_CONFIG</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a77b4762318f24dff847f94f382cfeea6" name="a77b4762318f24dff847f94f382cfeea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77b4762318f24dff847f94f382cfeea6">&#9670;&#160;</a></span>STATUS_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return STATUS_SUCCESS</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a63212990a463669c2face6cfbfd28d26" name="a63212990a463669c2face6cfbfd28d26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63212990a463669c2face6cfbfd28d26">&#9670;&#160;</a></span>TransactionCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">Statistics TransactionCount = 0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a11ec07dcb5c1cea421134a0b149443a5" name="a11ec07dcb5c1cea421134a0b149443a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11ec07dcb5c1cea421134a0b149443a5">&#9670;&#160;</a></span>WdfDevice</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> WdfDevice = Device</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_340a9c6f51eab01289f9b188a5d35565.html">devices</a></li><li class="navelem"><a href="i2c__device_8c.html">i2c_device.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
