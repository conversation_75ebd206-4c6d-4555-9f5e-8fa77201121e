<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/core/error/error_handling.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('error__handling_8h.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">error_handling.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>驱动程序错误处理和断言宏定义  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="error__codes_8h_source.html">core/error/error_codes.h</a>&quot;</code><br />
<code>#include &quot;core/log/driver_log.h&quot;</code><br />
<code>#include &lt;ntddk.h&gt;</code><br />
<code>#include &lt;wdf.h&gt;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for error_handling.h:</div>
<div class="dyncontent">
<div class="center"><img src="error__handling_8h__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2include_2core_2error_2error__handling_8h" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2include_2core_2error_2error__handling_8h" id="aC_1_2KMDF_01Driver1_2include_2core_2error_2error__handling_8h">
<area shape="rect" title="驱动程序错误处理和断言宏定义" alt="" coords="168,5,344,48"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="5,96,165,123"/>
<area shape="poly" title=" " alt="" coords="213,51,127,91,125,87,211,46"/>
<area shape="rect" title=" " alt="" coords="112,171,176,197"/>
<area shape="poly" title=" " alt="" coords="243,50,164,159,160,156,239,47"/>
<area shape="rect" title=" " alt="" coords="240,96,378,123"/>
<area shape="poly" title=" " alt="" coords="272,47,294,81,290,84,267,50"/>
<area shape="rect" title=" " alt="" coords="402,96,456,123"/>
<area shape="poly" title=" " alt="" coords="301,46,389,87,387,92,299,51"/>
<area shape="poly" title=" " alt="" coords="97,122,126,157,122,160,93,125"/>
</map>
</div>
</div>
<p><a href="error__handling_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a3a8ba39810c16fad65af2271c2dd6a6c" id="r_a3a8ba39810c16fad65af2271c2dd6a6c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3a8ba39810c16fad65af2271c2dd6a6c">DRIVER_ASSERT</a>(_condition,  _message, ...)</td></tr>
<tr class="memitem:a98899d26e6d8086d4ae7268a1a953c01" id="r_a98899d26e6d8086d4ae7268a1a953c01"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a98899d26e6d8086d4ae7268a1a953c01">RETURN_ON_FAILURE</a>(_status,  _message, ...)</td></tr>
<tr class="memdesc:a98899d26e6d8086d4ae7268a1a953c01"><td class="mdescLeft">&#160;</td><td class="mdescRight">检查NTSTATUS是否成功，如果失败则记录错误并返回  <br /></td></tr>
<tr class="memitem:a2929f142620a357d4bb33852c676e822" id="r_a2929f142620a357d4bb33852c676e822"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2929f142620a357d4bb33852c676e822">RETURN_ON_FALSE</a>(_condition,  _return_status,  _message, ...)</td></tr>
<tr class="memdesc:a2929f142620a357d4bb33852c676e822"><td class="mdescLeft">&#160;</td><td class="mdescRight">检查条件是否为真，如果为假则记录错误并返回指定的NTSTATUS  <br /></td></tr>
<tr class="memitem:a726809026748fc6eba00fc55e5a26ff0" id="r_a726809026748fc6eba00fc55e5a26ff0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a726809026748fc6eba00fc55e5a26ff0">RETURN_ON_NULL</a>(_ptr,  _message, ...)</td></tr>
<tr class="memdesc:a726809026748fc6eba00fc55e5a26ff0"><td class="mdescLeft">&#160;</td><td class="mdescRight">检查指针是否为空，如果为空则记录错误并返回STATUS_INSUFFICIENT_RESOURCES  <br /></td></tr>
</table>
<a name="details" id="details"></a><h2 id="header-details" class="groupheader">Detailed Description</h2>
<div class="textblock"><p>包含用于错误代码检查、断言和日志记录的宏，以提高驱动程序的健壮性和可调试性。</p>
<dl class="section author"><dt>Author</dt><dd>KMDF Team </dd></dl>
<dl class="section date"><dt>Date</dt><dd>2025-05-12 </dd></dl>
</div><a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="a3a8ba39810c16fad65af2271c2dd6a6c" name="a3a8ba39810c16fad65af2271c2dd6a6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a8ba39810c16fad65af2271c2dd6a6c">&#9670;&#160;</a></span>DRIVER_ASSERT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DRIVER_ASSERT</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">((void)0)</div>
</div><!-- fragment -->
</div>
</div>
<a id="a98899d26e6d8086d4ae7268a1a953c01" name="a98899d26e6d8086d4ae7268a1a953c01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98899d26e6d8086d4ae7268a1a953c01">&#9670;&#160;</a></span>RETURN_ON_FAILURE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RETURN_ON_FAILURE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_status</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">do</span> { \</div>
<div class="line">        if (!<a class="code hl_define" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(_status)) { \</div>
<div class="line">            LOG_ERROR(<span class="stringliteral">&quot;错误: %s (%!STATUS!) - &quot;</span> _message, __FUNCTION__, _status, ##__VA_ARGS__); \</div>
<div class="line">            <span class="keywordflow">return</span> _status; \</div>
<div class="line">        } \</div>
<div class="line">    } <span class="keywordflow">while</span> (0)</div>
<div class="ttc" id="aprecomp_8h_html_ad14231612b7a675d33f0ead0b695d21a"><div class="ttname"><a href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a></div><div class="ttdeci">#define NT_SUCCESS(Status)</div><div class="ttdef"><b>Definition</b> precomp.h:57</div></div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">_status</td><td>要检查的NTSTATUS值 </td></tr>
    <tr><td class="paramname">_message</td><td>错误消息字符串 </td></tr>
    <tr><td class="paramname">...</td><td>错误消息的格式化参数 </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>如果_status失败，则从当前函数返回_status </dd></dl>

</div>
</div>
<a id="a2929f142620a357d4bb33852c676e822" name="a2929f142620a357d4bb33852c676e822"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2929f142620a357d4bb33852c676e822">&#9670;&#160;</a></span>RETURN_ON_FALSE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RETURN_ON_FALSE</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_condition</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_return_status</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">do</span> { \</div>
<div class="line">        if (!(_condition)) { \</div>
<div class="line">            LOG_ERROR(<span class="stringliteral">&quot;断言失败: %s - &quot;</span> _message, __FUNCTION__, ##__VA_ARGS__); \</div>
<div class="line">            <span class="keywordflow">return</span> _return_status; \</div>
<div class="line">        } \</div>
<div class="line">    } <span class="keywordflow">while</span> (0)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">_condition</td><td>要检查的条件 </td></tr>
    <tr><td class="paramname">_return_status</td><td>如果条件为假，要返回的NTSTATUS值 </td></tr>
    <tr><td class="paramname">_message</td><td>错误消息字符串 </td></tr>
    <tr><td class="paramname">...</td><td>错误消息的格式化参数 </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>如果_condition为假，则从当前函数返回_return_status </dd></dl>

</div>
</div>
<a id="a726809026748fc6eba00fc55e5a26ff0" name="a726809026748fc6eba00fc55e5a26ff0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a726809026748fc6eba00fc55e5a26ff0">&#9670;&#160;</a></span>RETURN_ON_NULL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RETURN_ON_NULL</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_ptr</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>_message</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em></em></span>...&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">    <span class="keywordflow">do</span> { \</div>
<div class="line">        if ((_ptr) == NULL) { \</div>
<div class="line">            LOG_ERROR(<span class="stringliteral">&quot;空指针错误: %s - &quot;</span> _message, __FUNCTION__, ##__VA_ARGS__); \</div>
<div class="line">            <span class="keywordflow">return</span> STATUS_INSUFFICIENT_RESOURCES; \</div>
<div class="line">        } \</div>
<div class="line">    } <span class="keywordflow">while</span> (0)</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">_ptr</td><td>要检查的指针 </td></tr>
    <tr><td class="paramname">_message</td><td>错误消息字符串 </td></tr>
    <tr><td class="paramname">...</td><td>错误消息的格式化参数 </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>如果_ptr为空，则从当前函数返回STATUS_INSUFFICIENT_RESOURCES </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_3d69f64eaf81436fe2b22361382717e5.html">core</a></li><li class="navelem"><a href="dir_aa60e2c50bb7a16bcf7ccb58d97021cb.html">error</a></li><li class="navelem"><a href="error__handling_8h.html">error_handling.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
