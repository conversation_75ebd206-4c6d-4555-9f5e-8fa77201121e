<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="precomp_8h" kind="file" language="C++">
    <compoundname>precomp.h</compoundname>
    <includes local="no">ntddk.h</includes>
    <includes local="no">wdf.h</includes>
    <includes local="no">ntstrsafe.h</includes>
    <includes local="no">wdfusb.h</includes>
    <includes local="no">usbspec.h</includes>
    <includes local="no">usb.h</includes>
    <includes local="no">wdfldr.h</includes>
    <includes local="no">wdfinstaller.h</includes>
    <includes refid="driver__core_8h" local="yes">../include/core/driver/driver_core.h</includes>
    <includes refid="device__manager_8h" local="yes">../include/core/device/device_manager.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../include/core/log/driver_log.h</includes>
    <includes refid="ioctl_8h" local="yes">../include/common/ioctl.h</includes>
    <includedby refid="device__manager_8c" local="yes">C:/KMDF Driver1/src/core/device/device_manager.c</includedby>
    <includedby refid="driver__entry_8c" local="yes">C:/KMDF Driver1/src/core/driver/driver_entry.c</includedby>
    <includedby refid="driver__log_8c" local="yes">C:/KMDF Driver1/src/core/log/driver_log.c</includedby>
    <includedby refid="driver__main_8c" local="yes">C:/KMDF Driver1/src/driver_main.c</includedby>
    <includedby refid="gpio__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/gpio_core.c</includedby>
    <includedby refid="i2c__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/i2c_core.c</includedby>
    <includedby refid="spi__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/spi_core.c</includedby>
    <includedby refid="precomp_8c" local="yes">C:/KMDF Driver1/src/precomp.c</includedby>
    <incdepgraph>
      <node id="16">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
      </node>
      <node id="10">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="12">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="12" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="17">
        <label>devioctl.h</label>
      </node>
      <node id="2">
        <label>ntddk.h</label>
      </node>
      <node id="4">
        <label>ntstrsafe.h</label>
      </node>
      <node id="7">
        <label>usb.h</label>
      </node>
      <node id="6">
        <label>usbspec.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
      <node id="9">
        <label>wdfinstaller.h</label>
      </node>
      <node id="8">
        <label>wdfldr.h</label>
      </node>
      <node id="5">
        <label>wdfusb.h</label>
      </node>
      <node id="11">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="2">
        <label>C:/KMDF Driver1/src/core/device/device_manager.c</label>
        <link refid="device__manager_8c"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/core/driver/driver_entry.c</label>
        <link refid="driver__entry_8c"/>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/core/log/driver_log.c</label>
        <link refid="driver__log_8c"/>
      </node>
      <node id="5">
        <label>C:/KMDF Driver1/src/driver_main.c</label>
        <link refid="driver__main_8c"/>
      </node>
      <node id="6">
        <label>C:/KMDF Driver1/src/hal/bus/gpio_core.c</label>
        <link refid="gpio__core_8c"/>
      </node>
      <node id="7">
        <label>C:/KMDF Driver1/src/hal/bus/i2c_core.c</label>
        <link refid="i2c__core_8c"/>
      </node>
      <node id="8">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="9">
        <label>C:/KMDF Driver1/src/precomp.c</label>
        <link refid="precomp_8c"/>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
      </node>
    </invincdepgraph>
    <sectiondef kind="define">
      <memberdef kind="define" id="precomp_8h_1ac50762666aa00bd3a4308158510f1748" prot="public" static="no">
        <name>_WIN32_WINNT</name>
        <initializer>0x0A00</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="14" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="14" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1abf0924766241e8f46e68e2dcbca9ac5b" prot="public" static="no">
        <name>ARRAY_SIZE</name>
        <param><defname>Array</defname></param>
        <initializer>(sizeof(Array) / sizeof((Array)[0]))</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="47" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="47" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1a7ba5cb5a943f312e7b50de9cd8ffaf37" prot="public" static="no">
        <name>DEBUG_PRINT</name>
        <param><defname>Level</defname></param>
        <param><defname>Fmt</defname></param>
        <param><defname>...</defname></param>
        <initializer>do { } while (0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="43" column="10" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="43" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" prot="public" static="no">
        <name>IN</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="52" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="52" bodyend="-1"/>
        <referencedby refid="driver__core_8c_1aa0699c5861e30ca66c92343c6fe22011" compoundref="driver__core_8c" startline="40" endline="56">DirectISR</referencedby>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1afa99ec4acc4ecb2dc3c2d05da15d0e3f" prot="public" static="no">
        <name>MAX</name>
        <param><defname>a</defname></param>
        <param><defname>b</defname></param>
        <initializer>(((a) &gt; (b)) ? (a) : (b))</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="49" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="49" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1a3acffbd305ee72dcd4593c0d8af64a4f" prot="public" static="no">
        <name>MIN</name>
        <param><defname>a</defname></param>
        <param><defname>b</defname></param>
        <initializer>(((a) &lt; (b)) ? (a) : (b))</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="48" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="48" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1abc91c0b65b29dc8fb94e770e4066e07c" prot="public" static="no">
        <name>NT_ERROR</name>
        <param><defname>Status</defname></param>
        <initializer>((NTSTATUS)(Status) &lt; 0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="58" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="58" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" prot="public" static="no">
        <name>NT_SUCCESS</name>
        <param><defname>Status</defname></param>
        <initializer>((NTSTATUS)(Status) &gt;= 0)</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="57" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="57" bodyend="-1"/>
        <referencedby refid="device__manager_8c_1a85f48e60bea1385e67ec52def6e57442" compoundref="device__manager_8c" startline="1007" endline="1100">DeviceCreate</referencedby>
        <referencedby refid="device__manager_8c_1ad0a38f6ee5ec061af8f147cb6f9850aa" compoundref="device__manager_8c" startline="369" endline="465">DeviceIoControl</referencedby>
        <referencedby refid="device__manager_8c_1ae45323f3c2e302bf5f913275b84c7ce2" compoundref="device__manager_8c" startline="1113" endline="1145">DeviceManager_EvtDeviceAdd</referencedby>
        <referencedby refid="device__manager_8c_1abadb1053ad035a1858c6f71af0f00d56" compoundref="device__manager_8c" startline="519" endline="870">DevicePrepareHardware</referencedby>
        <referencedby refid="device__manager_8c_1a059e0debbb6a3741ada3018f40b25b79" compoundref="device__manager_8c" startline="471" endline="513">DeviceResetHardware</referencedby>
        <referencedby refid="driver__entry_8c_1a5bb5da6d33f6073fe0d12b60665c2a0d" compoundref="driver__entry_8c" startline="23" endline="93">DriverEntry</referencedby>
        <referencedby refid="gpio__device_8c_1a3df79e59d8cd67d1b0ea2bdd7b7e2662" compoundref="gpio__device_8c" startline="102" endline="123">EvtDebounceTimerFunc</referencedby>
        <referencedby refid="driver__entry_8c_1a0776c179fdcbdd09df07ee264e7e78e6" compoundref="driver__entry_8c" startline="102" endline="140">EvtDriverDeviceAdd</referencedby>
        <referencedby refid="i2c__device_8c_1a50d1319f95a5bfb01ed5c3ab0f60bf8b" compoundref="i2c__device_8c" startline="397">EvtI2cInterruptIsr</referencedby>
        <referencedby refid="device__manager_8c_1a091b9ef55e7ab6472a25567a30b1bf5a" compoundref="device__manager_8c" startline="69" endline="140">EvtUsbInterruptPipeReadComplete</referencedby>
        <referencedby refid="gpio__device_8c_1a1e860d4292f8df84e5d3101f8a415d6c" compoundref="gpio__device_8c" startline="445" endline="491">GpioDevicePulse</referencedby>
        <referencedby refid="i2c__core_8c_1a3730a6f611cf9feba7ba954330f41a6c" compoundref="i2c__core_8c" startline="44" endline="105">I2CInitialize</referencedby>
        <referencedby refid="i2c__core_8c_1a0dc1e54406b75f4efa145bbb512f87fe" compoundref="i2c__core_8c" startline="321" endline="355">I2CReadRegister</referencedby>
        <referencedby refid="i2c__core_8c_1a4440e6d849d5de8720702c225f6bd83b" compoundref="i2c__core_8c" startline="361" endline="431">I2CScanBus</referencedby>
        <referencedby refid="i2c__core_8c_1a83e1937f01cd4ec9a8e227bd544a0f06" compoundref="i2c__core_8c" startline="142" endline="244">I2CTransferSynchronous</referencedby>
        <referencedby refid="i2c__core_8c_1a7e9d20258e5842242cf0a532b4d60deb" compoundref="i2c__core_8c" startline="283" endline="316">I2CWriteRegister</referencedby>
        <referencedby refid="struct__GPIO__PIN__CONTEXT_1acd3cbb2291f76aabd30090072d539050" compoundref="gpio__core_8c" startline="80" endline="101">_GPIO_PIN_CONTEXT::if</referencedby>
        <referencedby refid="gpio__device_8c_1ad94ec7eba667568bcd9afe3483282304" compoundref="gpio__device_8c" startline="195" endline="208">if</referencedby>
        <referencedby refid="i2c__device_8c_1adfac0a96ec8249c69bd820670db7f2cd" compoundref="i2c__device_8c" startline="88" endline="113">if</referencedby>
        <referencedby refid="driver__log_8c_1aa2e9424857371175fc265253fbabcc5d" compoundref="driver__log_8c" startline="135" endline="241">LogInitialize</referencedby>
        <referencedby refid="driver__log_8c_1a8e8711da6408af7b3b313f892121215e" compoundref="driver__log_8c" startline="103" endline="129">LogMessageVA</referencedby>
        <referencedby refid="driver__log_8c_1aab8bcb7121136bc236fe5d55778fbaf2" compoundref="driver__log_8c" startline="45" endline="87">LogUninitialize</referencedby>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1afdff552467cc2d2d5815831e9656cffc" prot="public" static="no">
        <name>OPTIONAL</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="54" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="54" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1aec78e7a9e90a406a56f859ee456e8eae" prot="public" static="no">
        <name>OUT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="53" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="53" bodyend="-1"/>
      </memberdef>
      <memberdef kind="define" id="precomp_8h_1a966cd377b9f3fdeb1432460c33352af1" prot="public" static="no">
        <name>WINVER</name>
        <initializer>0x0A00</initializer>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/precomp.h" line="10" column="9" bodyfile="C:/KMDF Driver1/src/precomp.h" bodystart="10" bodyend="-1"/>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">//<sp/>Precompiled<sp/>header<sp/>for<sp/>KMDFDriver1</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="2"><highlight class="normal"></highlight></codeline>
<codeline lineno="3"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>_PRECOMP_H_</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="4"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>_PRECOMP_H_</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="5"><highlight class="normal"></highlight></codeline>
<codeline lineno="6"><highlight class="normal"></highlight><highlight class="preprocessor">#pragma<sp/>once</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="comment">//<sp/>确保正确的<sp/>Windows<sp/>目标版本</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>WINVER</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10" refid="precomp_8h_1a966cd377b9f3fdeb1432460c33352af1" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>WINVER<sp/>0x0A00<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Windows<sp/>10</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>_WIN32_WINNT</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14" refid="precomp_8h_1ac50762666aa00bd3a4308158510f1748" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>_WIN32_WINNT<sp/>0x0A00<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Windows<sp/>10</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="15"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16"><highlight class="normal"></highlight></codeline>
<codeline lineno="17"><highlight class="normal"></highlight><highlight class="comment">//<sp/>标准<sp/>Windows<sp/>头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntddk.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Windows<sp/>驱动程序开发工具包核心头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdf.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>Windows<sp/>驱动程序框架头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>标准库头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;ntstrsafe.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>安全字符串函数</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdfusb.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF<sp/>USB<sp/>支持</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;usbspec.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>USB<sp/>规范定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;usb.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>USB<sp/>核心功能</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>KMDF<sp/>头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdfldr.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF<sp/>加载器</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&lt;wdfinstaller.h&gt;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF<sp/>安装程序</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30"><highlight class="normal"></highlight></codeline>
<codeline lineno="31"><highlight class="normal"></highlight><highlight class="comment">//<sp/>项目头文件</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="driver__core_8h" kindref="compound">../include/core/driver/driver_core.h</ref>&quot;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>驱动核心功能</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="device__manager_8h" kindref="compound">../include/core/device/device_manager.h</ref>&quot;</highlight><highlight class="normal"><sp/><sp/></highlight><highlight class="comment">//<sp/>设备管理</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>日志功能</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="35"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="ioctl_8h" kindref="compound">../include/common/ioctl.h</ref>&quot;</highlight><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>IOCTL<sp/>定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36"><highlight class="normal"></highlight></codeline>
<codeline lineno="37"><highlight class="normal"></highlight><highlight class="comment">//<sp/>调试支持</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38"><highlight class="normal"></highlight><highlight class="preprocessor">#if<sp/>DBG</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DEBUG_PRINT(Level,<sp/>Fmt,<sp/>...)<sp/>\</highlight></codeline>
<codeline lineno="40"><highlight class="preprocessor"><sp/><sp/><sp/><sp/>DbgPrintEx(DPFLTR_IHVDRIVER_ID,<sp/>DPFLTR_ERROR_LEVEL,<sp/>&quot;[%s]<sp/>&quot;<sp/>Fmt<sp/>&quot;\n&quot;,<sp/>\</highlight></codeline>
<codeline lineno="41"><highlight class="preprocessor"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>__FUNCTION__,<sp/>##__VA_ARGS__)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42"><highlight class="normal"></highlight><highlight class="preprocessor">#else</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="precomp_8h_1a7ba5cb5a943f312e7b50de9cd8ffaf37" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>DEBUG_PRINT(Level,<sp/>Fmt,<sp/>...)<sp/>do<sp/>{<sp/>}<sp/>while<sp/>(0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44"><highlight class="normal"></highlight><highlight class="preprocessor">#endif</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45"><highlight class="normal"></highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight><highlight class="comment">//<sp/>常用宏</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="47" refid="precomp_8h_1abf0924766241e8f46e68e2dcbca9ac5b" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>ARRAY_SIZE(Array)<sp/>(sizeof(Array)<sp/>/<sp/>sizeof((Array)[0]))</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="precomp_8h_1a3acffbd305ee72dcd4593c0d8af64a4f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>MIN(a,<sp/>b)<sp/>(((a)<sp/>&lt;<sp/>(b))<sp/>?<sp/>(a)<sp/>:<sp/>(b))</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="49" refid="precomp_8h_1afa99ec4acc4ecb2dc3c2d05da15d0e3f" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>MAX(a,<sp/>b)<sp/>(((a)<sp/>&gt;<sp/>(b))<sp/>?<sp/>(a)<sp/>:<sp/>(b))</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50"><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal"></highlight><highlight class="comment">//<sp/>函数属性</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52" refid="precomp_8h_1ac2bbd6d630a06a980d9a92ddb9a49928" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>IN</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="53" refid="precomp_8h_1aec78e7a9e90a406a56f859ee456e8eae" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>OUT</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="54" refid="precomp_8h_1afdff552467cc2d2d5815831e9656cffc" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>OPTIONAL</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55"><highlight class="normal"></highlight></codeline>
<codeline lineno="56"><highlight class="normal"></highlight><highlight class="comment">//<sp/>返回值检查宏</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57" refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>NT_SUCCESS(Status)<sp/>((NTSTATUS)(Status)<sp/>&gt;=<sp/>0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58" refid="precomp_8h_1abc91c0b65b29dc8fb94e770e4066e07c" refkind="member"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>NT_ERROR(Status)<sp/>((NTSTATUS)(Status)<sp/>&lt;<sp/>0)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59"><highlight class="normal"></highlight></codeline>
<codeline lineno="60"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>_PRECOMP_H_</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/precomp.h"/>
  </compounddef>
</doxygen>
