<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/src/hal/bus/gpio_core.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('gpio__core_8c.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">gpio_core.c File Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;<a class="el" href="precomp_8h_source.html">../../precomp.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="kmdf__gpio_8h_source.html">../../../include/hal/bus/kmdf_gpio.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="include_2core_2log_2driver__log_8h_source.html">../../../include/core/log/driver_log.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="error__codes_8h_source.html">../../../include/core/error/error_codes.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for gpio_core.c:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c__incl.png" border="0" usemap="#aC_1_2KMDF_01Driver1_2src_2hal_2bus_2gpio__core_8c" loading="lazy" alt=""/></div>
<map name="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2gpio__core_8c" id="aC_1_2KMDF_01Driver1_2src_2hal_2bus_2gpio__core_8c">
<area shape="rect" title=" " alt="" coords="335,5,480,48"/>
<area shape="rect" href="precomp_8h.html" title=" " alt="" coords="1061,96,1168,123"/>
<area shape="poly" title=" " alt="" coords="481,34,1046,98,1045,103,480,39"/>
<area shape="rect" href="error__codes_8h.html" title=" " alt="" coords="470,427,629,453"/>
<area shape="poly" title=" " alt="" coords="398,50,368,111,352,152,339,197,331,244,330,291,339,336,361,377,380,397,403,412,428,422,455,430,454,435,427,427,400,416,376,401,356,380,334,338,325,292,326,244,334,196,347,150,363,109,393,47"/>
<area shape="rect" href="include_2core_2log_2driver__log_8h.html" title=" " alt="" coords="371,344,492,371"/>
<area shape="poly" title=" " alt="" coords="402,49,388,94,374,154,370,221,373,255,381,287,393,311,409,331,405,334,389,314,376,289,368,256,364,221,369,153,383,92,397,48"/>
<area shape="rect" href="kmdf__gpio_8h.html" title=" " alt="" coords="532,253,662,280"/>
<area shape="poly" title=" " alt="" coords="419,47,455,116,481,157,511,196,538,221,566,242,563,246,534,225,507,199,477,160,451,119,415,50"/>
<area shape="rect" title=" " alt="" coords="956,171,1039,197"/>
<area shape="poly" title=" " alt="" coords="1095,125,1032,165,1029,160,1093,121"/>
<area shape="rect" title=" " alt="" coords="517,501,581,528"/>
<area shape="poly" title=" " alt="" coords="1168,109,1363,113,1622,122,1860,140,1945,153,1974,160,1993,168,2018,188,2033,209,2040,234,2042,265,2042,359,2039,373,2028,387,1989,410,1929,431,1851,448,1757,463,1651,476,1414,495,1165,507,930,513,597,516,597,511,930,508,1165,501,1414,489,1650,470,1756,458,1850,443,1928,426,1987,406,2025,383,2034,371,2037,358,2037,265,2035,235,2028,211,2014,192,1990,173,1972,165,1944,158,1859,145,1622,128,1363,118,1168,114"/>
<area shape="rect" title=" " alt="" coords="956,427,1009,453"/>
<area shape="poly" title=" " alt="" coords="1110,124,1098,159,1098,178,1103,196,1113,207,1124,214,1150,219,1177,224,1190,231,1201,244,1206,256,1207,266,1201,289,1186,340,1173,360,1152,381,1122,402,1089,417,1025,435,1023,430,1087,412,1119,397,1149,377,1169,356,1181,338,1196,287,1201,266,1201,257,1196,247,1186,235,1175,229,1149,224,1123,219,1110,212,1099,199,1092,179,1093,158,1105,122"/>
<area shape="rect" title=" " alt="" coords="1113,171,1196,197"/>
<area shape="poly" title=" " alt="" coords="1124,122,1143,156,1138,158,1119,125"/>
<area shape="rect" title=" " alt="" coords="1220,171,1302,197"/>
<area shape="poly" title=" " alt="" coords="1141,121,1223,161,1221,166,1139,126"/>
<area shape="rect" href="src_2core_2log_2driver__log_8h.html" title=" " alt="" coords="1439,344,1577,371"/>
<area shape="poly" title=" " alt="" coords="1168,108,1360,109,1612,116,1735,123,1842,134,1922,149,1949,158,1966,169,1983,191,1990,212,1988,232,1978,250,1962,266,1940,280,1882,303,1811,322,1735,336,1593,353,1592,348,1734,331,1810,317,1880,298,1937,275,1959,261,1974,247,1983,231,1984,213,1978,194,1962,173,1947,163,1921,154,1841,139,1735,128,1612,121,1360,114,1168,113"/>
<area shape="rect" href="error__handling_8h.html" title="驱动程序错误处理和断言宏定义" alt="" coords="22,344,194,371"/>
<area shape="poly" title=" " alt="" coords="1061,115,870,124,610,146,477,163,356,185,255,213,215,230,184,248,163,266,146,287,121,331,117,328,142,284,160,262,181,243,213,225,253,208,355,180,477,158,609,140,869,119,1061,110"/>
<area shape="rect" href="driver__core_8h.html" title=" " alt="" coords="390,245,508,288"/>
<area shape="poly" title=" " alt="" coords="1061,113,933,114,771,122,690,129,616,140,555,154,511,173,496,185,483,200,464,232,459,230,479,197,492,181,508,168,553,149,615,135,689,124,770,116,933,109,1061,107"/>
<area shape="rect" href="device__manager_8h.html" title="Brief description." alt="" coords="1031,245,1161,288"/>
<area shape="poly" title=" " alt="" coords="1061,116,1030,122,999,133,969,149,946,172,942,184,946,196,956,206,974,217,1019,237,1017,242,971,221,953,210,942,199,936,184,942,169,966,145,996,128,1029,117,1060,111"/>
<area shape="rect" title=" " alt="" coords="1326,171,1460,197"/>
<area shape="poly" title=" " alt="" coords="1164,121,1331,164,1329,169,1162,126"/>
<area shape="rect" title=" " alt="" coords="1484,171,1612,197"/>
<area shape="poly" title=" " alt="" coords="1169,117,1469,167,1468,172,1168,122"/>
<area shape="rect" title=" " alt="" coords="1636,171,1764,197"/>
<area shape="poly" title=" " alt="" coords="1168,114,1363,135,1621,168,1620,173,1363,141,1168,119"/>
<area shape="rect" href="gpio__device_8h.html" title=" " alt="" coords="521,171,692,197"/>
<area shape="poly" title=" " alt="" coords="1061,121,707,171,707,166,1061,116"/>
<area shape="rect" href="i2c__device_8h.html" title=" " alt="" coords="716,171,881,197"/>
<area shape="poly" title=" " alt="" coords="1061,125,869,169,868,164,1060,120"/>
<area shape="rect" href="spi__device_8h.html" title=" " alt="" coords="1787,171,1951,197"/>
<area shape="poly" title=" " alt="" coords="1168,112,1424,134,1772,168,1772,173,1424,139,1168,118"/>
<area shape="poly" title=" " alt="" coords="1503,373,1472,415,1448,438,1421,456,1371,468,1284,479,1045,497,788,509,597,515,597,509,788,503,1045,492,1284,474,1370,463,1419,451,1445,434,1468,412,1498,370"/>
<area shape="poly" title=" " alt="" coords="1438,372,1025,435,1024,430,1438,367"/>
<area shape="poly" title=" " alt="" coords="119,369,158,410,185,433,214,451,290,479,368,497,442,507,502,510,502,516,441,512,367,502,288,484,212,456,181,437,154,414,115,373"/>
<area shape="poly" title=" " alt="" coords="195,364,941,432,940,437,194,369"/>
<area shape="poly" title=" " alt="" coords="178,369,465,421,464,426,177,374"/>
<area shape="rect" title=" " alt="" coords="5,427,144,453"/>
<area shape="poly" title=" " alt="" coords="105,372,88,413,83,411,100,370"/>
<area shape="poly" title=" " alt="" coords="552,454,552,486,546,486,546,454"/>
<area shape="poly" title=" " alt="" coords="509,284,585,310,642,334,660,345,671,356,683,366,700,376,763,400,829,416,941,433,940,438,828,421,762,405,697,381,680,371,668,360,656,349,640,338,583,315,508,289"/>
<area shape="poly" title=" " alt="" coords="470,287,506,335,527,374,542,411,537,413,522,376,502,337,466,290"/>
<area shape="rect" title=" " alt="" coords="568,344,629,371"/>
<area shape="poly" title=" " alt="" coords="486,286,565,333,563,338,483,291"/>
<area shape="poly" title=" " alt="" coords="448,289,440,329,435,328,443,288"/>
<area shape="poly" title=" " alt="" coords="435,370,441,409,448,431,459,452,481,474,507,492,504,496,478,478,455,455,443,434,436,410,430,371"/>
<area shape="poly" title=" " alt="" coords="493,366,556,376,941,430,940,436,555,381,493,371"/>
<area shape="poly" title=" " alt="" coords="1097,289,1091,326,1079,371,1057,418,1042,438,1023,456,989,472,942,486,824,504,699,513,597,516,597,510,699,507,824,498,941,481,987,468,1020,451,1038,435,1053,415,1074,370,1086,324,1092,288"/>
<area shape="poly" title=" " alt="" coords="1084,290,1001,415,997,412,1080,287"/>
<area shape="poly" title=" " alt="" coords="1076,291,1019,337,983,361,944,381,867,406,788,423,712,434,645,439,644,434,712,428,788,418,866,401,941,376,980,357,1016,333,1073,287"/>
<area shape="rect" href="kmdf__bus__common_8h.html" title=" " alt="" coords="810,336,931,379"/>
<area shape="poly" title=" " alt="" coords="1044,291,938,332,936,327,1042,286"/>
<area shape="poly" title=" " alt="" coords="901,377,954,415,951,420,898,381"/>
<area shape="poly" title=" " alt="" coords="810,376,615,425,614,420,809,371"/>
<area shape="poly" title=" " alt="" coords="520,193,453,200,383,211,324,227,301,236,286,247,271,267,267,286,266,336,259,371,252,399,252,425,266,452,281,464,304,474,367,491,438,502,502,508,501,513,437,507,366,496,302,479,278,468,262,455,247,426,247,399,254,370,261,336,262,286,266,265,282,243,299,232,322,222,382,206,452,195,520,188"/>
<area shape="poly" title=" " alt="" coords="623,196,675,243,710,282,734,315,760,346,800,376,835,395,872,410,941,429,940,434,871,415,833,400,797,381,756,349,730,319,706,286,671,247,619,200"/>
<area shape="poly" title=" " alt="" coords="608,198,603,238,598,238,602,198"/>
<area shape="poly" title=" " alt="" coords="637,278,796,329,795,334,636,283"/>
<area shape="poly" title=" " alt="" coords="716,199,505,227,380,248,356,277,346,307,348,340,361,378,373,399,387,418,424,451,465,476,504,495,501,499,462,481,421,455,383,422,368,402,356,380,343,341,340,306,351,274,377,243,504,222,716,193"/>
<area shape="poly" title=" " alt="" coords="882,190,1060,212,1135,227,1161,235,1175,244,1189,271,1187,298,1173,324,1149,349,1120,371,1087,392,1025,424,1022,419,1084,387,1116,367,1146,345,1168,321,1182,296,1184,272,1171,247,1158,240,1134,232,1059,217,881,196"/>
<area shape="rect" href="kmdf__i2c_8h.html" title=" " alt="" coords="737,253,860,280"/>
<area shape="poly" title=" " alt="" coords="801,198,801,238,796,238,796,198"/>
<area shape="poly" title=" " alt="" coords="811,279,846,322,842,326,807,282"/>
<area shape="poly" title=" " alt="" coords="1833,200,1427,339,1309,381,1182,423,1052,456,924,478,796,495,597,513,597,508,796,489,923,473,1051,451,1180,418,1307,376,1426,333,1831,195"/>
<area shape="poly" title=" " alt="" coords="1787,199,1776,200,1715,205,1662,205,1572,201,1529,202,1485,208,1438,223,1384,248,1362,262,1347,278,1327,312,1307,348,1291,365,1269,381,1206,409,1140,427,1077,437,1025,441,1025,435,1076,432,1139,422,1205,404,1266,376,1287,361,1302,345,1322,310,1343,275,1359,258,1381,243,1436,218,1484,203,1529,197,1572,196,1663,200,1715,200,1776,195,1786,193"/>
<area shape="rect" href="kmdf__spi_8h.html" title=" " alt="" coords="884,253,1006,280"/>
<area shape="poly" title=" " alt="" coords="1787,199,1776,200,1661,209,1563,213,1396,215,1230,222,1133,231,1019,248,1001,252,1000,246,1018,243,1132,226,1230,216,1396,210,1563,208,1660,204,1776,195,1786,194"/>
<area shape="poly" title=" " alt="" coords="937,282,900,326,896,322,932,279"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-nested-classes" class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:_5FGPIO_5FPIN_5FCONTEXT" id="r__5FGPIO_5FPIN_5FCONTEXT"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a></td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-define-members" class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aaccace669b39ad606306ac907224ae82" id="r_aaccace669b39ad606306ac907224ae82"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aaccace669b39ad606306ac907224ae82">GPIO_POOL_TAG</a>&#160;&#160;&#160;'OIPG'</td></tr>
<tr class="memitem:a09573d341b2d8f94a213241de2444b0b" id="r_a09573d341b2d8f94a213241de2444b0b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09573d341b2d8f94a213241de2444b0b">IOCTL_GPIO_GET_VALUE</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:aa5235f4dd44bf922bf5befb2ef0b3b4b" id="r_aa5235f4dd44bf922bf5befb2ef0b3b4b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5235f4dd44bf922bf5befb2ef0b3b4b">IOCTL_GPIO_SET_DIRECTION</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
<tr class="memitem:a921358974fe0b0cbe1288fd8bdc34196" id="r_a921358974fe0b0cbe1288fd8bdc34196"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a921358974fe0b0cbe1288fd8bdc34196">IOCTL_GPIO_SET_VALUE</a>&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-func-members" class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a50ea4c2976c29c52387cd273dc289c3b" id="r_a50ea4c2976c29c52387cd273dc289c3b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a50ea4c2976c29c52387cd273dc289c3b">GPIOInitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ <a class="el" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a> GpioConfig)</td></tr>
<tr class="memitem:a785f00e9c0879fb478077d2cdce99906" id="r_a785f00e9c0879fb478077d2cdce99906"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a785f00e9c0879fb478077d2cdce99906">GPIOUninitialize</a> (_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device, _In_ ULONG <a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a1a243a15dd793b6d0f7b7011461a8641" id="r_a1a243a15dd793b6d0f7b7011461a8641"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a243a15dd793b6d0f7b7011461a8641">if</a> (!<a class="el" href="precomp_8h.html#ad14231612b7a675d33f0ead0b695d21a">NT_SUCCESS</a>(<a class="el" href="#a9611b3a00430a86619b5923de30f9fdb">status</a>))</td></tr>
<tr class="memitem:a495a75edfacf86c27c05ce15ada3509e" id="r_a495a75edfacf86c27c05ce15ada3509e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a495a75edfacf86c27c05ce15ada3509e">if</a> (gpioManager==NULL)</td></tr>
<tr class="memitem:a977bbe3e09136dd34381e7f1b889a570" id="r_a977bbe3e09136dd34381e7f1b889a570"><td class="memItemLeft" align="right" valign="top">struct <a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a977bbe3e09136dd34381e7f1b889a570">if</a> (<a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a>==NULL)</td></tr>
<tr class="memitem:a9f7acfc8c5f9d7d32170223f628f766b" id="r_a9f7acfc8c5f9d7d32170223f628f766b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9f7acfc8c5f9d7d32170223f628f766b">LogError</a> (ERROR_PIN_ALREADY_REGISTERED, __FUNCTION__, __LINE__, &quot;Pin %d is already configured or out of range&quot;, GpioConfig-&gt;<a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a8f2aeb9f5f8525345d3f33df4340da13" id="r_a8f2aeb9f5f8525345d3f33df4340da13"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8f2aeb9f5f8525345d3f33df4340da13">LogInfo</a> (__FUNCTION__, __LINE__, &quot;GPIO pin %d initialized successfully&quot;, GpioConfig-&gt;<a class="el" href="gpio__device_8c.html#a2105af29d2c177b4d5c5d8e589b1caa3">PinNumber</a>)</td></tr>
<tr class="memitem:a401edc28835c1919fe788f9583c5b963" id="r_a401edc28835c1919fe788f9583c5b963"><td class="memItemLeft" align="right" valign="top"><a class="el" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a> (<a class="el" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a>)</td></tr>
<tr class="memitem:aa5ccd638c5bf670b734784f2601b7ec7" id="r_aa5ccd638c5bf670b734784f2601b7ec7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa5ccd638c5bf670b734784f2601b7ec7">RtlZeroMemory</a> (<a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a>, sizeof(GPIO_PIN_CONTEXT))</td></tr>
<tr class="memitem:aeb72a7a8c0020bfdcb5022360e8bd5ab" id="r_aeb72a7a8c0020bfdcb5022360e8bd5ab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeb72a7a8c0020bfdcb5022360e8bd5ab">WdfSpinLockRelease</a> (gpioManager-&gt;<a class="el" href="#ac91080baa5062b15cd46c8e08028fd51">Lock</a>)</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 id="header-var-members" class="groupheader"><a id="var-members" name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ac5d07e96f745ca56ee79420f0f039f66" id="r_ac5d07e96f745ca56ee79420f0f039f66"><td class="memItemLeft" align="right" valign="top">WDF_OBJECT_ATTRIBUTES_INIT &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac5d07e96f745ca56ee79420f0f039f66">attributes</a></td></tr>
<tr class="memitem:af0a4fb8c0dd33529dad81e93a0b0661f" id="r_af0a4fb8c0dd33529dad81e93a0b0661f"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_DPC&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af0a4fb8c0dd33529dad81e93a0b0661f">EvtGpioInterruptDpc</a></td></tr>
<tr class="memitem:a4d9377385f26f2d958a494c7ffbcc04f" id="r_a4d9377385f26f2d958a494c7ffbcc04f"><td class="memItemLeft" align="right" valign="top">EVT_WDF_INTERRUPT_ISR&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4d9377385f26f2d958a494c7ffbcc04f">EvtGpioInterruptIsr</a></td></tr>
<tr class="memitem:a319be52f8fb7536ca4d2f35163ab0ad3" id="r_a319be52f8fb7536ca4d2f35163ab0ad3"><td class="memItemLeft" align="right" valign="top">GENERIC_READ&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a319be52f8fb7536ca4d2f35163ab0ad3">GENERIC_WRITE</a></td></tr>
<tr class="memitem:a33c00fdea3f12acb400049b8ef710ea9" id="r_a33c00fdea3f12acb400049b8ef710ea9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a33c00fdea3f12acb400049b8ef710ea9">Initialized</a> = TRUE</td></tr>
<tr class="memitem:ac6c56d4f54252f6088c0d841efbc597e" id="r_ac6c56d4f54252f6088c0d841efbc597e"><td class="memItemLeft" align="right" valign="top">&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac6c56d4f54252f6088c0d841efbc597e">interruptConfig</a></td></tr>
<tr class="memitem:a4983b2b08534e2a12b6e0c30d87594f0" id="r_a4983b2b08534e2a12b6e0c30d87594f0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#ac6c56d4f54252f6088c0d841efbc597e">interruptConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4983b2b08534e2a12b6e0c30d87594f0">InterruptTranslated</a> = TRUE</td></tr>
<tr class="memitem:ac91080baa5062b15cd46c8e08028fd51" id="r_ac91080baa5062b15cd46c8e08028fd51"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac91080baa5062b15cd46c8e08028fd51">Lock</a></td></tr>
<tr class="memitem:ad7d33086f63a42bdcbecdd995751fb96" id="r_ad7d33086f63a42bdcbecdd995751fb96"><td class="memItemLeft" align="right" valign="top">&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad7d33086f63a42bdcbecdd995751fb96">openParams</a></td></tr>
<tr class="memitem:a9bfd47e9b367e692d16fa7a38e3944cc" id="r_a9bfd47e9b367e692d16fa7a38e3944cc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#ac5d07e96f745ca56ee79420f0f039f66">attributes</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9bfd47e9b367e692d16fa7a38e3944cc">ParentObject</a> = Device</td></tr>
<tr class="memitem:a41283b4328aa08e8f095578a73755e08" id="r_a41283b4328aa08e8f095578a73755e08"><td class="memItemLeft" align="right" valign="top"><a class="el" href="#ac6c56d4f54252f6088c0d841efbc597e">interruptConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a41283b4328aa08e8f095578a73755e08">PassiveHandling</a> = FALSE</td></tr>
<tr class="memitem:a3a285e05689c30bbe096ff555f7a5b68" id="r_a3a285e05689c30bbe096ff555f7a5b68"><td class="memItemLeft" align="right" valign="top">gpioManager&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3a285e05689c30bbe096ff555f7a5b68">PinCount</a></td></tr>
<tr class="memitem:aa601b044abcd0035f84077010771020b" id="r_aa601b044abcd0035f84077010771020b"><td class="memItemLeft" align="right" valign="top">PGPIO_PIN_CONTEXT&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa601b044abcd0035f84077010771020b">Pins</a> [256]</td></tr>
<tr class="memitem:af7d60c8c4b9613f737c7d254aced2bde" id="r_af7d60c8c4b9613f737c7d254aced2bde"><td class="memItemLeft" align="right" valign="top">&amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af7d60c8c4b9613f737c7d254aced2bde">spbDevicePath</a></td></tr>
<tr class="memitem:af781006198c718a3a6e46d55cdb1e74c" id="r_af781006198c718a3a6e46d55cdb1e74c"><td class="memItemLeft" align="right" valign="top">WDFIOTARGET&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af781006198c718a3a6e46d55cdb1e74c">SpbIoTarget</a></td></tr>
<tr class="memitem:a9611b3a00430a86619b5923de30f9fdb" id="r_a9611b3a00430a86619b5923de30f9fdb"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9611b3a00430a86619b5923de30f9fdb">status</a> = WdfIoTargetCreate(Device, &amp;<a class="el" href="#ac5d07e96f745ca56ee79420f0f039f66">attributes</a>, &amp;<a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a>-&gt;<a class="el" href="#af781006198c718a3a6e46d55cdb1e74c">SpbIoTarget</a>)</td></tr>
<tr class="memitem:a29feb6a8256030d356f8bfd96118d1cf" id="r_a29feb6a8256030d356f8bfd96118d1cf"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a29feb6a8256030d356f8bfd96118d1cf">STATUS_INVALID_PARAMETER</a></td></tr>
<tr class="memitem:a77b4762318f24dff847f94f382cfeea6" id="r_a77b4762318f24dff847f94f382cfeea6"><td class="memItemLeft" align="right" valign="top">return&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a77b4762318f24dff847f94f382cfeea6">STATUS_SUCCESS</a></td></tr>
<tr class="memitem:a11ec07dcb5c1cea421134a0b149443a5" id="r_a11ec07dcb5c1cea421134a0b149443a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11ec07dcb5c1cea421134a0b149443a5">WdfDevice</a> = Device</td></tr>
</table>
<a name="doc-define-members" id="doc-define-members"></a><h2 id="header-doc-define-members" class="groupheader">Macro Definition Documentation</h2>
<a id="aaccace669b39ad606306ac907224ae82" name="aaccace669b39ad606306ac907224ae82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaccace669b39ad606306ac907224ae82">&#9670;&#160;</a></span>GPIO_POOL_TAG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define GPIO_POOL_TAG&#160;&#160;&#160;'OIPG'</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a09573d341b2d8f94a213241de2444b0b" name="a09573d341b2d8f94a213241de2444b0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09573d341b2d8f94a213241de2444b0b">&#9670;&#160;</a></span>IOCTL_GPIO_GET_VALUE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_GPIO_GET_VALUE&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa5235f4dd44bf922bf5befb2ef0b3b4b" name="aa5235f4dd44bf922bf5befb2ef0b3b4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5235f4dd44bf922bf5befb2ef0b3b4b">&#9670;&#160;</a></span>IOCTL_GPIO_SET_DIRECTION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_GPIO_SET_DIRECTION&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a921358974fe0b0cbe1288fd8bdc34196" name="a921358974fe0b0cbe1288fd8bdc34196"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a921358974fe0b0cbe1288fd8bdc34196">&#9670;&#160;</a></span>IOCTL_GPIO_SET_VALUE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define IOCTL_GPIO_SET_VALUE&#160;&#160;&#160;CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a name="doc-func-members" id="doc-func-members"></a><h2 id="header-doc-func-members" class="groupheader">Function Documentation</h2>
<a id="a50ea4c2976c29c52387cd273dc289c3b" name="a50ea4c2976c29c52387cd273dc289c3b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50ea4c2976c29c52387cd273dc289c3b">&#9670;&#160;</a></span>GPIOInitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS if::GPIOInitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ <a class="el" href="kmdf__gpio_8h.html#aa14439653449111e1deb51cf90c5b7a0">PGPIO_PIN_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em>GpioConfig</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a50ea4c2976c29c52387cd273dc289c3b_cgraph.png" border="0" usemap="#agpio__core_8c_a50ea4c2976c29c52387cd273dc289c3b_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a50ea4c2976c29c52387cd273dc289c3b_cgraph" id="agpio__core_8c_a50ea4c2976c29c52387cd273dc289c3b_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,108,56"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="156,29,240,56"/>
<area shape="poly" title=" " alt="" coords="108,40,140,40,140,45,108,45"/>
<area shape="poly" title=" " alt="" coords="173,30,169,21,172,11,182,5,198,3,215,5,224,12,221,16,213,10,198,8,184,10,176,14,174,20,178,28"/>
</map>
</div>

</div>
</div>
<a id="a785f00e9c0879fb478077d2cdce99906" name="a785f00e9c0879fb478077d2cdce99906"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a785f00e9c0879fb478077d2cdce99906">&#9670;&#160;</a></span>GPIOUninitialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID GPIOUninitialize </td>
          <td>(</td>
          <td class="paramtype">_In_ <a class="el" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></td>          <td class="paramname"><span class="paramname"><em>Device</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">_In_ ULONG</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a785f00e9c0879fb478077d2cdce99906_cgraph.png" border="0" usemap="#agpio__core_8c_a785f00e9c0879fb478077d2cdce99906_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a785f00e9c0879fb478077d2cdce99906_cgraph" id="agpio__core_8c_a785f00e9c0879fb478077d2cdce99906_cgraph">
<area shape="rect" title=" " alt="" coords="5,56,124,83"/>
<area shape="rect" href="spi__device_8c.html#ae42ccb14fff6c8b1c06d1ff178b6c146" title=" " alt="" coords="177,5,314,32"/>
<area shape="poly" title=" " alt="" coords="114,53,180,34,181,39,116,58"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="367,81,451,108"/>
<area shape="poly" title=" " alt="" coords="124,71,351,88,351,93,124,76"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="172,107,319,133"/>
<area shape="poly" title=" " alt="" coords="116,81,181,99,180,105,114,86"/>
<area shape="poly" title=" " alt="" coords="381,82,376,73,379,63,391,57,409,55,429,57,440,64,437,68,427,62,409,60,392,62,383,67,381,72,385,80"/>
<area shape="poly" title=" " alt="" coords="319,106,351,101,352,106,320,111"/>
</map>
</div>

</div>
</div>
<a id="a1a243a15dd793b6d0f7b7011461a8641" name="a1a243a15dd793b6d0f7b7011461a8641"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a243a15dd793b6d0f7b7011461a8641">&#9670;&#160;</a></span>if() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if </td>
          <td>(</td>
          <td class="paramtype">!</td>          <td class="paramname"><span class="paramname"><em>NT_SUCCESS</em></span>status</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph.png" border="0" usemap="#agpio__core_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph" id="agpio__core_8c_a1a243a15dd793b6d0f7b7011461a8641_cgraph">
<area shape="rect" title=" " alt="" coords="5,81,44,108"/>
<area shape="rect" href="spi__device_8c.html#ae42ccb14fff6c8b1c06d1ff178b6c146" title=" " alt="" coords="97,5,234,32"/>
<area shape="poly" title=" " alt="" coords="39,79,62,60,90,42,102,36,105,41,93,46,65,64,42,83"/>
<area shape="rect" href="gpio__core_8c.html#a9f7acfc8c5f9d7d32170223f628f766b" title=" " alt="" coords="130,56,201,83"/>
<area shape="poly" title=" " alt="" coords="43,89,115,76,116,81,44,94"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="287,107,371,133"/>
<area shape="poly" title=" " alt="" coords="45,94,272,113,271,118,44,99"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="92,157,239,184"/>
<area shape="poly" title=" " alt="" coords="46,106,93,136,117,148,115,153,90,141,43,111"/>
<area shape="poly" title=" " alt="" coords="202,78,273,100,272,105,200,83"/>
<area shape="poly" title=" " alt="" coords="301,107,296,98,299,88,311,82,329,80,349,83,360,89,357,94,347,88,329,85,312,87,303,92,301,97,306,105"/>
<area shape="poly" title=" " alt="" coords="210,154,272,135,274,140,212,159"/>
</map>
</div>

</div>
</div>
<a id="a495a75edfacf86c27c05ce15ada3509e" name="a495a75edfacf86c27c05ce15ada3509e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a495a75edfacf86c27c05ce15ada3509e">&#9670;&#160;</a></span>if() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if::if </td>
          <td>(</td>
          <td class="paramtype">gpioManager</td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a977bbe3e09136dd34381e7f1b889a570" name="a977bbe3e09136dd34381e7f1b889a570"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a977bbe3e09136dd34381e7f1b889a570">&#9670;&#160;</a></span>if() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct <a class="el" href="struct__GPIO__PIN__CONTEXT.html">_GPIO_PIN_CONTEXT</a> if </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a></td>          <td class="paramname"><span class="paramname"><em></em></span><span class="paramdefsep"> = </span><span class="paramdefval">=&#160;NULL</span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9f7acfc8c5f9d7d32170223f628f766b" name="a9f7acfc8c5f9d7d32170223f628f766b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f7acfc8c5f9d7d32170223f628f766b">&#9670;&#160;</a></span>LogError()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">if::LogError </td>
          <td>(</td>
          <td class="paramtype">ERROR_PIN_ALREADY_REGISTERED</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;Pin %d is already configured or out of range&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">GpioConfig-&gt;</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_cgraph.png" border="0" usemap="#agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_cgraph" id="agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,76,56"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="124,29,208,56"/>
<area shape="poly" title=" " alt="" coords="76,40,108,40,108,45,76,45"/>
<area shape="poly" title=" " alt="" coords="143,30,140,21,142,11,151,5,166,3,181,5,190,12,186,16,179,10,166,8,153,10,147,14,145,20,148,28"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_icgraph.png" border="0" usemap="#agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_icgraph" id="agpio__core_8c_a9f7acfc8c5f9d7d32170223f628f766b_icgraph">
<area shape="rect" title=" " alt="" coords="92,5,162,32"/>
<area shape="rect" href="gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641" title=" " alt="" coords="5,5,44,32"/>
<area shape="poly" title=" " alt="" coords="76,21,44,21,44,16,76,16"/>
</map>
</div>

</div>
</div>
<a id="a8f2aeb9f5f8525345d3f33df4340da13" name="a8f2aeb9f5f8525345d3f33df4340da13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f2aeb9f5f8525345d3f33df4340da13">&#9670;&#160;</a></span>LogInfo()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">LogInfo </td>
          <td>(</td>
          <td class="paramtype">__FUNCTION__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">__LINE__</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;GPIO pin %d initialized successfully&quot;</td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">GpioConfig-&gt;</td>          <td class="paramname"><span class="paramname"><em>PinNumber</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a401edc28835c1919fe788f9583c5b963" name="a401edc28835c1919fe788f9583c5b963"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a401edc28835c1919fe788f9583c5b963">&#9670;&#160;</a></span>pinContext()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="i2c__device_8c.html#ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</a> &amp; pinContext </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="kmdf__gpio_8h.html#ab852084eda7787e469301a172c7498a5">GPIO_PIN_CONFIG</a></td>          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a401edc28835c1919fe788f9583c5b963_cgraph.png" border="0" usemap="#agpio__core_8c_a401edc28835c1919fe788f9583c5b963_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a401edc28835c1919fe788f9583c5b963_cgraph" id="agpio__core_8c_a401edc28835c1919fe788f9583c5b963_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,90,56"/>
<area shape="poly" title=" " alt="" coords="31,29,31,12,37,5,48,3,58,5,64,12,60,15,56,10,47,8,40,10,36,14,37,29"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_a401edc28835c1919fe788f9583c5b963_icgraph.png" border="0" usemap="#agpio__core_8c_a401edc28835c1919fe788f9583c5b963_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_a401edc28835c1919fe788f9583c5b963_icgraph" id="agpio__core_8c_a401edc28835c1919fe788f9583c5b963_icgraph">
<area shape="rect" title=" " alt="" coords="718,148,803,175"/>
<area shape="poly" title=" " alt="" coords="790,135,780,129,760,127,742,129,733,133,730,139,735,146,730,149,725,139,729,130,741,124,761,121,782,124,793,130"/>
<area shape="rect" href="struct__GPIO__PIN__CONTEXT.html#ab343a956248ebb07de6a72eaeb55ec35" title=" " alt="" coords="501,5,670,48"/>
<area shape="poly" title=" " alt="" coords="740,137,709,99,669,62,650,51,652,46,672,58,713,95,744,134"/>
<area shape="rect" href="gpio__core_8c.html#a50ea4c2976c29c52387cd273dc289c3b" title=" " alt="" coords="534,72,637,99"/>
<area shape="poly" title=" " alt="" coords="718,143,669,120,624,102,626,97,671,115,720,138"/>
<area shape="rect" href="gpio__core_8c.html#a785f00e9c0879fb478077d2cdce99906" title=" " alt="" coords="281,136,399,163"/>
<area shape="poly" title=" " alt="" coords="703,162,399,154,399,148,703,157"/>
<area shape="rect" href="gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641" title=" " alt="" coords="321,199,359,225"/>
<area shape="poly" title=" " alt="" coords="703,171,360,212,359,207,703,166"/>
<area shape="rect" href="gpio__core_8c.html#a9f7acfc8c5f9d7d32170223f628f766b" title=" " alt="" coords="550,224,621,251"/>
<area shape="poly" title=" " alt="" coords="720,185,671,208,622,227,620,222,669,203,718,180"/>
<area shape="rect" href="gpio__core_8c.html#aeb72a7a8c0020bfdcb5022360e8bd5ab" title=" " alt="" coords="512,275,659,301"/>
<area shape="poly" title=" " alt="" coords="745,189,714,228,672,265,649,276,646,272,669,260,710,225,740,186"/>
<area shape="poly" title=" " alt="" coords="535,235,359,217,360,211,535,230"/>
<area shape="poly" title=" " alt="" coords="506,269,499,265,482,245,474,226,467,207,451,189,426,174,399,163,400,158,429,169,455,185,472,204,479,224,487,243,503,261,509,265"/>
<area shape="poly" title=" " alt="" coords="518,272,500,265,476,252,452,240,402,225,359,217,360,212,403,220,454,235,478,247,502,260,520,267"/>
<area shape="rect" href="device__manager_8c.html#ad0a38f6ee5ec061af8f147cb6f9850aa" title=" " alt="" coords="282,249,398,276"/>
<area shape="poly" title=" " alt="" coords="496,281,398,271,398,266,496,276"/>
<area shape="rect" href="device__manager_8c.html#a091b9ef55e7ab6472a25567a30b1bf5a" title=" " alt="" coords="227,300,453,327"/>
<area shape="poly" title=" " alt="" coords="496,300,454,304,453,299,496,295"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="257,351,423,377"/>
<area shape="poly" title=" " alt="" coords="539,310,454,341,411,353,410,347,452,336,538,305"/>
<area shape="rect" href="i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe" title=" " alt="" coords="31,275,153,301"/>
<area shape="poly" title=" " alt="" coords="249,349,226,341,201,329,178,316,145,304,147,299,180,311,204,324,228,336,251,344"/>
<area shape="rect" href="i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b" title=" " alt="" coords="44,325,140,352"/>
<area shape="poly" title=" " alt="" coords="241,357,140,346,140,341,241,351"/>
<area shape="rect" href="i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4" title=" " alt="" coords="5,376,179,403"/>
<area shape="poly" title=" " alt="" coords="242,377,179,383,179,378,241,371"/>
<area shape="rect" href="i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb" title=" " alt="" coords="31,427,153,453"/>
<area shape="poly" title=" " alt="" coords="282,385,179,417,142,429,140,424,178,412,280,380"/>
</map>
</div>

</div>
</div>
<a id="aa5ccd638c5bf670b734784f2601b7ec7" name="aa5ccd638c5bf670b734784f2601b7ec7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5ccd638c5bf670b734784f2601b7ec7">&#9670;&#160;</a></span>RtlZeroMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">RtlZeroMemory </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a></td>          <td class="paramname"><span class="paramname"><em></em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">sizeof(GPIO_PIN_CONTEXT)</td>          <td class="paramname"><span class="paramname"><em></em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_aa5ccd638c5bf670b734784f2601b7ec7_icgraph.png" border="0" usemap="#agpio__core_8c_aa5ccd638c5bf670b734784f2601b7ec7_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_aa5ccd638c5bf670b734784f2601b7ec7_icgraph" id="agpio__core_8c_aa5ccd638c5bf670b734784f2601b7ec7_icgraph">
<area shape="rect" title=" " alt="" coords="690,132,801,159"/>
<area shape="rect" href="device__manager_8c.html#aebab0b9bc330432c9faaf78df6cfb6b2" title=" " alt="" coords="497,5,623,32"/>
<area shape="poly" title=" " alt="" coords="720,122,684,87,640,53,604,35,606,30,643,48,688,83,724,118"/>
<area shape="rect" href="driver__entry_8c.html#a0776c179fdcbdd09df07ee264e7e78e6" title=" " alt="" coords="141,56,281,83"/>
<area shape="poly" title=" " alt="" coords="704,127,641,104,545,86,447,75,356,71,281,70,281,65,356,66,447,70,545,80,642,99,706,122"/>
<area shape="rect" href="driver__entry_8c.html#a5bb5da6d33f6073fe0d12b60665c2a0d" title=" " alt="" coords="5,107,93,133"/>
<area shape="poly" title=" " alt="" coords="674,145,93,124,93,119,674,140"/>
<area shape="rect" href="device__manager_8c.html#abadb1053ad035a1858c6f71af0f00d56" title=" " alt="" coords="478,157,642,184"/>
<area shape="poly" title=" " alt="" coords="675,158,642,162,642,157,674,152"/>
<area shape="rect" href="driver__core_8c.html#aac97f3e68a787ac88617369283c60b79" title=" " alt="" coords="494,208,626,235"/>
<area shape="poly" title=" " alt="" coords="706,169,643,198,611,210,609,205,641,194,704,164"/>
<area shape="rect" href="driver__log_8c.html#aa7f5f3b01615029c1cb54753c0b03175" title=" " alt="" coords="511,259,609,285"/>
<area shape="poly" title=" " alt="" coords="726,173,690,212,643,249,611,263,609,258,640,244,687,208,722,169"/>
<area shape="rect" href="device__manager_8c.html#a85f48e60bea1385e67ec52def6e57442" title=" " alt="" coords="329,5,430,32"/>
<area shape="poly" title=" " alt="" coords="482,21,431,21,431,16,482,16"/>
<area shape="poly" title=" " alt="" coords="319,39,259,58,257,53,318,34"/>
<area shape="poly" title=" " alt="" coords="152,90,94,109,92,104,150,85"/>
<area shape="rect" href="driver__entry_8c.html#a075700d7117ddde115f3bb0db54b619e" title=" " alt="" coords="152,169,270,196"/>
<area shape="poly" title=" " alt="" coords="478,215,270,192,270,186,479,210"/>
<area shape="poly" title=" " alt="" coords="159,166,85,136,87,131,161,161"/>
</map>
</div>

</div>
</div>
<a id="aeb72a7a8c0020bfdcb5022360e8bd5ab" name="aeb72a7a8c0020bfdcb5022360e8bd5ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb72a7a8c0020bfdcb5022360e8bd5ab">&#9670;&#160;</a></span>WdfSpinLockRelease()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">WdfSpinLockRelease </td>
          <td>(</td>
          <td class="paramtype">gpioManager-&gt;</td>          <td class="paramname"><span class="paramname"><em>Lock</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_cgraph.png" border="0" usemap="#agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_cgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_cgraph" id="agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_cgraph">
<area shape="rect" title=" " alt="" coords="5,29,153,56"/>
<area shape="rect" href="gpio__core_8c.html#a401edc28835c1919fe788f9583c5b963" title=" " alt="" coords="201,29,285,56"/>
<area shape="poly" title=" " alt="" coords="153,40,185,40,185,45,153,45"/>
<area shape="poly" title=" " alt="" coords="214,30,210,21,213,11,224,5,243,3,263,5,273,12,270,16,261,10,243,8,226,10,217,15,215,20,219,28"/>
</map>
</div>
<div class="dynheader">
Here is the caller graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="gpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_icgraph.png" border="0" usemap="#agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_icgraph" loading="lazy" alt=""/></div>
<map name="agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_icgraph" id="agpio__core_8c_aeb72a7a8c0020bfdcb5022360e8bd5ab_icgraph">
<area shape="rect" title=" " alt="" coords="501,107,648,133"/>
<area shape="rect" href="device__manager_8c.html#ad0a38f6ee5ec061af8f147cb6f9850aa" title=" " alt="" coords="282,5,398,32"/>
<area shape="poly" title=" " alt="" coords="542,100,501,72,452,46,397,30,399,25,454,42,503,67,545,95"/>
<area shape="rect" href="device__manager_8c.html#a091b9ef55e7ab6472a25567a30b1bf5a" title=" " alt="" coords="227,56,453,83"/>
<area shape="poly" title=" " alt="" coords="494,105,404,86,406,81,495,100"/>
<area shape="rect" href="gpio__core_8c.html#a785f00e9c0879fb478077d2cdce99906" title=" " alt="" coords="281,107,399,133"/>
<area shape="poly" title=" " alt="" coords="485,123,399,123,399,117,485,117"/>
<area shape="rect" href="i2c__core_8c.html#a83e1937f01cd4ec9a8e227bd544a0f06" title=" " alt="" coords="257,157,423,184"/>
<area shape="poly" title=" " alt="" coords="495,140,406,159,404,154,494,135"/>
<area shape="rect" href="gpio__core_8c.html#a1a243a15dd793b6d0f7b7011461a8641" title=" " alt="" coords="321,208,359,235"/>
<area shape="poly" title=" " alt="" coords="545,145,503,173,454,198,404,214,360,222,359,216,402,209,452,194,501,168,542,140"/>
<area shape="rect" href="i2c__core_8c.html#a0dc1e54406b75f4efa145bbb512f87fe" title=" " alt="" coords="31,81,153,108"/>
<area shape="poly" title=" " alt="" coords="249,155,226,148,201,135,178,122,145,111,147,106,180,118,204,131,228,143,251,150"/>
<area shape="rect" href="i2c__core_8c.html#a4440e6d849d5de8720702c225f6bd83b" title=" " alt="" coords="44,132,140,159"/>
<area shape="poly" title=" " alt="" coords="241,163,140,153,140,148,241,158"/>
<area shape="rect" href="i2c__core_8c.html#ac03ee248114c6e0f051a792462609cb4" title=" " alt="" coords="5,183,179,209"/>
<area shape="poly" title=" " alt="" coords="242,183,179,190,179,184,241,178"/>
<area shape="rect" href="i2c__core_8c.html#a7e9d20258e5842242cf0a532b4d60deb" title=" " alt="" coords="31,233,153,260"/>
<area shape="poly" title=" " alt="" coords="251,191,228,198,204,211,180,224,147,235,145,230,178,219,201,206,226,194,249,186"/>
</map>
</div>

</div>
</div>
<a name="doc-var-members" id="doc-var-members"></a><h2 id="header-doc-var-members" class="groupheader">Variable Documentation</h2>
<a id="ac5d07e96f745ca56ee79420f0f039f66" name="ac5d07e96f745ca56ee79420f0f039f66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac5d07e96f745ca56ee79420f0f039f66">&#9670;&#160;</a></span>attributes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">WDF_OBJECT_ATTRIBUTES_INIT&amp; attributes</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af0a4fb8c0dd33529dad81e93a0b0661f" name="af0a4fb8c0dd33529dad81e93a0b0661f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0a4fb8c0dd33529dad81e93a0b0661f">&#9670;&#160;</a></span>EvtGpioInterruptDpc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EvtGpioInterruptDpc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4d9377385f26f2d958a494c7ffbcc04f" name="a4d9377385f26f2d958a494c7ffbcc04f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d9377385f26f2d958a494c7ffbcc04f">&#9670;&#160;</a></span>EvtGpioInterruptIsr</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">EvtGpioInterruptIsr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a319be52f8fb7536ca4d2f35163ab0ad3" name="a319be52f8fb7536ca4d2f35163ab0ad3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a319be52f8fb7536ca4d2f35163ab0ad3">&#9670;&#160;</a></span>GENERIC_WRITE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GENERIC_READ GENERIC_WRITE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a33c00fdea3f12acb400049b8ef710ea9" name="a33c00fdea3f12acb400049b8ef710ea9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33c00fdea3f12acb400049b8ef710ea9">&#9670;&#160;</a></span>Initialized</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a> Initialized = TRUE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac6c56d4f54252f6088c0d841efbc597e" name="ac6c56d4f54252f6088c0d841efbc597e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6c56d4f54252f6088c0d841efbc597e">&#9670;&#160;</a></span>interruptConfig</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">&amp; interruptConfig</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a4983b2b08534e2a12b6e0c30d87594f0" name="a4983b2b08534e2a12b6e0c30d87594f0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4983b2b08534e2a12b6e0c30d87594f0">&#9670;&#160;</a></span>InterruptTranslated</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#ac6c56d4f54252f6088c0d841efbc597e">interruptConfig</a> InterruptTranslated = TRUE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ac91080baa5062b15cd46c8e08028fd51" name="ac91080baa5062b15cd46c8e08028fd51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac91080baa5062b15cd46c8e08028fd51">&#9670;&#160;</a></span>Lock</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="core__types_8h.html#a5e60eaa7b959904ba022e5237f17ab98">WDFSPINLOCK</a> Lock</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="ad7d33086f63a42bdcbecdd995751fb96" name="ad7d33086f63a42bdcbecdd995751fb96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7d33086f63a42bdcbecdd995751fb96">&#9670;&#160;</a></span>openParams</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">&amp; openParams</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9bfd47e9b367e692d16fa7a38e3944cc" name="a9bfd47e9b367e692d16fa7a38e3944cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9bfd47e9b367e692d16fa7a38e3944cc">&#9670;&#160;</a></span>ParentObject</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#ac5d07e96f745ca56ee79420f0f039f66">attributes</a> ParentObject = Device</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a41283b4328aa08e8f095578a73755e08" name="a41283b4328aa08e8f095578a73755e08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41283b4328aa08e8f095578a73755e08">&#9670;&#160;</a></span>PassiveHandling</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="#ac6c56d4f54252f6088c0d841efbc597e">interruptConfig</a> PassiveHandling = FALSE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a3a285e05689c30bbe096ff555f7a5b68" name="a3a285e05689c30bbe096ff555f7a5b68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a285e05689c30bbe096ff555f7a5b68">&#9670;&#160;</a></span>PinCount</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">gpioManager PinCount</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="aa601b044abcd0035f84077010771020b" name="aa601b044abcd0035f84077010771020b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa601b044abcd0035f84077010771020b">&#9670;&#160;</a></span>Pins</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PGPIO_PIN_CONTEXT Pins[256]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af7d60c8c4b9613f737c7d254aced2bde" name="af7d60c8c4b9613f737c7d254aced2bde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7d60c8c4b9613f737c7d254aced2bde">&#9670;&#160;</a></span>spbDevicePath</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">&amp; spbDevicePath</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="af781006198c718a3a6e46d55cdb1e74c" name="af781006198c718a3a6e46d55cdb1e74c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af781006198c718a3a6e46d55cdb1e74c">&#9670;&#160;</a></span>SpbIoTarget</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">WDFIOTARGET SpbIoTarget</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a9611b3a00430a86619b5923de30f9fdb" name="a9611b3a00430a86619b5923de30f9fdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9611b3a00430a86619b5923de30f9fdb">&#9670;&#160;</a></span>status</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return status = WdfIoTargetCreate(Device, &amp;<a class="el" href="#ac5d07e96f745ca56ee79420f0f039f66">attributes</a>, &amp;<a class="el" href="#a401edc28835c1919fe788f9583c5b963">pinContext</a>-&gt;<a class="el" href="#af781006198c718a3a6e46d55cdb1e74c">SpbIoTarget</a>)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a29feb6a8256030d356f8bfd96118d1cf" name="a29feb6a8256030d356f8bfd96118d1cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29feb6a8256030d356f8bfd96118d1cf">&#9670;&#160;</a></span>STATUS_INVALID_PARAMETER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return STATUS_INVALID_PARAMETER</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a77b4762318f24dff847f94f382cfeea6" name="a77b4762318f24dff847f94f382cfeea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77b4762318f24dff847f94f382cfeea6">&#9670;&#160;</a></span>STATUS_SUCCESS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">return STATUS_SUCCESS</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a id="a11ec07dcb5c1cea421134a0b149443a5" name="a11ec07dcb5c1cea421134a0b149443a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11ec07dcb5c1cea421134a0b149443a5">&#9670;&#160;</a></span>WdfDevice</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="gpio__device_8c.html#a7b6a29716fe6f8117de54edaedc57974">deviceContext</a> WdfDevice = Device</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<div id="page-nav" class="page-nav-panel">
<div id="page-nav-resize-handle"></div>
<div id="page-nav-tree">
<div id="page-nav-contents">
</div><!-- page-nav-contents -->
</div><!-- page-nav-tree -->
</div><!-- page-nav -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li><li class="navelem"><a href="dir_4ce6a7f885e2866a554ba9e7335035f1.html">hal</a></li><li class="navelem"><a href="dir_43b43b2f79854d1934869d5a4aaeb79e.html">bus</a></li><li class="navelem"><a href="gpio__core_8c.html">gpio_core.c</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
