<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="kmdf__spi_8h" kind="file" language="C++">
    <compoundname>kmdf_spi.h</compoundname>
    <includes refid="kmdf__bus__common_8h" local="yes">kmdf_bus_common.h</includes>
    <includedby refid="spi__device_8h" local="yes">C:/KMDF Driver1/include/hal/devices/spi_device.h</includedby>
    <includedby refid="spi__core_8c" local="yes">C:/KMDF Driver1/src/hal/bus/spi_core.c</includedby>
    <includedby refid="spi__device_8c" local="yes">C:/KMDF Driver1/src/hal/devices/spi_device.c</includedby>
    <incdepgraph>
      <node id="4">
        <label>../../core/error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="5" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
      </node>
      <node id="5">
        <label>ntddk.h</label>
      </node>
      <node id="3">
        <label>wdf.h</label>
      </node>
    </incdepgraph>
    <invincdepgraph>
      <node id="1">
        <label>C:/KMDF Driver1/include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>C:/KMDF Driver1/include/hal/devices/spi_device.h</label>
        <link refid="spi__device_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="4">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
      </node>
      <node id="3">
        <label>C:/KMDF Driver1/src/hal/devices/spi_device.c</label>
        <link refid="spi__device_8c"/>
      </node>
    </invincdepgraph>
    <innerclass refid="struct__SPI__CONFIG" prot="public">_SPI_CONFIG</innerclass>
    <innerclass refid="struct__SPI__TRANSFER__PACKET" prot="public">_SPI_TRANSFER_PACKET</innerclass>
    <sectiondef kind="enum">
      <memberdef kind="enum" id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2" prot="public" static="no" strong="no">
        <type></type>
        <name>_SPI_BUS_SPEED</name>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2" prot="public">
          <name>SpiBusSpeed1MHz</name>
          <initializer>= 1000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab" prot="public">
          <name>SpiBusSpeed2MHz</name>
          <initializer>= 2000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94" prot="public">
          <name>SpiBusSpeed4MHz</name>
          <initializer>= 4000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928" prot="public">
          <name>SpiBusSpeed8MHz</name>
          <initializer>= 8000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997" prot="public">
          <name>SpiBusSpeed10MHz</name>
          <initializer>= 10000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90" prot="public">
          <name>SpiBusSpeed20MHz</name>
          <initializer>= 20000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef" prot="public">
          <name>SpiBusSpeed25MHz</name>
          <initializer>= 25000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc" prot="public">
          <name>SpiBusSpeed50MHz</name>
          <initializer>= 50000000</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="22" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" bodystart="22" bodyend="31"/>
      </memberdef>
      <memberdef kind="enum" id="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374" prot="public" static="no" strong="no">
        <type></type>
        <name>_SPI_MODE</name>
        <enumvalue id="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249" prot="public">
          <name>SpiMode0</name>
          <initializer>= 0</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972" prot="public">
          <name>SpiMode1</name>
          <initializer>= 1</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e" prot="public">
          <name>SpiMode2</name>
          <initializer>= 2</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470" prot="public">
          <name>SpiMode3</name>
          <initializer>= 3</initializer>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="14" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" bodystart="14" bodyend="19"/>
      </memberdef>
      <memberdef kind="enum" id="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21" prot="public" static="no" strong="no">
        <type></type>
        <name>_SPI_TRANSFER_TYPE</name>
        <enumvalue id="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a" prot="public">
          <name>SpiWrite</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d" prot="public">
          <name>SpiRead</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <enumvalue id="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36" prot="public">
          <name>SpiWriteRead</name>
          <briefdescription>
          </briefdescription>
          <detaileddescription>
          </detaileddescription>
        </enumvalue>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="48" column="1" bodyfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" bodystart="48" bodyend="52"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__CONFIG" kindref="compound">_SPI_CONFIG</ref> *</type>
        <definition>typedef struct _SPI_CONFIG * PSPI_CONFIG</definition>
        <argsstring></argsstring>
        <name>PSPI_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="45" column="25"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__TRANSFER__PACKET" kindref="compound">_SPI_TRANSFER_PACKET</ref> *</type>
        <definition>typedef struct _SPI_TRANSFER_PACKET * PSPI_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>PSPI_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="64" column="43"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1a02075f39766ac5419ad37fbd5e96de57" prot="public" static="no">
        <type>enum <ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2" kindref="member">_SPI_BUS_SPEED</ref></type>
        <definition>typedef enum _SPI_BUS_SPEED SPI_BUS_SPEED</definition>
        <argsstring></argsstring>
        <name>SPI_BUS_SPEED</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="31" column="15"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__CONFIG" kindref="compound">_SPI_CONFIG</ref></type>
        <definition>typedef struct _SPI_CONFIG SPI_CONFIG</definition>
        <argsstring></argsstring>
        <name>SPI_CONFIG</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="45" column="12"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" prot="public" static="no">
        <type>enum <ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374" kindref="member">_SPI_MODE</ref></type>
        <definition>typedef enum _SPI_MODE SPI_MODE</definition>
        <argsstring></argsstring>
        <name>SPI_MODE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="19" column="10"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1ae53b781a84db92ccef86085a53448289" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__TRANSFER__PACKET" kindref="compound">_SPI_TRANSFER_PACKET</ref></type>
        <definition>typedef struct _SPI_TRANSFER_PACKET SPI_TRANSFER_PACKET</definition>
        <argsstring></argsstring>
        <name>SPI_TRANSFER_PACKET</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="64" column="21"/>
      </memberdef>
      <memberdef kind="typedef" id="kmdf__spi_8h_1a9920771d941aa8c6e1b7b97ce21e77ca" prot="public" static="no">
        <type>enum <ref refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21" kindref="member">_SPI_TRANSFER_TYPE</ref></type>
        <definition>typedef enum _SPI_TRANSFER_TYPE SPI_TRANSFER_TYPE</definition>
        <argsstring></argsstring>
        <name>SPI_TRANSFER_TYPE</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="52" column="19"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="kmdf__spi_8h_1a685d8d7731e750c1512b975df16cc030" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPIInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</argsstring>
        <name>SPIInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref></type>
          <declname>SpiConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="68" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/spi_core.c" bodystart="33" bodyend="132" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="68" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" compoundref="gpio__core_8c" startline="154">GENERIC_WRITE</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" compoundref="gpio__core_8c" startline="152">openParams</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" compoundref="gpio__core_8c" startline="153">spbDevicePath</references>
        <references refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" compoundref="spi__device_8c" startline="20">SpiConfig</references>
        <references refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</references>
        <references refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" compoundref="spi__core_8c" startline="25">SPITransferTimerExpired</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1adb5a94e2dc80b87a505aea6c78f3b885" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPIReadRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>SPIReadRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_Out_ PUCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="112" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="112" declcolumn="1"/>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1a571fb3ea7eed247b3c46c57f506fa033" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPITransferAsynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PSPI_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</argsstring>
        <name>SPITransferAsynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kindref="member">PSPI_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref></type>
          <declname>CompletionCallback</declname>
        </param>
        <param>
          <type>_In_opt_ PVOID</type>
          <declname>Context</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="86" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="86" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1a682c974659ab89363d0baa22470a386c" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPITransferSynchronous</definition>
        <argsstring>(_In_ WDFDEVICE Device, _Inout_ PSPI_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</argsstring>
        <name>SPITransferSynchronous</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_Inout_ <ref refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kindref="member">PSPI_TRANSFER_PACKET</ref></type>
          <declname>TransferPacket</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="79" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="79" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1ad756f8e3b06fdfa545a7048661038513" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID SPIUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>SPIUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="74" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/spi_core.c" bodystart="138" bodyend="167" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="74" declcolumn="1"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPIWriteRead</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_reads_bytes_(WriteBufferLength) PVOID WriteBuffer, _In_ ULONG WriteBufferLength, _Out_writes_bytes_(ReadBufferLength) PVOID ReadBuffer, _In_ ULONG ReadBufferLength, _In_ ULONG TimeoutMs)</argsstring>
        <name>SPIWriteRead</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_reads_bytes_(WriteBufferLength) PVOID</type>
          <declname>WriteBuffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>WriteBufferLength</declname>
        </param>
        <param>
          <type>_Out_writes_bytes_(ReadBufferLength) PVOID</type>
          <declname>ReadBuffer</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>ReadBufferLength</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="94" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="94" declcolumn="1"/>
        <references refid="spi__device_8c_1ac2677e024009c29e2bcee99e0c32c735" compoundref="spi__device_8c" startline="223">ReadBuffer</references>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
        <references refid="spi__device_8c_1ad26ded9b73e8b14b4117614b39440d86" compoundref="spi__device_8c" startline="221">WriteBuffer</references>
      </memberdef>
      <memberdef kind="function" id="kmdf__spi_8h_1a261c6752bd8e05e7e4d7eb1e60ed64f8" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPIWriteRegister</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</argsstring>
        <name>SPIWriteRegister</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>RegisterAddress</declname>
        </param>
        <param>
          <type>_In_ UCHAR</type>
          <declname>Value</declname>
        </param>
        <param>
          <type>_In_ ULONG</type>
          <declname>TimeoutMs</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" line="104" column="1" declfile="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h" declline="104" declcolumn="1"/>
        <references refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" compoundref="core__types_8h" startline="21">WDFAPI</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>kmdf_spi.h</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>SPI总线接口头文件</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>提供SPI特定的接口和数据结构</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#ifndef<sp/>KMDF_SPI_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#define<sp/>KMDF_SPI_H</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__bus__common_8h" kindref="compound">kmdf_bus_common.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI的模式定义</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14" refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374" kindref="member">_SPI_MODE</ref><sp/>{</highlight></codeline>
<codeline lineno="15" refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249" kindref="member">SpiMode0</ref><sp/>=<sp/>0,<sp/><sp/></highlight><highlight class="comment">//<sp/>CPOL=0,<sp/>CPHA=0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972" kindref="member">SpiMode1</ref><sp/>=<sp/>1,<sp/><sp/></highlight><highlight class="comment">//<sp/>CPOL=0,<sp/>CPHA=1</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e" kindref="member">SpiMode2</ref><sp/>=<sp/>2,<sp/><sp/></highlight><highlight class="comment">//<sp/>CPOL=1,<sp/>CPHA=0</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470" kindref="member">SpiMode3</ref><sp/>=<sp/>3<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>CPOL=1,<sp/>CPHA=1</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kindref="member">SPI_MODE</ref>;</highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI最大速度选择</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2" kindref="member">_SPI_BUS_SPEED</ref><sp/>{</highlight></codeline>
<codeline lineno="23" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2" kindref="member">SpiBusSpeed1MHz</ref><sp/>=<sp/>1000000,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>1<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="24" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab" kindref="member">SpiBusSpeed2MHz</ref><sp/>=<sp/>2000000,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>2<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94" kindref="member">SpiBusSpeed4MHz</ref><sp/>=<sp/>4000000,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>4<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="26" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928" kindref="member">SpiBusSpeed8MHz</ref><sp/>=<sp/>8000000,<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>8<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="27" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997" kindref="member">SpiBusSpeed10MHz</ref><sp/>=<sp/>10000000,<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>10<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90" kindref="member">SpiBusSpeed20MHz</ref><sp/>=<sp/>20000000,<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>20<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="29" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef" kindref="member">SpiBusSpeed25MHz</ref><sp/>=<sp/>25000000,<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>25<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="30" refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc" kindref="member">SpiBusSpeed50MHz</ref><sp/>=<sp/>50000000<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>50<sp/>MHz</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="31" refid="kmdf__spi_8h_1a02075f39766ac5419ad37fbd5e96de57" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__spi_8h_1a02075f39766ac5419ad37fbd5e96de57" kindref="member">SPI_BUS_SPEED</ref>;</highlight></codeline>
<codeline lineno="32"><highlight class="normal"></highlight></codeline>
<codeline lineno="33"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI特定配置结构体</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="34" refid="struct__SPI__CONFIG" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__SPI__CONFIG" kindref="compound">_SPI_CONFIG</ref><sp/>{</highlight></codeline>
<codeline lineno="35" refid="struct__SPI__CONFIG_1a5995a0bc6695e2e61567693ec8e494ca" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1ae9c35ffd537d30a103775489f57c24cc" kindref="member">SPI_MODE</ref><sp/><ref refid="struct__SPI__CONFIG_1a5995a0bc6695e2e61567693ec8e494ca" kindref="member">Mode</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPI模式<sp/>(0-3)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="36" refid="struct__SPI__CONFIG_1a6cf9c836c9e4e1a5b0dd2ffc18e218e6" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a02075f39766ac5419ad37fbd5e96de57" kindref="member">SPI_BUS_SPEED</ref><sp/><ref refid="struct__SPI__CONFIG_1a6cf9c836c9e4e1a5b0dd2ffc18e218e6" kindref="member">MaxClockFrequency</ref>;<sp/></highlight><highlight class="comment">//<sp/>最大时钟频率</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="37" refid="struct__SPI__CONFIG_1a5568dcea126ea711e8d0f6b40dc951cc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__CONFIG_1a5568dcea126ea711e8d0f6b40dc951cc" kindref="member">ChipSelectLine</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>片选线</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="38" refid="struct__SPI__CONFIG_1a23bf21cffc603724543545d3c677093a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__SPI__CONFIG_1a23bf21cffc603724543545d3c677093a" kindref="member">ChipSelectPolarity</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>片选极性<sp/>(TRUE<sp/>=<sp/>高电平有效)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="39" refid="struct__SPI__CONFIG_1a2fb798d2a102a3bc148918a6528aa329" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__CONFIG_1a2fb798d2a102a3bc148918a6528aa329" kindref="member">WordLength</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>字长度<sp/>(4-32位)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="40" refid="struct__SPI__CONFIG_1ad132bb074410f64505375ed71b0173f8" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__SPI__CONFIG_1ad132bb074410f64505375ed71b0173f8" kindref="member">IsLsbFirst</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>低位优先传输</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="41" refid="struct__SPI__CONFIG_1a85af30c409a08baa8e9c3e31567c07c2" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__CONFIG_1a85af30c409a08baa8e9c3e31567c07c2" kindref="member">TimeoutMs</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>默认超时时间<sp/>(毫秒)</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="42" refid="struct__SPI__CONFIG_1a3eecf9d65abadb2106c4930606d31c7a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>ULONG<sp/><ref refid="struct__SPI__CONFIG_1a3eecf9d65abadb2106c4930606d31c7a" kindref="member">RetryCount</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>失败重试次数</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="43" refid="struct__SPI__CONFIG_1a065d24ae185eeafe1fc5ce74cf823454" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__SPI__CONFIG_1a065d24ae185eeafe1fc5ce74cf823454" kindref="member">SpbDeviceObject</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB设备对象</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="44" refid="struct__SPI__CONFIG_1aed13983e08690b185df2a6c06f44ffd3" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>LARGE_INTEGER<sp/><ref refid="struct__SPI__CONFIG_1aed13983e08690b185df2a6c06f44ffd3" kindref="member">SpbConnectionId</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB连接ID</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="45" refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>,<sp/>*<ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref>;</highlight></codeline>
<codeline lineno="46"><highlight class="normal"></highlight></codeline>
<codeline lineno="47"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI传输类型</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="48" refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21" refkind="member"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">enum</highlight><highlight class="normal"><sp/><ref refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21" kindref="member">_SPI_TRANSFER_TYPE</ref><sp/>{</highlight></codeline>
<codeline lineno="49" refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a" kindref="member">SpiWrite</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="50" refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d" kindref="member">SpiRead</ref>,<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51" refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36" kindref="member">SpiWriteRead</ref><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>同时读写操作</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="52" refid="kmdf__spi_8h_1a9920771d941aa8c6e1b7b97ce21e77ca" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__spi_8h_1a9920771d941aa8c6e1b7b97ce21e77ca" kindref="member">SPI_TRANSFER_TYPE</ref>;</highlight></codeline>
<codeline lineno="53"><highlight class="normal"></highlight></codeline>
<codeline lineno="54"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI传输包结构体</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="55" refid="struct__SPI__TRANSFER__PACKET" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__SPI__TRANSFER__PACKET" kindref="compound">_SPI_TRANSFER_PACKET</ref><sp/>{</highlight></codeline>
<codeline lineno="56" refid="struct__SPI__TRANSFER__PACKET_1a00931840236e9b48912a56116bd0d109" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__bus__common_8h_1aac06c68a58c9667998bbe0975aa78c51" kindref="member">BUS_TRANSFER_PACKET</ref><sp/><ref refid="struct__SPI__TRANSFER__PACKET_1a00931840236e9b48912a56116bd0d109" kindref="member">Common</ref>;<sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>通用总线传输包</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57" refid="struct__SPI__TRANSFER__PACKET_1ae3ca53594a2138cf5aea46199d1f00fc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1a9920771d941aa8c6e1b7b97ce21e77ca" kindref="member">SPI_TRANSFER_TYPE</ref><sp/><ref refid="struct__SPI__TRANSFER__PACKET_1ae3ca53594a2138cf5aea46199d1f00fc" kindref="member">Type</ref>;<sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>传输类型</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="58" refid="struct__SPI__TRANSFER__PACKET_1adfd0120a5d0f6554506d7a8c4454609b" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1adfd0120a5d0f6554506d7a8c4454609b" kindref="member">WriteBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写缓冲区</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="59" refid="struct__SPI__TRANSFER__PACKET_1a60c1c9ddb35c8b5aa7ef2d65b6a278a7" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>SIZE_T<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1a60c1c9ddb35c8b5aa7ef2d65b6a278a7" kindref="member">WriteBufferLength</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>写缓冲区长度</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="60" refid="struct__SPI__TRANSFER__PACKET_1a1bd99745fd3afc8bf6567a4460b85f28" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>PVOID<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1a1bd99745fd3afc8bf6567a4460b85f28" kindref="member">ReadBuffer</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读缓冲区</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="61" refid="struct__SPI__TRANSFER__PACKET_1afc8f8284a74ea4a78d2531d7b09838ed" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>SIZE_T<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1afc8f8284a74ea4a78d2531d7b09838ed" kindref="member">ReadBufferLength</ref>;<sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>读缓冲区长度</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="62" refid="struct__SPI__TRANSFER__PACKET_1ab7012a8e826ed6c62ef8b4e77d5dac24" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1ab7012a8e826ed6c62ef8b4e77d5dac24" kindref="member">AssertChipSelect</ref>;<sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>是否主动控制片选</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="63" refid="struct__SPI__TRANSFER__PACKET_1a2c4bee3baf8bbe9d12ea2b02a12deb1c" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>BOOLEAN<sp/><ref refid="struct__SPI__TRANSFER__PACKET_1a2c4bee3baf8bbe9d12ea2b02a12deb1c" kindref="member">DeassertChipSelect</ref>;<sp/><sp/></highlight><highlight class="comment">//<sp/>是否在传输后取消片选</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="64" refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" refkind="member"><highlight class="normal">}<sp/><ref refid="kmdf__spi_8h_1ae53b781a84db92ccef86085a53448289" kindref="member">SPI_TRANSFER_PACKET</ref>,<sp/>*<ref refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kindref="member">PSPI_TRANSFER_PACKET</ref>;</highlight></codeline>
<codeline lineno="65"><highlight class="normal"></highlight></codeline>
<codeline lineno="66"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI总线特定函数声明</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="67"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="68"><highlight class="normal"><ref refid="kmdf__spi_8h_1a685d8d7731e750c1512b975df16cc030" kindref="member">SPIInitialize</ref>(</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></highlight></codeline>
<codeline lineno="71"><highlight class="normal">);</highlight></codeline>
<codeline lineno="72"><highlight class="normal"></highlight></codeline>
<codeline lineno="73"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><ref refid="kmdf__spi_8h_1ad756f8e3b06fdfa545a7048661038513" kindref="member">SPIUninitialize</ref>(</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="76"><highlight class="normal">);</highlight></codeline>
<codeline lineno="77"><highlight class="normal"></highlight></codeline>
<codeline lineno="78"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="79" refid="kmdf__spi_8h_1a682c974659ab89363d0baa22470a386c" refkind="member"><highlight class="normal"><ref refid="kmdf__spi_8h_1a682c974659ab89363d0baa22470a386c" kindref="member">SPITransferSynchronous</ref>(</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kindref="member">PSPI_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="83"><highlight class="normal">);</highlight></codeline>
<codeline lineno="84"><highlight class="normal"></highlight></codeline>
<codeline lineno="85"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="86" refid="kmdf__spi_8h_1a571fb3ea7eed247b3c46c57f506fa033" refkind="member"><highlight class="normal"><ref refid="kmdf__spi_8h_1a571fb3ea7eed247b3c46c57f506fa033" kindref="member">SPITransferAsynchronous</ref>(</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/>_Inout_<sp/><ref refid="kmdf__spi_8h_1a8c0c38014d644418137aa056ce518223" kindref="member">PSPI_TRANSFER_PACKET</ref><sp/>TransferPacket,</highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__bus__common_8h_1a3709500586d6c79d8df0693c133a3f2d" kindref="member">BUS_OPERATION_CALLBACK</ref><sp/>CompletionCallback,</highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_opt_<sp/>PVOID<sp/>Context</highlight></codeline>
<codeline lineno="91"><highlight class="normal">);</highlight></codeline>
<codeline lineno="92"><highlight class="normal"></highlight></codeline>
<codeline lineno="93"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="94" refid="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f" refkind="member"><highlight class="normal"><ref refid="kmdf__spi_8h_1a038c52771ec4b0654c0e59f37fccb29f" kindref="member">SPIWriteRead</ref>(</highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_reads_bytes_(WriteBufferLength)<sp/>PVOID<sp/><ref refid="spi__device_8c_1ad26ded9b73e8b14b4117614b39440d86" kindref="member">WriteBuffer</ref>,</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>WriteBufferLength,</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_writes_bytes_(ReadBufferLength)<sp/>PVOID<sp/><ref refid="spi__device_8c_1ac2677e024009c29e2bcee99e0c32c735" kindref="member">ReadBuffer</ref>,</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>ReadBufferLength,</highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="101"><highlight class="normal">);</highlight></codeline>
<codeline lineno="102"><highlight class="normal"></highlight></codeline>
<codeline lineno="103"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="104" refid="kmdf__spi_8h_1a261c6752bd8e05e7e4d7eb1e60ed64f8" refkind="member"><highlight class="normal"><ref refid="kmdf__spi_8h_1a261c6752bd8e05e7e4d7eb1e60ed64f8" kindref="member">SPIWriteRegister</ref>(</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="109"><highlight class="normal">);</highlight></codeline>
<codeline lineno="110"><highlight class="normal"></highlight></codeline>
<codeline lineno="111"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="112" refid="kmdf__spi_8h_1adb5a94e2dc80b87a505aea6c78f3b885" refkind="member"><highlight class="normal"><ref refid="kmdf__spi_8h_1adb5a94e2dc80b87a505aea6c78f3b885" kindref="member">SPIReadRegister</ref>(</highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>UCHAR<sp/>RegisterAddress,</highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/>_Out_<sp/>PUCHAR<sp/>Value,</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/>ULONG<sp/>TimeoutMs</highlight></codeline>
<codeline lineno="117"><highlight class="normal">);</highlight></codeline>
<codeline lineno="118"><highlight class="normal"></highlight></codeline>
<codeline lineno="119"><highlight class="normal"></highlight><highlight class="preprocessor">#endif<sp/></highlight><highlight class="comment">//<sp/>KMDF_SPI_H</highlight><highlight class="normal"></highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/include/hal/bus/kmdf_spi.h"/>
  </compounddef>
</doxygen>
