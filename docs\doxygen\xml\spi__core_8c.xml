<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.14.0" xml:lang="en-US">
  <compounddef id="spi__core_8c" kind="file" language="C++">
    <compoundname>spi_core.c</compoundname>
    <includes refid="precomp_8h" local="yes">../../precomp.h</includes>
    <includes refid="kmdf__spi_8h" local="yes">../../../include/hal/bus/kmdf_spi.h</includes>
    <includes refid="include_2core_2log_2driver__log_8h" local="yes">../../../include/core/log/driver_log.h</includes>
    <includes refid="error__codes_8h" local="yes">../../../include/core/error/error_codes.h</includes>
    <incdepgraph>
      <node id="17">
        <label>../include/common/ioctl.h</label>
        <link refid="ioctl_8h"/>
        <childnode refid="18" relation="include">
        </childnode>
      </node>
      <node id="15">
        <label>../include/core/device/device_manager.h</label>
        <link refid="device__manager_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="16" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="11">
        <label>../include/core/driver/driver_core.h</label>
        <link refid="driver__core_8h"/>
        <childnode refid="12" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
      </node>
      <node id="13">
        <label>../error/error_codes.h</label>
        <link refid="error__codes_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
      </node>
      <node id="14">
        <label>../log/driver_log.h</label>
        <link refid="include_2core_2log_2driver__log_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
      </node>
      <node id="16">
        <label>../../hal/bus/kmdf_bus_common.h</label>
        <link refid="kmdf__bus__common_8h"/>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="19">
        <label>../../../include/hal/bus/kmdf_spi.h</label>
        <link refid="kmdf__spi_8h"/>
        <childnode refid="16" relation="include">
        </childnode>
      </node>
      <node id="1">
        <label>C:/KMDF Driver1/src/hal/bus/spi_core.c</label>
        <link refid="spi__core_8c"/>
        <childnode refid="2" relation="include">
        </childnode>
        <childnode refid="19" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="13" relation="include">
        </childnode>
      </node>
      <node id="2">
        <label>../../precomp.h</label>
        <link refid="precomp_8h"/>
        <childnode refid="3" relation="include">
        </childnode>
        <childnode refid="4" relation="include">
        </childnode>
        <childnode refid="5" relation="include">
        </childnode>
        <childnode refid="6" relation="include">
        </childnode>
        <childnode refid="7" relation="include">
        </childnode>
        <childnode refid="8" relation="include">
        </childnode>
        <childnode refid="9" relation="include">
        </childnode>
        <childnode refid="10" relation="include">
        </childnode>
        <childnode refid="11" relation="include">
        </childnode>
        <childnode refid="15" relation="include">
        </childnode>
        <childnode refid="14" relation="include">
        </childnode>
        <childnode refid="17" relation="include">
        </childnode>
      </node>
      <node id="18">
        <label>devioctl.h</label>
      </node>
      <node id="3">
        <label>ntddk.h</label>
      </node>
      <node id="5">
        <label>ntstrsafe.h</label>
      </node>
      <node id="8">
        <label>usb.h</label>
      </node>
      <node id="7">
        <label>usbspec.h</label>
      </node>
      <node id="4">
        <label>wdf.h</label>
      </node>
      <node id="10">
        <label>wdfinstaller.h</label>
      </node>
      <node id="9">
        <label>wdfldr.h</label>
      </node>
      <node id="6">
        <label>wdfusb.h</label>
      </node>
      <node id="12">
        <label>wdm.h</label>
      </node>
    </incdepgraph>
    <innerclass refid="struct__SPI__DEVICE__CONTEXT" prot="public">_SPI_DEVICE_CONTEXT</innerclass>
    <sectiondef kind="typedef">
      <memberdef kind="typedef" id="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__DEVICE__CONTEXT" kindref="compound">_SPI_DEVICE_CONTEXT</ref> *</type>
        <definition>typedef struct _SPI_DEVICE_CONTEXT * PSPI_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>PSPI_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c" line="19" column="41"/>
      </memberdef>
      <memberdef kind="typedef" id="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" prot="public" static="no">
        <type>struct <ref refid="struct__SPI__DEVICE__CONTEXT" kindref="compound">_SPI_DEVICE_CONTEXT</ref></type>
        <definition>typedef struct _SPI_DEVICE_CONTEXT SPI_DEVICE_CONTEXT</definition>
        <argsstring></argsstring>
        <name>SPI_DEVICE_CONTEXT</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c" line="19" column="20"/>
      </memberdef>
    </sectiondef>
    <sectiondef kind="var">
      <memberdef kind="variable" id="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" prot="public" static="no" mutable="no">
        <type>EVT_WDF_TIMER</type>
        <definition>EVT_WDF_TIMER SPITransferTimerExpired</definition>
        <argsstring></argsstring>
        <name>SPITransferTimerExpired</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c" line="25" column="15" bodyfile="C:/KMDF Driver1/src/hal/bus/spi_core.c" bodystart="25" bodyend="-1"/>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
    </sectiondef>
    <sectiondef kind="func">
      <memberdef kind="function" id="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> NTSTATUS</type>
        <definition>WDFAPI NTSTATUS SPIInitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</argsstring>
        <name>SPIInitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <param>
          <type>_In_ <ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref></type>
          <declname>SpiConfig</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c" line="33" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/spi_core.c" bodystart="33" bodyend="132"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" compoundref="error__codes_8h" startline="28">ERROR_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" compoundref="gpio__core_8c" startline="154">GENERIC_WRITE</references>
        <references refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" compoundref="error__handling_8c" startline="62" endline="84">LogError</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" compoundref="precomp_8h" startline="57">NT_SUCCESS</references>
        <references refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" compoundref="gpio__core_8c" startline="152">openParams</references>
        <references refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0">RtlCopyMemory</references>
        <references refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" compoundref="gpio__core_8c" startline="153">spbDevicePath</references>
        <references refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" compoundref="spi__device_8c" startline="20">SpiConfig</references>
        <references refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</references>
        <references refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" compoundref="spi__core_8c" startline="25">SPITransferTimerExpired</references>
        <references refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" compoundref="gpio__core_8c" startline="127">status</references>
        <references refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" compoundref="gpio__core_8c" startline="191">STATUS_INVALID_PARAMETER</references>
        <references refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" compoundref="gpio__core_8c" startline="218">STATUS_SUCCESS</references>
        <referencedby refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" compoundref="spi__core_8c" startline="33" endline="132">SPIInitialize</referencedby>
      </memberdef>
      <memberdef kind="function" id="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" prot="public" static="no" const="no" explicit="no" inline="no" virt="non-virtual">
        <type><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref> VOID</type>
        <definition>WDFAPI VOID SPIUninitialize</definition>
        <argsstring>(_In_ WDFDEVICE Device)</argsstring>
        <name>SPIUninitialize</name>
        <param>
          <type>_In_ <ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref></type>
          <declname>Device</declname>
        </param>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c" line="138" column="1" bodyfile="C:/KMDF Driver1/src/hal/bus/spi_core.c" bodystart="138" bodyend="167"/>
        <references refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974">deviceContext</references>
        <references refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" compoundref="include_2core_2log_2driver__log_8h" startline="80" endline="81">LogInfo</references>
        <references refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" compoundref="include_2core_2log_2driver__log_8h" startline="83" endline="84">LogWarning</references>
      </memberdef>
    </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
    </detaileddescription>
    <programlisting>
<codeline lineno="1"><highlight class="comment">/*</highlight></codeline>
<codeline lineno="2"><highlight class="comment"><sp/>*<sp/>spi_core.c</highlight></codeline>
<codeline lineno="3"><highlight class="comment"><sp/>*</highlight></codeline>
<codeline lineno="4"><highlight class="comment"><sp/>*<sp/>SPI閹崵鍤庨弽绋跨妇閸旂喕鍏樼€圭偟骞?</highlight></codeline>
<codeline lineno="5"><highlight class="comment"><sp/>*<sp/>閹绘劒绶礢PI閹崵鍤庨崚婵嗩潗閸栨牓鈧椒绱舵潏鎾虫嫲缁狅紕鎮婇崝鐔诲厴</highlight></codeline>
<codeline lineno="6"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="7"><highlight class="normal"></highlight></codeline>
<codeline lineno="8"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="precomp_8h" kindref="compound">../../precomp.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="9"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="kmdf__spi_8h" kindref="compound">../../../include/hal/bus/kmdf_spi.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="10"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="include_2core_2log_2driver__log_8h" kindref="compound">../../../include/core/log/driver_log.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="11"><highlight class="normal"></highlight><highlight class="preprocessor">#include<sp/>&quot;<ref refid="error__codes_8h" kindref="compound">../../../include/core/error/error_codes.h</ref>&quot;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="12"><highlight class="normal"></highlight></codeline>
<codeline lineno="13"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI鐠佹儳顦稉濠佺瑓閺傚洨绮ㄩ弸鍕秼<sp/>-<sp/>鐠虹喕閲滅拋鎯ь槵閻楃懓鐣鹃弫鐗堝祦</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="14" refid="struct__SPI__DEVICE__CONTEXT" refkind="compound"><highlight class="normal"></highlight><highlight class="keyword">typedef</highlight><highlight class="normal"><sp/></highlight><highlight class="keyword">struct<sp/></highlight><highlight class="normal"><ref refid="struct__SPI__DEVICE__CONTEXT" kindref="compound">_SPI_DEVICE_CONTEXT</ref><sp/>{</highlight></codeline>
<codeline lineno="15" refid="struct__SPI__DEVICE__CONTEXT_1a1d960500b2572de5188fa339fba643cb" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/><ref refid="struct__SPI__DEVICE__CONTEXT_1a1d960500b2572de5188fa339fba643cb" kindref="member">WdfDevice</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>WDF鐠佹儳顦€电钖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="16" refid="struct__SPI__DEVICE__CONTEXT_1ad94201a4a15b3f381e7016679cfb5231" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref><sp/><ref refid="struct__SPI__DEVICE__CONTEXT_1ad94201a4a15b3f381e7016679cfb5231" kindref="member">Config</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPI闁板秶鐤嗘穱鈩冧紖</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="17" refid="struct__SPI__DEVICE__CONTEXT_1a081bab7f80eae29e5a23517be063bbdc" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="core__types_8h_1a5e60eaa7b959904ba022e5237f17ab98" kindref="member">WDFSPINLOCK</ref><sp/><ref refid="struct__SPI__DEVICE__CONTEXT_1a081bab7f80eae29e5a23517be063bbdc" kindref="member">TransferLock</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>娴肩姾绶崥灞绢劄闁?<sp/><sp/><sp/><sp/>BOOLEAN<sp/>DeviceInitialized;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>閸掓繂顫愰崠鏍ㄧ垼韫?<sp/><sp/><sp/><sp/>WDFTIMER<sp/>WatchdogTimer;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>//<sp/>鐡掑懏妞傞惇瀣，閻欐鐣鹃弮璺烘珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="18" refid="struct__SPI__DEVICE__CONTEXT_1a764c0d4c344ec67dc804a438695d17bd" refkind="member"><highlight class="normal"><sp/><sp/><sp/><sp/>WDFIOTARGET<sp/><ref refid="struct__SPI__DEVICE__CONTEXT_1a764c0d4c344ec67dc804a438695d17bd" kindref="member">SpbIoTarget</ref>;<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>SPB<sp/>I/O閻╊喗鐖?</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="19" refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" refkind="member"><highlight class="normal">}<sp/><ref refid="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" kindref="member">SPI_DEVICE_CONTEXT</ref>,<sp/>*<ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref>;</highlight></codeline>
<codeline lineno="20"><highlight class="normal"></highlight></codeline>
<codeline lineno="21"><highlight class="normal"></highlight><highlight class="comment">//<sp/>鐠佹儳顦稉濠佺瑓閺傚洩顔栭梻顔兼珤</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="22"><highlight class="normal">WDF_DECLARE_CONTEXT_TYPE_WITH_NAME(<ref refid="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" kindref="member">SPI_DEVICE_CONTEXT</ref>,<sp/>GetSPIContext)</highlight></codeline>
<codeline lineno="23"><highlight class="normal"></highlight></codeline>
<codeline lineno="24"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI娴肩姾绶搾鍛婢跺嫮鎮婇崶鐐剁殶</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="25" refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" refkind="member"><highlight class="normal">EVT_WDF_TIMER<sp/><ref refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" kindref="member">SPITransferTimerExpired</ref>;</highlight></codeline>
<codeline lineno="26"><highlight class="normal"></highlight></codeline>
<codeline lineno="27"><highlight class="normal"></highlight><highlight class="comment">//<sp/>SPI濮圭姵鐖ｇ拋?#define<sp/>SPI_POOL_TAG<sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&apos;IPSK&apos;</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="28"><highlight class="normal"></highlight></codeline>
<codeline lineno="29"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="30"><highlight class="comment"><sp/>*<sp/>SPIInitialize<sp/>-<sp/>u521du59cbu5316SPIu603bu7ebfu63a5u53e3</highlight></codeline>
<codeline lineno="31"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="32"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>NTSTATUS</highlight></codeline>
<codeline lineno="33" refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" refkind="member"><highlight class="normal"><ref refid="spi__core_8c_1a685d8d7731e750c1512b975df16cc030" kindref="member">SPIInitialize</ref>(</highlight></codeline>
<codeline lineno="34"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device,</highlight></codeline>
<codeline lineno="35"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="kmdf__spi_8h_1a25212ee83b198babc11d7c726564c07c" kindref="member">PSPI_CONFIG</ref><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref></highlight></codeline>
<codeline lineno="36"><highlight class="normal">)</highlight></codeline>
<codeline lineno="37"><highlight class="normal">{</highlight></codeline>
<codeline lineno="38"><highlight class="normal"><sp/><sp/><sp/><sp/>NTSTATUS<sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="39"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="40"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG<sp/>timerConfig;</highlight></codeline>
<codeline lineno="41"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>timerAttributes;</highlight></codeline>
<codeline lineno="42"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG<sp/>lockConfig;</highlight></codeline>
<codeline lineno="43"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>lockAttributes;</highlight></codeline>
<codeline lineno="44"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES<sp/>objectAttributes;</highlight></codeline>
<codeline lineno="45"><highlight class="normal"><sp/><sp/><sp/><sp/>DECLARE_UNICODE_STRING_SIZE(<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,<sp/>RESOURCE_HUB_PATH_SIZE);</highlight></codeline>
<codeline lineno="46"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_IO_TARGET_OPEN_PARAMS<sp/><ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>;</highlight></codeline>
<codeline lineno="47"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="48"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Initializing<sp/>SPI<sp/>bus&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="49"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="50"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u9a8cu8bc1u53c2u6570</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="51"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(Device<sp/>==<sp/>NULL<sp/>||<sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="52"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="error__codes_8h_1a176960f93d11db6f58d10a6957a80f38" kindref="member">ERROR_INVALID_PARAMETER</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Invalid<sp/>parameters&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="53"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a29feb6a8256030d356f8bfd96118d1cf" kindref="member">STATUS_INVALID_PARAMETER</ref>;</highlight></codeline>
<codeline lineno="54"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="55"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="56"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u5206u914du548cu521du59cbu5316u4e0au4e0bu6587</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="57"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&amp;objectAttributes,<sp/><ref refid="spi__core_8c_1a2a4a689dbe0ef33045635ddfa5db3194" kindref="member">SPI_DEVICE_CONTEXT</ref>);</highlight></codeline>
<codeline lineno="58"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfObjectAllocateContext(Device,<sp/>&amp;objectAttributes,<sp/>(PVOID*)&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>);</highlight></codeline>
<codeline lineno="59"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="60"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>allocate<sp/>SPI<sp/>device<sp/>context&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="61"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="62"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="63"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="64"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WdfDevice<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="65"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="i2c__device_8c_1ae00ba03b0ccf840fa864cc07b330dbd0" kindref="member">RtlCopyMemory</ref>(&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;Config,<sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>,<sp/></highlight><highlight class="keyword">sizeof</highlight><highlight class="normal">(<ref refid="kmdf__spi_8h_1aa750b6896a759b95054bedea9ad132d9" kindref="member">SPI_CONFIG</ref>));</highlight></codeline>
<codeline lineno="66"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="67"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u521bu5efau81eau65cbu9501u4ee5u4fddu62a4SPIu4f20u8f93</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="68"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_SPINLOCK_CONFIG_INIT(&amp;lockConfig,<sp/>WdfSpinLockTypeExclusive);</highlight></codeline>
<codeline lineno="69"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;lockAttributes);</highlight></codeline>
<codeline lineno="70"><highlight class="normal"><sp/><sp/><sp/><sp/>lockAttributes.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="71"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="72"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfSpinLockCreate(&amp;lockAttributes,<sp/>&amp;lockConfig,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;TransferLock);</highlight></codeline>
<codeline lineno="73"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="74"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>spinlock<sp/>for<sp/>SPI<sp/>transfers&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="75"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="76"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="77"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="78"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u521bu5efau770bu95e8u72d7u5b9au65f6u5668u4ee5u5904u7406SPIu8d85u65f6</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="79"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_TIMER_CONFIG_INIT(&amp;timerConfig,<sp/><ref refid="spi__core_8c_1a85e21cb755e2b8afb53a12e0413ddfb1" kindref="member">SPITransferTimerExpired</ref>);</highlight></codeline>
<codeline lineno="80"><highlight class="normal"><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;timerAttributes);</highlight></codeline>
<codeline lineno="81"><highlight class="normal"><sp/><sp/><sp/><sp/>timerAttributes.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="82"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="83"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfTimerCreate(&amp;timerConfig,<sp/>&amp;timerAttributes,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer);</highlight></codeline>
<codeline lineno="84"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="85"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>watchdog<sp/>timer<sp/>for<sp/>SPI&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="86"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="87"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="88"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="89"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u521bu5efau5e76u8fdeu63a5u5230SPB<sp/>I/Ou76eeu6807</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="90"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>-&gt;SpbDeviceObject<sp/>!=<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="91"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u521bu5efaIOu76eeu6807</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="92"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_OBJECT_ATTRIBUTES_INIT(&amp;objectAttributes);</highlight></codeline>
<codeline lineno="93"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>objectAttributes.ParentObject<sp/>=<sp/>Device;</highlight></codeline>
<codeline lineno="94"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="95"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfIoTargetCreate(Device,<sp/>&amp;objectAttributes,<sp/>&amp;<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="96"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="97"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>IO<sp/>target&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="98"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="99"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="100"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="101"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u6784u5efa<sp/>SPB<sp/>u8d44u6e90u8defu5f84</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="102"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>RESOURCE_HUB_CREATE_PATH_FROM_ID(</highlight></codeline>
<codeline lineno="103"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,</highlight></codeline>
<codeline lineno="104"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>-&gt;SpbConnectionId.LowPart,</highlight></codeline>
<codeline lineno="105"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>-&gt;SpbConnectionId.HighPart);</highlight></codeline>
<codeline lineno="106"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="107"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="108"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>create<sp/>SPB<sp/>resource<sp/>path&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="109"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="110"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="111"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="112"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u6253u5f00IOu76eeu6807</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="113"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WDF_IO_TARGET_OPEN_PARAMS_INIT_OPEN_BY_NAME(</highlight></codeline>
<codeline lineno="114"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>,</highlight></codeline>
<codeline lineno="115"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>&amp;<ref refid="gpio__core_8c_1af7d60c8c4b9613f737c7d254aced2bde" kindref="member">spbDevicePath</ref>,</highlight></codeline>
<codeline lineno="116"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>GENERIC_READ<sp/>|<sp/><ref refid="gpio__core_8c_1a319be52f8fb7536ca4d2f35163ab0ad3" kindref="member">GENERIC_WRITE</ref>);</highlight></codeline>
<codeline lineno="117"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="118"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref><sp/>=<sp/>WdfIoTargetOpen(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget,<sp/>&amp;<ref refid="gpio__core_8c_1ad7d33086f63a42bdcbecdd995751fb96" kindref="member">openParams</ref>);</highlight></codeline>
<codeline lineno="119"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(!<ref refid="precomp_8h_1ad14231612b7a675d33f0ead0b695d21a" kindref="member">NT_SUCCESS</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>))<sp/>{</highlight></codeline>
<codeline lineno="120"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="error__handling_8c_1a1a6e352a97a92a34f977476294a16f4e" kindref="member">LogError</ref>(<ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>,<sp/>__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Failed<sp/>to<sp/>open<sp/>SPB<sp/>IO<sp/>target&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="121"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a9611b3a00430a86619b5923de30f9fdb" kindref="member">status</ref>;</highlight></codeline>
<codeline lineno="122"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="123"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="124"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPB<sp/>IO<sp/>target<sp/>opened<sp/>successfully&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="125"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="126"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="127"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>TRUE;</highlight></codeline>
<codeline lineno="128"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>bus<sp/>initialized<sp/>successfully,<sp/>mode=%d,<sp/>speed=%d<sp/>Hz&quot;</highlight><highlight class="normal">,<sp/></highlight></codeline>
<codeline lineno="129"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>-&gt;Mode,<sp/><ref refid="spi__device_8c_1addbc5753ca32543e25382ea5a386d59b" kindref="member">SpiConfig</ref>-&gt;MaxClockFrequency);</highlight></codeline>
<codeline lineno="130"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="131"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal"><sp/><ref refid="gpio__core_8c_1a77b4762318f24dff847f94f382cfeea6" kindref="member">STATUS_SUCCESS</ref>;</highlight></codeline>
<codeline lineno="132"><highlight class="normal">}</highlight></codeline>
<codeline lineno="133"><highlight class="normal"></highlight></codeline>
<codeline lineno="134"><highlight class="normal"></highlight><highlight class="comment">/*</highlight></codeline>
<codeline lineno="135"><highlight class="comment"><sp/>*<sp/>SPIUninitialize<sp/>-<sp/>濞撳懐鎮奡PI閹崵鍤庨幒銉ュ經</highlight></codeline>
<codeline lineno="136"><highlight class="comment"><sp/>*/</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="137"><highlight class="normal"><ref refid="core__types_8h_1a1cb14808a3eba8cd3fcc47bd1207a805" kindref="member">WDFAPI</ref><sp/>VOID</highlight></codeline>
<codeline lineno="138" refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" refkind="member"><highlight class="normal"><ref refid="spi__core_8c_1ad756f8e3b06fdfa545a7048661038513" kindref="member">SPIUninitialize</ref>(</highlight></codeline>
<codeline lineno="139"><highlight class="normal"><sp/><sp/><sp/><sp/>_In_<sp/><ref refid="core__types_8h_1a12801eda5ee93795601aebf8aa218fb1" kindref="member">WDFDEVICE</ref><sp/>Device</highlight></codeline>
<codeline lineno="140"><highlight class="normal">)</highlight></codeline>
<codeline lineno="141"><highlight class="normal">{</highlight></codeline>
<codeline lineno="142"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="spi__core_8c_1a5e8b4813a61b999753aee353d4944c23" kindref="member">PSPI_DEVICE_CONTEXT</ref><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>=<sp/>GetSPIContext(Device);</highlight></codeline>
<codeline lineno="143"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="144"><highlight class="normal"><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;Uninitializing<sp/>SPI<sp/>bus&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="145"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="146"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref><sp/>==<sp/>NULL)<sp/>{</highlight></codeline>
<codeline lineno="147"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1aa47a100aaaa86f29c113feda40125d64" kindref="member">LogWarning</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>device<sp/>context<sp/>is<sp/>NULL&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="148"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">return</highlight><highlight class="normal">;</highlight></codeline>
<codeline lineno="149"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="150"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="151"><highlight class="normal"><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized)<sp/>{</highlight></codeline>
<codeline lineno="152"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u505cu6b62u4efbu4f55u6b63u5728u8fdbu884cu7684u4f20u8f93</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="153"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer)<sp/>{</highlight></codeline>
<codeline lineno="154"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfTimerStop(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;WatchdogTimer,<sp/>TRUE);</highlight></codeline>
<codeline lineno="155"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="156"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="157"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="comment">//<sp/>u5173u95edSPB<sp/>IOu76eeu6807</highlight><highlight class="normal"></highlight></codeline>
<codeline lineno="158"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight><highlight class="keywordflow">if</highlight><highlight class="normal"><sp/>(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget)<sp/>{</highlight></codeline>
<codeline lineno="159"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfIoTargetClose(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="160"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>WdfObjectDelete(<ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget);</highlight></codeline>
<codeline lineno="161"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;SpbIoTarget<sp/>=<sp/>NULL;</highlight></codeline>
<codeline lineno="162"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="163"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/></highlight></codeline>
<codeline lineno="164"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="gpio__device_8c_1a7b6a29716fe6f8117de54edaedc57974" kindref="member">deviceContext</ref>-&gt;DeviceInitialized<sp/>=<sp/>FALSE;</highlight></codeline>
<codeline lineno="165"><highlight class="normal"><sp/><sp/><sp/><sp/><sp/><sp/><sp/><sp/><ref refid="include_2core_2log_2driver__log_8h_1a1f2322291188e5d028f82ff2f340e5e3" kindref="member">LogInfo</ref>(__FUNCTION__,<sp/>__LINE__,<sp/></highlight><highlight class="stringliteral">&quot;SPI<sp/>bus<sp/>uninitialized&quot;</highlight><highlight class="normal">);</highlight></codeline>
<codeline lineno="166"><highlight class="normal"><sp/><sp/><sp/><sp/>}</highlight></codeline>
<codeline lineno="167"><highlight class="normal">}</highlight></codeline>
    </programlisting>
    <location file="C:/KMDF Driver1/src/hal/bus/spi_core.c"/>
  </compounddef>
</doxygen>
