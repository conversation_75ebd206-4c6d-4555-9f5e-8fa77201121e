digraph "C:/KMDF Driver1/include/common/ioctl.h"
{
 // LATEX_PDF_SIZE
  bgcolor="transparent";
  edge [fontname=Helvetica,fontsize=10,labelfontname=Helvetica,labelfontsize=10];
  node [fontname=Helvetica,fontsize=10,shape=box,height=0.2,width=0.4];
  Node1 [id="Node000001",label="C:/KMDF Driver1/include\l/common/ioctl.h",height=0.2,width=0.4,color="gray40", fillcolor="grey60", style="filled", fontcolor="black",tooltip=" "];
  Node1 -> Node2 [id="edge1_Node000001_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node2 [id="Node000002",label="C:/KMDF Driver1/src\l/core/device/device\l_manager.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$device__manager_8c.html",tooltip=" "];
  Node1 -> Node3 [id="edge2_Node000001_Node000003",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node3 [id="Node000003",label="C:/KMDF Driver1/src\l/precomp.h",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8h.html",tooltip=" "];
  Node3 -> Node2 [id="edge3_Node000003_Node000002",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node3 -> Node4 [id="edge4_Node000003_Node000004",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node4 [id="Node000004",label="C:/KMDF Driver1/src\l/core/driver/driver\l_entry.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__entry_8c.html",tooltip=" "];
  Node3 -> Node5 [id="edge5_Node000003_Node000005",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node5 [id="Node000005",label="C:/KMDF Driver1/src\l/core/log/driver_log.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__log_8c.html",tooltip=" "];
  Node3 -> Node6 [id="edge6_Node000003_Node000006",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node6 [id="Node000006",label="C:/KMDF Driver1/src\l/driver_main.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$driver__main_8c.html",tooltip=" "];
  Node3 -> Node7 [id="edge7_Node000003_Node000007",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node7 [id="Node000007",label="C:/KMDF Driver1/src\l/hal/bus/gpio_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$gpio__core_8c.html",tooltip=" "];
  Node3 -> Node8 [id="edge8_Node000003_Node000008",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node8 [id="Node000008",label="C:/KMDF Driver1/src\l/hal/bus/i2c_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$i2c__core_8c.html",tooltip=" "];
  Node3 -> Node9 [id="edge9_Node000003_Node000009",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node9 [id="Node000009",label="C:/KMDF Driver1/src\l/hal/bus/spi_core.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$spi__core_8c.html",tooltip=" "];
  Node3 -> Node10 [id="edge10_Node000003_Node000010",dir="back",color="steelblue1",style="solid",tooltip=" "];
  Node10 [id="Node000010",label="C:/KMDF Driver1/src\l/precomp.c",height=0.2,width=0.4,color="grey40", fillcolor="white", style="filled",URL="$precomp_8c.html",tooltip=" "];
}
