<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.14.0"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>KMDF Driver: C:/KMDF Driver1/include/hal/bus/kmdf_spi.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">KMDF Driver
   </div>
   <div id="projectbrief">Windows Kernel Mode Driver Framework Project</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.14.0 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search/",'.html');
</script>
<script type="text/javascript">
$(function() { codefold.init(); });
</script>
<div id="main-nav">
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect" class="search-icon" onmouseover="return searchBox.OnSearchSelectShow()" onmouseout="return searchBox.OnSearchSelectHide()"><span class="search-icon-dropdown"></span></span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><div id="MSearchCloseImg" class="close-icon"></div></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- main-nav -->
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(function(){initNavTree('kmdf__spi_8h_source.html','',''); });
</script>
<div id="container">
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">kmdf_spi.h</div></div>
</div><!--header-->
<div class="contents">
<a href="kmdf__spi_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment"> * kmdf_spi.h</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment"> *</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment"> * SPI总线接口头文件</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment"> * 提供SPI特定的接口和数据结构</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"> */</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span> </div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="preprocessor">#ifndef KMDF_SPI_H</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#define KMDF_SPI_H</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span> </div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#include &quot;<a class="code" href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a>&quot;</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span> </div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment">// SPI的模式定义</span></div>
<div class="foldopen" id="foldopen00014" data-start="{" data-end="};">
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374">   14</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a> {</div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249">   15</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249">SpiMode0</a> = 0,  <span class="comment">// CPOL=0, CPHA=0</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972">   16</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972">SpiMode1</a> = 1,  <span class="comment">// CPOL=0, CPHA=1</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e">   17</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e">SpiMode2</a> = 2,  <span class="comment">// CPOL=1, CPHA=0</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470">   18</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470">SpiMode3</a> = 3   <span class="comment">// CPOL=1, CPHA=1</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">   19</a></span>} <a class="code hl_typedef" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a>;</div>
</div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span> </div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment">// SPI最大速度选择</span></div>
<div class="foldopen" id="foldopen00022" data-start="{" data-end="};">
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2">   22</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a> {</div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2">   23</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2">SpiBusSpeed1MHz</a> = 1000000,      <span class="comment">// 1 MHz</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab">   24</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab">SpiBusSpeed2MHz</a> = 2000000,      <span class="comment">// 2 MHz</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94">   25</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94">SpiBusSpeed4MHz</a> = 4000000,      <span class="comment">// 4 MHz</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928">   26</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928">SpiBusSpeed8MHz</a> = 8000000,      <span class="comment">// 8 MHz</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997">   27</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997">SpiBusSpeed10MHz</a> = 10000000,    <span class="comment">// 10 MHz</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90">   28</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90">SpiBusSpeed20MHz</a> = 20000000,    <span class="comment">// 20 MHz</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef">   29</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef">SpiBusSpeed25MHz</a> = 25000000,    <span class="comment">// 25 MHz</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc">   30</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc">SpiBusSpeed50MHz</a> = 50000000     <span class="comment">// 50 MHz</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57">   31</a></span>} <a class="code hl_typedef" href="kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a>;</div>
</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span> </div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="comment">// SPI特定配置结构体</span></div>
<div class="foldopen" id="foldopen00034" data-start="{" data-end="};">
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html">   34</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__spi_8h.html#struct__SPI__CONFIG">_SPI_CONFIG</a> {</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a5995a0bc6695e2e61567693ec8e494ca">   35</a></span>    <a class="code hl_typedef" href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a> <a class="code hl_variable" href="kmdf__spi_8h.html#a5995a0bc6695e2e61567693ec8e494ca">Mode</a>;                   <span class="comment">// SPI模式 (0-3)</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a6cf9c836c9e4e1a5b0dd2ffc18e218e6">   36</a></span>    <a class="code hl_typedef" href="kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a> <a class="code hl_variable" href="kmdf__spi_8h.html#a6cf9c836c9e4e1a5b0dd2ffc18e218e6">MaxClockFrequency</a>; <span class="comment">// 最大时钟频率</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a5568dcea126ea711e8d0f6b40dc951cc">   37</a></span>    ULONG <a class="code hl_variable" href="kmdf__spi_8h.html#a5568dcea126ea711e8d0f6b40dc951cc">ChipSelectLine</a>;           <span class="comment">// 片选线</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a23bf21cffc603724543545d3c677093a">   38</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__spi_8h.html#a23bf21cffc603724543545d3c677093a">ChipSelectPolarity</a>;     <span class="comment">// 片选极性 (TRUE = 高电平有效)</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a2fb798d2a102a3bc148918a6528aa329">   39</a></span>    ULONG <a class="code hl_variable" href="kmdf__spi_8h.html#a2fb798d2a102a3bc148918a6528aa329">WordLength</a>;                <span class="comment">// 字长度 (4-32位)</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ad132bb074410f64505375ed71b0173f8">   40</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__spi_8h.html#ad132bb074410f64505375ed71b0173f8">IsLsbFirst</a>;              <span class="comment">// 低位优先传输</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a85af30c409a08baa8e9c3e31567c07c2">   41</a></span>    ULONG <a class="code hl_variable" href="kmdf__spi_8h.html#a85af30c409a08baa8e9c3e31567c07c2">TimeoutMs</a>;                <span class="comment">// 默认超时时间 (毫秒)</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a3eecf9d65abadb2106c4930606d31c7a">   42</a></span>    ULONG <a class="code hl_variable" href="kmdf__spi_8h.html#a3eecf9d65abadb2106c4930606d31c7a">RetryCount</a>;               <span class="comment">// 失败重试次数</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a065d24ae185eeafe1fc5ce74cf823454">   43</a></span>    PVOID <a class="code hl_variable" href="kmdf__spi_8h.html#a065d24ae185eeafe1fc5ce74cf823454">SpbDeviceObject</a>;           <span class="comment">// SPB设备对象</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#aed13983e08690b185df2a6c06f44ffd3">   44</a></span>    LARGE_INTEGER <a class="code hl_variable" href="kmdf__spi_8h.html#aed13983e08690b185df2a6c06f44ffd3">SpbConnectionId</a>;   <span class="comment">// SPB连接ID</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">   45</a></span>} <a class="code hl_typedef" href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a>, *<a class="code hl_typedef" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a>;</div>
</div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span> </div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="comment">// SPI传输类型</span></div>
<div class="foldopen" id="foldopen00048" data-start="{" data-end="};">
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21">   48</a></span><span class="keyword">typedef</span> <span class="keyword">enum</span> <a class="code hl_enumeration" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a> {</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a">   49</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a">SpiWrite</a>,              <span class="comment">// 写操作</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d">   50</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d">SpiRead</a>,               <span class="comment">// 读操作</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36">   51</a></span>    <a class="code hl_enumvalue" href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36">SpiWriteRead</a>           <span class="comment">// 同时读写操作</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca">   52</a></span>} <a class="code hl_typedef" href="kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a>;</div>
</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span> </div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="comment">// SPI传输包结构体</span></div>
<div class="foldopen" id="foldopen00055" data-start="{" data-end="};">
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html">   55</a></span><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="kmdf__spi_8h.html#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a> {</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a00931840236e9b48912a56116bd0d109">   56</a></span>    <a class="code hl_typedef" href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a> <a class="code hl_variable" href="kmdf__spi_8h.html#a00931840236e9b48912a56116bd0d109">Common</a>;   <span class="comment">// 通用总线传输包</span></div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ae3ca53594a2138cf5aea46199d1f00fc">   57</a></span>    <a class="code hl_typedef" href="kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a> <a class="code hl_variable" href="kmdf__spi_8h.html#ae3ca53594a2138cf5aea46199d1f00fc">Type</a>;      <span class="comment">// 传输类型</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#adfd0120a5d0f6554506d7a8c4454609b">   58</a></span>    PVOID <a class="code hl_variable" href="kmdf__spi_8h.html#adfd0120a5d0f6554506d7a8c4454609b">WriteBuffer</a>;           <span class="comment">// 写缓冲区</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a60c1c9ddb35c8b5aa7ef2d65b6a278a7">   59</a></span>    SIZE_T <a class="code hl_variable" href="kmdf__spi_8h.html#a60c1c9ddb35c8b5aa7ef2d65b6a278a7">WriteBufferLength</a>;    <span class="comment">// 写缓冲区长度</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a1bd99745fd3afc8bf6567a4460b85f28">   60</a></span>    PVOID <a class="code hl_variable" href="kmdf__spi_8h.html#a1bd99745fd3afc8bf6567a4460b85f28">ReadBuffer</a>;            <span class="comment">// 读缓冲区</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#afc8f8284a74ea4a78d2531d7b09838ed">   61</a></span>    SIZE_T <a class="code hl_variable" href="kmdf__spi_8h.html#afc8f8284a74ea4a78d2531d7b09838ed">ReadBufferLength</a>;     <span class="comment">// 读缓冲区长度</span></div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#ab7012a8e826ed6c62ef8b4e77d5dac24">   62</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__spi_8h.html#ab7012a8e826ed6c62ef8b4e77d5dac24">AssertChipSelect</a>;    <span class="comment">// 是否主动控制片选</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a2c4bee3baf8bbe9d12ea2b02a12deb1c">   63</a></span>    BOOLEAN <a class="code hl_variable" href="kmdf__spi_8h.html#a2c4bee3baf8bbe9d12ea2b02a12deb1c">DeassertChipSelect</a>;  <span class="comment">// 是否在传输后取消片选</span></div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">   64</a></span>} <a class="code hl_typedef" href="kmdf__spi_8h.html#ae53b781a84db92ccef86085a53448289">SPI_TRANSFER_PACKET</a>, *<a class="code hl_typedef" href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a>;</div>
</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span> </div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><span class="comment">// SPI总线特定函数声明</span></div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><a class="code hl_function" href="kmdf__spi_8h.html#a685d8d7731e750c1512b975df16cc030">SPIInitialize</a>(</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    _In_ <a class="code hl_typedef" href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a> <a class="code hl_variable" href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>);</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span> </div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> VOID</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span><a class="code hl_function" href="kmdf__spi_8h.html#ad756f8e3b06fdfa545a7048661038513">SPIUninitialize</a>(</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>);</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span> </div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c">   79</a></span><a class="code hl_function" href="kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c">SPITransferSynchronous</a>(</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    _Inout_ <a class="code hl_typedef" href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>);</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span> </div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033">   86</a></span><a class="code hl_function" href="kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033">SPITransferAsynchronous</a>(</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    _Inout_ <a class="code hl_typedef" href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a> TransferPacket,</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    _In_ <a class="code hl_typedef" href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a> CompletionCallback,</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    _In_opt_ PVOID Context</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>);</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span> </div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f">   94</a></span><a class="code hl_function" href="kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f">SPIWriteRead</a>(</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    _In_reads_bytes_(WriteBufferLength) PVOID <a class="code hl_variable" href="spi__device_8c.html#ad26ded9b73e8b14b4117614b39440d86">WriteBuffer</a>,</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    _In_ ULONG WriteBufferLength,</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>    _Out_writes_bytes_(ReadBufferLength) PVOID <a class="code hl_variable" href="spi__device_8c.html#ac2677e024009c29e2bcee99e0c32c735">ReadBuffer</a>,</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>    _In_ ULONG ReadBufferLength,</div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>);</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span> </div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8">  104</a></span><a class="code hl_function" href="kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8">SPIWriteRegister</a>(</div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    _In_ UCHAR Value,</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>);</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span> </div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><a class="code hl_define" href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a> NTSTATUS</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno"><a class="line" href="kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885">  112</a></span><a class="code hl_function" href="kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885">SPIReadRegister</a>(</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>    _In_ <a class="code hl_typedef" href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a> Device,</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    _In_ UCHAR RegisterAddress,</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>    _Out_ PUCHAR Value,</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>    _In_ ULONG TimeoutMs</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span>);</div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span> </div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span><span class="preprocessor">#endif </span><span class="comment">// KMDF_SPI_H</span></div>
<div class="ttc" id="acore__types_8h_html_a12801eda5ee93795601aebf8aa218fb1"><div class="ttname"><a href="core__types_8h.html#a12801eda5ee93795601aebf8aa218fb1">WDFDEVICE</a></div><div class="ttdeci">struct WDFDEVICE__ * WDFDEVICE</div><div class="ttdef"><b>Definition</b> core_types.h:26</div></div>
<div class="ttc" id="acore__types_8h_html_a1cb14808a3eba8cd3fcc47bd1207a805"><div class="ttname"><a href="core__types_8h.html#a1cb14808a3eba8cd3fcc47bd1207a805">WDFAPI</a></div><div class="ttdeci">#define WDFAPI</div><div class="ttdef"><b>Definition</b> core_types.h:21</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html"><div class="ttname"><a href="kmdf__bus__common_8h.html">kmdf_bus_common.h</a></div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_a3709500586d6c79d8df0693c133a3f2d"><div class="ttname"><a href="kmdf__bus__common_8h.html#a3709500586d6c79d8df0693c133a3f2d">BUS_OPERATION_CALLBACK</a></div><div class="ttdeci">VOID(* BUS_OPERATION_CALLBACK)(PBUS_TRANSFER_PACKET TransferPacket)</div><div class="ttdef"><b>Definition</b> kmdf_bus_common.h:50</div></div>
<div class="ttc" id="akmdf__bus__common_8h_html_aac06c68a58c9667998bbe0975aa78c51"><div class="ttname"><a href="kmdf__bus__common_8h.html#aac06c68a58c9667998bbe0975aa78c51">BUS_TRANSFER_PACKET</a></div><div class="ttdeci">struct _BUS_TRANSFER_PACKET BUS_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a00931840236e9b48912a56116bd0d109"><div class="ttname"><a href="kmdf__spi_8h.html#a00931840236e9b48912a56116bd0d109">_SPI_TRANSFER_PACKET::Common</a></div><div class="ttdeci">BUS_TRANSFER_PACKET Common</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:56</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a02075f39766ac5419ad37fbd5e96de57"><div class="ttname"><a href="kmdf__spi_8h.html#a02075f39766ac5419ad37fbd5e96de57">SPI_BUS_SPEED</a></div><div class="ttdeci">enum _SPI_BUS_SPEED SPI_BUS_SPEED</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a038c52771ec4b0654c0e59f37fccb29f"><div class="ttname"><a href="kmdf__spi_8h.html#a038c52771ec4b0654c0e59f37fccb29f">SPIWriteRead</a></div><div class="ttdeci">WDFAPI NTSTATUS SPIWriteRead(_In_ WDFDEVICE Device, _In_reads_bytes_(WriteBufferLength) PVOID WriteBuffer, _In_ ULONG WriteBufferLength, _Out_writes_bytes_(ReadBufferLength) PVOID ReadBuffer, _In_ ULONG ReadBufferLength, _In_ ULONG TimeoutMs)</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a065d24ae185eeafe1fc5ce74cf823454"><div class="ttname"><a href="kmdf__spi_8h.html#a065d24ae185eeafe1fc5ce74cf823454">_SPI_CONFIG::SpbDeviceObject</a></div><div class="ttdeci">PVOID SpbDeviceObject</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:43</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a1bd99745fd3afc8bf6567a4460b85f28"><div class="ttname"><a href="kmdf__spi_8h.html#a1bd99745fd3afc8bf6567a4460b85f28">_SPI_TRANSFER_PACKET::ReadBuffer</a></div><div class="ttdeci">PVOID ReadBuffer</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:60</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a23bf21cffc603724543545d3c677093a"><div class="ttname"><a href="kmdf__spi_8h.html#a23bf21cffc603724543545d3c677093a">_SPI_CONFIG::ChipSelectPolarity</a></div><div class="ttdeci">BOOLEAN ChipSelectPolarity</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:38</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a25212ee83b198babc11d7c726564c07c"><div class="ttname"><a href="kmdf__spi_8h.html#a25212ee83b198babc11d7c726564c07c">PSPI_CONFIG</a></div><div class="ttdeci">struct _SPI_CONFIG * PSPI_CONFIG</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a261c6752bd8e05e7e4d7eb1e60ed64f8"><div class="ttname"><a href="kmdf__spi_8h.html#a261c6752bd8e05e7e4d7eb1e60ed64f8">SPIWriteRegister</a></div><div class="ttdeci">WDFAPI NTSTATUS SPIWriteRegister(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _In_ UCHAR Value, _In_ ULONG TimeoutMs)</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a2c4bee3baf8bbe9d12ea2b02a12deb1c"><div class="ttname"><a href="kmdf__spi_8h.html#a2c4bee3baf8bbe9d12ea2b02a12deb1c">_SPI_TRANSFER_PACKET::DeassertChipSelect</a></div><div class="ttdeci">BOOLEAN DeassertChipSelect</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:63</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a2fb798d2a102a3bc148918a6528aa329"><div class="ttname"><a href="kmdf__spi_8h.html#a2fb798d2a102a3bc148918a6528aa329">_SPI_CONFIG::WordLength</a></div><div class="ttdeci">ULONG WordLength</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:39</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a3eecf9d65abadb2106c4930606d31c7a"><div class="ttname"><a href="kmdf__spi_8h.html#a3eecf9d65abadb2106c4930606d31c7a">_SPI_CONFIG::RetryCount</a></div><div class="ttdeci">ULONG RetryCount</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:42</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a500fe65207e47be6e52eee4a885d4374"><div class="ttname"><a href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374">_SPI_MODE</a></div><div class="ttdeci">_SPI_MODE</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:14</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249"><div class="ttname"><a href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a3f7ebc9eed0fa3fd7ff2ce6574dfe249">SpiMode0</a></div><div class="ttdeci">@ SpiMode0</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:15</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470"><div class="ttname"><a href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374a4569b4c26e94cb58875edfe995617470">SpiMode3</a></div><div class="ttdeci">@ SpiMode3</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:18</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972"><div class="ttname"><a href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ac1cf990ceaa849737f9b3919fe87a972">SpiMode1</a></div><div class="ttdeci">@ SpiMode1</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:16</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e"><div class="ttname"><a href="kmdf__spi_8h.html#a500fe65207e47be6e52eee4a885d4374ad96a07076874a9907404bb187e26c75e">SpiMode2</a></div><div class="ttdeci">@ SpiMode2</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:17</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a5568dcea126ea711e8d0f6b40dc951cc"><div class="ttname"><a href="kmdf__spi_8h.html#a5568dcea126ea711e8d0f6b40dc951cc">_SPI_CONFIG::ChipSelectLine</a></div><div class="ttdeci">ULONG ChipSelectLine</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:37</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a571fb3ea7eed247b3c46c57f506fa033"><div class="ttname"><a href="kmdf__spi_8h.html#a571fb3ea7eed247b3c46c57f506fa033">SPITransferAsynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS SPITransferAsynchronous(_In_ WDFDEVICE Device, _Inout_ PSPI_TRANSFER_PACKET TransferPacket, _In_ BUS_OPERATION_CALLBACK CompletionCallback, _In_opt_ PVOID Context)</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a5995a0bc6695e2e61567693ec8e494ca"><div class="ttname"><a href="kmdf__spi_8h.html#a5995a0bc6695e2e61567693ec8e494ca">_SPI_CONFIG::Mode</a></div><div class="ttdeci">SPI_MODE Mode</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:35</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a60c1c9ddb35c8b5aa7ef2d65b6a278a7"><div class="ttname"><a href="kmdf__spi_8h.html#a60c1c9ddb35c8b5aa7ef2d65b6a278a7">_SPI_TRANSFER_PACKET::WriteBufferLength</a></div><div class="ttdeci">SIZE_T WriteBufferLength</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:59</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a682c974659ab89363d0baa22470a386c"><div class="ttname"><a href="kmdf__spi_8h.html#a682c974659ab89363d0baa22470a386c">SPITransferSynchronous</a></div><div class="ttdeci">WDFAPI NTSTATUS SPITransferSynchronous(_In_ WDFDEVICE Device, _Inout_ PSPI_TRANSFER_PACKET TransferPacket, _In_ ULONG TimeoutMs)</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a685d8d7731e750c1512b975df16cc030"><div class="ttname"><a href="kmdf__spi_8h.html#a685d8d7731e750c1512b975df16cc030">SPIInitialize</a></div><div class="ttdeci">WDFAPI NTSTATUS SPIInitialize(_In_ WDFDEVICE Device, _In_ PSPI_CONFIG SpiConfig)</div><div class="ttdef"><b>Definition</b> spi_core.c:33</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a6cf9c836c9e4e1a5b0dd2ffc18e218e6"><div class="ttname"><a href="kmdf__spi_8h.html#a6cf9c836c9e4e1a5b0dd2ffc18e218e6">_SPI_CONFIG::MaxClockFrequency</a></div><div class="ttdeci">SPI_BUS_SPEED MaxClockFrequency</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:36</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a85af30c409a08baa8e9c3e31567c07c2"><div class="ttname"><a href="kmdf__spi_8h.html#a85af30c409a08baa8e9c3e31567c07c2">_SPI_CONFIG::TimeoutMs</a></div><div class="ttdeci">ULONG TimeoutMs</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:41</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a8c0c38014d644418137aa056ce518223"><div class="ttname"><a href="kmdf__spi_8h.html#a8c0c38014d644418137aa056ce518223">PSPI_TRANSFER_PACKET</a></div><div class="ttdeci">struct _SPI_TRANSFER_PACKET * PSPI_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__spi_8h_html_a9920771d941aa8c6e1b7b97ce21e77ca"><div class="ttname"><a href="kmdf__spi_8h.html#a9920771d941aa8c6e1b7b97ce21e77ca">SPI_TRANSFER_TYPE</a></div><div class="ttdeci">enum _SPI_TRANSFER_TYPE SPI_TRANSFER_TYPE</div></div>
<div class="ttc" id="akmdf__spi_8h_html_aa750b6896a759b95054bedea9ad132d9"><div class="ttname"><a href="kmdf__spi_8h.html#aa750b6896a759b95054bedea9ad132d9">SPI_CONFIG</a></div><div class="ttdeci">struct _SPI_CONFIG SPI_CONFIG</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ab41da20e3858f2c27bb25ef675858c21"><div class="ttname"><a href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21">_SPI_TRANSFER_TYPE</a></div><div class="ttdeci">_SPI_TRANSFER_TYPE</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:48</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a"><div class="ttname"><a href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a47ee50a4a8281a6a032045f5c4e3de2a">SpiWrite</a></div><div class="ttdeci">@ SpiWrite</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:49</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36"><div class="ttname"><a href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21a7272906f27851ec3b9bc5dc92b5d8b36">SpiWriteRead</a></div><div class="ttdeci">@ SpiWriteRead</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:51</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d"><div class="ttname"><a href="kmdf__spi_8h.html#ab41da20e3858f2c27bb25ef675858c21ab9bf44fc0bf9869af7c97bf5e312fe8d">SpiRead</a></div><div class="ttdeci">@ SpiRead</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:50</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ab7012a8e826ed6c62ef8b4e77d5dac24"><div class="ttname"><a href="kmdf__spi_8h.html#ab7012a8e826ed6c62ef8b4e77d5dac24">_SPI_TRANSFER_PACKET::AssertChipSelect</a></div><div class="ttdeci">BOOLEAN AssertChipSelect</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:62</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ad132bb074410f64505375ed71b0173f8"><div class="ttname"><a href="kmdf__spi_8h.html#ad132bb074410f64505375ed71b0173f8">_SPI_CONFIG::IsLsbFirst</a></div><div class="ttdeci">BOOLEAN IsLsbFirst</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:40</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ad756f8e3b06fdfa545a7048661038513"><div class="ttname"><a href="kmdf__spi_8h.html#ad756f8e3b06fdfa545a7048661038513">SPIUninitialize</a></div><div class="ttdeci">WDFAPI VOID SPIUninitialize(_In_ WDFDEVICE Device)</div><div class="ttdef"><b>Definition</b> spi_core.c:138</div></div>
<div class="ttc" id="akmdf__spi_8h_html_adb5a94e2dc80b87a505aea6c78f3b885"><div class="ttname"><a href="kmdf__spi_8h.html#adb5a94e2dc80b87a505aea6c78f3b885">SPIReadRegister</a></div><div class="ttdeci">WDFAPI NTSTATUS SPIReadRegister(_In_ WDFDEVICE Device, _In_ UCHAR RegisterAddress, _Out_ PUCHAR Value, _In_ ULONG TimeoutMs)</div></div>
<div class="ttc" id="akmdf__spi_8h_html_adfd0120a5d0f6554506d7a8c4454609b"><div class="ttname"><a href="kmdf__spi_8h.html#adfd0120a5d0f6554506d7a8c4454609b">_SPI_TRANSFER_PACKET::WriteBuffer</a></div><div class="ttdeci">PVOID WriteBuffer</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:58</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ae3ca53594a2138cf5aea46199d1f00fc"><div class="ttname"><a href="kmdf__spi_8h.html#ae3ca53594a2138cf5aea46199d1f00fc">_SPI_TRANSFER_PACKET::Type</a></div><div class="ttdeci">SPI_TRANSFER_TYPE Type</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:57</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ae53b781a84db92ccef86085a53448289"><div class="ttname"><a href="kmdf__spi_8h.html#ae53b781a84db92ccef86085a53448289">SPI_TRANSFER_PACKET</a></div><div class="ttdeci">struct _SPI_TRANSFER_PACKET SPI_TRANSFER_PACKET</div></div>
<div class="ttc" id="akmdf__spi_8h_html_ae9c35ffd537d30a103775489f57c24cc"><div class="ttname"><a href="kmdf__spi_8h.html#ae9c35ffd537d30a103775489f57c24cc">SPI_MODE</a></div><div class="ttdeci">enum _SPI_MODE SPI_MODE</div></div>
<div class="ttc" id="akmdf__spi_8h_html_aed13983e08690b185df2a6c06f44ffd3"><div class="ttname"><a href="kmdf__spi_8h.html#aed13983e08690b185df2a6c06f44ffd3">_SPI_CONFIG::SpbConnectionId</a></div><div class="ttdeci">LARGE_INTEGER SpbConnectionId</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:44</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2">_SPI_BUS_SPEED</a></div><div class="ttdeci">_SPI_BUS_SPEED</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:22</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a091d3394d050e28226301149105b82fc">SpiBusSpeed50MHz</a></div><div class="ttdeci">@ SpiBusSpeed50MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:30</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a1134c5f96a05a2f1da6ccdc5b9e79d94">SpiBusSpeed4MHz</a></div><div class="ttdeci">@ SpiBusSpeed4MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:25</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a328ddb4fb884d7c3bb86a026494bf997">SpiBusSpeed10MHz</a></div><div class="ttdeci">@ SpiBusSpeed10MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:27</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a358b291abb7137e1e4400e930ad72928">SpiBusSpeed8MHz</a></div><div class="ttdeci">@ SpiBusSpeed8MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:26</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a4ba0662d51e35b071d97c0df4aaac7f2">SpiBusSpeed1MHz</a></div><div class="ttdeci">@ SpiBusSpeed1MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:23</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2a9f4188db8b3792109ae30573e2a28fef">SpiBusSpeed25MHz</a></div><div class="ttdeci">@ SpiBusSpeed25MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:29</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2ae6da8c1ee0db2808137e858daae1c6ab">SpiBusSpeed2MHz</a></div><div class="ttdeci">@ SpiBusSpeed2MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:24</div></div>
<div class="ttc" id="akmdf__spi_8h_html_af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90"><div class="ttname"><a href="kmdf__spi_8h.html#af2e782dfd4d775865e0976660817e6e2aec0170eb8cf508311fae30688cbdaf90">SpiBusSpeed20MHz</a></div><div class="ttdeci">@ SpiBusSpeed20MHz</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:28</div></div>
<div class="ttc" id="akmdf__spi_8h_html_afc8f8284a74ea4a78d2531d7b09838ed"><div class="ttname"><a href="kmdf__spi_8h.html#afc8f8284a74ea4a78d2531d7b09838ed">_SPI_TRANSFER_PACKET::ReadBufferLength</a></div><div class="ttdeci">SIZE_T ReadBufferLength</div><div class="ttdef"><b>Definition</b> kmdf_spi.h:61</div></div>
<div class="ttc" id="akmdf__spi_8h_html_struct__SPI__CONFIG"><div class="ttname"><a href="kmdf__spi_8h.html#struct__SPI__CONFIG">_SPI_CONFIG</a></div><div class="ttdef"><b>Definition</b> kmdf_spi.h:34</div></div>
<div class="ttc" id="akmdf__spi_8h_html_struct__SPI__TRANSFER__PACKET"><div class="ttname"><a href="kmdf__spi_8h.html#struct__SPI__TRANSFER__PACKET">_SPI_TRANSFER_PACKET</a></div><div class="ttdef"><b>Definition</b> kmdf_spi.h:55</div></div>
<div class="ttc" id="aspi__device_8c_html_ac2677e024009c29e2bcee99e0c32c735"><div class="ttname"><a href="spi__device_8c.html#ac2677e024009c29e2bcee99e0c32c735">ReadBuffer</a></div><div class="ttdeci">packet ReadBuffer</div><div class="ttdef"><b>Definition</b> spi_device.c:223</div></div>
<div class="ttc" id="aspi__device_8c_html_ad26ded9b73e8b14b4117614b39440d86"><div class="ttname"><a href="spi__device_8c.html#ad26ded9b73e8b14b4117614b39440d86">WriteBuffer</a></div><div class="ttdeci">packet WriteBuffer</div><div class="ttdef"><b>Definition</b> spi_device.c:221</div></div>
<div class="ttc" id="aspi__device_8c_html_addbc5753ca32543e25382ea5a386d59b"><div class="ttname"><a href="spi__device_8c.html#addbc5753ca32543e25382ea5a386d59b">SpiConfig</a></div><div class="ttdeci">PSPI_CONFIG SpiConfig</div><div class="ttdef"><b>Definition</b> spi_device.c:20</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
</div><!-- container -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a href="dir_a413b7f902cba5167b433a6fe834d5bd.html">hal</a></li><li class="navelem"><a href="dir_c5d1a81f9f5aef5a9f7467903b289108.html">bus</a></li><li class="navelem"><a href="kmdf__spi_8h.html">kmdf_spi.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.14.0 </li>
  </ul>
</div>
</body>
</html>
